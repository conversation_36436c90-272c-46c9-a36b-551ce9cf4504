/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: 存储实例，提供存储引擎初始化，以及获取存储实例等相关接口。
 * 以及相关的存储实例配置都会在此模块中定义。
 * Author: yangenle
 * Create: 2020-8-12
 */
#include "se_instance.h"
#include "db_inter_process_mgr.h"
#include "se_database.h"
#include "se_diskann_redo_am.h"
#include "se_lpasmem_redo_am.h"
#include "se_fixed_heap_inner.h"
#ifdef FEATURE_CSTORE
#include "se_cstore.h"
#endif
#include "se_heap_utils.h"
#include "se_resource_column_inner.h"
#include "se_index_inner.h"
#include "se_hash_redo_am.h"
#include "se_lfsmgr.h"
#include "se_trx_base.h"
#include "se_trx_mgr.h"
#include "se_index_common.h"
#include "se_lock.h"
#include "se_log.h"
#include "se_resource_session_pub.h"
#include "se_undo.h"
#include "db_sysapp_context.h"
#include "db_dyn_load.h"
#include "se_space_inner.h"
#include "se_recovery.h"
#include "se_redo.h"
#include "se_redo_inner.h"
#include "se_persist_inner.h"
#include "se_ckpt.h"
#include "se_heap_redo_am.h"
#include "se_lfsmgr_redo_am.h"
#include "se_durable_memdata_redo.h"
#include "se_buffer_pool.h"
#include "se_btree_redo_am.h"
#include "db_thread_pool_tls.h"
#include "se_rsm_tablespace_am.h"
#include "se_ckpt_inner.h"
#include "adpt_digest_generate.h"
#if defined(FEATURE_DAF) || defined(FEATURE_HAC)
#include "se_daf.h"
#endif
#include "db_instance.h"

#ifdef __cplusplus
extern "C" {
#endif

#define SE_TOP_SHM_CTX_BASE_SIZE (1024 * 512)  // 512K is enough for SE instance during server initialization
#define SE_TOP_SHM_CTX_BASE_SIZE_16G (1024 * 1024 * 512)

#ifdef FEATURE_DURABLE_MEMDATA
#define SE_TOP_SHM_CTX_STEP_SIZE (1024 * 1024 * 16)  // 16M is enough for SE instance during memory allocation
#else
#define SE_TOP_SHM_CTX_STEP_SIZE (1024 * 1024 * 5)  // 5M is enough for SE instance during memory allocation
#endif

#define SE_TOP_SHM_CTX_MAX_SIZE (1024 * 1024 * 1024)  // 1G equal to default setting SHM_BLOCK_POOL_MAX_SIZE

// durable memdata array need >5g size when sememctx is 1T
#define SE_TOP_SHM_CTX_MAX_SIZE_16G (uint64_t)(10ULL * 1024ULL * 1024ULL * 1024ULL)

// 为什么以8G内存上限为分界线？
// 1. 先讨论为什么需要4字节保存chunkId:
// 如果device为512M，那么在设置pageSize为8K后，共切分为65536个chunk，再大一点chunkId就需要32位才能存下。
// 为了保持一致，chunkId就一直使用32位保存，参考ChunkStatusT(多余的用来做状态位)
// 2. 再讨论为什么以8G为分界线，使用更大的步长:
// 又根据maxSeMem最大可以设置16G，那么全部切分完后，有2M个chunk，那至少需要2M x 4 = 8M 的内存
// 而在maxSeMem设置8G，pageSize设置8K情况下，内存需要上述一半即4M，那5M也够用了。
#define SE_MEM_8G (8 * 1024 * 1024)  // 8G 属于较大的内存，需要做一些内存控制
#define SE_MEM_16G (16 * 1024 * 1024)
#define SE_TOP_SHM_CTX_STEP_SIZE_8G (1024 * 1024 * 9)     // 内存需求较大时的步长控制
#define SE_TOP_SHM_CTX_STEP_SIZE_16G (1024 * 1024 * 512)  // 内存需求较大时的步长控制
#define SE_MAX_LPASMEM_INDEX_NUM (64)                     // 单个实例最大算法索引表数量

void SeReleasePathConfigMem(DbMemCtxT *memCtx, SeConfigT *config, char **path)
{
    DB_POINTER2(memCtx, config);
    if (path != NULL) {
        for (uint32_t i = 0; i < config->multiZoneNum; i++) {
            if (path[i] != NULL) {
                DbDynMemCtxFree(memCtx, path[i]);
            }
        }
        DbDynMemCtxFree(memCtx, path);
    }
}

void SeReleaseConfigMem(DbMemCtxT *memCtx, SeConfigT *config)
{
    SeReleasePathConfigMem(memCtx, config, config->redoDir);
    config->redoDir = NULL;
    SeReleasePathConfigMem(memCtx, config, config->dataFileDirPath);
    config->dataFileDirPath = NULL;
    SeReleasePathConfigMem(memCtx, config, config->ctrlFileDirPath);
    config->ctrlFileDirPath = NULL;
    DbDynMemCtxFree(memCtx, config->ctrlFileName);
    config->ctrlFileName = NULL;
    DbDynMemCtxFree(memCtx, config->systemSpaceFileName);
    config->systemSpaceFileName = NULL;
    DbDynMemCtxFree(memCtx, config->undoSpaceFileName);
    config->undoSpaceFileName = NULL;
    DbDynMemCtxFree(memCtx, config->userSpaceFileName);
    config->userSpaceFileName = NULL;
    DbDynMemCtxFree(memCtx, config->safeFileName);
    config->safeFileName = NULL;
    DbDynMemCtxFree(memCtx, config->redoFilePrefix);
    config->redoFilePrefix = NULL;
}

Status SeMallocPathConfigMem(DbMemCtxT *memCtx, SeConfigT *config, char ***path)
{
    DB_POINTER3(memCtx, config, path);
    uint32_t size = (uint32_t)sizeof(char *) * config->multiZoneNum;
    char **newPath = (char **)DbDynMemCtxAlloc(memCtx, size);
    if (newPath == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "alloc path array, multiZoneNum %" PRIu32, config->multiZoneNum);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memset_s(newPath, size, 0, size);
    *path = newPath;
    return GMERR_OK;
}

StatusInter SeRegisterReplayFunc(SeInstanceT *seInsPtr)
{
    // 目前它不能放在 SeCtrlInit 里注册space的replay函数，因为 RedoInit 不应该放在 SeCtrlInit前面
    StatusInter status = SeSpaceReplayFuncRegister(seInsPtr);
    if (status != STATUS_OK_INTER) {
        return status;
    }
    HeapRedoInit(seInsPtr);
    LfsRedoInit(seInsPtr);
    status = UndoRedoInit(seInsPtr);
    if (status != STATUS_OK_INTER) {
        return status;
    }
    BTreeIndexRedoInit(seInsPtr);
#ifdef FEATURE_DISKANN
    DiskAnnIndexRedoInit(seInsPtr);
#endif
#ifdef FEATURE_LPASMEM
    LpasMemIndexRedoInit(seInsPtr);
#endif
    HashIndexRedoInit(seInsPtr);
    /* sub module redo log init here, eg.
    FixedHeapRedoInit */
    return STATUS_OK_INTER;
}

StatusInter SeInitPersistence(SeInstanceT *seInsPtr)
{
    StatusInter status = STATUS_OK_INTER;
    if (seInsPtr->seConfig.tamperProofEnable) {
        status = DbGetStatusInterErrno(DbAdptLoadDigestFunc(seInsPtr->seConfig.tamperProofSoPath));
        if (status != STATUS_OK_INTER) {
            return status;
        }
    }
    status = SeCtrlInit(seInsPtr);
    if (status != STATUS_OK_INTER) {
        return status;
    }
    status = DbAllocCtrlLatch(seInsPtr);
    if (status != STATUS_OK_INTER) {
        return status;
    }
    SeSpaceInit(seInsPtr);
    status = DbLoadCtrlFile(seInsPtr, false);
    if (status != STATUS_OK_INTER) {
        return status;
    }
    status = DbSpaceCompressionInit(seInsPtr);
    if (status != STATUS_OK_INTER) {
        return status;
    }
    if (SeGetPersistMode() == PERSIST_INCREMENT) {
        status = RedoInit(seInsPtr);
        if (status != STATUS_OK_INTER) {
            return status;
        }
        status = DbGetStatusInterErrno(CkptInit(seInsPtr));
        if (status != STATUS_OK_INTER) {
            return status;
        }
        status = SeRegisterReplayFunc(seInsPtr);
        if (status != STATUS_OK_INTER) {
            return status;
        }
    }
    return STATUS_OK_INTER;
}

#ifdef FEATURE_LPASMEM
void SeDestroyLpasIndexMap(SeInstanceT *seIns)
{
    if (seIns->lpasIndexMap != NULL) {
        DbOamapDestroy(seIns->lpasIndexMap);
        DbDynMemCtxFree(seIns->seServerMemCtx, seIns->lpasIndexMap);
    }

    if (seIns->adpt != NULL) {
        if (((LpasMemCtxT *)seIns->adpt)->lpasCtx != NULL) {
            DbDynMemCtxFree(seIns->seServerMemCtx, ((LpasMemCtxT *)seIns->adpt)->lpasCtx);
        }
        DbDynMemCtxFree(seIns->seServerMemCtx, seIns->adpt);
    }
}
#endif

void SeDestroyInstance(uint16_t instanceId)
{
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(instanceId);
    if (seIns == NULL) {
        return;
    }
#ifdef FEATURE_LPASMEM
    SeDestroyLpasIndexMap(seIns);
#endif
    CkptDestroy(seIns);
    SeDbPreLoadPagesStopProc(seIns);
    seIns->ckptCtx = NULL;
    if (seIns->redoMgr != NULL) {
        (void)RedoMgrDestroy(seIns->redoMgr);
        seIns->redoMgr = NULL;
    }
    SeCtrlDestroy(seIns);
    DbDestoryCtrlLatch(seIns);
    SeReleasePageMgr(seIns);
    SeReleaseConfigMem(seIns->seServerMemCtx, &seIns->seConfig);
    if (!DbDynLoadHasFeature(COMPONENT_MINIKV)) {
        IdxAmUninit();
    }
    SeUuidStatusReset();
}

bool SeBufPoolDeployInDynMem(uint16_t instanceId)
{
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(instanceId);
    if (seIns == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "instance null");
        return false;
    }
    return (!SeBufferPoolIsShmem(seIns));
}

// 计算heapaddr需要的寻址空间下限，为addr压缩做准备
void SeCalcHeapTupleAddrMode(SeConfigT *config)
{
    DB_POINTER(config);
    uint64_t maxMemSize = (uint64_t)config->maxSeMem;  // unit：K
    if (config->isUseRsm) {
        maxMemSize += ((uint64_t)config->rsmBlockSize * config->rsmBlockCnt * DB_KIBI) +
                      (RSM_DEVICE_ID_BASE * config->deviceSize);
    }
    if (config->isUseRsmExtendBlock) {
        maxMemSize += (uint64_t)config->rsmExtendBlockSize * DB_KIBI;
    }
    uint64_t reservedPage = SE_PAGE_ID_SHIFT + SE_MAX_PAGE_ID_SHIFT;

    if ((uint64_t)(SE_MAX_PAGE_COUNT_32 - reservedPage) * config->pageSize >= maxMemSize) {
        config->heapTupleAddrMode = SE_HEAP_TUPLE_ADDR_32;
        return;
    }

    // 当前DB不开放64位版本，在所有场景下，addr都应该能被压缩成32或48位
    DB_ASSERT((uint64_t)(SE_MAX_PAGE_COUNT_48 - reservedPage) * config->pageSize >= maxMemSize);
    config->heapTupleAddrMode = SE_HEAP_TUPLE_ADDR_48;
}

Status SeCalcRsmHeapTupleAddrMode(SeConfigT *config)
{
    DB_POINTER(config);
    if (!config->isUseRsm) {
        return GMERR_OK;
    }

    uint32_t rsmSubBlockCnt = config->rsmBlockSize * DB_KIBI / config->pageSize;
    uint16_t rsmPageAddrShiftBit = 0;
    uint32_t tmpBlkPerDev = rsmSubBlockCnt - 1;
    while (tmpBlkPerDev != 0) {
        tmpBlkPerDev = tmpBlkPerDev >> 1;
        rsmPageAddrShiftBit++;
    }
    uint64_t maxDevId = ((config->maxSeMem + (uint64_t)config->rsmBlockCnt * config->rsmBlockSize * DB_KIBI +
                             (uint64_t)config->rsmExtendBlockSize * DB_KIBI) /
                         config->deviceSize);
    uint64_t maxPageId = (maxDevId << rsmPageAddrShiftBit) + config->rsmBlockSize * DB_KIBI / config->pageSize - 1;
    if (maxPageId <= SE_MAX_PAGE_COUNT_32) {
        config->rsmHeapTupleAddrMode = SE_HEAP_TUPLE_ADDR_32;
    } else if (maxPageId <= SE_MAX_PAGE_COUNT_48) {
        config->rsmHeapTupleAddrMode = SE_HEAP_TUPLE_ADDR_48;
    } else {
        SE_LAST_ERROR(PROGRAM_LIMIT_EXCEEDED_HEAP_TUPLE_ADDR,
            "maxSeMem:%" PRIu32 ", rsmBlockCnt:%" PRIu32 ", rsmBlockSize:%" PRIu32 ", deviceSize:%" PRIu32
            ", pageSize:%" PRIu32,
            config->maxSeMem, config->rsmBlockCnt, config->rsmBlockSize * DB_KIBI, config->deviceSize,
            config->pageSize);
        DB_ASSERT(false);
        return DbGetExternalErrno(PROGRAM_LIMIT_EXCEEDED_HEAP_TUPLE_ADDR);
    }
    return GMERR_OK;
}

StatusInter SeInitWithConfig(SeInstanceT *seIns, const SeConfigT *config)
{
    DB_POINTER2(seIns, config);

    seIns->seConfig = *config;
    seIns->instanceId = config->instanceId;

    // 在这里面设置文件名和路径
    Status ret = GMERR_OK;

    typedef Status (*PersistGetDataDirFunc)(SeInstanceT * seIns);
    PersistGetDataDirFunc persistGetDataDirFunc =
        (PersistGetDataDirFunc)DbDynLoadGetFunc(COMPONENT_PERSISTENCE, "string_config");
    if (persistGetDataDirFunc != NULL) {
        ret = persistGetDataDirFunc(seIns);
        if (ret != GMERR_OK) {
            SeReleaseConfigMem(seIns->seServerMemCtx, &seIns->seConfig);
        } else if (!SeIsStringEmpty(seIns->seConfig.recoveryPath)) {
            ret = DbFormatDirPath(seIns->seConfig.recoveryPath, DB_MAX_PATH);
        }
    } else {
        DB_LOG_INFO("persist off");
    }

    return DbGetStatusInterErrno(ret);
}

StatusInter SeCreateSubMemCtx(SeInstanceT *seInsPtr, DbMemCtxT *seTopShmMemCtx)
{
    // 创建 heapShmMemCtx
    StatusInter ret = HeapSeShmMemCreate(seInsPtr, seTopShmMemCtx);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    // 创建 fixedHeapShmMemCtx
    ret = FixedHeapMemContextCreate(seInsPtr, seTopShmMemCtx);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    if (!DbCfgIsTsInstance()) {
        // 创建 indexShmMemCtx
        return IndexSeShmMemCreate(seInsPtr, seTopShmMemCtx);
    }
    return ret;
}

StatusInter SeCreateTrxModule(SeInstanceT *seInsPtr, DbMemCtxT *seTopShmMemCtx)
{
    DB_POINTER2(seInsPtr, seTopShmMemCtx);

    // 创建 trxMgr 模块
    StatusInter status = TrxMgrCreate(seInsPtr, seTopShmMemCtx);
    if (status != STATUS_OK_INTER) {
        return status;
    }

    // 创建 LockMgr 模块
    SeLockMgrCreatePhase1(seInsPtr);

    // 创建 undo 表空间, 此处不会分配回滚段空间
    status = UndoSpaceCreate(seInsPtr, seTopShmMemCtx);
    if (status != STATUS_OK_INTER) {
        return status;
    }

#if defined(FEATURE_DAF) || defined(FEATURE_HAC)
    status = DafInit(seInsPtr->seServerMemCtx);  // 初始化DAF
    if (status != STATUS_OK_INTER) {
        return status;
    }
#endif
    // 初始化purger相关变量
    DbSpinInit(&seInsPtr->purgerStartLatch);
    seInsPtr->undoPurgerStarted = false;

    return STATUS_OK_INTER;
}

Status InitSeInstanceImp(
    DbInstanceHdT dbInstance, SeInitMemCtxT *seInitMemCtx, const SeConfigT *config, SeInstanceT *seInsPtr)
{
    typedef Status (*MultiInitSeInstanceFuncT)(
        DbInstanceHdT dbInstance, SeInitMemCtxT * seInitMemCtx, const SeConfigT *config, SeInstanceT *seInsPtr);
    MultiInitSeInstanceFuncT multiInitSeInstance =
        (MultiInitSeInstanceFuncT)DbDynLoadGetFunc(COMPONENT_MINIKV, "multi-init-se-instance");
    if (multiInitSeInstance) {
        return multiInitSeInstance(dbInstance, seInitMemCtx, config, seInsPtr);
    } else {
        return InitSeInstance(dbInstance, seInitMemCtx, config, seInsPtr);
    }
}

#ifdef FEATURE_LPASMEM
static uint32_t DbOamapVertexIdCompare(const void *key1, const void *key2)
{
    DB_POINTER2(key1, key2);
    const VertexIdT *v1 = (const VertexIdT *)key1;
    const VertexIdT *v2 = (const VertexIdT *)key2;
    return v1->blockId == v2->blockId && v1->deviceId == v2->deviceId;
}

StatusInter SeLpasIndexMapInit(SeInstanceT *seInsPtr)
{
    DbOamapT *SeinsIndexMap = (DbOamapT *)DbDynMemCtxAlloc(seInsPtr->seServerMemCtx, sizeof(DbOamapT));
    Status ret = DbOamapInit(SeinsIndexMap, SE_MAX_LPASMEM_INDEX_NUM, DbOamapVertexIdCompare, seInsPtr->seServerMemCtx,
        true);  // 64 is index table max num in one instance
    if (ret != GMERR_OK) {
        SE_ERROR(ret, "Init SeLpasIndexMap");
        return ret;
    }
    seInsPtr->lpasIndexMap = SeinsIndexMap;
    return STATUS_OK_INTER;
}
#endif

static StatusInter SeInitModuleAndStartup(SeInstanceT *seInsPtr, DbMemCtxT *seTopShmMemCtx)
{
    DB_POINTER2(seInsPtr, seTopShmMemCtx);
#ifdef FEATURE_LPASMEM
    // 初始化lpasIndexMap
    StatusInter statusTmp = SeLpasIndexMapInit(seInsPtr);
    if (statusTmp != STATUS_OK_INTER) {
        SE_ERROR(statusTmp, "Init SeLpasIndexMap");
        return statusTmp;
    }
#endif
    // 初始化page manager
    StatusInter status = SePageMgrInit(seInsPtr, seTopShmMemCtx);
    if (status != STATUS_OK_INTER) {
        SE_ERROR(status, "Init SePageMgr");
        return status;
    }

    status = SeInitPersistence(seInsPtr);
    if (status != STATUS_OK_INTER) {
        SE_ERROR(status, "init persist");
        return status;
    }

    status = SeCreateSrvPageMgrCtx(seInsPtr, seInsPtr->seServerMemCtx);
    if (status != STATUS_OK_INTER) {
        SE_ERROR(status, "Create sePageMgr");
        return status;
    }

    status = DbGetStatusInterErrno(StartupDatabase(seInsPtr));
    if (status != STATUS_OK_INTER) {
        SE_ERROR(status, "start db");
    }
    return status;
}

StatusInter SeInitSubModule(SeInstanceT *seInsPtr, DbMemCtxT *seTopShmMemCtx)
{
    DB_POINTER2(seInsPtr, seTopShmMemCtx);
    StatusInter status = SeInitModuleAndStartup(seInsPtr, seTopShmMemCtx);
    if (status != STATUS_OK_INTER) {
        return status;
    }

    if (!DbCfgIsTsInstance()) {
        // 创建resource column模块
        status = DbGetStatusInterErrno(SeResColInit(seInsPtr, seTopShmMemCtx));
        if (status != STATUS_OK_INTER) {
            return status;
        }
    }

    // 初始化索引Hac管理模块
#ifdef FEATURE_HAC
    status = IdxHacInit(seInsPtr, seTopShmMemCtx);
    if (status != STATUS_OK_INTER) {
        return status;
    }
#endif

    // 初始化fixedheap模块
    FixedHeapInit();

#ifdef FEATURE_CSTORE
    // 初始化CStore模块
    SeCStoreInit();
#endif
    // 创建事务相关模块
    status = SeCreateTrxModule(seInsPtr, seTopShmMemCtx);
    if (status != STATUS_OK_INTER) {
        return status;
    }

    // 创建ResRecord模块
    status = DbGetStatusInterErrno(DbSessionMgrCreate(seTopShmMemCtx));
    if (status != STATUS_OK_INTER) {
        SE_ERROR(status, "create sessionMgr");
        return status;
    }

    // 创建 子模块使用的 memCtx
    return SeCreateSubMemCtx(seInsPtr, (DbMemCtxT *)seTopShmMemCtx);
}

StatusInter SeCreateShmCtx(const SeConfigT *config, DbMemCtxT **seTopShmMemCtx, DbMemCtxT **seAppShmMemCtx)
{
    // 获取存储自己的顶部上下文
    DbMemCtxArgsT storageCtxArgs = {0};
    storageCtxArgs.instanceId = config->instanceId;
    storageCtxArgs.ctxId = DB_TOP_SE_SHMEMCTX_ID;

    // Use private memory context setting
    char param[sizeof(DbBlockMemParamT)] = {0};
    DbBlockMemParamT *blockParam = (DbBlockMemParamT *)param;
    blockParam->isHugePage = false;
    uint64_t seTopMaxSize = SE_TOP_SHM_CTX_MAX_SIZE;
    if (config->maxSeMem < SE_MEM_8G) {
        blockParam->baseSize = SE_TOP_SHM_CTX_BASE_SIZE;
        blockParam->stepSize = SE_TOP_SHM_CTX_STEP_SIZE;
    } else if (config->maxSeMem <= SE_MEM_16G) {
        blockParam->baseSize = SE_TOP_SHM_CTX_BASE_SIZE;
        blockParam->stepSize = SE_TOP_SHM_CTX_STEP_SIZE_8G;
    } else {
        blockParam->baseSize = SE_TOP_SHM_CTX_BASE_SIZE_16G;
        blockParam->stepSize = SE_TOP_SHM_CTX_STEP_SIZE_16G;
        seTopMaxSize = SE_TOP_SHM_CTX_MAX_SIZE_16G;
    }
    blockParam->isReused = false;
    blockParam->allowBigChunk = DbDynLoadHasFeature(COMPONENT_PERSISTENCE) ? true : false;
    uint64_t maxSize = DbGetTopShmemMaxSize(config->instanceId);

    blockParam->maxSize = DB_MIN(maxSize, seTopMaxSize);
    blockParam->blkPoolType = BLK_NORMAL;
    AlgoParamT algoParam;
    algoParam.blockParam = blockParam;
    storageCtxArgs.algoParam = &algoParam;
    /* SeTopShmCtx 说明
        用    途: 用于存储引擎中管理结构体等元数据的共享内存申请和释放
        生命周期: 长进程级别
        释放策略: 就近释放
        兜底清空措施: 依赖上层sysShmCtx, server退出时销毁
    */
    *seTopShmMemCtx = DbCreateBlockPoolShmemCtx(DbSrvGetSysShmCtx(config->instanceId), "SeTopShmCtx", &storageCtxArgs);
    if (*seTopShmMemCtx == NULL) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "Create SeTopShmCtx ctxId:%" PRIu32 ", instanceId:%" PRIu16 "",
            (uint32_t)DB_TOP_SE_SHMEMCTX_ID, config->instanceId);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    storageCtxArgs.ctxId = DB_SE_APP_SHM_CTX_ID;
    /* SeAppTopShmCtx 说明
        用    途: 用于存储引擎在共享内存应用区使用内存申请和释放
        生命周期: 长进程级别
        释放策略: 就近释放
        兜底清空措施: 依赖上层AppShmCtx, server退出时销毁
    */
    *seAppShmMemCtx =
        DbCreateBlockPoolShmemCtx(DbSrvGetAppShmCtx(config->instanceId), "SeAppTopShmCtx", &storageCtxArgs);
    if (*seAppShmMemCtx == NULL) {
        DbDeleteShmemCtx(*seTopShmMemCtx);
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "Create SeAppTopShmCtx ctxId:%" PRIu32 ", instanceId:%" PRIu16 "",
            (uint32_t)DB_SE_APP_SHM_CTX_ID, config->instanceId);
        return MEMORY_OPERATE_FAILED_INTER;
    }
    return STATUS_OK_INTER;
}

static StatusInter SeCreatMemCtx(DbMemCtxT **seTopMemCtx, DbMemCtxT **seTopShmMemCtx, DbMemCtxT **seEscapeMemCtx,
    DbMemCtxT **seAppShmMemCtx, const SeConfigT *config)
{
    DB_POINTER4(seTopMemCtx, seTopShmMemCtx, seEscapeMemCtx, seAppShmMemCtx);
    StatusInter ret = SeCreateShmCtx(config, seTopShmMemCtx, seAppShmMemCtx);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    DbMemCtxT *sysDynCtx = DbSrvGetSysDynCtx(config->instanceId);
    if (sysDynCtx == NULL) {
        DbDeleteShmemCtx(*seTopShmMemCtx);
        DbDeleteShmemCtx(*seAppShmMemCtx);
        SE_LAST_ERROR(INTERNAL_ERROR_INTER, "get sysDynCtx");
        return INTERNAL_ERROR_INTER;
    }
    /* SeTopMemCtx 说明
        用    途: 用于存储内部使用, 如heap批量插入时临时申请缓存、资源池bitmap、缩容子memCtx创建以及undo log结构体
        生命周期: 长进程
        释放策略: 就近释放
        兜底清空措施: 在存储实例结束后销毁
    */
    DbMemCtxArgsT args = {0};
    args.liteModOn = true;
    args.usingRoot = DbDynLoadHasFeature(COMPONENT_MINIKV);
    *seTopMemCtx = DbCreateDynMemCtx(sysDynCtx, true, "SeTopMemCtx", &args);
    if (*seTopMemCtx == NULL) {
        DbDeleteShmemCtx(*seTopShmMemCtx);
        DbDeleteShmemCtx(*seAppShmMemCtx);
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "Create DynMemCtx");
        return MEMORY_OPERATE_FAILED_INTER;
    }
    args.usingRoot = false;
    args.noLimited = true;
    args.maxTotalPhySize = SeGetEscapeMemCtxMaxSize(config->pageSize * DB_KIBI);
    DbSrvSetMaxEscapeMemCtxSize(args.maxTotalPhySize);
    args.isEscapeCtx = true;
    /* SeEscapeMemCtx 说明
        用    途: 用于存储内部使用, 在事务提交或回滚阶段出现内存不足时代替sessionMemCtx用作逃生使用
        生命周期: 长进程
        释放策略: 就近释放
        兜底清空措施: 依赖上层sysDynCtx，在进程退出时销毁
    */
    *seEscapeMemCtx = DbCreateDynMemCtx(sysDynCtx, true, "SeEscapeMemCtx", &args);
    if (*seEscapeMemCtx == NULL) {
        DbDeleteShmemCtx(*seTopShmMemCtx);
        DbDeleteShmemCtx(*seAppShmMemCtx);
        DbDeleteDynMemCtx(*seTopMemCtx);
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "Create escape dynMemCtx");
        return MEMORY_OPERATE_FAILED_INTER;
    }
    return STATUS_OK_INTER;
}

static void InitSingleProcessShmemResource(SeInstanceT *seIns)
{
    // init lock
    DbInterProcLockFnInit(&seIns->seLockFn);
    DbInterProcRWLockFnInit(&seIns->seRWLockFn);
    seIns->memUtils.memAlloc = DbDynShmemMemCtxAlloc;
    seIns->memUtils.memFree = DbDynShmemMemCtxFree;
    seIns->memUtils.shmPtr2addr = DbDynShmemPtrToAddr;
    seIns->memUtils.memCtxShmPtr2addr = DbMemCtxDynShmemPtrToAddr;
    seIns->memUtils.dynPtr2addr = DynamicPtr2MemAddr;
    seIns->memUtils.dynPtrShift = DynamicMemAddrShift;
    seIns->memUtils.dynPtrShift64 = DynamicMemAddrShift64;
    seIns->memUtils.memCtx = seIns->seServerMemCtx;
    seIns->memUtils.sharedMemory = true;
}

static void InitSingleProcessDynMemResource(SeInstanceT *seIns)
{
    // init lock
    DbInterProcLockFnInit(&seIns->seLockFn);
    DbInterProcRWLockFnInit(&seIns->seRWLockFn);

    seIns->memUtils.memAlloc = DbDynShmemMemCtxAlloc;
    seIns->memUtils.memFree = DbDynShmemMemCtxFree;
    seIns->memUtils.shmPtr2addr = DbDynShmemPtrToAddr;
    seIns->memUtils.memCtxShmPtr2addr = DbMemCtxDynShmemPtrToAddr;
    seIns->memUtils.dynPtr2addr = DynamicPtr2MemAddr;
    seIns->memUtils.dynPtrShift = DynamicMemAddrShift;
    seIns->memUtils.dynPtrShift64 = DynamicMemAddrShift64;
    seIns->memUtils.memCtx = seIns->seServerMemCtx;
    seIns->memUtils.sharedMemory = false;
}

void SeInitSingleProcess(SeInstanceT *seIns)
{
    seIns->fileMapMgr = NULL;
    if (SeBufferPoolIsShmem(seIns)) {
        InitSingleProcessShmemResource(seIns);
    } else {
        InitSingleProcessDynMemResource(seIns);
    }
}

StatusInter SeRecoveryRsmTableSpace(SeInstanceT *seInsPtr)
{
    // 恢复rsm-tableSpace
    RsmemTableSpaceArrayT *rsmTspArray = DbRsmGetMetaInfoPtr(DB_RSM_META_TYPE_TSP_ARRAY, NULL);
    if (rsmTspArray == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get rsmem tsp array:%" PRIu32, (uint32_t)DB_RSM_META_TYPE_TSP_ARRAY);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    StatusInter ret;
    for (uint32_t i = 0; i < RSMEM_TABLESPACE_ARRAY_NUM; i++) {
        if (!DbIsShmPtrValid(rsmTspArray->array[i])) {
            continue;
        }
        RsmemTableSpaceEntryT *rsmTspEntry = DbShmPtrToAddr(rsmTspArray->array[i]);
        if (rsmTspEntry == NULL) {
            DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "rsmTspEntry, segId:%" PRIu32 ", offset:%" PRIu32,
                rsmTspArray->array[i].segId, rsmTspArray->array[i].offset);
            return UNEXPECTED_NULL_VALUE_INTER;
        }
        TableSpaceCfgT tableSpaceCfg = {.initSize = rsmTspEntry->initSize,
            .stepSize = rsmTspEntry->stepSize,
            .maxMemSize = rsmTspEntry->maxMemSize,
            .tableSpaceId = rsmTspEntry->tspId,
            .isUseRsm = true};
        ret = RsmSpaceMgrRecoveryTspInfo(seInsPtr, &tableSpaceCfg, (uint16_t)rsmTspEntry->tspMgrIdx);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Rsm recovery tableSpace info");
            return ret;
        }
    }
    ret = RsmSpaceMgrRecoveryDefaultTspInfo(seInsPtr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Rsm default tableSpace info");
        return ret;
    }
    // 处理存储层创建成功、但上层tspInfo没有记录下来的tsp
    return RsmSpaceDestoryTspWhenRecovery(seInsPtr);
}

StatusInter SeRecoveryTrmId(SeInstanceT *seInsPtr)
{
    ShmemPtrT rsmGrobalConfigPtr = DbRsmMgrGetMetaInfo(DB_RSM_META_TYPE_GLOBAL_CONFIG);
    RsmemGlobalConfigT *globalConfig = DbShmPtrToAddr(rsmGrobalConfigPtr);
    if (globalConfig == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "get global config in check rsm, segId:%" PRIu32 ", offset:%" PRIu32,
            rsmGrobalConfigPtr.segId, rsmGrobalConfigPtr.offset);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    return SeInitTrmIdCtrl(seInsPtr, globalConfig->trmId);
}

static void ResetSeInstance(SeInstanceT *seInsPtr, DbMemCtxT *seTopMemCtx, DbMemCtxT *seEscapeMemCtx)
{
    (void)memset_s(seInsPtr, sizeof(SeInstanceT), 0, sizeof(SeInstanceT));
    seInsPtr->magicNum = SE_INSTANCE_MAGIC_NUM;
    seInsPtr->seTopShmMemCtxId = DB_TOP_SE_SHMEMCTX_ID;
    seInsPtr->seAppShmMemCtxId = DB_SE_APP_SHM_CTX_ID;
    seInsPtr->seServerMemCtx = seTopMemCtx;
    seInsPtr->seEscapeMemCtx = seEscapeMemCtx;
    seInsPtr->pageDescArray = DB_INVALID_SHMPTR;
    seInsPtr->dataMemMgrCtxId = DB_INVALID_ID32;
    seInsPtr->redoMgr = NULL;
    seInsPtr->trxMgrShm = DB_INVALID_SHMPTR;
    seInsPtr->undoSpaceShm = DB_INVALID_SHMPTR;
    seInsPtr->ckptCtxShm = DB_INVALID_SHMPTR;
    seInsPtr->redoCtxShm = DB_INVALID_SHMPTR;
    seInsPtr->coreCtrlCtxShm = DB_INVALID_SHMPTR;
    seInsPtr->bufPoolShm = DB_INVALID_SHMPTR;
}

Status InitSeInstance(
    DbInstanceHdT dbInstance, SeInitMemCtxT *seInitMemCtx, const SeConfigT *config, SeInstanceT *seInsPtr)
{
    DB_POINTER3(seInitMemCtx, config, seInsPtr);
    seInsPtr->dbInstance = dbInstance;

    // 初始化存储实例根据config参数
    StatusInter ret = SeInitWithConfig(seInsPtr, config);
    if (ret != STATUS_OK_INTER) {
        goto EXIT;
    }

#ifdef FEATURE_LPASMEM
    seInsPtr->lpasIndexMap = NULL;
    seInsPtr->adpt = NULL;
#endif

    seInsPtr->needVfd = true;
#ifdef IDS_HAOTIAN
    seInsPtr->needVfd = false;
#endif

    SeInitSingleProcess(seInsPtr);
    // 初始化存储实例子模块
    ret = SeInitSubModule(seInsPtr, seInitMemCtx->seTopShmMemCtx);
    if (ret != STATUS_OK_INTER) {
        goto EXIT;
    }
    if (!DbCommonGetIsWarmReboot()) {
        ret = SeInitTrmIdCtrl(seInsPtr, 0);
    } else {
        // 恢复trmId
        ret = SeRecoveryTrmId(seInsPtr);
        if (ret != STATUS_OK_INTER) {
            goto EXIT;
        }
        ret = SeRecoveryRsmTableSpace(seInsPtr);
    }
    if (ret != STATUS_OK_INTER) {
        goto EXIT;
    }
    DbRecoverySetState(seInsPtr, RECOVERY_BUTT);
    return GMERR_OK;

EXIT:
    return DbGetExternalErrno(ret);
}

Status SeCreateInstance(
    DbInstanceHdT dbInstance, DbMemCtxT *dbTopShmMemCtx, const SeConfigT *config, SeInstanceHdT *instance)
{
    DB_POINTER3(dbTopShmMemCtx, config, instance);
    DbMemCtxT *seTopMemCtx = NULL;
    DbMemCtxT *seTopShmMemCtx = NULL;
    DbMemCtxT *seEscapeMemCtx = NULL;
    DbMemCtxT *seAppShmMemCtx = NULL;

    StatusInter interRet = SeCreatMemCtx(&seTopMemCtx, &seTopShmMemCtx, &seEscapeMemCtx, &seAppShmMemCtx, config);
    if (interRet != STATUS_OK_INTER) {
        return DbGetExternalErrno(interRet);
    }
    DbRegEscapeCtx(dbInstance, seEscapeMemCtx);

    // 从存储的上下文中，申请共享内存，用来承载一个存储实例
    ShmemPtrT seInsShmPtr = DbShmemStructAllocById(seTopShmMemCtx, sizeof(SeInstanceT), DB_SE_STRUCT_ID);
    SeInstanceT *seInsPtr = (SeInstanceT *)DbShmPtrToAddr(seInsShmPtr);
    Status ret = GMERR_OK;
    if (seInsPtr == NULL) {
        ret = GMERR_UNEXPECTED_NULL_VALUE;
        SE_LAST_ERROR(
            UNEXPECTED_NULL_VALUE_INTER, "SeInstance:%" PRIu32 ",%" PRIu32, seInsShmPtr.segId, seInsShmPtr.offset);
        goto EXIT;
    }
    ResetSeInstance(seInsPtr, seTopMemCtx, seEscapeMemCtx);

    SeInitMemCtxT seInitMemCtx = (SeInitMemCtxT){.dbTopShmMemCtx = dbTopShmMemCtx,
        .seTopMemCtx = seTopMemCtx,
        .seTopShmMemCtx = seTopShmMemCtx,
        .seEscapeMemCtx = seEscapeMemCtx};
    ret = InitSeInstanceImp(dbInstance, &seInitMemCtx, config, seInsPtr);
    if (ret == GMERR_OK) {
        *instance = (SeInstanceHdT)seInsPtr;
        return GMERR_OK;
    }
EXIT:
    SeDestroyInstance(config->instanceId);
    DbShmemStructFreeById(seTopShmMemCtx, seInsShmPtr, DB_SE_STRUCT_ID);
    DbDeleteShmemCtx(seTopShmMemCtx);
    DbDeleteShmemCtx(seAppShmMemCtx);
    DbDeleteDynMemCtx(seTopMemCtx);
    DbDeleteDynMemCtx(seEscapeMemCtx);
    return ret;
}

static StatusInter SeOpenCreateTrxMemCtx(SeRunCtxT *seRunCtx, TrxT *trx)
{
    DB_POINTER2(seRunCtx, trx);
    DbMemCtxArgsT args = {0};
    /* trxPeriodMemCtx 说明
        用    途: 用于轻量化事务undo log节点和undo log buf内存申请释放、序列化时keyData内存申请和释放
                  回滚以及提交场景作为heap容器或聚簇容器的userMemCtx
        生命周期: 连接级别
        释放策略: 就近释放为主, 在事务提交或回滚阶段reset进行兜底
        兜底清空措施: 在连接断连时，调用memCtx delete兜底
    */
    trx->trxPeriodMemCtx = DbCreateDynMemCtx(seRunCtx->sessionMemCtx, false, "trxPeriodMemCtx", &args);
    if (trx->trxPeriodMemCtx == NULL) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "alloc trxPeriodMemCtx");
        return MEMORY_OPERATE_FAILED_INTER;
    }
    return STATUS_OK_INTER;
}

static StatusInter SeOpenTrxInitLock(SeRunCtxT *seRunCtx, SeInstanceT *seIns)
{
    StatusInter ret;
    // 初始化锁相关
    if (!seIns->isLockMgrInited) {
        ret = SeLockMgrCreatePhase2(seIns);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }
    return SeLockMgrOpen(seRunCtx, seIns);
}

// 服务器和直连写均会调用该函数
StatusInter SeOpenTrx(SeRunCtxT *seRunCtx, SeInstanceT *seIns)
{
    DB_POINTER2(seRunCtx, seIns);
    // SeOpen的时候分配trx, 在SeRunCtx中初始化
    seRunCtx->trxMgr = (TrxMgrT *)DbShmPtrToAddr(seIns->trxMgrShm);
    if (seRunCtx->trxMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "trxMgrShm(%" PRIu32 ",%" PRIu32 ")", seIns->trxMgrShm.segId,
            seIns->trxMgrShm.offset);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    // 打开undo表空间
    StatusInter ret = UndoSpaceOpen(seIns->undoSpaceShm, seRunCtx, (SeUndoCtxT **)&seRunCtx->undoCtx);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "undoSpace open, addr(%" PRIu32 ",%" PRIu32 ")", seIns->undoSpaceShm.segId,
            seIns->undoSpaceShm.offset);
        return ret;
    }
    ret = TrxMgrAllocTrx(seRunCtx->trxMgr, seRunCtx, (TrxT **)&(seRunCtx->trx));
    if (ret != STATUS_OK_INTER) {
        UndoSpaceClose((SeUndoCtxT *)seRunCtx->undoCtx);
        seRunCtx->undoCtx = NULL;
        SE_ERROR(ret, "alloc trx");
        return ret;
    }
    // 初始化锁相关
    if (!DbDynLoadHasFeature(COMPONENT_MINIKV)) {
        ret = SeOpenTrxInitLock(seRunCtx, seIns);
        if (ret != STATUS_OK_INTER) {
            goto CLEAR;
        }
    }
    TrxT *trx = (TrxT *)seRunCtx->trx;
    SeUndoCtxT *undoCtx = seRunCtx->undoCtx;
    trx->trx.base.rseg = DbShmPtrToAddr(undoCtx->undoSpace->rsegShmPtr);
    if (trx->trx.base.rseg == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "rseg shmAddr(%" PRIu32 ",%" PRIu32 ")",
            undoCtx->undoSpace->rsegShmPtr.segId, undoCtx->undoSpace->rsegShmPtr.offset);
        ret = UNEXPECTED_NULL_VALUE_INTER;
        goto CLEAR;
    }
    ret = SeOpenCreateTrxMemCtx(seRunCtx, (TrxT *)seRunCtx->trx);
    if (ret == STATUS_OK_INTER) {
        return STATUS_OK_INTER;
    }
CLEAR:
    TrxMgrFreeTrx(seRunCtx->trxMgr, seRunCtx->trx);
    UndoSpaceClose((SeUndoCtxT *)seRunCtx->undoCtx);
    seRunCtx->trx = NULL;
    seRunCtx->undoCtx = NULL;
    return ret;
}

Status SeOpenResSession(SeRunCtxHdT seRunCtx)
{
    DB_POINTER(seRunCtx);
    SeRunCtxT *seRunCtxPtr = seRunCtx;
    DbSessionCtxT *ctx = &seRunCtxPtr->resSessionCtx;
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    ctx->session = DbSessionAlloc(ctx->sessionMgr, seIns->dbInstance);
    if (ctx->session == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "open resource record");
        return DbGetExternalErrno(OUT_OF_MEMORY_INTER);
    }
    ctx->sessionPool = DbShmPtrToAddr(ctx->sessionMgr->sessionPoolShmPtr);
    if (ctx->sessionPool == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "sessionPool(%" PRIu32 ", %" PRIu32 ")",
            ctx->sessionMgr->sessionPoolShmPtr.segId, ctx->sessionMgr->sessionPoolShmPtr.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    ctx->latchStack = DbShmPtrToAddr(ctx->session->shmLatchStack);
    if (ctx->latchStack == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "latchStack(%" PRIu32 ", %" PRIu32 ")",
            ctx->session->shmLatchStack.segId, ctx->session->shmLatchStack.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    ctx->refArray = DbShmPtrToAddr(ctx->session->shmRefArray);
    if (ctx->refArray == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "refArray(%" PRIu32 ", %" PRIu32 ")",
            ctx->session->shmRefArray.segId, ctx->session->shmRefArray.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    ctx->session->currAccessDsId = DB_INVALID_UINT32;
    ctx->isOpenSession = true;
    return GMERR_OK;
}

static Status SeAttachResSessionShareMem(DbSessionCtxT *ctx, DbSessionT *session)
{
    DB_POINTER2(ctx, session);
    ctx->latchStack = DbShmPtrToAddr(session->shmLatchStack);
    if (ctx->latchStack == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "latchStack(%" PRIu32 ", %" PRIu32 ")", session->shmLatchStack.segId,
            session->shmLatchStack.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
#ifndef NDEBUG
    ctx->latchStack->pid = DbRWlatchGetPid();
#endif
    ctx->refArray = DbShmPtrToAddr(session->shmRefArray);
    if (ctx->refArray == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "shmRefArray(%" PRIu32 ", %" PRIu32 ")", session->shmRefArray.segId,
            session->shmRefArray.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    if (!DbIsShmPtrValid(session->shmRole)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "shmRole(%" PRIu32 ", %" PRIu32 ")", session->shmRole.segId,
            session->shmRole.offset);  // attach, 占据客户端进程空间失败
        return GMERR_UNEXPECTED_NULL_VALUE;
    } else if (DbShmPtrToAddr(session->shmRole) == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "shmRole(%" PRIu32 ", %" PRIu32 ")", session->shmRole.segId,
            session->shmRole.offset);  // attach, 占据客户端进程空间失败
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    return GMERR_OK;
}

Status SeAttachResSessionById(SeRunCtxHdT seRunCtx, uint32_t sid)
{
    DB_POINTER(seRunCtx);
    SeRunCtxT *seRunCtxPtr = seRunCtx;
    DbSessionCtxT *ctx = &seRunCtxPtr->resSessionCtx;
    DB_ASSERT(ctx->session == NULL);

    DbSessionMgr *recordMgr = ctx->sessionMgr;
    ctx->sessionPool = DbShmPtrToAddr(recordMgr->sessionPoolShmPtr);
    if (ctx->sessionPool == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "sessionPool(%" PRIu32 ", %" PRIu32 ")",
            recordMgr->sessionPoolShmPtr.segId, recordMgr->sessionPoolShmPtr.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    if (sid == 0 || sid >= recordMgr->maxNums) {
        SE_LAST_ERROR(INT_ERR_RES_SESSION_INVALID_RECORD_ID, "inv resSessionId(%" PRIu32 ", %" PRIu16 ")", sid,
            recordMgr->maxNums);
        return DbGetExternalErrno(INT_ERR_RES_SESSION_INVALID_RECORD_ID);
    }
    DbSessionT *session = GetSessionFromSessionPool(ctx->sessionPool, sid);
    if (!session) {
        SE_LAST_ERROR(OBJECT_NOT_IN_PREREQUISITE_STATE_INTER, "sessionId: %" PRIu32 "", sid);
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    if (session->isFree) {
        SE_LAST_ERROR(OBJECT_NOT_IN_PREREQUISITE_STATE_INTER,
            "session:%" PRIu32 " unexpected, latchStack:(%" PRIu32 ", %" PRIu32 "), refArray:(%" PRIu32 ", %" PRIu32
            ")",
            sid, session->shmLatchStack.segId, session->shmLatchStack.offset, session->shmRefArray.segId,
            session->shmRefArray.offset);
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    Status ret = SeAttachResSessionShareMem(ctx, session);
    if (ret != GMERR_OK) {
        return ret;
    }
    ctx->session = session;
    // 客户端侧必须关闭，防止客户端调用SeClose的时候将session重复释放
    ctx->isOpenSession = false;
    return GMERR_OK;
}

void SeGetResSessionID(SeRunCtxHdT seRunCtx, uint32_t *recordId)
{
    DB_POINTER2(seRunCtx, recordId);
    SeRunCtxT *seRunCtxPtr = seRunCtx;
    DbSessionCtxT *ctx = &seRunCtxPtr->resSessionCtx;
    *recordId = ctx->session->id;
}

Status SeGetResSessionRole(SeRunCtxHdT seRunCtx, uint8_t **role)
{
    DB_POINTER2(seRunCtx, role);
    SeRunCtxT *seRunCtxPtr = seRunCtx;
    DbSessionCtxT *ctx = &seRunCtxPtr->resSessionCtx;
    DbSessionT *session = ctx->session;
    *role = DbShmPtrToAddr(session->shmRole);
    if (*role == NULL) {
        SE_LAST_ERROR(MEMORY_OPERATE_FAILED_INTER, "shmRole(%" PRIu32 ", %" PRIu32 ")", session->shmRole.segId,
            session->shmRole.offset);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

inline void SeGetResSessionCtx(SeRunCtxHdT seRunCtx, DbSessionCtxT **ctx)
{
    DB_POINTER2(seRunCtx, ctx);
    SeRunCtxT *seRunCtxPtr = seRunCtx;
    *ctx = &seRunCtxPtr->resSessionCtx;
}

Status SeRunCtxInitMemCtxAndTrxForServer(SeInstanceT *seIns, SeRunCtxT *seRunCtxPtr, ShmemPtrT rsmCtxPtr)
{
#ifdef FEATURE_RSMEM
    if (DbIsShmPtrValid(rsmCtxPtr)) {
        void *rsmCtx = DbShmPtrToAddr(rsmCtxPtr);
        if (rsmCtx != NULL) {
            // 使用rsmCtxId对应的rsmMemCtx, 生命周期与链接一致
            seRunCtxPtr->rsmCtx = rsmCtx;
        } else {
            SE_ERROR(MEMORY_OPERATE_FAILED_INTER, "server get rsmCtx(%" PRIu32 ", %" PRIu32 ")", rsmCtxPtr.segId,
                rsmCtxPtr.offset);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
    }
#endif
    return DbGetExternalErrno(SeOpenTrx(seRunCtxPtr, seIns));
}

static Status SeRunCtxInitForServer(
    SeInstanceT *seIns, DbMemCtxT *sessionDynMemCtx, SeRunCtxT *seRunCtxPtr, ShmemPtrT rsmCtxPtr)
{
    DB_POINTER(seRunCtxPtr);
    seRunCtxPtr->sessionMemCtx = sessionDynMemCtx;
    seRunCtxPtr->reserveMemCtx = seIns->seEscapeMemCtx;
    seRunCtxPtr->instanceId = seIns->instanceId;
    seRunCtxPtr->trxLockTimeOut = seIns->seConfig.lockTimeOut;
    seRunCtxPtr->isInited = true;
    seRunCtxPtr->seIns = seIns;

    seRunCtxPtr->pageMgr = seIns->pageMgr;
    seRunCtxPtr->mdMgr = seIns->mdMgr;
    DbSessionCtxCfgT sessionCtxCfg = {
        .instanceId = (uint32_t)seIns->instanceId,
        .isDirectRead = false,
        .isDirectWrite = false,
        .pageMgr = seRunCtxPtr->pageMgr,
        .getPageLatchFunc = (DbGetPageLatchFunc)(void *)SeGetPage,
        .leavePageLatchFunc = (DbLeavePageLatchFunc)(void *)SeLeavePage,
    };
    DbSessionCtxInit(&seRunCtxPtr->resSessionCtx, &sessionCtxCfg);
    if (seRunCtxPtr->resSessionCtx.sessionMgr == NULL && !DbDynLoadHasFeature(COMPONENT_MINIKV)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "sessionMgr:%" PRIu32, (uint32_t)DB_SE_RES_SESSION_SHM_STRUCT_ID);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    return SeRunCtxInitMemCtxAndTrxForServer(seIns, seRunCtxPtr, rsmCtxPtr);
}

// 建链时服务器会申请事务槽并将trxSlot回传客户端，直连读通过该trxSlot获取事务槽，与服务器共享该事务槽，不额外申请新的事务槽
StatusInter SeOpenTrxForDirectRead(SeRunCtxT *seRunCtx, SeInstanceT *seIns, uint16_t trxSlot)
{
    DB_POINTER2(seRunCtx, seIns);
    seRunCtx->trxMgr = (TrxMgrT *)DbShmPtrToAddr(seIns->trxMgrShm);
    if (seRunCtx->trxMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "trxMgrShm");
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    StatusInter ret = UndoSpaceOpen(seIns->undoSpaceShm, seRunCtx, (SeUndoCtxT **)&seRunCtx->undoCtx);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "open undoSpace, addr(%" PRIu32 ",%" PRIu32 ")", seIns->undoSpaceShm.segId,
            seIns->undoSpaceShm.offset);
        return ret;
    }
    // 此处是客户端获取服务端帮忙申请的trx槽，trxs不会有并发初始化问题，且申请后不会释放，此处可以不加trxPool的锁
    TrxT *trx = TrxMgrGetTrxBySlot(seRunCtx->trxMgr, trxSlot);
    if (trx == NULL) {
        UndoSpaceClose((SeUndoCtxT *)seRunCtx->undoCtx);
        seRunCtx->undoCtx = NULL;
        SE_ERROR(UNEXPECTED_NULL_VALUE_INTER, "get trx by slot:%" PRIu16, trxSlot);
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    seRunCtx->trx = trx;

    return ret;
}

// 客户端直连写对应的事务槽由客户端申请，申请后由直连写模块独享
static inline StatusInter SeOpenTrxForDirectWrite(SeRunCtxT *seRunCtxPtr, SeInstanceT *seIns)
{
    return SeOpenTrx(seRunCtxPtr, seIns);
}

static Status SeRsmCtxInitForClt(const SeOpenCfgT *cltSeOpenCfg, SeRunCtxT *seRunCtxPtr)
{
#ifdef FEATURE_RSMEM
    if (DbIsShmPtrValid(cltSeOpenCfg->rsmCtxPtr)) {
        void *rsmCtx = DbShmPtrToAddr(cltSeOpenCfg->rsmCtxPtr);
        if (rsmCtx != NULL) {
            seRunCtxPtr->rsmCtx = rsmCtx;
        } else {
            SE_ERROR(MEMORY_OPERATE_FAILED_INTER, "Clt get rsmCtx(%" PRIu32 ", %" PRIu32 ")",
                cltSeOpenCfg->rsmCtxPtr.segId, cltSeOpenCfg->rsmCtxPtr.offset);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
    }
#endif
    return GMERR_OK;
}

static Status SeRunCtxInitForClt(
    SeInstanceT *seIns, DbMemCtxT *sessionDynMemCtx, const SeOpenCfgT *cltSeOpenCfg, SeRunCtxT *seRunCtxPtr)
{
    DB_POINTER2(cltSeOpenCfg, seRunCtxPtr);
    seRunCtxPtr->sessionMemCtx = sessionDynMemCtx;
    seRunCtxPtr->reserveMemCtx = cltSeOpenCfg->isDirectWrite ? cltSeOpenCfg->reserveMemCtx : seIns->seEscapeMemCtx;
    seRunCtxPtr->instanceId = seIns->instanceId;
    seRunCtxPtr->trxLockTimeOut = seIns->seConfig.lockTimeOut;
    seRunCtxPtr->isInited = true;
    seRunCtxPtr->seIns = seIns;
    seRunCtxPtr->rsmCtx = NULL;

    StatusInter ret = SeCreateCltPageMgrCtx(seIns, cltSeOpenCfg->pageMgrMemCtx, &seRunCtxPtr->resSessionCtx);
    if (ret != STATUS_OK_INTER) {
        SE_LAST_ERROR(ret, "memDataMgr(segid:%" PRIu32 " offset:%" PRIu32 ")", seIns->spaceMgrShm.segId,
            seIns->spaceMgrShm.offset);
        return DbGetExternalErrno(ret);
    }

    seRunCtxPtr->pageMgr = SeGetPageMgr(seIns->instanceId);
    seRunCtxPtr->mdMgr = SeGetPageMgrByType(seIns->instanceId, SE_MEMDATA);
    DbSessionCtxCfgT sessionCtxCfg = {
        .instanceId = (uint32_t)seIns->instanceId,
        .isDirectRead = !cltSeOpenCfg->isDirectWrite,
        .isDirectWrite = cltSeOpenCfg->isDirectWrite,
        .pageMgr = seRunCtxPtr->pageMgr,
        .getPageLatchFunc = (DbGetPageLatchFunc)(void *)SeGetPage,
        .leavePageLatchFunc = (DbLeavePageLatchFunc)(void *)SeLeavePage,
    };
    DbSessionCtxInit(&seRunCtxPtr->resSessionCtx, &sessionCtxCfg);
    if (seRunCtxPtr->resSessionCtx.sessionMgr == NULL) {
        SE_LAST_ERROR(
            UNEXPECTED_NULL_VALUE_INTER, "get sessionMgr:%" PRIu32, (uint32_t)DB_SE_RES_SESSION_SHM_STRUCT_ID);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    Status retOut = SeRsmCtxInitForClt(cltSeOpenCfg, seRunCtxPtr);
    if (retOut != GMERR_OK) {
        return retOut;
    }

    if (cltSeOpenCfg->isDirectWrite) {
        ret = SeOpenTrxForDirectWrite(seRunCtxPtr, seIns);
    } else {
        ret = SeOpenTrxForDirectRead(seRunCtxPtr, seIns, cltSeOpenCfg->trxSlot);
    }
    return DbGetExternalErrno(ret);
}

// 服务器和客户端都会调用，通过入参seOpenCfg中的isClient进行判断，如果为NULL，则认为是服务端
Status SeOpen(uint16_t instanceId, DbMemCtxT *sessionDynMemCtx, const SeOpenCfgT *seOpenCfg, SeRunCtxHdT *seRunCtx)
{
    DB_POINTER2(seRunCtx, sessionDynMemCtx);
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(instanceId);
    if (seIns == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "SeInstance %" PRIu16, instanceId);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DB_ASSERT(seIns->instanceId == instanceId);
    // 申请存储引擎实例运行上下文，在SeClose中配对释放, 生命
    SeRunCtxT *seRunCtxPtr = DbDynMemCtxAlloc(sessionDynMemCtx, sizeof(SeRunCtxT));
    if (seRunCtxPtr == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc SeRunCtx");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(seRunCtxPtr, sizeof(SeRunCtxT), 0, sizeof(SeRunCtxT));

    // UndoSpaceOpen 会用到 redoCtx，首先创建
    Status ret = DbGetExternalErrno(RedoCtxCreate(seIns->redoMgr, (RedoRunCtxT **)&seRunCtxPtr->redoCtx));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "redo ctx open unsucc");
        DbDynMemCtxFree(sessionDynMemCtx, seRunCtxPtr);
        return ret;
    }
    DbRegisterTlsSwapFunc(TLS_TYPE_REDO_CTX, (void *)seRunCtxPtr->redoCtx, SeSetCurRedoCtxForTls);
    if (seOpenCfg == NULL || seOpenCfg->isClient == false) {
        ShmemPtrT rsmCtxPtr = seOpenCfg == NULL ? DB_INVALID_SHMPTR : seOpenCfg->rsmCtxPtr;
        ret = SeRunCtxInitForServer(seIns, sessionDynMemCtx, seRunCtxPtr, rsmCtxPtr);
    } else {
        ret = SeRunCtxInitForClt(seIns, sessionDynMemCtx, seOpenCfg, seRunCtxPtr);
    }
    if (ret != GMERR_OK) {
        RedoCtxRelease(seRunCtxPtr->redoCtx);
        DbDynMemCtxFree(sessionDynMemCtx, seRunCtxPtr);
        return ret;
    }

    seRunCtxPtr->isPersistence = seIns->storageType != SE_MEMDATA;

    *seRunCtx = (SeRunCtxHdT)seRunCtxPtr;
    return GMERR_OK;
}

Status SeLockResourceDestroy(uint16_t instanceId)
{
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(instanceId);
    if (seIns == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "SeInstance %" PRIu16, instanceId);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    if (!seIns->isLockMgrInited) {
        return GMERR_OK;
    }
    StatusInter ret = SeLockMgrDestroyInner(seIns);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "lockMgr destroy");
        return DbGetExternalErrno(ret);
    }
    return GMERR_OK;
}

// 直连读，直连写和服务器侧均会调用该函数
void SeCloseTrx(SeRunCtxT *seRunCtx)
{
    DB_POINTER(seRunCtx);

    // 如果是直连读，则只需关闭undo表空间；而对于直连写，则需要释放trx等资源
    if (seRunCtx->resSessionCtx.isDirectRead) {
        // 关闭undo表空间
        UndoSpaceClose((SeUndoCtxT *)seRunCtx->undoCtx);
        return;
    }

    TrxT *trx = (TrxT *)seRunCtx->trx;
    if (trx->base.state != TRX_STATE_NOT_STARTED) {
        DB_LOG_INFO("TrxState %" PRId32 " and try TrxRollback.", (int32_t)trx->base.state);
        Status ret = SeTransRollback(seRunCtx, false);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Rollback");
        }
    }

    // 关闭undo表空间
    UndoSpaceClose((SeUndoCtxT *)seRunCtx->undoCtx);

    // 释放trx
    if (trx->trxPeriodMemCtx) {  // 清空并删除事务周期的 memCtx.
        DbDeleteDynMemCtx((DbMemCtxT *)trx->trxPeriodMemCtx);
        trx->trxPeriodMemCtx = NULL;
    }
    if (!APtrIsNULL(trx->liteTrx.cacheBuf)) {
        APtrMemCtxFree(trx->seRunCtx->sessionMemCtx, trx->liteTrx.cacheBuf);
    }
    if (!APtrIsNULL(trx->liteTrx.cacheExtendArrFirstNode)) {
        DbMemCtxT *cacheCtx = trx->liteTrx.isUseRsm ? trx->seRunCtx->rsmCtx : trx->seRunCtx->sessionMemCtx;
        APtrMemCtxFree(cacheCtx, trx->liteTrx.cacheExtendArrFirstNode);
    }

    if (seRunCtx->resSessionCtx.isDirectWrite) {
        // 直连写只释放事务相关的本进程资源，对应的事务槽dwTrxSlot在断链时由服务器侧归还(TrxMgrFreeTrxForDirectWrite)
        TrxReleaseRes(trx);
    } else {
        TrxMgrFreeTrx((TrxMgrT *)seRunCtx->trxMgr, (TrxT *)seRunCtx->trx);
    }
}

void SeCloseSession(SeRunCtxT *seRunCtx)
{
    DB_POINTER(seRunCtx);
    DbSessionCtxT *resSessionCtx = &seRunCtx->resSessionCtx;
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    DbSessionReleaseClientResource(resSessionCtx);
    if (resSessionCtx->isOpenSession) {
        DbSessionFree(resSessionCtx->sessionMgr, resSessionCtx->session->id, seIns->dbInstance);
        resSessionCtx->session = NULL;
        resSessionCtx->isOpenSession = false;
    }
}

Status SeClose(SeRunCtxHdT seRunCtx)
{
    if (seRunCtx == NULL) {
        DB_LOG_WARN(GMERR_UNEXPECTED_NULL_VALUE, "seRunCtx");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    SeRunCtxT *seRunCtxPtr = seRunCtx;
    if (seRunCtx->resSessionCtx.isOpenSession) {        // 客户端直连读流程，不用释放session
        DbSessionCleanLatch(&seRunCtx->resSessionCtx);  // 根据session清理锁资源
    }
    // 先关闭事务, 再关闭会话 (会话关闭的流程, 会触发 引用计数检查)
    SeCloseTrx(seRunCtx);
    if (seRunCtxPtr->activeTrxIds != NULL) {
        DbDynMemCtxFree(seRunCtxPtr->sessionMemCtx, seRunCtxPtr->activeTrxIds);
        seRunCtxPtr->activeTrxIds = NULL;
        seRunCtxPtr->trxIdsSize = 0;
    }
    SeCloseSession(seRunCtxPtr);
    // seRunCtxPtr->rsmCtx为链接级别，在断链时释放
    seRunCtxPtr->rsmCtx = NULL;
    RedoCtxRelease(seRunCtx->redoCtx);
    seRunCtxPtr->isInited = false;
    DbDynMemCtxFree(seRunCtxPtr->sessionMemCtx, seRunCtxPtr);
    seRunCtxPtr = NULL;
    return GMERR_OK;
}

uint32_t SeGetNewTrmId(SeInstanceT *seIns)
{
    SeTrmIdCtrlT *ctrl = &seIns->trmIdCtrl;
    DbSpinLock(&ctrl->lock);
    uint32_t newFileId = ctrl->trmId++;
    DbSpinUnlock(&ctrl->lock);
    DbRsmGlobalConfigFlashId(false, newFileId);
    return newFileId;
}

void *SeGetInstance(uint16_t instanceId)
{
    // 为多实例预留的参数
    if (!DbIsMultiInstanceEnabled() && instanceId > MAX_INSTANCE_ID && !DbDynLoadHasFeature(COMPONENT_MINIKV)) {
        return NULL;
    }
    // attach上存储实例的数据结构
    void *seIns = DbGetShmemStructById(DB_SE_STRUCT_ID, instanceId);
    if (seIns != NULL && !SeInsMagicNumIsValid(seIns)) {
        SeSetStorageEmergency(seIns, "se instance unexpect");
    }
    return seIns;
}

inline void *SeShmAlloc(DbMemCtxT *shmCtx, uint32_t size, ShmemPtrT *shmPtr)
{
    DB_POINTER2(shmCtx, shmPtr);
    *shmPtr = DbShmemCtxAlloc(shmCtx, size);
    return DbShmPtrToAddr(*shmPtr);
}

inline void *SeShmAllocAlign(DbMemCtxT *shmCtx, uint32_t size, ShmemPtrT *shmPtr, uint32_t alignSize)
{
    DB_POINTER2(shmCtx, shmPtr);
    ShmemPtrT newHtShm = DbShmemCtxAllocAlign(shmCtx, size, alignSize);
    if (!DbIsShmPtrValid(newHtShm)) {
        return NULL;
    }
    *shmPtr = newHtShm;
    return DbShmPtrToAddrAlign(newHtShm, alignSize);
}

// 获取服务端对象权限模式
uint32_t SeGetPolicyMode(const SeRunCtxHdT seRunCtx)
{
    DB_POINTER(seRunCtx);
    SeRunCtxT *seRunCtxPtr = seRunCtx;
    DbSessionCtxT *ctx = &(seRunCtxPtr->resSessionCtx);
    return ctx->session->privPolicyMode;
}

Status SeGetHeapTupleAddrLenByInstanceId(uint16_t instanceId, uint32_t *len)
{
    DB_POINTER(len);
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(instanceId);
    if (SECUREC_UNLIKELY(seIns == NULL)) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "SeInstance %" PRIu16, instanceId);
        return DbGetExternalErrno(UNEXPECTED_NULL_VALUE_INTER);
    }
    // IdxGetTupleAddrMode第二个参数标识是否使用保留内存，edge label后续需要适配
    *len = GetHeapTupleAddrLen(IdxGetTupleAddrMode(seIns, false));
    return GMERR_OK;
}

Status SeGetTopShmctxUsedSize(uint16_t instanceId, uint64_t *size)
{
    DB_POINTER(size);
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(instanceId);
    if (seIns == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "SeInstance %" PRIu16, instanceId);
        return DbGetExternalErrno(UNEXPECTED_NULL_VALUE_INTER);
    }
    void *shmCtx = DbGetShmemCtxById(seIns->seTopShmMemCtxId, instanceId);
    if (shmCtx == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "SeInstance %" PRIu16 "'s top shmctx inv", instanceId);
        return DbGetExternalErrno(UNEXPECTED_NULL_VALUE_INTER);
    }
    *size = DbShmCtxGetTotalPhySize((DbMemCtxT *)shmCtx, true);
    return GMERR_OK;
}

Status SeInitTrxMgrCheckFunc(uint16_t instanceId,
    OptiTrxSetLabelLastTrxIdAndTrxCommitTime setFunc[(uint32_t)TRX_CHECK_READVIEW_NUM],
    OptiTrxGetLabelLastTrxIdAndTrxCommitTime getFunc[(uint32_t)TRX_CHECK_READVIEW_NUM],
    OptiTrxGetLabelName getLabelName[(uint32_t)TRX_CHECK_READVIEW_NUM])
{
    SeInstanceT *seIns = (SeInstanceT *)SeGetInstance(instanceId);
    if (seIns == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "SeInstance %" PRIu16, instanceId);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    TrxMgrT *trxMgr = (TrxMgrT *)DbShmPtrToAddr(seIns->trxMgrShm);
    if (trxMgr == NULL) {
        SE_LAST_ERROR(UNEXPECTED_NULL_VALUE_INTER, "trxMgr(segid:%" PRIu32 " offset:%" PRIu32 ")",
            seIns->trxMgrShm.segId, seIns->trxMgrShm.offset);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    for (uint32_t i = 0; i < TRX_CHECK_READVIEW_NUM; i++) {
        trxMgr->setFunc[i] = setFunc[i];
        trxMgr->getFunc[i] = getFunc[i];
        trxMgr->getLabelName[i] = getLabelName[i];
    }

    return GMERR_OK;
}

Status StorageCheckTotalFileSizeLimit(const SeConfigT *config)
{
    // 由于对外统一是使用32为的pageId，所以总共只能提供4G * pageSize这么大的空间
    uint64_t seCapacity = ((uint64_t)DB_MAX_UINT32 + 1) * config->pageSize;
    uint64_t requestCapacity = (uint64_t)config->dbFilesMaxCnt * config->dbFileSize;
    if (seCapacity < requestCapacity) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR,
            "Requested db files' capacity out of range. Max capacity %" PRIu64 " K, requested %" PRIu64
            " K. Consider larger deviceSize|PageSize or lower dbFilesMaxCnt|dbFileSize.",
            seCapacity, requestCapacity);
        return GMERR_CONFIG_ERROR;
    }
    // 压缩区打开时，定义的总大小不建议超过磁盘大小（超过了不报错）
    if (config->compressSpaceEnable && (config->compressSpaceSize > requestCapacity)) {
        DB_LOG_WARN(GMERR_CONFIG_ERROR, "Compression space:%" PRIu32 "KB exceeds datafile: %" PRIu64 "KB",
            config->compressSpaceSize, requestCapacity);
    }
    return GMERR_OK;
}

// 校验bufferpoolSize并根据chunkSize转换（如果配置了合理有效的chunkSize）
static Status ConvertBpSizeWithChunkSizeIfNeed(SeConfigT *config)
{
    // If chunkSize is [0], No conversion required.
    if (!config->bpChunkSize) {
        return GMERR_OK;
    }
    // 范围已经在配置项校验过，此处校验是否为【0】 + 【128,*】以及步长。步长不对<128M不合理
    // SIZE_M(1) / SIZE_K(1) 等价于SIZE_K(), 上限SIZE_M(128) / SIZE_K(1)等价 SIZE_K(128)
    if (config->bpChunkSize % SIZE_K(1) != 0 || (config->bpChunkSize > 0 && config->bpChunkSize < SIZE_K(128))) {
        //"unable validate bp chunk size:%?, check if it is multiple of 1MB and Ge 128MB LE 4G
        DB_LOG_WARN(GMERR_CONFIG_ERROR, "bp chunk size:%" PRIu32 "KB", config->bpChunkSize);
        return GMERR_CONFIG_ERROR;
    }

    // 校验bufferpoolSize此时必须>= chunkSize,保证至少有一个chunk.否则报错
    if (config->bufferPoolSize < config->bpChunkSize) {
        // bufferPoolSize must less than bufferChunkSize
        DB_LOG_WARN(GMERR_CONFIG_ERROR, "bp size:%" PRIu32 "KB, chunk:%" PRIu32 "KB", config->bufferPoolSize,
            config->bpChunkSize);
        return GMERR_CONFIG_ERROR;
    }

    // bufferpoolSize转换=bufferPoolSize/chunkSize * chunkSize。
    config->bufferPoolSize = config->bufferPoolSize / config->bpChunkSize * config->bpChunkSize;
    return GMERR_OK;
}

static Status StorageBufferPoolConfigCheck(SeConfigT *config)
{
    if (config->compressSpaceEnable && config->bufferpoolMemType == BUFFERPOOL_DYNAMIC_MEMORY) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "open compress space without dynamic memory");
        return GMERR_CONFIG_ERROR;
    }

    if (SeGetPersistMode() == PERSIST_INCREMENT && config->bufferpoolMemType == BUFFERPOOL_SHARED_MEMORY) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "bufferpool increment persist without shared memory");
        return GMERR_CONFIG_ERROR;
    }

    // 先尝试校验bufferpoolSize是否配置合适，再去转换
    if (config->bufferPoolSize % config->pageSize != 0) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "bufferPoolSize:%" PRIu32 " not multiple of pageSize:%" PRIu32,
            config->bufferPoolSize, config->pageSize);
        return GMERR_CONFIG_ERROR;
    }

    Status ret = ConvertBpSizeWithChunkSizeIfNeed(config);
    if (ret != GMERR_OK) {
        return ret;
    }

#if defined(IDS_HAOTIAN)
    if (config->bufferPoolPriorityRatio < config->loadTablePriorityRatio) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "bufferPoolPriorityRatio:%" PRIu32 " loadTablePriorityRatio:%" PRIu32,
            config->bufferPoolPriorityRatio, config->loadTablePriorityRatio);
        return GMERR_CONFIG_ERROR;
    }
#endif
    uint64_t bufferPoolSizeTotal =
        config->bufferPoolNum ? config->bufferPoolNum * config->bufferPoolSize : config->bufferPoolSize;
#if defined(IDS_HAOTIAN)
    if (bufferPoolSizeTotal > BUFFERPOOL_LIMITED_SIZE_UNIT_K) {
#else
    if (bufferPoolSizeTotal + config->pageSize > BUFFERPOOL_LIMITED_SIZE_UNIT_K) {
#endif
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "bpsize:%" PRIu32 "KB, limit:%" PRIu64, config->bufferPoolSize,
            BUFFERPOOL_LIMITED_SIZE_UNIT_K);
        return GMERR_CONFIG_ERROR;
    }

    // 极限场景，可能一个原子范围的页都在某bufferpool实例下，要求这个bufferpool容量，满足一个原子范围的页
    if (config->bufferPoolSize < REDO_MIN_ATOMIC_PAGE_CAPACITY * config->pageSize) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "bufferPoolSize: %" PRIu32 " KB should at least %" PRIu32 " KB",
            config->bufferPoolSize, REDO_MIN_ATOMIC_PAGE_CAPACITY * config->pageSize);
        return GMERR_CONFIG_ERROR;
    }
    return GMERR_OK;
}

static Status StoragePersistIncrementConfigCheck(SeConfigT *config)
{
    if (config->persCompMode == (uint32_t)COMP_MODE_DEVICE) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "Incremental persist not support compress by device");
        return GMERR_CONFIG_ERROR;
    }

    if (config->multiZoneNum <= config->recoveryZoneId) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "recoveryZoneId(%" PRIu32 ") more than multiZoneNum(%" PRIu32 ")",
            config->recoveryZoneId + 1, config->multiZoneNum);
        return GMERR_CONFIG_ERROR;
    }

    if (config->redoFlushByTime != 0 && config->redoFlushCheckPeriod > config->redoFlushByTime) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR,
            "if redoFlushByTime(%" PRIu32 ") != 0, redoFlushCheckPeriod(%" PRIu32 ") should smaller",
            config->redoFlushByTime, config->redoFlushCheckPeriod);
        return GMERR_CONFIG_ERROR;
    }

    // 因为要保证一个满的公共buffer，和至少一个满的私有buffer能够刷盘成功，否则单文件一个redo原子范围都无法开启（多文件也会有无法切换文件的问题）
    // 由于私有buffer的计算公式，过于复杂，还有结构体大小，对用户不友好，所以这里直接约束为2倍的公共buffer
    uint32_t redoFileMinSize =
        config->redoPubBufSize * REDO_LOG_BUFF_DUAL + DB_CALC_ALIGN(sizeof(RedoLogFileHeadT), REDO_DEFAULT_BLOCK_SIZE);
    if (config->redoFileSize < redoFileMinSize) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "redoFileSize(%" PRIu32 ") greater redoPubBufSize * 2 + 512B (%" PRIu32 ")",
            config->redoFileSize, redoFileMinSize);
        return GMERR_CONFIG_ERROR;
    }
    return GMERR_OK;
}

static Status StoragePersistCommonConfigCheck(SeConfigT *config)
{
    if (config->pageSize > config->deviceSize) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "PageSize %" PRIu16 " greater DeviceSize %" PRIu16 "", config->pageSize,
            config->deviceSize);
        return GMERR_CONFIG_ERROR;
    }

    if (SE_PERSIST_MIN_DEVICE_COUNT * config->deviceSize > config->maxSeMem) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "%" PRIu8 " times of DeviceSize %" PRIu16 " greater maxSeMem %" PRIu16,
            SE_PERSIST_MIN_DEVICE_COUNT, config->deviceSize, config->maxSeMem);
        return GMERR_CONFIG_ERROR;
    }

    // 文件拓展步长是pageSize的整数倍
    if (config->extendSize % config->pageSize != 0) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "ExtendSize(%" PRIu32 ") not multiple of PageSize(%" PRIu32 ")",
            config->deviceSize, config->pageSize);
        return GMERR_CONFIG_ERROR;
    }

    // deviceSize是文件拓展步长的整数倍
    if (config->deviceSize % config->extendSize != 0) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "DeviceSize(%" PRIu32 ") not multiple of ExtendSize(%" PRIu32 ")",
            config->deviceSize, config->extendSize);
        return GMERR_CONFIG_ERROR;
    }

    // 每个space最少占用一个文件, 所以space的数量不能比文件数量多
    if (config->spaceMaxNum > config->dbFilesMaxCnt) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "spaceMaxnum(%" PRIu32 ") greater dbFilesMaxCnt(%" PRIu32 ")",
            config->spaceMaxNum, config->dbFilesMaxCnt);
        return GMERR_CONFIG_ERROR;
    }

    // 每个文件最少占用一个device
    if (config->dbFileSize < config->deviceSize) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "dbFileSize(%" PRIu32 ") less deviceSize(%" PRIu32 ")", config->dbFileSize,
            config->deviceSize);
        return GMERR_CONFIG_ERROR;
    }

    if (config->crcCheckEnable && config->tamperProofEnable) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "crcCheckEnable and tamperProofEnable can't both enable");
        return GMERR_CONFIG_ERROR;
    }

    if (config->shaCheckEnable && config->tamperProofEnable) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "shaCheckEnable and tamperProofEnable can't both enable");
        return GMERR_CONFIG_ERROR;
    }

    return GMERR_OK;
}

static Status StoragePersistConfigCheck(SeConfigT *config)
{
    Status ret = StoragePersistCommonConfigCheck(config);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = StorageCheckTotalFileSizeLimit(config);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (DbDynLoadHasFeature(COMPONENT_BUFFER_POOL)) {
        ret = StorageBufferPoolConfigCheck(config);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    if (SeGetPersistMode() == PERSIST_INCREMENT) {
        ret = StoragePersistIncrementConfigCheck(config);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    return GMERR_OK;
}

static Status StorageConfigCheck(SeConfigT *config)
{
    if (SeHasMiniKvFeature() || (SeGetPersistMode() == PERSIST_ON_DEMAND)) {
        if (config->extendSize != config->deviceSize) {
            config->extendSize = config->deviceSize;
        }
    }

    if (SeGetPersistMode() == PERSIST_ON_DEMAND) {
        if (config->multiZoneNum != 1) {
            config->multiZoneNum = 1;
        }
        if (config->recoveryZoneId != 0) {
            config->recoveryZoneId = 0;
        }
        if (config->dwrEnable) {
            config->dwrEnable = false;
        }
        if (config->dwrBlockNum != 0) {
            config->dwrBlockNum = 0;
        }
    }

    if (config->pageSize % PAGE_SIZE_MOD_NUM != 0) {
        DB_LOG_ERROR(
            GMERR_CONFIG_ERROR, "PageSize %" PRIu16 " not multiple of %" PRIu16, config->pageSize, PAGE_SIZE_MOD_NUM);
        return GMERR_CONFIG_ERROR;
    }

    if (config->deviceSize > config->maxSeMem) {
        SE_ERROR(
            CONFIG_ERROR_INTER, "DeviceSize %" PRIu16 ", maxMemSize %" PRIu16, config->deviceSize, config->maxSeMem);
        return CONFIG_ERROR_INTER;
    }

    if (config->fixPersistEnable && config->multiZoneNum != DB_MAX_MULTIZONE) {
        DB_LOG_ERROR(GMERR_CONFIG_ERROR, "fix persist require two zones, now %" PRIu32, config->multiZoneNum);
        return GMERR_CONFIG_ERROR;
    }

    if (SeGetPersistMode() != PERSIST_OFF) {
        return StoragePersistConfigCheck(config);
    }

    return GMERR_OK;
}

void StorageConfigGetRsmExtendBlockConfig(SeConfigT *config)
{
#ifdef FEATURE_RSMEM
    config->rsmExtendBlockSize = DbCfgGetInt32Lite(DB_CFG_RSM_EXTEND_BLOCK_SIZE, NULL);
    if (config->rsmExtendBlockSize == 0) {
        config->isUseRsmExtendBlock = false;
    } else {
        config->isUseRsmExtendBlock = true;
    }
#else
    config->rsmExtendBlockSize = 0;
    config->isUseRsmExtendBlock = false;
#endif
}

#define RSM_DFGMT_RATE (50)
Status StorageConfigGetRsmBlockConfig(SeConfigT *config)
{
    uint32_t rsmBlockCnt = 0;
    Status ret = DbGetRsmBlockCnt(&rsmBlockCnt);
    if (ret != GMERR_OK) {
        return ret;
    }
    StorageConfigGetRsmExtendBlockConfig(config);
    config->rsmBlockCnt = (rsmBlockCnt == 0) ? 0 : rsmBlockCnt - 1;  // 扣掉一个控制区的rsm-block
    config->rsmDfgmtRateThreshold = RSM_DFGMT_RATE;                  // 后面可以做成一个对外配置项
    return GMERR_OK;
}

static Status StorageConfigGetUint8Cfgs(DbInstanceHdT dbInstance, SeConfigT *config)
{
    CfgPairUintT cfgPairsUint8[] = {
        {DB_CFG_IS_USE_RSM, (void *)&config->isUseRsm, "IS_USE_RSM"},
        {DB_CFG_ENABLE_RELEASE_DEVICE, (void *)&config->enableReleaseDevice, "ENABLE_RELEASE_DEVICE"},
        {DB_CFG_CONDENSED_CTRL_PAGES, (void *)&config->condensedCtrlPages, "SINGLE_FILE_STORAGE_ENABLE"},
        {DB_CFG_FIX_PERSIST_ENABLE, (void *)&config->fixPersistEnable, "FIX_PERSIST_ENABLE"},
#ifdef FEATURE_PERSISTENCE
        {DB_CFG_CRC_CHECK_ENABLE, (void *)&config->crcCheckEnable, "CRC_CHECK_ENABLE"},
        {DB_CFG_SHA_CHECK_ENABLE, (void *)&config->shaCheckEnable, "SHA_CHECK_ENABLE"},
        {DB_CFG_TAMPER_PROOF_ENABLE, (void *)&config->tamperProofEnable, "TAMPER_PROOF_ENABLE"},
        {DB_CFG_SPACE_COMPRESS_AREA_ENABLE, (void *)&config->compressSpaceEnable, "SPACE_COMPRESS_AREA_ENABLE"},
        {DB_CFG_BUFFERPOOL_MEM_TYPE, (void *)&config->bufferpoolMemType, "BUFFERPOOL_MEM_TYPE"},
#endif
#ifdef IDS_HAOTIAN
        {DB_CFG_PRE_FETCH_PAGES_ENABLE, (void *)&config->preFetchPagesEnable, "DB_PRE_FETCH_PAGES_ENABLE"},
#endif
    };

    Status ret = DbSrvGetUint8CfgsQuick(DbGetCfgHandle(dbInstance), cfgPairsUint8, ELEMENT_COUNT(cfgPairsUint8));
    if (ret != GMERR_OK) {
        return ret;
    }

#ifdef FEATURE_SIMPLEREL
    config->enableReleaseDevice = true;  // v1场景确保开启device内存归还至os
#endif
    return GMERR_OK;
}

static Status StorageConfigGetUint16Cfgs(DbInstanceHdT dbInstance, SeConfigT *config)
{
    CfgPairUintT cfgPairsUint16[] = {
        {DB_CFG_SE_PAGE_SIZE, (void *)&config->pageSize, "SE_PAGE_SIZE"},
#ifdef FEATURE_PERSISTENCE
        {DB_CFG_SE_CTRL_PAGE_SIZE, (void *)&config->ctrlPageSize, "SE_CTRL_PAGE_SIZE"},
#endif
        {DB_CFG_SE_GET_INSTANCE_ID, (void *)&config->instanceId, "SE_INSTANCE_ID"},
        {DB_CFG_SE_BUFFERPOOL_POLICY, (void *)&config->bufferPoolPolicy, "SE_BUFFERPOOL_POLICY"},
        {DB_CFG_CONN_MAX, (void *)&config->maxTrxNum, "CONN_MAX"},
    };

    Status ret = DbSrvGetUint16CfgsQuick(DbGetCfgHandle(dbInstance), cfgPairsUint16, ELEMENT_COUNT(cfgPairsUint16));
    if (ret != GMERR_OK) {
        return ret;
    }

    if (DbIsMultiInstanceEnabled()) {
        config->instanceId = DbGetInstanceId(dbInstance);
    }

    config->maxLockShareCnt = (uint16_t)((config->maxTrxNum / 2u) & 0x3FFu);  // 最大连接数除以2
    config->maxTrxNum = (uint16_t)(config->maxTrxNum + MAX_BG_WORKER_NUM);
    return GMERR_OK;
}

static Status TryGetPersistenceConfigItem(DbInstanceHdT dbInstance, SeConfigT *config)
{
    Status ret = GMERR_OK;
#ifdef FEATURE_PERSISTENCE
    DbCfgValueT cfgValue;
    DbCfgMgrHandleT cfgHdl = DbGetCfgHandle(dbInstance);
    ret = DbCfgGet(cfgHdl, DB_CFG_SE_BUFFER_POOL_CHUNK_SIZE, &cfgValue);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "bufferPoolChunkSize");
        return ret;
    }
    // 配置项不支持配置64位
    config->bpChunkSize = cfgValue.int32Val;  // 配置项读取
#endif
    return ret;
}

static Status StorageConfigGetUint32Cfgs(DbInstanceHdT dbInstance, SeConfigT *config)
{
    CfgPairUintT cfgPairsUint32[] = {
        {DB_CFG_SE_DEV_SIZE, (void *)&config->deviceSize, "SE_DEV_SIZE"},
        {DB_CFG_SE_MAX_MEM, (void *)&config->maxSeMem, "SE_MAX_MEM"},
        {DB_CFG_DEFRAGMENTATION_RATE_THRESHOLD, (void *)&config->fragmentationRateThreshold,
            "DEFRAGMENTATION_RATE_THRESHOLD"},
        {DB_CFG_DEFRAGMENTATION_MEM_THRESHOLD, (void *)&config->fragmentationMemoryThreshold,
            "DEFRAGMENTATION_MEM_THRESHOLD"},
        {DB_CFG_TRX_LOCK_WAKEUP_PERIOD, (void *)&config->lockWakeupPeriod, "TRX_LOCK_WAKEUP_PERIOD"},
        {DB_CFG_TRX_LOCK_DEADLOCK_CHECK_PERIOD, (void *)&config->deadlockCheckPeriod, "TRX_LOCK_DEADLOCK_CHECK_PERIOD"},
        {DB_CFG_LATCH_DEADLOCK_DEBUG_TIMEOUT, (void *)&g_gmdbLatchDeadlockTimeout, "LATCH_DEADLOCK_DEBUG_TIMEOUT"},
        {DB_CFG_TRX_LOCK_JUMP_QUEUE_PERIOD, (void *)&config->lockJumpQueuePeriod, "TRX_LOCK_JUMP_QUEUE_PERIOD"},
        {DB_CFG_SE_BUFFERPOOL_SIZE, (void *)&config->bufferPoolSize, "SE_BUFFERPOOL_SIZE"},
#ifdef FEATURE_PERSISTENCE
        {DB_CFG_SPACE_COMPRESS_AREA_SIZE, (void *)&config->compressSpaceSize, "SPACE_COMPRESS_AREA_SIZE"},
#endif
#if defined(IDS_HAOTIAN)
        {DB_CFG_SE_BUFFERPOOL_NUM, (void *)&config->bufferPoolNum, "SE_BUFFERPOOL_NUM"},
        {DB_CFG_SE_BUFFERPOOL_HIGH_PRIORITY_RATIO, (void *)&config->loadTablePriorityRatio,
            "SE_BUFFERPOOL_HIGH_PRIORITY_RATIO"},
        {DB_CFG_SE_BUFFERPOOL_PRIORITY_RATIO, (void *)&config->bufferPoolPriorityRatio, "SE_BUFFERPOOL_PRIORITY_RATIO"},
        {DB_CFG_MAX_PRE_FETCH_THRE_NUM, (void *)&config->maxPreFetchThreNum, "DB_MAX_PRE_FETCH_THRE_NUM"},
#endif
        {DB_CFG_TRX_LOCK_TIME_OUT, (void *)&config->lockTimeOut, "TRX_LOCK_TIME_OUT"},
        {DB_CFG_RSM_BLOCK_MAX_SIZE, (void *)&config->rsmBlockSize, "RSM_BLOCK_SIZE"}
    };
    Status ret = DbSrvGetUint32CfgsQuick(DbGetCfgHandle(dbInstance), cfgPairsUint32, ELEMENT_COUNT(cfgPairsUint32));
    if (ret != GMERR_OK) {
        return ret;
    }

    config->deviceSize *= DB_KIBI;
    config->maxSeMem *= DB_KIBI;
    config->encryptReservedEnable = 0;

    // 在此处初始化持久化模式
    if (DbDynLoadHasFeature(COMPONENT_PERSISTENCE)) {
        DbCfgValueT cfgValue;
        DbCfgMgrHandleT cfgHdl = DbGetCfgHandle(dbInstance);
        ret = DbCfgGet(cfgHdl, DB_CFG_PERSISTENT_MODE, &cfgValue);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get persistentMode ER");
            return ret;
        }
        SeSetPersistMode((SePersistModeE)cfgValue.int32Val);
    } else {
        SeSetPersistMode(PERSIST_OFF);
    }

    return TryGetPersistenceConfigItem(dbInstance, config);
}

static Status StorageMINIKVConfigGet(DbInstanceHdT dbInstance, SeConfigT *config)
{
    // 终端场景配置项天然为K，因此回撤之前的转换
    config->deviceSize /= DB_KIBI;
    config->maxSeMem /= DB_KIBI;
    config->encryptReservedEnable = 1;  // 终端场景默认开启加密保留区
    config->fixPersistEnable = 0;       // 终端不开启持久化数据页修复功能
    config->compressSpaceEnable = 0;    // 终端不开启压缩区
    config->bufferpoolMemType = 0;

    return DbGetSharedModeVal(DbGetCfgHandle(dbInstance), &config->sharedModeEnable);
}

static Status StoragePersistIncrementConfigGet(DbInstanceHdT dbInstance, SeConfigT *config)
{
    CfgPairUintT cfgPairsUint32[] = {
        {DB_CFG_REDO_PUB_BUF_SIZE, &config->redoPubBufSize, "redoPubBufSize"},
        {DB_CFG_REDO_BUF_PARTS, &config->redoBufParts, "redoBufParts"},
        {DB_CFG_REDO_FLUSH_BY_TRX, &config->redoFlushByTrx, "redoFlushByTrx"},
        {DB_CFG_REDO_FLUSH_BY_SIZE, &config->redoFlushBySize, "redoFlushBySize"},
        {DB_CFG_REDO_FLUSH_BY_TIME, &config->redoFlushByTime, "redoFlushByTime"},
        {DB_CFG_REDO_FLUSH_CHECK_PERIOD, &config->redoFlushCheckPeriod, "redoFlushCheckPeriod"},
        {DB_CFG_REDO_FILE_SIZE, &config->redoFileSize, "redoFileSize"},
        {DB_CFG_REDO_FILE_COUNT, &config->redoFileCount, "redoFileCount"},
        {DB_CFG_REDO_FILE_DROP_ON_CLOSE, &config->redoFileDropOnClose, "redoFileDropOnClose"},
#ifdef FEATURE_PERSISTENCE
        {DB_CFG_CKPT_PERIOD, (uint32_t *)&config->ckptPeriod, "ckptPeriod"},
        {DB_CFG_CKPT_THLD, &config->ckptThreshold, "ckptThreshold"},
#endif
    };
    Status ret = DbSrvGetUint32CfgsQuick(DbGetCfgHandle(dbInstance), cfgPairsUint32, ELEMENT_COUNT(cfgPairsUint32));
    if (ret != GMERR_OK) {
        return ret;
    }
    config->redoFileSize = config->redoFileSize * DB_MEBI;
    config->redoPubBufSize = config->redoPubBufSize * DB_KIBI;
    config->redoFlushBySize = config->redoFlushBySize * DB_KIBI;
    config->isRedoConfigInit = true;

    return ret;
}

static Status StoragePersistConfigGet(DbInstanceHdT dbInstance, SeConfigT *config)
{
    uint32_t dwrEnable = 0;
    uint32_t enableSyncWriteFile = 0;
    uint32_t recoveryZoneId = 0;

    CfgPairUintT cfgPairsUint32[] = {
        {DB_CFG_SE_EXTEND_SIZE, &config->extendSize, "dbFileExtendSize"},
#ifdef FEATURE_PERSISTENCE
        {DB_CFG_DB_FILES_MAX_COUNT, (uint32_t *)&config->dbFilesMaxCnt, "dbFilesMaxCnt"},
        {DB_CFG_SPACE_MAX_NUM, (uint32_t *)&config->spaceMaxNum, "spaceMaxNum"},
        {DB_CFG_DB_FILE_SIZE, (uint32_t *)&config->dbFileSize, "dbFileSize"},
        {DB_CFG_DWR_ENABLE, &dwrEnable, "dwrEnable"},
        {DB_CFG_DWR_BLOCK_NUM, &config->dwrBlockNum, "dwrBlockNum"},
        {DB_CFG_PERSISTENT_COMPRESS_MODE, (uint32_t *)&config->persCompMode, "persistentCompressMode"},
        {DB_CFG_FILE_SYNC_WRITE_ENABLE, (uint32_t *)&enableSyncWriteFile, "fileSyncWriteEnable"},
        {DB_CFG_MULTIZONE_NUM, (uint32_t *)&config->multiZoneNum, "multizonePersistNum"},
        {DB_CFG_RECOVERY_ZONE_ID, &recoveryZoneId, "recoveryZoneId"},
#endif
    };
    Status ret = DbSrvGetUint32CfgsQuick(DbGetCfgHandle(dbInstance), cfgPairsUint32, ELEMENT_COUNT(cfgPairsUint32));
    if (ret != GMERR_OK) {
        return ret;
    }

    config->dwrEnable = (dwrEnable == 1u);
    config->enableSyncWriteFile = (enableSyncWriteFile == 1u);
    config->recoveryZoneId = recoveryZoneId - 1;

    if (SeGetPersistMode() == PERSIST_INCREMENT) {
        ret = StoragePersistIncrementConfigGet(dbInstance, config);
    }

    return ret;
}

Status StorageConfigGet(DbInstanceHdT dbInstance, SeConfigT *config, const char *recoveryPath)
{
    DB_POINTER(config);

    // 为适配当前测试，避免调用前已加载recoveryPath
    if (SeIsStringEmpty(config->recoveryPath)) {
        if (recoveryPath == NULL) {
            config->recoveryPath[0] = '\0';
        } else {
            errno_t err = snprintf_s(config->recoveryPath, DB_MAX_PATH, DB_MAX_PATH - 1, "%s", recoveryPath);
            if (err < 0) {
                DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "get recovery path %s", recoveryPath);
                return GMERR_INVALID_PARAMETER_VALUE;
            }
        }
    }

    Status ret = StorageConfigGetUint8Cfgs(dbInstance, config);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = StorageConfigGetUint16Cfgs(dbInstance, config);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = StorageConfigGetUint32Cfgs(dbInstance, config);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = StorageConfigGetRsmBlockConfig(config);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (SeHasMiniKvFeature()) {
        ret = StorageMINIKVConfigGet(dbInstance, config);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    if (SeGetPersistMode() != PERSIST_OFF) {
        ret = StoragePersistConfigGet(dbInstance, config);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    SeCalcHeapTupleAddrMode(config);
    ret = SeCalcRsmHeapTupleAddrMode(config);
    if (ret != GMERR_OK) {
        return ret;
    }
    DmSetAddrMode(config->heapTupleAddrMode);
    SeSetPageTailReservedSize(config);
    return StorageConfigCheck(config);
}

void SeThreadVarSwitch(SeRunCtxHdT seRunCtx)
{
    DB_POINTER(seRunCtx);
    SeSetCurRedoCtx(seRunCtx->redoCtx);
}

bool g_hasMiniKvFeature = false;
inline void SeSetMiniKvFeature(bool hasFeature)
{
    g_hasMiniKvFeature = hasFeature;
}
inline bool SeHasMiniKvFeature(void)
{
    return g_hasMiniKvFeature;
}

Status SeBindCpuSet(SeRunCtxHdT seRunCtx, void *cpuSet)
{
    DB_POINTER2(seRunCtx, cpuSet);
    SeInstanceT *seInstance = (SeInstanceT *)seRunCtx->seIns;

    Status status = RedoBindCpuSet(seInstance->redoMgr, cpuSet);
    if (status != GMERR_OK) {
        DB_LOG_ERROR(status, "bind redo thread to cpu set");
        return status;
    }

    status = CkptBindCpuSet(seInstance, cpuSet);
    if (status != GMERR_OK) {
        DB_LOG_ERROR(status, "bind ckpt thread to cpu set");
    }

    return status;
}

ALWAYS_INLINE void SeMarkSwapCtx(SeRunCtxHdT seRunCtx)
{
    seRunCtx->swapCtx = true;
}

ALWAYS_INLINE void SeUnMarkSwapCtx(SeRunCtxHdT seRunCtx)
{
    seRunCtx->swapCtx = false;
}

#ifdef __cplusplus
}
#endif
