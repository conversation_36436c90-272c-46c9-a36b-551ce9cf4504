/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: redo_replay.c
 * Description:
 * Author:
 * Create: 2021.8.16
 */
#include "se_replay_inner.h"
#include "se_redo_buf.h"
#include "adpt_atomic.h"
#include "se_log.h"
#include "se_page_mgr.h"
#include "se_define.h"
#include "se_ckpt_inner.h"
#include "db_utils.h"
#include "db_crc.h"
#ifdef FEATURE_TS
#include "se_db_file.h"
#include "se_db_file_space.h"
#include "se_db_file_device.h"
#endif

StatusInter RedoLogReplayRegister(RedoMgrT *redoMgr, RedoLogTypeE type, RedoLogReplayFuncT replayFunc)
{
    DB_POINTER2(redoMgr, replayFunc);
    if (!RedoIsEnable(NULL)) {
        return STATUS_OK_INTER;
    }

    if ((uint32_t)type >= (uint32_t)REDO_OP_BUTT) {
        SE_LAST_ERROR(INT_ERR_REDO_INVALID_TYPE, "redo replay type %d inv", (int)type);
        return INT_ERR_REDO_INVALID_TYPE;
    }

    if (redoMgr->replayInfo[type].replayFunc != NULL) {
        SE_LAST_ERROR(INT_ERR_REDO_REPLAY_REGISTERED, "type %d func has registered", (int)type);
        return INT_ERR_REDO_REPLAY_REGISTERED;
    }

    redoMgr->replayInfo[type].replayFunc = replayFunc;
    return STATUS_OK_INTER;
}

void RedoLoadBatch(RedoMgrT *redoMgr, PartDataT *partData, uint8_t *data, uint32_t size, uint32_t maxPart)
{
    DB_POINTER(data);

    RedoLogBatchT *batch = (RedoLogBatchT *)(void *)data;
    DB_ASSERT(batch->size <= size);
    uint32_t offset = sizeof(RedoLogBatchT);
    RedoLogPartT *part = NULL;
    PartInfoT *partInfo = NULL;
    partData->partCount = maxPart;
    for (uint32_t i = 0; i < maxPart; i++) {
        if (offset >= RedoGetBatchTailOffset(redoMgr, batch)) {
            partData->partCount = i;
            break;
        }
        part = (RedoLogPartT *)(void *)(data + offset);
        partInfo = &partData->partInfo[i];
        partInfo->partSize = part->size - sizeof(RedoLogPartT);
        partInfo->partStart = offset + sizeof(RedoLogPartT);
        partInfo->partPos = partInfo->partStart;

        offset += part->size;
    }
    partData->dataSize = batch->size;
    partData->spaceSize = batch->spaceSize;
    partData->padding = batch->padding;
    partData->data = data;
    partData->point = batch->head.point;
}

void RedoLoadReservedMemBatch(PartDataT *partData, uint8_t *data, uint32_t maxPartsize, uint32_t maxPart)
{
    DB_POINTER(data);

    RedoLogBatchT *batch = (RedoLogBatchT *)(void *)data;
    uint32_t offset = sizeof(RedoLogBatchT);
    RedoLogPartT *part = NULL;
    PartInfoT *partInfo = NULL;
    partData->partCount = maxPart;
    for (uint32_t i = 0; i < maxPart; i++) {
        part = (RedoLogPartT *)(void *)(data + offset);
        partInfo = &partData->partInfo[i];
        partInfo->partSize = part->size;
        partInfo->partStart = offset + sizeof(RedoLogPartT);
        partInfo->partPos = partInfo->partStart;
        offset += maxPartsize;
        DB_BOOT_LOG_DBG_DEBUG("Load reservedMem, part:%" PRIu32 ", partSize:%" PRIu32 ", startPos:%" PRIu32 "", i,
            partInfo->partSize, partInfo->partStart);
    }
    partData->dataSize = batch->size;
    partData->data = data;
    partData->point = batch->head.point;
}

RedoLogGroupT *RedoReplayGetNextGroup(PartDataT *partData)
{
    DB_POINTER(partData);
    uint32_t partId = (uint32_t)(-1);
    uint64_t minLsn = DB_INVALID_UINT64;
    RedoLogGroupT *minGroup = NULL;
    RedoLogGroupT *group = NULL;
    PartInfoT *partInfo = NULL;
    for (uint32_t i = 0; i < partData->partCount; i++) {
        partInfo = &partData->partInfo[i];
        if (partInfo->partPos - partInfo->partStart >= partInfo->partSize) {
            continue;
        }
        group = (RedoLogGroupT *)(void *)(partData->data + partInfo->partPos);
        // group的内容是不可信的,尤其是保留内存中的内容,需要提前校验一下,避免越界访问
        if (group->size > partInfo->partSize || group->size < sizeof(RedoLogGroupT)) {
            SE_LAST_ERROR(DATA_EXCEPTION_INTER, "group size is:%" PRIu32, group->size);
            return NULL;
        }
        if (group->lsn < minLsn) {
            minLsn = group->lsn;
            minGroup = group;
            partId = i;
        }
    }
    if (minGroup == NULL) {
        return NULL;
    }
    partData->partInfo[partId].partPos += minGroup->size;
    return minGroup;
}

// 仅用于RedoLogReplayRecord，大函数拆分；leave之后按照写方式拿页。内部异常分支保证页释放
static StatusInter ReplayReloadPageWithWrite(
    PageMgrT *pageMgr, PageIdT recordPage, PageHeadT **page, uint16_t replayType)
{
#ifndef NDEBUG
    // 先访问page，避免拿到的是一个野指针
    PageIdT readPageId = (*page)->addr;
    // 仅限特定type对一个已经释放的页进行重放，增加打印不作异常返回
    if (!DbIsPageIdValid(readPageId) && replayType != REDO_SPACE_UPDATA_NEXT_PAGE_ADDR &&
        replayType != REDO_RESET_PAGE_HEAD && replayType != REDO_LFS_MGR_INIT) {
        // Addr mismatch in replay
        DB_LOG_WARN(DATA_EXCEPTION_INTER,
            "type:%" PRIu16 ", pageHead:%" PRIu32 ",%" PRIu32 ", record pageId:%" PRIu32 ",%" PRIu32, replayType,
            readPageId.deviceId, readPageId.blockId, recordPage.deviceId, recordPage.blockId);
    }
#endif
    SeLeavePage(pageMgr, recordPage, false);
    PageHeadT *pageHead = NULL;
    // 外部调用leave page
    StatusInter ret = SeGetPage(pageMgr, recordPage, (uint8_t **)&pageHead, ENTER_PAGE_NORMAL, true);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Reload in replay, pageId: %" PRIu32 ",%" PRIu32, recordPage.deviceId, recordPage.blockId);
        return ret;
    }
    *page = pageHead;
    return STATUS_OK_INTER;
}

#ifdef FEATURE_TS
bool IsReplayVolatilePage(SeInstanceT *seIns, PageIdT addr)
{
    DbDeviceT *device = (DbDeviceT *)CtrlGroupGetItem(&seIns->db->deviceGroup, addr.deviceId);
    DB_ASSERT(device != NULL && device->ctrl->online);
    SpaceT *space = (SpaceT *)CtrlGroupGetItem(&seIns->db->spaceGroup, device->ctrl->spcId);
    DB_ASSERT(space != NULL && space->ctrl->used);
    if (!space->ctrl->cfg.isVolatile) {
        return false;
    }
    DbFileT *file = (DbFileT *)CtrlGroupGetItem(&seIns->db->fileGroup, device->ctrl->fileId);
    DB_ASSERT(file != NULL && DbFileIsOnline(file->ctrl));
    return file->handle[SeGetMainZoneId(seIns)] == DB_INVALID_FD ? true : false;
}
#endif

static StatusInter RedoLogReplayRecord(
    SeInstanceT *seIns, const RedoLogGroupT *group, RedoLogRecordT *record, RedoLogReplayFuncT func)
{
    DB_POINTER2(seIns, record);
    if (record->type > (uint16_t)REDO_NO_ADDR_PAGE_START && record->type < (uint16_t)REDO_NO_ADDR_PAGE_END) {
        RedoReplayArgsT args = {group->trxId, record->size - sizeof(RedoLogRecordT), NULL,
            (uint8_t *)record + sizeof(RedoLogRecordT), seIns};
        return func(&args);
    }

#ifdef FEATURE_TS
    if (IsReplayVolatilePage(seIns, record->addr)) {
        return STATUS_OK_INTER;
    }
#endif

    PageHeadT *page = NULL;
    bool isChanged = false;
    PageMgrT *pageMgr = (PageMgrT *)seIns->pageMgr;
    if (pageMgr == NULL) {
        return UNEXPECTED_NULL_VALUE_INTER;
    }
    StatusInter ret;
    if (record->type == REDO_INIT_PAGE_HEAD) {
        ret = SeGetPageInit(pageMgr, 0, record->addr, ENTER_PAGE_INIT, (uint8_t **)&page);
    } else {
        ret = SeGetPage(pageMgr, record->addr, (uint8_t **)&page, ENTER_PAGE_NORMAL, false);
    }
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "get page Id:%" PRIu32 ",%" PRIu32, record->addr.deviceId, record->addr.blockId);
        return ret;
    }
    if (group->lsn > page->lsn) {
        ret = ReplayReloadPageWithWrite(pageMgr, record->addr, &page, record->type);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        RedoReplayArgsT args = {group->trxId, record->size - sizeof(RedoLogRecordT), page,
            (uint8_t *)record + sizeof(RedoLogRecordT), seIns};

        ret = func(&args);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Replay type %d, ret=%d", (int)record->type, (int)ret);
            SeLeavePage(pageMgr, record->addr, false);
            return ret;
        }
        isChanged = true;
    }
    SeLeavePage(pageMgr, record->addr, isChanged);
    return ret;
}

StatusInter RecoveryLaunchDirtyPage(SeInstanceT *seIns)
{
    RedoRunCtxT *redoCtx = SeGetCurRedoCtxImpl();

    RedoDirtyPageCkptEnqueue(seIns, redoCtx);
    RedoModifyChangedPage(redoCtx);
    return STATUS_OK_INTER;
}

StatusInter RedoLogReplayGroup(RedoMgrT *redoMgr, RedoLogGroupT *group)
{
    DB_POINTER2(redoMgr, group);
    StatusInter ret = STATUS_OK_INTER;
    RedoLogRecordT *record = NULL;
    uint32_t offset = sizeof(RedoLogGroupT);
    DbAtomicSet64((uint64_t *)&redoMgr->redoBuf.redoPubBuf->curLsn, group->lsn);
    while (offset < group->size) {
        record = (RedoLogRecordT *)(void *)((uint8_t *)group + offset);
        DB_ASSERT(record->type < (uint16_t)REDO_OP_BUTT);
        RedoLogReplayFuncT func = redoMgr->replayInfo[record->type].replayFunc;
        if (func == NULL) {
            SE_LAST_ERROR(INT_ERR_REDO_REPLAY_FUNC_NULL, "func null, record type:%" PRIu32, record->type);
        } else {
            ret = RedoLogReplayRecord(redoMgr->seIns, group, record, func);
            if (ret != STATUS_OK_INTER) {
                return ret;
            }
        }
        offset += record->size;
    }
    ret = RecoveryLaunchDirtyPage(redoMgr->seIns);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    DB_LOG_DEBUG("replay group, lsn:%" PRIu32, (uint32_t)group->lsn);
    return STATUS_OK_INTER;
}

StatusInter RedoLogReplayBatch(RedoMgrT *redoMgr, uint8_t *data, uint32_t size)
{
    DB_POINTER2(redoMgr, data);

    PartDataT partData = {0};
    // redoBufParts配置项有可能会由大改小，因此这里不能传redoMgr->redoBuf.partCount
    RedoLoadBatch(redoMgr, &partData, data, size, MAX_REDO_LOG_PART);
    RedoLogGroupT *group = NULL;
    while ((group = RedoReplayGetNextGroup(&partData)) != NULL) {
        StatusInter ret = RedoLogReplayGroup(redoMgr, group);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }
    redoMgr->pubCtx->curPoint = partData.point;
    uint32_t batchBlockNum = partData.spaceSize / partData.padding;
    if (redoMgr->isSingleRedoFile) {
        // 如果是单文件，blockId需要加上文件头所在的block
        uint32_t fileBlockNum = redoMgr->redoFiles.fileMaxSize / redoMgr->redoFiles.blockSize;
        if (redoMgr->pubCtx->curPoint.blockId % fileBlockNum + batchBlockNum >= fileBlockNum) {
            redoMgr->pubCtx->curPoint.blockId++;
        }
    }
    redoMgr->pubCtx->curPoint.blockId += batchBlockNum;
    redoMgr->pubCtx->curPoint.batchId++;
    DB_LOG_DEBUG("replay batch, batchId:%" PRIu64 ", blockId:%" PRIu64 ", fsn:%" PRIu32, partData.point.batchId,
        partData.point.blockId, partData.point.fsn);

    return STATUS_OK_INTER;
}

StatusInter RedoLogReplayMemBatch(RedoMgrT *redoMgr, uint8_t *data, uint32_t size)
{
    DB_POINTER2(redoMgr, data);

    StatusInter ret;
    PartDataT partData = {0};
    RedoLoadReservedMemBatch(&partData, data, redoMgr->priBufSize + sizeof(RedoLogPartT), redoMgr->redoBuf.partCount);
    RedoLogGroupT *group = NULL;
    uint32_t reservedMaxSize =
        size - sizeof(RedoLogBatchT) - sizeof(RedoLogBatchTailT) - redoMgr->redoBuf.partCount * sizeof(RedoLogPartT);
    // 开启了防篡改，batch尾会多出DB_TAMPER_PROOF_DIGEST_LEN
    if (redoMgr->tamperProofEnable) {
        reservedMaxSize -= DB_TAMPER_PROOF_DIGEST_LEN;
    }
    while ((group = RedoReplayGetNextGroup(&partData)) != NULL) {
        if (group->lsn != redoMgr->redoBuf.redoPubBuf->curLsn + 1) {
            // reserveMem group is old
            DB_LOG_DEBUG("Group lsn:%" PRIu64 ", currLsn:%" PRIu64, group->lsn, redoMgr->redoBuf.redoPubBuf->curLsn);
            continue;
        }
        if (group->size > reservedMaxSize) {
            SE_LAST_ERROR(
                DATA_EXCEPTION_INTER, "group size:%" PRIu32 " reservedMaxSize:%" PRIu32, group->size, reservedMaxSize);
            return DATA_EXCEPTION_INTER;
        }
        if (redoMgr->crcCheckEnable && !DbCheckCrc((char *)group, group->size, &group->checkSum)) {
            // data check is not equal
            SE_LAST_ERROR(INT_ERR_CRC_CHECK_ERROR, "CRC lsn %" PRIu64 ", trxId: %" PRIu64 ", size: %" PRIu32,
                group->lsn, group->trxId, group->size);
            return INT_ERR_CRC_CHECK_ERROR;
        }
        ret = RedoLogReplayGroup(redoMgr, group);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        reservedMaxSize -= group->size;
        DB_LOG_DEBUG(
            "replay reservedMem group, lsn:%" PRIu32 ", checkSum:%" PRIu32 "", (uint32_t)group->lsn, group->checkSum);
    }

    RedoLogBatchT *head = (RedoLogBatchT *)data;
    DB_LOG_DEBUG("replay reservedMem batch, size:%" PRIu32 ", lsn:%" PRIu64, head->size, head->lsn);
    return STATUS_OK_INTER;
}
