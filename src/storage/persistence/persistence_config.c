/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name:persistence_config.c
 * Description:
 * Author:xuj
 * Create:2022.10.17
 */

#include <string.h>
#include "se_instance.h"
#include "db_config.h"
#include "db_dyn_load.h"
#include "se_ckpt_inner.h"
#include "se_persist_inner.h"
#include "se_redo_inner.h"
#include "se_space_inner.h"
#include "se_database.h"
#include "adpt_file.h"
#include "se_persist_config.h"

#define SPACE_PATH_SEPARATOR ","

inline static bool PathIsValid(char *str)
{
    if (str == NULL || strlen(str) == 0) {
        return false;
    }
    return true;
}

static Status PathMallocAndCopy(void *memCtx, const char *srcDir, char **dstDir)
{
    DbDynMemCtxFree(memCtx, *dstDir);  // 释放原本的字符串
    uint32_t pathLen = (uint32_t)strlen(srcDir);
    uint32_t dstDirLen = pathLen + 2;  // FormatDirPath时会再增加一位长度，因此真实长度需要+2（format与末尾0）
    *dstDir = (char *)DbDynMemCtxAlloc(memCtx, sizeof(char) * dstDirLen);
    if (*dstDir == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "alloc path");
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t err = strcpy_s(*dstDir, dstDirLen, srcDir);
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "copy path %s, ret=%d", srcDir, err);
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

Status StorageGetFilePath(void *memCtx, SeInstanceT *seIns, const SeConfigT *config, char **dstDir)
{
    char path[DB_MULTIZONE_MAX_PATH] = {0};
    Status ret = DbCfgGetDirPath(DbGetCfgHandle(seIns->dbInstance), DB_MULTIZONE_MAX_PATH, path);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get cfg dir path");
        return ret;
    }
    char *pathSlitPos = NULL;
    char *pathCurrent = strtok_s(path, SPACE_PATH_SEPARATOR, &pathSlitPos);
    if ((pathCurrent == NULL && SeGetPersistMode() == (uint32_t)PERSIST_ON_DEMAND)) {
        return GMERR_OK;
    }
    uint32_t pathNum = 0;
    while (PathIsValid(pathCurrent)) {
        if (pathNum == config->multiZoneNum) {
            DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "too many path exist. %u == %u", pathNum, config->multiZoneNum);
            return GMERR_DATA_EXCEPTION;
        }
        ret = PathMallocAndCopy(memCtx, pathCurrent, &dstDir[pathNum]);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (!DbDynLoadHasFeature(COMPONENT_MINIKV)) {  // mini kv传入的是文件路径而不是目录路径
            ret = DbFormatDirPath(
                dstDir[pathNum], (uint32_t)strlen(dstDir[pathNum]) +
                                     2);  // FormatDirPath时会再增加一位长度，因此真实长度需要+2（format与末尾0）
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        for (uint32_t j = 0; j < pathNum; j++) {
            if (SeIsSamePath(pathCurrent, dstDir[j])) {
                DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "path:%s already exists.", pathCurrent);
                return GMERR_DATA_EXCEPTION;
            }
        }
        pathNum++;
        pathCurrent = strtok_s(NULL, SPACE_PATH_SEPARATOR, &pathSlitPos);
    }
    if (pathNum != config->multiZoneNum) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "path_num:%" PRIu32 " not equal multi_zone_num:%" PRIu32 ".", pathNum,
            config->multiZoneNum);
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

static Status StorageMallocNameConfigMem(DbMemCtxT *memCtx, char **name)
{
    DB_POINTER2(memCtx, name);
    char *newName = (char *)DbDynMemCtxAlloc(memCtx, DB_MAX_NAME_LEN);
    if (newName == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "alloc name mem.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memset_s(newName, DB_MAX_NAME_LEN, 0, DB_MAX_NAME_LEN);
    *name = newName;
    return GMERR_OK;
}

static Status StorageGetRedoFilePrefix(SeConfigT *config)
{
    const char *redoPrefix = "gmdb_redo_";
    Status ret = GMERR_OK;
    if (DbIsEnablePersistFileNameConvert()) {
        ret = DbPersistFileNameConvert(redoPrefix, config->redoFilePrefix, DB_MAX_NAME_LEN);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get redo file prefix name.");
        }
        return ret;
    }
    if (DbDynLoadHasFeature(COMPONENT_MINIKV)) {
        redoPrefix = ".redo";
    }
    ret = strncpy_s(config->redoFilePrefix, DB_MAX_NAME_LEN, redoPrefix, strlen(redoPrefix));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "strncpy_s redoPrefix name");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

static Status StorageMallocCtrlAndSpaceNameMem(DbMemCtxT *memCtx, SeConfigT *config)
{
    Status ret = StorageMallocNameConfigMem(memCtx, &config->ctrlFileName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "alloc ctrl file name");
        return ret;
    }
    ret = StorageMallocNameConfigMem(memCtx, &config->systemSpaceFileName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "alloc system space file name");
        return ret;
    }
    ret = StorageMallocNameConfigMem(memCtx, &config->undoSpaceFileName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "alloc undo space file name");
        return ret;
    }
    ret = StorageMallocNameConfigMem(memCtx, &config->userSpaceFileName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "alloc user space file name");
        return ret;
    }
    ret = StorageMallocNameConfigMem(memCtx, &config->safeFileName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "alloc safe file name");
    }
    return ret;
}

Status StorageGetFileName(const char *srcName, char *destName)
{
    if (DbIsEnablePersistFileNameConvert()) {
        Status ret = DbPersistFileNameConvert(srcName, destName, DB_MAX_NAME_LEN);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "get (%s) file name.", srcName);
            return ret;
        }
    } else {
        errno_t err = strncpy_s(destName, DB_MAX_NAME_LEN, srcName, strlen(srcName));
        if (err != EOK) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "strncpy_s (%s) name", srcName);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
    }
    return GMERR_OK;
}

static Status StorageGetCtrlAndSpaceFileName(SeConfigT *config)
{
    const char *dbCtrlFile = "dbCtrlFile";
    const char *dbSystemSpace = "dbSystemSpace";
    const char *dbUndoSpace = "dbUndoSpace";
    const char *dbUserSpace = "dbUserSpace";
    const char *dbSafeFile = "dbSafeFile";
    if (DbDynLoadHasFeature(COMPONENT_MINIKV)) {
        dbCtrlFile = ".ctrl";
        dbSystemSpace = "";  // sysSpace使用配置项作为文件名
        dbUndoSpace = ".undo";
        dbUserSpace = ".user";
        dbSafeFile = ".safe";
    }
    Status ret = StorageGetFileName(dbCtrlFile, config->ctrlFileName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get ctrl file name.");
        return ret;
    }
    ret = StorageGetFileName(dbSystemSpace, config->systemSpaceFileName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get system space file name.");
        return ret;
    }
    ret = StorageGetFileName(dbUndoSpace, config->undoSpaceFileName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get undo space file mame.");
        return ret;
    }
    ret = StorageGetFileName(dbUserSpace, config->userSpaceFileName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get user space file name.");
        return ret;
    }
    ret = StorageGetFileName(dbSafeFile, config->safeFileName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get safe file name.");
        return ret;
    }
    return GMERR_OK;
}

Status PersistenceEnable(SeRunCtxHdT seRunCtx)
{
    DB_POINTER(seRunCtx);
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    if (seIns->db->enable) {
        return GMERR_OK;
    }
    CkptRefreshPoint(seIns);

    // flush all data
    for (uint32_t i = 0; i < seIns->seConfig.multiZoneNum; i++) {
        StatusInter ret = SpaceFlushOneZone(seIns, i);
        if (ret != STATUS_OK_INTER) {
            DB_LOG_ERROR(ret, "flush zone, zoneId=%" PRIu32, i);
            return DbGetExternalErrno(ret);
        }
    }

    RedoSetEnableWithLock(seIns->redoMgr, true);
    CkptSetEnable(seIns, true);
    seIns->db->enable = true;
    return GMERR_OK;
}

Status PersistenceDisable(SeRunCtxHdT seRunCtx)
{
    DB_POINTER(seRunCtx);
    SeInstanceT *seIns = (SeInstanceT *)seRunCtx->seIns;
    if (!seIns->db->enable) {
        return GMERR_OK;
    }
    Status ret = CkptTrigger(seIns, CKPT_MODE_FULL, true, WAIT_MSECONDS_FOR_CHECKPOINT);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "persistence disable redo flush");
        return ret;
    }
    RedoSetEnableWithLock(seIns->redoMgr, false);
    CkptSetEnable(seIns, false);
    seIns->db->enable = false;
    return GMERR_OK;
}

Status SePersistenceFlushEnable(SeRunCtxHdT seRunCtx, bool enable)
{
    if (SeGetPersistMode() != PERSIST_INCREMENT) {
        return GMERR_OK;
    }
    Status ret = DbGetExternalErrno(SeRegisterFlush(seRunCtx, DB_FLUSH_LATCH_TIMEOUTMS));
    if (ret != GMERR_OK) {
        return ret;
    }
    if (enable) {
        ret = PersistenceEnable(seRunCtx);
    } else {
        ret = PersistenceDisable(seRunCtx);
    }
    SeUnRegisterFlush(seRunCtx);
    return ret;
}

static Status StorageRedoPathAndPrefixConfigGet(SeInstanceT *seIns)
{
    SeConfigT *config = &seIns->seConfig;
    void *memCtx = seIns->seServerMemCtx;

    Status ret = SeMallocPathConfigMem(memCtx, config, &config->redoDir);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "alloc redo dir");
        return ret;
    }
    ret = StorageGetFilePath(memCtx, seIns, config, config->redoDir);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get redo dir config");
        return ret;
    }
    ret = StorageMallocNameConfigMem(memCtx, &config->redoFilePrefix);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "alloc redo file prefix");
        return ret;
    }
    ret = StorageGetRedoFilePrefix(config);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get redo file prefix");
        return ret;
    }

    return GMERR_OK;
}

static Status StorageGetTamperProofPath(void *memCtx, SeInstanceT *seIns, char **dstDir)
{
    *dstDir = NULL;
    DbCfgMgrHandleT handle = DbGetCfgHandle(seIns->dbInstance);
    if (handle == NULL) {
        DB_LOG_WARN(GMERR_NO_DATA, "miss config handle");
        return GMERR_NO_DATA;
    }
    DbCfgValueT cfgValue = {0};
    Status ret = DbCfgGet(handle, DB_CFG_TAMPER_PROOF_SO_PATH, &cfgValue);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t pathLen = (uint32_t)strlen(cfgValue.str);
    // strlen不包含\0，所以长度加1
    char *tamperProofSoPath = (char *)DbDynMemCtxAlloc(memCtx, sizeof(char) * (pathLen + 1));
    if (tamperProofSoPath == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "alloc tamper path");
        return GMERR_OUT_OF_MEMORY;
    }

    errno_t err = strcpy_s(tamperProofSoPath, pathLen + 1, cfgValue.str);
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "copy path:%s, osno:%d", cfgValue.str, err);
        DbDynMemCtxFree(memCtx, tamperProofSoPath);
        return GMERR_DATA_EXCEPTION;
    }

    *dstDir = tamperProofSoPath;
    return STATUS_OK_INTER;
}

SO_EXPORT_FOR_TS Status StorageStringConfigGet(SeInstanceT *seIns)
{
    SeConfigT *config = &seIns->seConfig;
    void *memCtx = seIns->seServerMemCtx;

    Status ret = SeMallocPathConfigMem(memCtx, config, &config->dataFileDirPath);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "alloc data file dir");
        return ret;
    }
    ret = StorageGetFilePath(memCtx, seIns, config, config->dataFileDirPath);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get data file dir config");
        return ret;
    }
    ret = SeMallocPathConfigMem(memCtx, config, &config->ctrlFileDirPath);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "alloc ctrl file dir");
        return ret;
    }
    ret = StorageGetFilePath(memCtx, seIns, config, config->ctrlFileDirPath);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get ctrl file dir config");
        return ret;
    }
    ret = StorageMallocCtrlAndSpaceNameMem(memCtx, config);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "alloc ctrl file name");
        return ret;
    }
    ret = StorageGetCtrlAndSpaceFileName(config);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get ctrl file name");
        return ret;
    }
    ret = StorageGetTamperProofPath(memCtx, seIns, &config->tamperProofSoPath);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "alloc tamper proof path");
        return ret;
    }
    if (SeGetPersistMode() == PERSIST_INCREMENT) {
        ret = StorageRedoPathAndPrefixConfigGet(seIns);
    }

    return ret;
}
