/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: storage file manager implementation
 * Author: zhangfengyu
 * Create: 2023-01-16
 */
#include "adpt_sleep.h"
#include "se_page_desc.h"
#include "se_redo_inner.h"
#include "se_space_inner.h"

// 持久化关键结构体对齐： (不区分64/32位平台)强制8字节对齐
#pragma pack(8)
typedef struct TagSePageInitRedoLog {
    PageIdT addr;
    uint32_t trmId;
} SePageInitRedoLogT;

#pragma pack()

// 等待其他线程写页操作完毕，休眠时间 unit:us
#define WAIT_WRITE_COMPLETE_SLEEP_TIME_INTERVAL_US 200
StatusInter WaitPageWriteComplete(SeInstanceT *seIns, DbSpinlockFnT *lockFn, BufDescT *pageDesc)
{
    RedoRunCtxT *redoCtx = SeGetCurRedoCtxImpl();
    DB_ASSERT(redoCtx->statusOk || !redoCtx->redoMgr->enable);
    for (;;) {
        while (pageDesc->isWriting && pageDesc->threadId != redoCtx->threadId) {
            // 避免等待其他线程写页操作完毕时候陷入死循环
            if (SECUREC_UNLIKELY(SeGetStorageStatus(seIns) == SE_ON_DISK_EMRGNCY)) {
                SE_ERROR(DATABASE_NOT_AVAILABLE_INTER, "wait current thread get pageDesc.");
                return DATABASE_NOT_AVAILABLE_INTER;
            }
            // 休眠等待
            DbUsleep(WAIT_WRITE_COMPLETE_SLEEP_TIME_INTERVAL_US);
        }

        // 避免当前线程拿到了这个页，但是没有完成写操作，陷入死循环
        if (SECUREC_UNLIKELY(SeGetStorageStatus(seIns) == SE_ON_DISK_EMRGNCY)) {
            SE_ERROR(DATABASE_NOT_AVAILABLE_INTER, "wait page write complete.");
            return DATABASE_NOT_AVAILABLE_INTER;
        }
        DbSpinLock(&pageDesc->lock);
        if (pageDesc->threadId == redoCtx->threadId) {
            break;
        }
        DB_RMB();
        if (pageDesc->isWriting) {
            DbSpinUnlock(&pageDesc->lock);
            continue;
        } else {
            pageDesc->threadId = redoCtx->threadId;
            pageDesc->isWriting = true;
            break;
        }
    }
    AddPageToRedoDirtyPage(pageDesc, redoCtx);
    DbSpinUnlock(&pageDesc->lock);
    return STATUS_OK_INTER;
}

void SePageHeadInitRedo(PageIdT *pageId, uint32_t trmId)
{
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    SePageInitRedoLogT log = {.addr = *pageId, .trmId = trmId};
    RedoLogWrite(redoCtx, REDO_INIT_PAGE_HEAD, pageId, (uint8_t *)&log, sizeof(SePageInitRedoLogT));
}

StatusInter SePageHeadInitReplay(RedoReplayArgsT *arg)
{
    SeInstanceT *seIns = arg->seInstance;
    SePageInitRedoLogT log = *(SePageInitRedoLogT *)(void *)arg->data;
    SeInitPageHead((uint8_t *)arg->pageHead, log.trmId, seIns->seConfig.pageSize * DB_KIBI, log.addr, true);
    return SpaceAllocPageById(arg->seInstance, log.addr);
}

StatusInter SePageHeadResetRedo(PageIdT *pageId)
{
    RedoRunCtxT *redoCtx = SeGetCurRedoCtx();
    RedoLogWrite(redoCtx, REDO_RESET_PAGE_HEAD, pageId, NULL, 0);
    return STATUS_OK_INTER;
}

StatusInter SePageHeadResetReplay(RedoReplayArgsT *arg)
{
    SeResetPageHead((uint8_t *)arg->pageHead);
    return STATUS_OK_INTER;
}
