/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * Description: checkpoint功能实现
 */

#include "adpt_atomic.h"
#include "adpt_string.h"
#include "adpt_sleep.h"
#include "db_crc.h"
#include "db_dyn_load.h"
#include "drt_instance.h"
#include "se_ctrl_page.h"
#include "se_ckpt_inner.h"
#include "se_database.h"
#include "se_log.h"
#include "se_redo_inner.h"
#include "se_recovery.h"
#include "se_verify_inner.h"
#include "se_ckpt.h"
#include "db_log.h"

#define CKPT_PROC_INTERVAL 200  // checkpoint线程需要再次continue处理间隔时间，unit: us
#define CKPT_LATCH_TIMEOUT 3    // 一写多读，锁超时时间，unit: us

#define CKPT_DIRTY_PAGE_THRESHOLD_RATE 0.8
#define BUF_LOCK_MAX_TRY_TIME 20      // 一写多读，最大尝试锁次数
#define CKPT_WAIT_PERIOD 1            // 等待刷盘结束，再次continue处理间隔时间. unit: ms
#define CKPT_DEFAULT_SPILT_TIME 1000  // 等待刷盘结束，如果超过一定次数，则尝试喂狗一次（split）
#define CKPT_ERR_RETRY_PERFORM_TIMES (3)  // checkpoint perform失败时候重试最大次数
// bufferpoolSize小于等于该阈值时，Checkpoint的groupBuffer才采用动态申请
#define CKPT_GROUP_BUF_DYNAMIC_ALLOC_THRESHOLD_SIZE 512  // unit KB

void *CkptProc(void *param);

void CkptInitQueue(CkptCtxT *ckptCtx)
{
    DB_POINTER(ckptCtx);
    DbSpinInit(&ckptCtx->pubCtx->queue.lock);
}

inline static uint32_t DwrCount(CkptGroupT *group, uint32_t groupSize)
{
    return group->dwrBlockNum == 0 ? 1 : (groupSize + (group->dwrBlockNum - 1)) / group->dwrBlockNum;  // 向上取整
}

static StatusInter CkptAllocGroupBuffer(SeInstanceT *seInsPtr, uint32_t groupBufAllocSize)
{
    seInsPtr->ckptCtx->group.buf = DbDynMemCtxAlloc(seInsPtr->seServerMemCtx, groupBufAllocSize);
    if (seInsPtr->ckptCtx->group.buf != NULL) {
        return STATUS_OK_INTER;
    }
    // 如果是初始化DB时，groupBuf申请失败，不用尝试使用逃生通道内存
    if (!seInsPtr->ckptCtx->isGroupDynAlloc) {
        // Unable to alloc group buffer when init checkpoint
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "Alloc buffer init ckpt, Size:%" PRIu32, groupBufAllocSize);
        return OUT_OF_MEMORY_INTER;
    }
    // unable to alloc group buffer, try to use excape memory
    SE_WARN(OUT_OF_MEMORY_INTER, "Alloc buffer try excape-mem, Size:%" PRIu32, groupBufAllocSize);
    // 如果申请内存失败，则尝试使用逃生通道的内存来申请
    DbSetUseEscapeFlag(true);
    seInsPtr->ckptCtx->group.buf = DbDynMemCtxAlloc(seInsPtr->seServerMemCtx, groupBufAllocSize);
    if (seInsPtr->ckptCtx->group.buf == NULL) {
        // Unable to alloc group buffer using excape memory, allocSize
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "Alloc buffer use excape-mem, Size:%" PRIu32, groupBufAllocSize);
        return OUT_OF_MEMORY_INTER;
    }
    return STATUS_OK_INTER;
}

StatusInter CkptInitGroup(SeInstanceT *seInsPtr, uint32_t groupSize)
{
    DB_POINTER(seInsPtr);
    CkptGroupT *group = &seInsPtr->ckptCtx->group;

    // 已经初始化过或者失败继续flush
    if (group->buf != NULL) {
        return STATUS_OK_INTER;
    }

    if (seInsPtr->seConfig.dwrEnable) {
        group->dwrBlockNum = seInsPtr->seConfig.dwrBlockNum;
        DB_ASSERT(group->dwrBlockNum <= SE_CKPT_GROUP_SIZE);
    } else {
        group->dwrBlockNum = SE_CKPT_GROUP_SIZE;
    }

    uint32_t pageSize = SIZE_K(seInsPtr->seConfig.pageSize);

    /* 分批双写，批数为dwrCount，buffer中就会将pages分多组，每组page数为seInsPtr->seConfig.dwrBlockNum
       格式：||SafeFileHeadT|pages|DwPageMetaT|SafeFileTailT||...||SafeFileHeadT|pages|DwPageMetaT|SafeFileTailT||
       最后一个page数可以少于seInsPtr->seConfig.dwrBlockNum
    */
    uint32_t dwrCount = DwrCount(group, groupSize);
    uint32_t bufSize = sizeof(SafeFileHeadT) * dwrCount + groupSize * (pageSize + (uint32_t)sizeof(DwPageMetaT)) +
                       sizeof(SafeFileTailT) * dwrCount;
    uint32_t groupBufAllocSize = bufSize + (uint32_t)sizeof(CkptSortItemT) * groupSize;

    StatusInter ret = CkptAllocGroupBuffer(seInsPtr, groupBufAllocSize);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "alloc group buffer.");
        return ret;
    }

    group->pageSize = pageSize;
    group->bufSize = bufSize;
    group->offset = sizeof(SafeFileHeadT);
    group->count = 0;
    group->dwrPartIndex = 0;
    group->flushPartIndex = 0;
    group->flushStartPoint = 0;
    group->capacity = groupSize;
    group->items = (CkptSortItemT *)(group->buf + bufSize);
    group->seIns = seInsPtr;
    return STATUS_OK_INTER;
}

void CkptInitParam(SeInstanceT *seInsPtr)
{
    DB_POINTER(seInsPtr);

    CkptCtxT *ckptCtx = seInsPtr->ckptCtx;

    ckptCtx->period = seInsPtr->seConfig.ckptPeriod;
    ckptCtx->threshold = seInsPtr->seConfig.ckptThreshold;
}

StatusInter CkptStartProc(SeInstanceT *seInsPtr)
{
    DB_POINTER(seInsPtr);

    ThreadAttrsT attr = {.name = "CkptProc",
        .priority = THREAD_PRIORITY_MIDDLE,
        .type = DB_THREAD_JOINABLE,
        .entryFunc = CkptProc,
        .exitFunc = NULL,
        .entryArgs = (void *)seInsPtr,
        .exitArgs = NULL,
        .stackSize = DB_DEFAULT_THREAD_STACK_SIZE,
        .bindCpuFlag = 0,
        .cpu = 0,
        .userAttr = NULL};

    seInsPtr->ckptCtx->isRunning = true;
    Status ret = DbThreadCreate(&attr, &seInsPtr->ckptCtx->thread);
    if (ret != GMERR_OK) {
        seInsPtr->ckptCtx->isRunning = false;
        DB_LOG_ERROR(ret, "create ckpt-proc thread.");
        return DbGetStatusInterErrno(ret);
    }

    return STATUS_OK_INTER;
}

void CkptStopProc(SeInstanceT *seInsPtr)
{
    DB_POINTER(seInsPtr);
    // 如果ckptCtx未初始化，不进行后面流程
    if (seInsPtr->ckptCtx == NULL) {
        return;
    }
    if (seInsPtr->ckptCtx->isFlushOnMiniDemand) {
        // stop流程下 即使刷盘失败后续也无法处理，因此忽略返回值
        (void)CkptTriggerImpl(seInsPtr, CKPT_MODE_FULL, false, WAIT_MSECONDS_FOR_FLUSH_IN_DESTROY);
        return;
    }
    if (seInsPtr->ckptCtx->isRunning) {
        // 应对启动DB失败场景，这里不能触发刷盘
        seInsPtr->ckptCtx->isRunning = false;
        (void)DbThreadJoin(seInsPtr->ckptCtx->thread, NULL);
    }
}

static inline void ModifyCkptThreshold(CkptCtxT *ckptCtx, SeInstanceT *seInstance, DbCfgValueT *newValue)
{
    DbSpinLock(&ckptCtx->pubCtx->lock);
    ckptCtx->threshold = (uint32_t)newValue->int32Val;
    seInstance->seConfig.ckptThreshold = (uint32_t)newValue->int32Val;
    DbSpinUnlock(&ckptCtx->pubCtx->lock);
}

static inline void ModifyCkptPeriod(CkptCtxT *ckptCtx, SeInstanceT *seInstance, DbCfgValueT *newValue)
{
    DbSpinLock(&ckptCtx->pubCtx->lock);
    ckptCtx->period = (uint32_t)newValue->int32Val;
    seInstance->seConfig.ckptPeriod = (uint32_t)newValue->int32Val;
    // 用户配置了周期时间，需要清零flush time
    ckptCtx->flushEndTime = DbToMseconds(DbRdtsc());
    DbSpinUnlock(&ckptCtx->pubCtx->lock);
}

static inline void ModifyRedoFlushByTrx(RedoMgrT *redoMgr, SeInstanceT *seInstance, DbCfgValueT *newValue)
{
    DbSpinLock(&redoMgr->lock);
    redoMgr->cfg.flushByTrx = (bool)newValue->int32Val;
    seInstance->seConfig.redoFlushByTrx = (bool)newValue->int32Val;
    DbSpinUnlock(&redoMgr->lock);
}

static inline void ModifyRedoFlushBySize(RedoMgrT *redoMgr, SeInstanceT *seInstance, DbCfgValueT *newValue)
{
    DbSpinLock(&redoMgr->lock);
    redoMgr->cfg.flushBySize = (uint32_t)newValue->int32Val * DB_KIBI;
    seInstance->seConfig.redoFlushBySize = (uint32_t)newValue->int32Val * DB_KIBI;
    DbSpinUnlock(&redoMgr->lock);
}

static inline void ModifyRedoFlushByTime(RedoMgrT *redoMgr, SeInstanceT *seInstance, DbCfgValueT *newValue)
{
    DbSpinLock(&redoMgr->lock);
    redoMgr->cfg.flushByTime = (uint32_t)newValue->int32Val;
    seInstance->seConfig.redoFlushByTime = (uint32_t)newValue->int32Val;
    DbSpinUnlock(&redoMgr->lock);
}

static inline void ModifyRedoFlushCheckPeriod(RedoMgrT *redoMgr, SeInstanceT *seInstance, DbCfgValueT *newValue)
{
    DbSpinLock(&redoMgr->lock);
    redoMgr->cfg.flushCheckPeriod = (uint32_t)newValue->int32Val;
    seInstance->seConfig.redoFlushCheckPeriod = (uint32_t)newValue->int32Val;
    DbSpinUnlock(&redoMgr->lock);
}

void CkptConfigChangeNotify(
    const char *configName, const DbCfgValueT *oldValue, DbCfgValueT *newValue, DbInstanceHdT dbInstance)
{
    DB_POINTER2(configName, newValue);
    if (oldValue == NULL || DbCfgValueIsEqual(oldValue, newValue)) {
        return;
    }
    SeInstanceT *seInstance = SeGetInstance(DbGetInstanceId(dbInstance));
    if (seInstance == NULL) {
        return;
    }
    // 修改ctx的配置数据，如果配置需要上锁，则修改前后需要加锁
    CkptCtxT *ckptCtx = seInstance->ckptCtx;
    RedoMgrT *redoMgr = seInstance->redoMgr;
    DB_ASSERT(ckptCtx != NULL);
    DB_ASSERT(redoMgr != NULL);
    DbCfgEmItemIdE id = DbCfgGetIdByName(configName);
    switch (id) {
        case DB_CFG_CKPT_THLD:
            ModifyCkptThreshold(ckptCtx, seInstance, newValue);
            break;
        case DB_CFG_CKPT_PERIOD:
            ModifyCkptPeriod(ckptCtx, seInstance, newValue);
            break;
        case DB_CFG_REDO_FLUSH_BY_TRX:
            ModifyRedoFlushByTrx(redoMgr, seInstance, newValue);
            break;
        case DB_CFG_REDO_FLUSH_BY_SIZE:
            ModifyRedoFlushBySize(redoMgr, seInstance, newValue);
            break;
        case DB_CFG_REDO_FLUSH_BY_TIME:
            ModifyRedoFlushByTime(redoMgr, seInstance, newValue);
            break;
        case DB_CFG_REDO_FLUSH_CHECK_PERIOD:
            ModifyRedoFlushCheckPeriod(redoMgr, seInstance, newValue);
            break;
        default:
            // Not normal parameter value. config id
            DB_LOG_WARN(GMERR_INVALID_PARAMETER_VALUE, "cfg id:%" PRIu32, (uint32_t)id);
            return;
    }
    DB_LOG_INFO("update ckpt-cfg, id:%" PRIu32, (uint32_t)id);
}

static StatusInter AttachExistCkptCtx(SeInstanceT *seInsPtr)
{
    seInsPtr->ckptCtx->pubCtx = (CkptPublicCtxT *)DbDynShmemPtrToAddr(seInsPtr->ckptCtxShm);
    if (seInsPtr->ckptCtx->pubCtx == NULL) {
        SE_ERROR(MEMORY_OPERATE_FAILED_INTER, "get ckpt public ctx.");
        return MEMORY_OPERATE_FAILED_INTER;
    }
    return STATUS_OK_INTER;
}

StatusInter AllocCkpt(SeInstanceT *seInsPtr)
{
    uint32_t ckptAllocSize = sizeof(CkptCtxT);
    seInsPtr->ckptCtx = DbDynMemCtxAlloc(seInsPtr->seServerMemCtx, ckptAllocSize);
    if (seInsPtr->ckptCtx == NULL) {
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "alloc checkpoint ctx, size:%" PRIu32 ".", ckptAllocSize);
        return OUT_OF_MEMORY_INTER;
    }
    (void)memset_s(seInsPtr->ckptCtx, sizeof(CkptCtxT), 0, sizeof(CkptCtxT));
    StatusInter ret = STATUS_OK_INTER;
    CkptCtxT *ckptCtx = seInsPtr->ckptCtx;
    if (!DbIsShmPtrValid(seInsPtr->ckptCtxShm)) {
        ckptCtx->pubCtx =
            DbWrappedMemCtxAllocAddr(seInsPtr->memUtils.memCtx, sizeof(CkptPublicCtxT), &seInsPtr->ckptCtxShm);
        if (ckptCtx->pubCtx == NULL) {
            SE_ERROR(MEMORY_OPERATE_FAILED_INTER, "alloc ckpt public ctx.");
            ret = MEMORY_OPERATE_FAILED_INTER;
            goto RELEASE;
        }
        (void)memset_s(ckptCtx->pubCtx, sizeof(CkptPublicCtxT), 0, sizeof(CkptPublicCtxT));
        DbSpinInit(&ckptCtx->pubCtx->lock);
        ckptCtx->pubCtx->queue.first = DB_INVALID_SHMPTR;
        ckptCtx->pubCtx->queue.last = DB_INVALID_SHMPTR;
        return STATUS_OK_INTER;
    }
    ret = AttachExistCkptCtx(seInsPtr);
    if (ret != STATUS_OK_INTER) {
        goto RELEASE;
    }
    return STATUS_OK_INTER;
RELEASE:
    if (seInsPtr->ckptCtx->pubCtx != NULL) {
        DbDynShmemMemCtxFree(seInsPtr->memUtils.memCtx, seInsPtr->ckptCtxShm);
        seInsPtr->ckptCtxShm = DB_INVALID_SHMPTR;
    }
    DbDynMemCtxFree(seInsPtr->seServerMemCtx, seInsPtr->ckptCtx);
    seInsPtr->ckptCtx = NULL;
    return ret;
}

static StatusInter CkptAllocTempBufForComp(SeInstanceT *seIns)
{
    DB_POINTER(seIns);

    if (seIns->ckptCtx->ckptCompBuf != NULL) {
        // The temporary compression buffer should be empty before flushing the disk in ckpt.
        SE_LAST_ERROR(DATA_EXCEPTION_INTER, "compression buffer not empty.");
        return DATA_EXCEPTION_INTER;
    }

    if (SeGetFlushCompMode(seIns) == COMP_MODE_NONE) {
        return STATUS_OK_INTER;
    }
    uint32_t allocSize = 0;
    if (SeGetFlushCompMode(seIns) == COMP_MODE_PAGE) {
        allocSize = seIns->seConfig.pageSize * DB_KIBI;
    }
    seIns->ckptCtx->ckptCompBuf = DbDynMemCtxAlloc(seIns->seServerMemCtx, allocSize);
    if (seIns->ckptCtx->ckptCompBuf == NULL) {
        // Unable to alloc temp buffer for compress when flushing the disk in ckpt.
        SE_LAST_ERROR(OUT_OF_MEMORY_INTER, "ckpt compress buf, Size=%" PRIu32, allocSize);
        return OUT_OF_MEMORY_INTER;
    }

    return STATUS_OK_INTER;
}

static void CkptFreeTempBufForComp(SeInstanceT *seIns)
{
    DbDynMemCtxFree(seIns->seServerMemCtx, seIns->ckptCtx->ckptCompBuf);
    seIns->ckptCtx->ckptCompBuf = NULL;
}

StatusInter CkptInitImpl(SeInstanceT *seInsPtr)
{
    DB_POINTER(seInsPtr);
    StatusInter ret = AllocCkpt(seInsPtr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "alloc checkpoint-mem.");
        return ret;
    }
    seInsPtr->ckptCtx->isFlushOnMiniDemand = DbDynLoadHasFeature(COMPONENT_MINIKV);
    // 终端，或者bufferpoolSize小于等于128KB时，Checkpoint 的 groupBuffer 才采用动态申请
    // bufferpool中页数量较少时，脏页数量少，所以groupBuf也可以小一些，改为动态申请
    seInsPtr->ckptCtx->isGroupDynAlloc =
        DbDynLoadHasFeature(COMPONENT_MINIKV) ||
        seInsPtr->seConfig.bufferPoolSize <= CKPT_GROUP_BUF_DYNAMIC_ALLOC_THRESHOLD_SIZE;
    seInsPtr->ckptCtx->enable = true;

    // 动态申请group
    if (!seInsPtr->ckptCtx->isGroupDynAlloc) {
        ret = CkptInitGroup(seInsPtr, SE_CKPT_GROUP_SIZE);
        if (ret != STATUS_OK_INTER) {
            CkptDestroyImpl(seInsPtr);
            SE_ERROR(ret, "init checkpoint group.");
            return ret;
        }
    }

    CkptInitQueue(seInsPtr->ckptCtx);

    CkptInitParam(seInsPtr);
    seInsPtr->ckptCtx->truncPoint = seInsPtr->db->core.truncPoint;
    seInsPtr->ckptCtx->lrpPoint = seInsPtr->db->core.lrpPoint;
    if (!seInsPtr->ckptCtx->isFlushOnMiniDemand) {
        // 为压缩申请临时内存，非终端场景才需要申请
        ret = CkptAllocTempBufForComp(seInsPtr);
        if (ret != STATUS_OK_INTER) {
            CkptDestroyImpl(seInsPtr);
            // Unable to alloc temp buffer for checkpoint compress.
            SE_ERROR(ret, "temp-buffer for ckpt compress.");
            return ret;
        }
        ret = CkptStartProc(seInsPtr);
        if (ret != STATUS_OK_INTER) {
            CkptDestroyImpl(seInsPtr);
            SE_ERROR(ret, "start ckpt-proc thread.");
            return ret;
        }
    }
    seInsPtr->ckptCtx->seIns = seInsPtr;

    DB_LOG_INFO("Checkpoint init");
    // 初始化之后，注册配置变动通知函数
    (void)DbCfgRegisterNofityFunc(DB_CFG_REDO_FLUSH_BY_TRX, CkptConfigChangeNotify, seInsPtr->dbInstance);
    (void)DbCfgRegisterNofityFunc(DB_CFG_REDO_FLUSH_BY_SIZE, CkptConfigChangeNotify, seInsPtr->dbInstance);
    (void)DbCfgRegisterNofityFunc(DB_CFG_REDO_FLUSH_BY_TIME, CkptConfigChangeNotify, seInsPtr->dbInstance);
    (void)DbCfgRegisterNofityFunc(DB_CFG_REDO_FLUSH_CHECK_PERIOD, CkptConfigChangeNotify, seInsPtr->dbInstance);
    (void)DbCfgRegisterNofityFunc(DB_CFG_CKPT_PERIOD, CkptConfigChangeNotify, seInsPtr->dbInstance);
    (void)DbCfgRegisterNofityFunc(DB_CFG_CKPT_THLD, CkptConfigChangeNotify, seInsPtr->dbInstance);
    return DB_SUCCESS;
}

void CkptDestroyImpl(SeInstanceT *seInsPtr)
{
    DB_POINTER(seInsPtr);
    CkptCtxT *ckptCtx = seInsPtr->ckptCtx;

    CkptStopProc(seInsPtr);
    if (ckptCtx != NULL) {
        if (ckptCtx->group.buf != NULL) {
            DbDynMemCtxFree(seInsPtr->seServerMemCtx, ckptCtx->group.buf);
            ckptCtx->group.buf = NULL;
            ckptCtx->group.items = NULL;
            ckptCtx->group.seIns = NULL;
        }
        if (ckptCtx->ckptCompBuf != NULL) {
            CkptFreeTempBufForComp(seInsPtr);
        }
        if (ckptCtx->pubCtx != NULL) {
            DbDynShmemMemCtxFree(seInsPtr->memUtils.memCtx, seInsPtr->ckptCtxShm);
            seInsPtr->ckptCtxShm = DB_INVALID_SHMPTR;
            ckptCtx->pubCtx = NULL;
        }
        DbDynMemCtxFree(seInsPtr->seServerMemCtx, ckptCtx);
        seInsPtr->ckptCtx = NULL;
    }

    DB_LOG_INFO("Checkpoint destroy");
}

void CkptEnQueue(SeInstanceT *seInsPtr, BufDescT *buf)
{
    DB_POINTER2(seInsPtr, buf);
    CkptQueueT *queue = &seInsPtr->ckptCtx->pubCtx->queue;

    DbSpinLock(&queue->lock);
    if (buf->ckptFlag == IN_CKPT_QUEUE) {
        DbSpinUnlock(&queue->lock);
        return;
    }

    if (queue->count == 0) {
        queue->first = buf->thisPtr;
        buf->ckptPrev = DB_INVALID_SHMPTR;
    } else {
        GetBufDescAddr(queue->last)->ckptNext = buf->thisPtr;
        buf->ckptPrev = queue->last;
    }

    queue->last = buf->thisPtr;
    buf->ckptNext = DB_INVALID_SHMPTR;
    queue->count++;
    buf->truncPoint = RedoGetCurrPoint(seInsPtr->redoMgr);
    if (queue->batchEnd != NULL && RedoPointCmp(&queue->batchEnd->truncPoint, &buf->truncPoint) == 0) {
        queue->batchEnd = buf;
    }
    buf->ckptFlag = IN_CKPT_QUEUE;
    DbSpinUnlock(&queue->lock);
}

BufDescT *CkptDeQueue(SeInstanceT *seInsPtr)
{
    DB_POINTER(seInsPtr);
    BufDescT *buf = NULL;
    CkptQueueT *queue = &seInsPtr->ckptCtx->pubCtx->queue;

    DbSpinLock(&queue->lock);
    if (queue->count == 0) {
        DbSpinUnlock(&queue->lock);
        return NULL;
    }

    buf = GetBufDescAddr(queue->first);
    queue->first = buf->ckptNext;
    if (DbIsShmPtrValid(queue->first)) {
        GetBufDescAddr(queue->first)->ckptPrev = DB_INVALID_SHMPTR;
    } else {
        queue->last = DB_INVALID_SHMPTR;
    }
    queue->count--;

    buf->ckptNext = DB_INVALID_SHMPTR;
    buf->ckptFlag = NOT_IN_CKPT_QUEUE;

    DbSpinUnlock(&queue->lock);

    return buf;
}

void CkptPopQueue(SeInstanceT *seInsPtr, BufDescT *buf)
{
    DB_POINTER(seInsPtr);
    CkptQueueT *queue = &seInsPtr->ckptCtx->pubCtx->queue;

    DbSpinLock(&queue->lock);
    queue->count--;
    if (queue->batchEnd == buf) {
        queue->batchEnd = GetBufDescAddr(buf->ckptPrev);
    }
    if (queue->count == 0) {
        queue->first = DB_INVALID_SHMPTR;
        queue->last = DB_INVALID_SHMPTR;
    } else {
        if (DbIsShmPtrValid(buf->ckptPrev)) {
            GetBufDescAddr(buf->ckptPrev)->ckptNext = buf->ckptNext;
        }

        if (DbIsShmPtrValid(buf->ckptNext)) {
            GetBufDescAddr(buf->ckptNext)->ckptPrev = buf->ckptPrev;
        }

        if (DbShmemPtrCmp(&queue->last, &buf->thisPtr)) {
            queue->last = buf->ckptPrev;
        }

        if (DbShmemPtrCmp(&queue->first, &buf->thisPtr)) {
            queue->first = buf->ckptNext;
        }
    }

    buf->ckptNext = DB_INVALID_SHMPTR;
    buf->ckptFlag = NOT_IN_CKPT_QUEUE;

    DbSpinUnlock(&queue->lock);
}

void CkptCopyPage(CkptGroupT *group, BufDescT *buf)
{
    DB_POINTER2(group, buf);

    void *descPage = buf->page;
    DB_ASSERT(descPage != NULL);
    errno_t err = memcpy_s(group->buf + group->offset, group->bufSize - group->offset, descPage, group->pageSize);
    DB_ASSERT(err == EOK);
    group->offset += group->pageSize;
    if (group->dwrBlockNum > 0 && (group->count + 1) % group->dwrBlockNum == 0) {
        group->offset += sizeof(SafeFileTailT) + sizeof(DwPageMetaT) * group->dwrBlockNum + sizeof(SafeFileHeadT);
    }

    buf->inCkptBuf = 1;
    COMPILER_BARRIER;
    buf->isDirty = false;
    buf->isReadOnly = false;
}

bool CkptPushGroup(CkptGroupT *const group, BufDescT *buf)
{
    DB_POINTER2(group, buf);

    if (group->count >= group->capacity) {
        return false;
    }

    CkptCopyPage(group, buf);
    group->items[group->count].buf = buf;
    group->items[group->count].itemId = group->count;
    group->count++;

    return true;
}

void CkptClearGroup(SeInstanceT *seInsPtr)
{
    DB_POINTER(seInsPtr);
    CkptCtxT *ckptCtx = seInsPtr->ckptCtx;
    if (ckptCtx->isGroupDynAlloc) {
        DbDynMemCtxFree(seInsPtr->seServerMemCtx, ckptCtx->group.buf);
        ckptCtx->group.buf = NULL;
        ckptCtx->group.items = NULL;
        ckptCtx->group.seIns = NULL;
        ckptCtx->group.pageSize = 0;
        ckptCtx->group.bufSize = 0;
        ckptCtx->group.capacity = 0;
        ckptCtx->group.flushStartPoint = 0;

        // 大块内存及时归还，减少底噪占用
        (void)DbAdptMallocTrim(0);
        // 关闭逃生通道标记，这里不管有没有开，都直接关掉
        // 走到这里说明逃生通道内存肯定已经释放
        DbSetUseEscapeFlag(false);
    }
    ckptCtx->group.dwrPartIndex = 0;
    ckptCtx->group.flushPartIndex = 0;
    ckptCtx->group.count = 0;
    ckptCtx->group.offset = sizeof(SafeFileHeadT);
}

static int CkptGroupItemCmp(const void *pa, const void *pb)
{
    DB_POINTER2(pa, pb);
    PageHeadT *pageA = (PageHeadT *)(((const CkptSortItemT *)pa)->buf->page);
    PageHeadT *pageB = (PageHeadT *)(((const CkptSortItemT *)pb)->buf->page);

    PageIdT *addrA = &pageA->addr;
    PageIdT *addrB = &pageB->addr;

    // compare fileid
    if (addrA->deviceId < addrB->deviceId) {
        return -1;
    } else if (addrA->deviceId > addrB->deviceId) {
        return 1;
    }

    // compare page
    if (addrA->blockId < addrB->blockId) {
        return -1;
    } else if (addrA->blockId > addrB->blockId) {
        return 1;
    }

    return 0;
}

void CkptSortGroup(CkptGroupT *group)
{
    DB_POINTER(group);

    qsort(group->items, group->count, sizeof(CkptSortItemT), CkptGroupItemCmp);
}

static StatusInter CkptTryLatchDesc(DbSpinlockFnT *lockFn, BufDescT *bufDesc, bool *locked)
{
    *locked = false;
    if (DbSpinTimedLock(&bufDesc->lock, CKPT_LATCH_TIMEOUT)) {
        (void)DbAtomicTAS(&bufDesc->lockRetryCnt, 0);
        if (bufDesc->isWriting == false) {
            *locked = true;
            return STATUS_OK_INTER;
        }
        DbSpinUnlock(&bufDesc->lock);
        return STATUS_OK_INTER;
    }
    uint32_t cnt = DbAtomicInc(&bufDesc->lockRetryCnt);
    if (cnt < BUF_LOCK_MAX_TRY_TIME) {
        return STATUS_OK_INTER;
    }
    (void)DbAtomicTAS(&bufDesc->lockRetryCnt, 0);
    if (lockFn->checkFn == NULL) {
        return STATUS_OK_INTER;
    }
    // 多进程场景，有进程崩溃的话，DbInterProcLockTimedLock会一直加不上，此时需要检查是否有进程异常退出
    return DbGetStatusInterErrno((*lockFn->checkFn)(lockFn->checkArgs));
}

StatusInter CkptCopyPages(SeInstanceT *seInsPtr)
{
    DB_POINTER(seInsPtr);
    CkptCtxT *ckptCtx = seInsPtr->ckptCtx;
    BufDescT *desc = (BufDescT *)DbDynShmemPtrToAddr(ckptCtx->pubCtx->queue.first);
    BufDescT *nextDesc = NULL;
    while (desc) {
        nextDesc = (BufDescT *)DbDynShmemPtrToAddr(desc->ckptNext);
        bool locked = false;
        StatusInter ret = CkptTryLatchDesc(&seInsPtr->seLockFn, desc, &locked);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        if (!locked) {
            desc = nextDesc;
            continue;
        }

        if (desc->inCkptBuf) {
            DbSpinUnlock(&desc->lock);
            desc = nextDesc;
            continue;
        }

        // 一写读多场景下，buf->page实际为ShmemPtrT强转为void *保存, 需要转换成本进程MemAddr
        void *descPage = desc->page;
        ret = SeUpdateDataPageDigest(seInsPtr, descPage, ckptCtx->group.pageSize);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "update page digest.");
            return ret;
        }
        if (!CkptPushGroup(&ckptCtx->group, desc)) {
            DbSpinUnlock(&desc->lock);
            break;
        }
        CkptPopQueue(seInsPtr, desc);
        DbSpinUnlock(&desc->lock);
        desc = nextDesc;
    }

    DB_LOG_DBG_DEBUG("Ckpt prepare, group count %" PRIu32 "", seInsPtr->ckptCtx->group.count);
    return STATUS_OK_INTER;
}

StatusInter CkptPrepare(SeInstanceT *seInsPtr)
{
    DB_POINTER(seInsPtr);
    CkptCtxT *ckptCtx = seInsPtr->ckptCtx;
    uint32_t groupSize =
        ckptCtx->pubCtx->queue.count > SE_CKPT_GROUP_SIZE ? SE_CKPT_GROUP_SIZE : ckptCtx->pubCtx->queue.count;
    if (groupSize == 0 && ckptCtx->group.count == 0) {  // 没有要flush的数据以及没有因为失败需要继续flush数据
        return STATUS_OK_INTER;
    }
    StatusInter ret = CkptInitGroup(seInsPtr, groupSize);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    return CkptCopyPages(seInsPtr);
}

StatusInter CkptFlushItem(CkptCtxT *ckptCtx, const CkptSortItemT *item, uint32_t zoneId)
{
    DB_POINTER2(ckptCtx, item);
    CkptGroupT *group = &ckptCtx->group;
    uint32_t partNum = item->itemId / group->dwrBlockNum;
    uint32_t pageIndex = item->itemId % group->dwrBlockNum;
    uint64_t offset = partNum * SafeFileGetPartFileLen(&ckptCtx->group) + sizeof(SafeFileHeadT) +
                      (uint64_t)pageIndex * ckptCtx->group.pageSize;
    PageHeadT *page = (PageHeadT *)(void *)(ckptCtx->group.buf + offset);
    StatusInter ret = SpaceWriteBlock(ckptCtx->seIns, page, item->buf->pageId, zoneId, false);
    if (ret != STATUS_OK_INTER) {
        // Checkpoint unable to flush page
        SE_ERROR(ret, "Flush pageAddr:%" PRIu32 ",%" PRIu32 ", pageId:%" PRIu32 ",%" PRIu32, page->addr.deviceId,
            page->addr.blockId, item->buf->pageId.deviceId, item->buf->pageId.blockId);
        return ret;
    }

    return STATUS_OK_INTER;
}

static StatusInter CkptFlushItemPart(SeInstanceT *seInsPtr, uint32_t itemStart, uint32_t itemCount)
{
    CkptCtxT *ckptCtx = seInsPtr->ckptCtx;
    CkptGroupT *group = &ckptCtx->group;
    uint32_t count = group->count;
    if (itemCount != 0) {
        count = (itemStart + itemCount) <= group->count ? itemCount : group->count - itemStart;
    }
    SeMultiZoneCtrlT zoneCtrl = seInsPtr->db->zoneCtrl;
    uint32_t flushStartIndex = group->flushStartPoint;
    for (uint32_t i = flushStartIndex; i < count; i++) {
        uint32_t itemIdx = itemStart + i;
        for (uint32_t j = 0; j < seInsPtr->seConfig.multiZoneNum; j++) {
            if (SeCanSkipOfflineZone(seInsPtr, &zoneCtrl, j)) {
                // 当分区句柄已经被释放或损坏，不会继续向副分区落盘
                continue;
            }
            StatusInter ret = CkptFlushItem(ckptCtx, &group->items[itemIdx], j);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "Checkpoint flush item:%" PRIu32 ".", itemIdx);
                return ret;
            }
        }
        // page flush to disk and update desc status
        group->items[itemIdx].buf->inCkptBuf = 0;
        group->flushStartPoint++;
        ckptCtx->stat.flushPageCount++;
    }
    return STATUS_OK_INTER;
}

StatusInter CkptFlush(SeInstanceT *seInsPtr)
{
    DB_POINTER(seInsPtr);
    CkptCtxT *ckptCtx = seInsPtr->ckptCtx;
    CkptGroupT *group = &ckptCtx->group;

    // sort is not necessary for on-demand disk flushing mode. can not sort while parting double write
    if (!ckptCtx->isFlushOnMiniDemand &&
        (!seInsPtr->seConfig.dwrEnable || seInsPtr->seConfig.dwrBlockNum == SE_CKPT_GROUP_SIZE)) {
        CkptSortGroup(group);
    }

    StatusInter ret = STATUS_OK_INTER;
    uint32_t dwrFlushTimes = DwrCount(group, group->count);
    while (group->flushPartIndex < dwrFlushTimes) {
        // 异常场景下，flsuhPartIndex最多比dwrPartIndex小1个part
        DB_ASSERT(group->dwrPartIndex - group->flushPartIndex <= 1);
        if (group->dwrPartIndex == group->flushPartIndex) {
            ret = DoubleWrite(seInsPtr, group->dwrPartIndex);
            if (ret != STATUS_OK_INTER) {
                SE_ERROR(ret, "DoubleWrite i:%" PRIu32 ", count:%" PRIu32, group->dwrPartIndex, dwrFlushTimes);
                return ret;
            }
            group->dwrPartIndex++;
        }
        ret = CkptFlushItemPart(seInsPtr, group->flushPartIndex * group->dwrBlockNum, group->dwrBlockNum);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        group->flushPartIndex++;
        // 保证safeFile和dataFile一致性的标记位
        DB_ASSERT(group->dwrPartIndex == group->flushPartIndex);
        // Reset the flush start point index, using for the next part to start from the beginning.
        group->flushStartPoint = 0;
    }

    // 上面的异常分支不用clearGroup，等下次刷盘的时候再继续刷
    CkptClearGroup(seInsPtr);
    ckptCtx->stat.flushCount++;

    return STATUS_OK_INTER;
}

StatusInter CkptFlushRedo(SeInstanceT *seInsPtr)
{
    DB_POINTER(seInsPtr);
    CkptCtxT *ckptCtx = seInsPtr->ckptCtx;

    StatusInter ret = RedoLogFlush(seInsPtr->redoMgr, &ckptCtx->lrpPoint);
    if (ret != STATUS_OK_INTER) {
        // unable to flush redo log in ckpt flushing
        SE_ERROR(ret, "redo log in ckpt-flush");
        return ret;
    }
    ret = RedoLogFileSync(seInsPtr->redoMgr, true);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "ckpt sync redo.");
        return ret;
    }
    // 重启场景，如果redo日志没有扫描完，enable为flase，不更新truncPoint；终端ckpt非独立线程也不用enable控制
    if (RedoIsEnable(seInsPtr->redoMgr)) {
        DbSpinLock(&ckptCtx->pubCtx->queue.lock);
        if (DbIsShmPtrValid(ckptCtx->pubCtx->queue.first)) {
            // 表明脏页队列中还有脏页未落盘，第一个脏页入队时间点之后的redo日志都不能被截断
            ckptCtx->truncPoint = GetBufDescAddr(ckptCtx->pubCtx->queue.first)->truncPoint;
        } else {
            // 脏页队列中没有脏页，可以把所有的redo日志都截断
            ckptCtx->truncPoint = ckptCtx->lrpPoint;
        }
        DbSpinUnlock(&ckptCtx->pubCtx->queue.lock);
    }

    return STATUS_OK_INTER;
}

StatusInter CkptFlushWithRedo(SeInstanceT *seInsPtr)
{
    DB_POINTER(seInsPtr);
    if (SECUREC_UNLIKELY(SeGetStorageStatus(seInsPtr) == SE_ON_DISK_EMRGNCY)) {
        SE_ERROR(DATABASE_NOT_AVAILABLE_INTER, "flush redo in ckpt");
        return DATABASE_NOT_AVAILABLE_INTER;
    }
    CkptCtxT *ckptCtx = seInsPtr->ckptCtx;
    ckptCtx->stat.performCount++;
    StatusInter ret = CkptPrepare(seInsPtr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Checkpoint prepare wrong.");
        return ret;
    }
    RedoPointT curPoint = seInsPtr->redoMgr->pubCtx->curPoint;
    if (ckptCtx->group.count == 0 && ckptCtx->pubCtx->queue.count == 0) {
        ckptCtx->truncPoint = curPoint;  // can not use seInsPtr->redoMgr->curPoint directly for concurrent reason
        return STATUS_OK_INTER;
    }
    ckptCtx->stat.flushBeginTime = DbToMseconds(DbRdtsc());
    CRASHPOINT(DB_CRASH_EVENT_PERSIST_CKPT, DB_CRASH_STATUS_PERSIST_CKPT_FLUSH_REDO);
    ret = CkptFlushRedo(seInsPtr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Checkpoint flush redo.");
        return ret;
    }

    CRASHPOINT(DB_CRASH_EVENT_PERSIST_CKPT, DB_CRASH_STATUS_PERSIST_CKPT_FLUSH_FULL);
    ret = CkptFlush(seInsPtr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Checkpoint flush item.");
        return ret;
    }
    return ret;
}

static StatusInter CkptUpdateFileDigest(SeInstanceT *seInsPtr)
{
    if (!seInsPtr->seConfig.shaCheckEnable) {
        return STATUS_OK_INTER;
    }
    // 用户主动触发时才会为True
    if (!seInsPtr->ckptCtx->updateFileDigest) {
        return STATUS_OK_INTER;
    }

    // 记录此时的 redo truncate point, 如果后续再落脏页，文件摘要将无法校验文件一致性
    UpdateFileDigestPoint(seInsPtr);
    StatusInter ret = DbGetStatusInterErrno(UpdateDataFileDigest(seInsPtr));
    if (ret != STATUS_OK_INTER) {
        seInsPtr->ckptCtx->updateFileDigest = false;
        SE_ERROR(ret, "update dataFile digest in ckpt.");
        return ret;
    }
    ret = DbGetStatusInterErrno(UpdateCtrlFileDigest(seInsPtr, true));
    seInsPtr->ckptCtx->updateFileDigest = false;
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "update ctrlFile digest in ckpt.");
        return ret;
    }
    return STATUS_OK_INTER;
}

StatusInter CkptUpdateCoreRedoPoint(SeInstanceT *seIns)
{
    DB_POINTER(seIns);
    DbRWLatchW(&seIns->db->coreLock);
    StCoreCtrlT *core = &seIns->db->core;
    RedoPointT backupTruncPoint = core->truncPoint;
    core->truncPoint = seIns->ckptCtx->truncPoint;
    RedoPointT backupLrpPoint = core->lrpPoint;
    core->lrpPoint = seIns->ckptCtx->lrpPoint;
    uint64_t backupLsn = core->lsn;
    core->lsn = DbAtomicGet64(&seIns->redoMgr->redoBuf.redoPubBuf->curLsn);
    StatusInter ret = DbSaveCore(seIns);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "save core redo points");
        core->truncPoint = backupTruncPoint;
        core->lrpPoint = backupLrpPoint;
        core->lsn = backupLsn;
    }
    DbRWUnlatchW(&seIns->db->coreLock);
    return ret;
}

StatusInter CkptPerform(SeInstanceT *seInsPtr)
{
    DB_POINTER(seInsPtr);
    if (seInsPtr->ckptCtx->pubCtx->queue.count != 0) {
        DB_LOG_INFO("Ckpt begin, queue count:%" PRIu32, seInsPtr->ckptCtx->pubCtx->queue.count);
    }
    uint64_t startTime = DbRdtsc();
    StatusInter ret = CkptFlushWithRedo(seInsPtr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Ckpt flush redo");
        return ret;
    }
    RedoLogRecycle(seInsPtr->redoMgr, &seInsPtr->ckptCtx->truncPoint);

    CRASHPOINT(DB_CRASH_EVENT_PERSIST_CKPT, DB_CRASH_STATUS_PERSIST_CKPT_SAVE_CTRL);
    ret = CkptUpdateCoreRedoPoint(seInsPtr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Ckpt save control");
        return ret;
    }

    ret = ResetSafeFile(seInsPtr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Ckpt reset safe file");
        return ret;
    }

    seInsPtr->ckptCtx->incrSeqId++;
    ret = CkptUpdateFileDigest(seInsPtr);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Ckpt update file digest.");
        return ret;
    }
    DB_LOG_INFO("Checkpoint end, took: %" PRIu64 " ms, count: %" PRIu32 ", truncpoint(" REDO_POINT_LOG_FMT
                "), lrp(" REDO_POINT_LOG_FMT ")",
        DbToMseconds(DbRdtsc() - startTime), seInsPtr->ckptCtx->pubCtx->queue.count,
        REDO_POINT(&seInsPtr->db->core.truncPoint), REDO_POINT(&seInsPtr->db->core.lrpPoint));
    return STATUS_OK_INTER;
}

static bool CheckNeedFlush(CkptCtxT *ckptCtx)
{
    uint64_t currTime = DbToMseconds(DbRdtsc());
    // 1.是否到了周期
    if ((currTime - ckptCtx->flushEndTime) >= (ckptCtx->period * MSECONDS_IN_SECOND)) {
        return true;
    }

    // 2.脏页队列是否到了阈值
    if (ckptCtx->pubCtx->queue.count >= ckptCtx->threshold) {
        return true;
    }

    // 4.是否有内部触发刷盘（接口方式触发）
    if ((ckptCtx->trigger != CKPT_MODE_NONE)) {
        return true;
    }

    return false;
}

static void CkptProcAfterPerform(CkptCtxT *ckptCtx)
{
    ckptCtx->flushEndTime = DbToMseconds(DbRdtsc());
    ckptCtx->pubCtx->queue.batchEnd = NULL;
    ckptCtx->fullSeqId++;
    ckptCtx->trigger = CKPT_MODE_NONE;
}

void *CkptProc(void *param)
{
    DB_POINTER(param);
    SeInstanceT *seInsPtr = (SeInstanceT *)param;
    CkptCtxT *ckptCtx = seInsPtr->ckptCtx;
    ckptCtx->flushEndTime = DbToMseconds(DbRdtsc());
    ckptCtx->errPerformRet = STATUS_OK_INTER;
    uint8_t errTryCnt = 0;
    DbSetServerThreadFlag();
    DB_LOG_INFO("Checkpoint thread start.");
    while (ckptCtx->isRunning) {
        if (!SeInsMagicNumIsValid(seInsPtr)) {
            SeSetStorageEmergency(seInsPtr, "se instance magic num unexpect.");
        }
        DbSpinLock(&ckptCtx->pubCtx->lock);
        if (!ckptCtx->enable) {
            DbSpinUnlock(&ckptCtx->pubCtx->lock);
            DbSleep(CKPT_PROC_INTERVAL);
            continue;
        }
        if (CheckNeedFlush(ckptCtx) == false) {
            DbSpinUnlock(&ckptCtx->pubCtx->lock);
            DbSleep(CKPT_PROC_INTERVAL);
            continue;
        }
        if (ckptCtx->pubCtx->queue.batchEnd == NULL) {
            ckptCtx->pubCtx->queue.batchEnd = GetBufDescAddr(ckptCtx->pubCtx->queue.last);
        }
        StatusInter performRet = CkptPerform(seInsPtr);
        // 增加3次重试机制，避免权限不足或者其他异常刷盘失败时候，陷入死循环
        if (SECUREC_UNLIKELY(performRet != STATUS_OK_INTER && errTryCnt < (uint8_t)CKPT_ERR_RETRY_PERFORM_TIMES)) {
            SE_LAST_ERROR(performRet, "perform ckpt, try cnt:%" PRIu32 "", errTryCnt);
            DbSpinUnlock(&ckptCtx->pubCtx->lock);
            errTryCnt++;
            DbSleep(CKPT_PROC_INTERVAL);  //  此次落盘未成功，重试
            continue;
        }
        errTryCnt = 0;
        ckptCtx->errPerformRet = performRet;

        if ((ckptCtx->trigger == CKPT_MODE_FULL || ckptCtx->trigger == CKPT_MODE_BOOT_FULL) &&
            ckptCtx->pubCtx->queue.batchEnd != NULL) {
            DbSpinUnlock(&ckptCtx->pubCtx->lock);
            DbSleep(CKPT_PROC_INTERVAL);
            continue;
        }

        CkptProcAfterPerform(ckptCtx);
        DbSpinUnlock(&ckptCtx->pubCtx->lock);
    }
    DbClearServerThreadFlag();
    DB_LOG_INFO("Checkpoint thread end.");
    return NULL;
}

static bool IsContinueKeepAlive(const CkptCtxT *ckptCtx, uint32_t *count, CkptModeT mode)
{
    // 每1ms检查一次
    DbInstanceHdT dbInstance = ckptCtx->seIns->dbInstance;
    DbSleep(CKPT_WAIT_PERIOD);
    if (ckptCtx->errPerformRet == OUT_OF_MEMORY_INTER) {
        // when ckpt process out of memory, consider try multiple times then exit.
        SE_ERROR(OUT_OF_MEMORY_INTER, "ckpt trigger, mode=%" PRIu32 ",count=%" PRIu32, (uint32_t)mode, *count);
        return false;
    }
    // 当前时序会预留redo和安全文件，所有场景会预留space文件；所以没锁库且错误码为磁盘不足的场景只有安全文件扩展（非时序）
    if (ckptCtx->errPerformRet == FILE_OPERATE_FAILED_INTER || ckptCtx->errPerformRet == DISK_NO_SPACE_ERROR_INTER) {
        SeSetStorageEmergency(ckptCtx->seIns, "trigger ckpt emergency");
        return false;
    }
    // 每1s喂一次狗
    // 如果是boot线程，不必喂狗
    (*count)++;
    if ((*count) >= CKPT_DEFAULT_SPILT_TIME && mode != CKPT_MODE_BOOT_FULL) {
        (void)DrtKeepThisWorkerAlive(dbInstance);
        (*count) = 0;
    }
    return true;
}

static StatusInter CkptWaitFlushFinish(const CkptCtxT *ckptCtx, uint32_t ckptId, CkptModeT mode, uint32_t timeoutMs)
{
    uint32_t count = 0;
    SeInstanceT *seIns = ckptCtx->seIns;
    uint64_t startTime = DbClockGetTsc();
    if (mode >= CKPT_MODE_FULL) {
        while (ckptCtx->trigger >= CKPT_MODE_FULL && ckptId == ckptCtx->fullSeqId) {
            if (SECUREC_UNLIKELY(SeGetStorageStatus(seIns) == SE_ON_DISK_EMRGNCY)) {
                SE_ERROR(DATABASE_NOT_AVAILABLE_INTER, "db is not available, while wait ckpt finish in full");
                return DATABASE_NOT_AVAILABLE_INTER;
            }
            if (!IsContinueKeepAlive(ckptCtx, &count, mode)) {
                return ckptCtx->errPerformRet;
            }
            if (timeoutMs == 0 || !DbExceedTime(startTime, timeoutMs)) {  // 0表示一直等待
                continue;
            }
            // 等待超时
            SE_LAST_ERROR(INT_ERR_WAIT_CKPT_TIMEOUT,
                "wait ckpt flush finish unsucc. timeoutMs = %" PRIu32 ", usedTimeMs = %" PRIu64, timeoutMs,
                DbToMseconds(DbClockGetTsc() - startTime));
            return INT_ERR_WAIT_CKPT_TIMEOUT;
        }
    } else {
        while (ckptCtx->trigger != CKPT_MODE_NONE && ckptId == ckptCtx->incrSeqId) {
            if (SECUREC_UNLIKELY(SeGetStorageStatus(seIns) == SE_ON_DISK_EMRGNCY)) {
                // db is not available, while wait ckpt finish in increment persist.
                SE_ERROR(DATABASE_NOT_AVAILABLE_INTER, "db unavailable to wait finish.");
                return DATABASE_NOT_AVAILABLE_INTER;
            }
            if (!IsContinueKeepAlive(ckptCtx, &count, mode)) {
                return ckptCtx->errPerformRet;
            }
        }
    }

    return ckptCtx->errPerformRet;
}

static StatusInter CkptProcess4MiniKv(SeInstanceT *seInsPtr, CkptModeT mode, uint32_t timeoutMs)
{
    if (!seInsPtr->ckptCtx->enable) {
        return STATUS_OK_INTER;
    }
    uint64_t startTime = DbGettimeMonotonicMsec();
    uint64_t processTimeout = (uint64_t)timeoutMs;
    CkptCtxT *ckptCtx = seInsPtr->ckptCtx;

    bool isFlushOnce = (timeoutMs == DB_INVALID_UINT32 || (mode != CKPT_MODE_FULL && mode != CKPT_MODE_BOOT_FULL));

    DbSpinLock(&ckptCtx->pubCtx->lock);
    while (DbGettimeMonotonicMsec() - startTime < processTimeout) {
        StatusInter ret = CkptPerform(seInsPtr);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "|CkptProcess4MiniKv| CkptPerform go wrong.");
            DbSpinUnlock(&ckptCtx->pubCtx->lock);
            return ret;
        }
        if (isFlushOnce || ckptCtx->pubCtx->queue.count == 0) {
            break;
        }
    }
    ckptCtx->fullSeqId++;
    DbSpinUnlock(&ckptCtx->pubCtx->lock);
    return STATUS_OK_INTER;
}

StatusInter CkptTriggerImpl(SeInstanceT *seInsPtr, CkptModeT mode, bool wait, uint32_t timeoutMs)
{
    DB_POINTER(seInsPtr);
    StatusInter ret = STATUS_OK_INTER;
    if (mode == CKPT_MODE_NONE) {
        return ret;
    }
    if (seInsPtr->ckptCtx->isFlushOnMiniDemand) {
        ret = CkptProcess4MiniKv(seInsPtr, mode, timeoutMs);
    } else {
        CkptCtxT *ckptCtx = seInsPtr->ckptCtx;
        RedoPointT targetTruncPoint = ckptCtx->truncPoint;
        DbSpinLock(&ckptCtx->pubCtx->lock);
        uint32_t ckptId = mode == CKPT_MODE_INC ? ckptCtx->incrSeqId : ckptCtx->fullSeqId;
        ckptCtx->trigger = ckptCtx->trigger >= CKPT_MODE_FULL ? ckptCtx->trigger : mode;
        if (mode == CKPT_MODE_FULL) {
            ckptCtx->pubCtx->queue.batchEnd = GetBufDescAddr(ckptCtx->pubCtx->queue.last);
            if (ckptCtx->pubCtx->queue.batchEnd != NULL) {
                targetTruncPoint = ckptCtx->pubCtx->queue.batchEnd->truncPoint;
            }
        }
        ckptCtx->stat.triggerCount++;
        DbSpinUnlock(&ckptCtx->pubCtx->lock);

        if (wait) {
            ret = CkptWaitFlushFinish(ckptCtx, ckptId, mode, timeoutMs);
            if (ret == OUT_OF_MEMORY_INTER) {
                SE_ERROR(ret, "but Ckpt-proc trigger ok.");
                return ret;
            }
            if (ret == STATUS_OK_INTER && mode == CKPT_MODE_FULL) {
                DbSpinLock(&ckptCtx->pubCtx->lock);
                if (RedoPointCmp(&targetTruncPoint, &ckptCtx->truncPoint) > 0) {
                    SE_ERROR(INTERNAL_ERROR_INTER,
                        "target trunc point " REDO_POINT_LOG_FMT " - " REDO_POINT_LOG_FMT " unreached!",
                        REDO_POINT(&ckptCtx->truncPoint), REDO_POINT(&targetTruncPoint));
                    DB_ASSERT(false);
                    DbSpinUnlock(&ckptCtx->pubCtx->lock);
                    return INTERNAL_ERROR_INTER;
                }
                DbSpinUnlock(&ckptCtx->pubCtx->lock);
            }
        }
    }
    return ret;
}

StatusInter CkptTriggerFileErase(SeInstanceT *seIns)
{
    CkptCtxT *ckptCtx = seIns->ckptCtx;
    RedoPointT targetPoint = RedoGenInitPoint();
    StatusInter ret = RedoLogFlush(seIns->redoMgr, &targetPoint);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "File erase flush redo.");
        return ret;
    }
    ret = RedoLogFileSync(seIns->redoMgr, true);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "File erase sync redo.");
        return ret;
    }
    uint32_t count = 0;
    DbRWLatchR(&seIns->db->coreLock);
    while (RedoPointCmp(&targetPoint, &seIns->db->core.truncPoint) > 0) {
        DbRWUnlatchR(&seIns->db->coreLock);
        if (SECUREC_UNLIKELY(SeGetStorageStatus(seIns) == SE_ON_DISK_EMRGNCY)) {
            // db is not available, while wait ckpt finish in increment persist.
            SE_ERROR(DATABASE_NOT_AVAILABLE_INTER, "db unavailable to wait finish.");
            return DATABASE_NOT_AVAILABLE_INTER;
        }
        if (!IsContinueKeepAlive(ckptCtx, &count, CKPT_MODE_FULL)) {
            return ckptCtx->errPerformRet;
        }
        ckptCtx->trigger = CKPT_MODE_FULL;
        DbRWLatchR(&seIns->db->coreLock);
    }
    DbRWUnlatchR(&seIns->db->coreLock);
    return ckptCtx->errPerformRet;
}

void CkptRefreshPoint(SeInstanceT *seIns)
{
    DB_POINTER(seIns);

    RedoPointT curPoint = RedoGetCurrPoint(seIns->redoMgr);
    seIns->ckptCtx->lrpPoint = curPoint;
    seIns->ckptCtx->truncPoint = curPoint;

    StCoreCtrlT *coreCtrl = &seIns->db->core;
    coreCtrl->truncPoint = curPoint;
    coreCtrl->lrpPoint = curPoint;
    coreCtrl->lsn = DbAtomicGet64(&seIns->redoMgr->redoBuf.redoPubBuf->curLsn);
}

void CkptReset(SeInstanceT *seIns)
{
    DB_POINTER(seIns);

    BufDescT *desc = NULL;
    while (seIns->ckptCtx->pubCtx->queue.count > 0) {
        desc = CkptDeQueue(seIns);
        DB_ASSERT(desc != NULL);
        desc->isDirty = false;
        desc->isReadOnly = false;
    }
    seIns->ckptCtx->pubCtx->queue.batchEnd = NULL;
}

void CkptUnLoad(SeInstanceT *seIns)
{
    CkptSetEnable(seIns, false);
    seIns->ckptCtx->errPerformRet = STATUS_OK_INTER;
}

void CkptLoad(SeInstanceT *seIns)
{
    if (seIns->ckptCtx == NULL || seIns->ckptCtx->pubCtx == NULL) {
        return;
    }
    // 重新加载时才清理，上锁保证等待ckptPerform结束
    CkptCtxT *ckptCtx = seIns->ckptCtx;
    DbSpinLock(&ckptCtx->pubCtx->lock);
    ckptCtx->truncPoint = seIns->db->core.truncPoint;
    ckptCtx->lrpPoint = seIns->db->core.lrpPoint;
    // 清理checkpoint GroupBuffer
    CkptClearGroup(seIns);
    // 清理checkpoint queue脏页队列
    ckptCtx->pubCtx->queue.count = 0;
    ckptCtx->pubCtx->queue.first = DB_INVALID_SHMPTR;
    ckptCtx->pubCtx->queue.last = DB_INVALID_SHMPTR;
    ckptCtx->enable = true;
    DbSpinUnlock(&ckptCtx->pubCtx->lock);
}

void CkptFreeResource(SeInstanceT *seIns)
{
    if (seIns->ckptCtx->group.buf != NULL) {
        DbDynMemCtxFree(seIns->seServerMemCtx, seIns->ckptCtx->group.buf);
        seIns->ckptCtx->group.buf = NULL;
    }
    seIns->ckptCtx->group.count = 0;
}

// 终端多进程场景，有进程异常退出，其他进程做恢复时，ckpt队列数据可能是不准确的，需要丢弃ckpt数据
void CkptDiscard(SeInstanceT *seIns)
{
    CkptCtxT *ckptCtx = seIns->ckptCtx;
    DbSpinLock(&ckptCtx->pubCtx->lock);
    CkptFreeResource(seIns);
    ckptCtx->pubCtx->queue.count = 0;
    ckptCtx->pubCtx->queue.first = DB_INVALID_SHMPTR;
    ckptCtx->pubCtx->queue.last = DB_INVALID_SHMPTR;
    DbSpinUnlock(&ckptCtx->pubCtx->lock);
}

void CkptSetEnable(SeInstanceT *seIns, bool enable)
{
    DB_POINTER(seIns);
    if (seIns->ckptCtx == NULL || seIns->ckptCtx->pubCtx == NULL) {
        return;
    }
    if (!seIns->ckptCtx->enable && enable) {
        // 只有从关闭状态变为开启状态时，才需要重置ckpt，避免checkpoint线程和当前线程并发操作ckpt内容
        CkptReset(seIns);
    }

    seIns->ckptCtx->enable = enable;
}

void CkptEnableDigestUpdateImpl(SeInstanceT *seInsPtr)
{
    seInsPtr->ckptCtx->updateFileDigest = true;
}

Status CkptBindCpuSetImpl(SeInstanceT *seInsPtr, void *cpuSet)
{
    DB_POINTER3(seInsPtr, seInsPtr->ckptCtx, cpuSet);
    return DbThreadBindCpuSet(seInsPtr->ckptCtx->thread, cpuSet);
}

SO_EXPORT_FOR_TS void CkptAmInit(void)
{
    CkptAmFuncT ckptAmFunc = {
        CkptTriggerImpl, CkptInitImpl, CkptDestroyImpl, CkptEnableDigestUpdateImpl, CkptBindCpuSetImpl};
    CkptSetAmFunc(&ckptAmFunc);
}
