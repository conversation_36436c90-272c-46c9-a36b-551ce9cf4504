/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: se_recovery.c
 * Description: 恢复模块实现
 */
#include "se_recovery_inner.h"
#include "se_database.h"
#include "se_log.h"
#include "se_redo_inner.h"
#include "se_undo.h"
#include "se_trx_mgr.h"
#include "se_resource_session_pub.h"
#include "se_redo_file.h"
#include "db_dyn_load.h"
#include "ee_systbl.h"
#include "dm_meta_mini_basic.h"
#include "ee_init.h"
#include "se_lfsmgr_redo_inner.h"
#include "se_persist_inner.h"
#include "se_persist.h"
#include "se_persistence_translate_impl.h"
#include "se_buffer_pool.h"  // BufpoolEmptyAllPagesInMem ts
#include "se_space_inner.h"

#define DB_COPY_CMD_RESERVE_LENGTH 10

StatusInter RecoveryInit(SeInstanceT *seIns)
{
    DB_POINTER2(seIns, seIns->redoMgr);
    RedoMgrT *redoMgr = seIns->redoMgr;
    redoMgr->pubCtx->curPoint = seIns->db->core.truncPoint;
    redoMgr->redoBuf.redoPubBuf->curLsn = seIns->db->core.lsn;
    return STATUS_OK_INTER;
}

StatusInter RecoveryRedo(RedoMgrT *redoMgr, const RedoPointT *startPoint, const RedoPointT *lrpPoint)
{
    DB_POINTER3(redoMgr, startPoint, lrpPoint);
    RedoCursorT *cursor = NULL;
    StatusInter ret = RedoScanBegin(redoMgr, startPoint, &cursor);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Unable to begin Redo Scan." REDO_POINT_LOG_FMT "," REDO_POINT_LOG_FMT ".",
            REDO_POINT(startPoint), REDO_POINT(lrpPoint));
        return ret;
    }
    ret = RedoScanNext(cursor);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Unable to Redo Scan Next." REDO_POINT_LOG_FMT "," REDO_POINT_LOG_FMT "," REDO_POINT_LOG_FMT ".",
            REDO_POINT(startPoint), REDO_POINT(lrpPoint), REDO_POINT(&cursor->point));
        RedoScanClose(cursor);
        return ret;
    }
    while (cursor->hasData) {
        ret = RedoLogReplay(cursor);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "Unable to Redo Reply." REDO_POINT_LOG_FMT "," REDO_POINT_LOG_FMT "," REDO_POINT_LOG_FMT ".",
                REDO_POINT(startPoint), REDO_POINT(lrpPoint), REDO_POINT(&cursor->point));
            break;
        }
        ret = RedoScanNext(cursor);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret,
                "Unable to redo scan next. " REDO_POINT_LOG_FMT "," REDO_POINT_LOG_FMT "," REDO_POINT_LOG_FMT ".",
                REDO_POINT(startPoint), REDO_POINT(lrpPoint), REDO_POINT(&cursor->point));
            break;
        }
    }
    RedoScanClose(cursor);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    if (RedoPointCmp(&(redoMgr->pubCtx->curPoint), lrpPoint) < 0) {
        ret = INT_ERR_REDO_POINT_INVALID;
        SE_LAST_ERROR(ret, "novalid Recovery Point. lrp " REDO_POINT_LOG_FMT ", redo " REDO_POINT_LOG_FMT ".",
            REDO_POINT(lrpPoint), REDO_POINT(&redoMgr->pubCtx->curPoint));
    } else {
        redoMgr->redoBuf.redoPubBuf->curBatchId = redoMgr->pubCtx->curPoint.batchId;
    }
    return ret;
}

StatusInter RecoverySeOpen(SeInstanceT *seIns, SeRunCtxT **seRunCtx)
{
    Status ret = SeOpen(seIns->instanceId, (DbMemCtxT *)seIns->seServerMemCtx, NULL, seRunCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "recovery se open.");
        return DbGetStatusInterErrno(ret);
    }
    if (!DbDynLoadHasFeature(COMPONENT_MINIKV)) {
        ret = SeOpenResSession(*seRunCtx);
        if (ret != GMERR_OK) {
            (void)SeClose(*seRunCtx);
            DB_LOG_ERROR(ret, "open se session recovery.");
        }
    }
    return DbGetStatusInterErrno(ret);
}

StatusInter RecoveryReservedMemRedo(RedoMgrT *redoMgr)
{
    DB_POINTER(redoMgr);
    return RedoReservedMemLogReplay(redoMgr);
}

static StatusInter DbRecoveryUndo(SeInstanceT *seIns)
{
    SeRunCtxT *seRunCtx;
    StatusInter ret = RecoverySeOpen(seIns, &seRunCtx);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    SeTransSetRecoveryFlag(seRunCtx);
    SeSetCurRedoCtx(seRunCtx->redoCtx);
    // 只有重启或者swap时才会走到
    SeMarkSwapCtx(seRunCtx);
    ret = UndoRecoverSpaceStat(seRunCtx);
    if (ret != STATUS_OK_INTER) {
        (void)SeClose(seRunCtx);
        return ret;
    }
    ret = RecoveryUndo(seRunCtx);
    TrxHandleContainerCtxList(seRunCtx->trx, TRX_CLEAR_HANDLE);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "recovery undo");
        (void)SeClose(seRunCtx);
        return ret;
    }

    if (SeGetPersistMode() == PERSIST_INCREMENT) {
        // 增量持久化要在datafile prepare之后校验，按需在之前流程校验了，此时-r路径下的持久化文件已读完,关闭预留分区句柄
        ret = SeCheckDiskIntegrity(seRunCtx);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "check data file");
            (void)SeClose(seRunCtx);
            return ret;
        }
        ret = SeTakeZoneOffline(seIns, DB_RESERVE_HANDLE_INDEX);
        if (ret != STATUS_OK_INTER) {
            (void)SeClose(seRunCtx);
            return ret;
        }
    }

    return DbGetStatusInterErrno(SeClose(seRunCtx));
}

static StatusInter DbRecoveryAfterRedoRecovery(SeInstanceT *seIns, RecoveryCtxT *recoveryCtx)
{
    uint32_t uuid;
    StatusInter ret = DbGetStatusInterErrno(SeLoadUuid(&uuid, seIns->instanceId));
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "load uuid");
        return ret;
    }
    recoveryCtx->cataSetUuidFunc(uuid, seIns->dbInstance);
    ret = SeRecoverTrxIds(seIns);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "recovery redo trxId");
        return ret;
    }
    ret = DbGetStatusInterErrno(SysTableStart(seIns->dbInstance));
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "catalog systable init");
        return ret;
    }
    ret = DbGetStatusInterErrno(MiniCatalogStbInit(seIns->dbInstance, false));
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "mini catalog systable init!");
        return ret;
    }
    return DbRecoveryUndo(seIns);
}

StatusInter DbRecoveryExec(SeInstanceT *seIns, RecoveryCtxT *recoveryCtx, RedoPointT *truncPoint, RedoPointT *lrpPoint)
{
    RedoRunCtxT *redoCtx = NULL;
    StatusInter ret = RedoCtxCreate(seIns->redoMgr, &redoCtx);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "recovery:create RedoRunCtx");
        return ret;
    }
    SeSetCurRedoCtx(redoCtx);

    ret = RecoveryRedo(seIns->redoMgr, truncPoint, lrpPoint);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "recovery:redo");
        RedoLogFailCleanDirtyPage(seIns);
        RedoCtxRelease(redoCtx);
        return ret;
    }
    if (seIns->redoMgr->useReservedMemRecovery) {
        ret = RecoveryReservedMemRedo(seIns->redoMgr);
        if (ret != STATUS_OK_INTER) {
            SE_ERROR(ret, "recovery reservedMem redo");
            RedoLogFailCleanDirtyPage(seIns);
            RedoCtxRelease(redoCtx);
            return ret;
        }
    }
    RedoCtxRelease(redoCtx);
    RedoSetEnableImpl(seIns->redoMgr, true);

    // 将脏页全量刷盘
    ret = DbGetStatusInterErrno(CkptTrigger(seIns, CKPT_MODE_FULL, true, WAIT_MSECONDS_IN_REDO_LOG));
    if (ret != STATUS_OK_INTER) {
        DB_LOG_ERROR(ret, "Trigger ckpt after redo");
        return ret;
    }
    // 所有 redo 日志已经重演完毕，redo文件可以全部重置，避免启动过程中扩展redo文件磁盘不足，导致重启失败
    ret = RedoLogFilesReset(&seIns->redoMgr->redoFiles);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "reset redo files");
        return ret;
    }

    // redo 回放后数据库的数据应该是一致性的状态
    ret = SpaceMount(seIns);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    return DbRecoveryAfterRedoRecovery(seIns, recoveryCtx);
}

static StatusInter RecycleAutoIncId(SeInstanceT *seIns, VertexLabelAutoIncIdRecycleFunc autoIncIdRecycleFunc)
{
    SeRunCtxT *seRunCtx;
    StatusInter retInter = RecoverySeOpen(seIns, &seRunCtx);
    if (retInter != STATUS_OK_INTER) {
        return retInter;
    }

    Status ret = autoIncIdRecycleFunc(seRunCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "autoInc id reset!");
        (void)SeClose(seRunCtx);
        return DbGetStatusInterErrno(ret);
    }
    return DbGetStatusInterErrno(SeClose(seRunCtx));
}

static StatusInter CopyMainDirectoryToDstZone(const char *srcDataFilePath, const char *dstDataFilePath)
{
    if (SeIsSamePath(srcDataFilePath, dstDataFilePath)) {
        return STATUS_OK_INTER;
    }
    char cmdCopyFiles[DB_MAX_PATH + DB_MAX_PATH + DB_COPY_CMD_RESERVE_LENGTH];
    uint32_t cmdMaxLength = DB_MAX_PATH + DB_MAX_PATH + DB_COPY_CMD_RESERVE_LENGTH;
    errno_t err =
        snprintf_s(cmdCopyFiles, cmdMaxLength, cmdMaxLength - 1, "cp -rf %s/* %s", srcDataFilePath, dstDataFilePath);
    if (err < 0) {
        SE_LAST_ERROR(INTERNAL_ERROR_INTER, "generate copy cmd from: %s* to: %s.", srcDataFilePath, dstDataFilePath);
        return INTERNAL_ERROR_INTER;
    }
    StatusInter status = system(cmdCopyFiles);
    if (status != STATUS_OK_INTER) {
        SE_LAST_ERROR(
            INTERNAL_ERROR_INTER, "syn files from mainZone: %s to multiZone:%s.", srcDataFilePath, dstDataFilePath);
        return INTERNAL_ERROR_INTER;
    }
    return STATUS_OK_INTER;
}

static StatusInter CopyMainZoneToMultiZone(SeInstanceT *seIns, bool isRcvryPathEmpty)
{
    StatusInter ret = STATUS_OK_INTER;
    uint32_t mainZoneId = SeGetMainZoneId(seIns);
    char *srcPath = (isRcvryPathEmpty) ? seIns->seConfig.dataFileDirPath[mainZoneId] : seIns->seConfig.recoveryPath;
    for (uint32_t i = 0; i < seIns->seConfig.multiZoneNum; i++) {
        // only if using -r specific path, all the multizones need to syn.
        if ((i != mainZoneId) || !isRcvryPathEmpty) {
            ret = CopyMainDirectoryToDstZone(srcPath, seIns->seConfig.dataFileDirPath[i]);
            if (ret != STATUS_OK_INTER) {
                return ret;
            }
        }
    }

    return ret;
}

StatusInter MultiZoneSynRecoveryData(SeInstanceT *seIns)
{
    bool isRcvryPathEmpty = SeIsStringEmpty(seIns->seConfig.recoveryPath);
    if (seIns->storageType == SE_BUFFER_POOL && !isRcvryPathEmpty) {
        // copy all the files from specific data to main zone.
        StatusInter ret = CopyMainZoneToMultiZone(seIns, isRcvryPathEmpty);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
    }
    return STATUS_OK_INTER;
}

static StatusInter RecoveryForIncrementMode(SeInstanceT *seIns, RecoveryCtxT *recoveryCtx)
{
    DbRecoverySetState(seIns, RECOVERY_DOING);
    DB_BOOT_LOG_INFO("recovery begin!");

    RedoPointT *truncPoint = &(seIns->db->core.truncPoint);
    RedoPointT *lrpPoint = &(seIns->db->core.lrpPoint);
    StatusInter ret = RecoveryInit(seIns);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Recovery init.");
        return ret;
    }
    ret = DbRecoveryExec(seIns, recoveryCtx, truncPoint, lrpPoint);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Recovery exec.");
        return ret;
    }

    // 原地升级结束。后边流程使用到的数据都是最新版本的数据
    SePersistenceTranslateSetToEnd(seIns);
    DB_LOG_INFO("ready to rebuild index");
    ret = recoveryCtx->idxRebuildFunc(seIns->instanceId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Recovery build index.");
        return ret;
    }
    SeSetNormalShutDown(seIns, false);
    ret = SeSpaceFlushAllZone(seIns);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Recovery flush zone.");
        return ret;
    }
    DbRecoverySetState(seIns, RECOVERY_DONE);
    DB_BOOT_LOG_INFO("recovery done!");
    return ret;
}

static StatusInter RecoveryUpdateLabels(
    SeInstanceT *seIns, DbOamapT *metaDiffMap, RecoverUpdateLabelsFunc recoverUpgradeLabelsFunc)
{
    StorageStatusE status = SeGetStorageStatus(seIns);
    (void)SeSetStorageStatus(seIns, SE_ON_DISK_UPGRADE);
    Status ret = recoverUpgradeLabelsFunc(seIns->dbInstance, metaDiffMap);
    (void)SeSetStorageStatus(seIns, status);
    if (ret != GMERR_OK) {
        return DbGetStatusInterErrno(ret);
    }
    return STATUS_OK_INTER;
}

static StatusInter RecoveryUpdateLeafListLabels(
    SeInstanceT *seIns, DbOamapT *metaDiffMap, RecoverUpdateLeafListLabelsFunc recoverUpdateLeafListLabelsFunc)
{
    if (metaDiffMap == NULL || DbOamapUsedSize(metaDiffMap) == 0) {
        return STATUS_OK_INTER;
    }

    StorageStatusE status = SeGetStorageStatus(seIns);
    (void)SeSetStorageStatus(seIns, SE_ON_DISK_UPGRADE);
    Status ret = recoverUpdateLeafListLabelsFunc(metaDiffMap);
    (void)SeSetStorageStatus(seIns, status);
    if (ret != GMERR_OK) {
        return DbGetStatusInterErrno(ret);
    }
    return STATUS_OK_INTER;
}

static StatusInter RebuildPlanCache(SeInstanceT *seIns, PlanCacheRebuildFunc planCacheRebuildFunc)
{
    for (uint32_t i = 0; i < SE_NAMESPACE_MAX; i++) {
        if (seIns->db->core.yangModelCheckNspIds[i] != 0) {
            Status ret = planCacheRebuildFunc(seIns->db->core.yangModelCheckNspIds[i]);
            if (ret != GMERR_OK) {
                return DbGetStatusInterErrno(ret);
            }
        }
    }
    return STATUS_OK_INTER;
}

static StatusInter RecoveryForOnDemandMode(SeInstanceT *seIns, RecoveryCtxT *recoveryCtx)
{
    // redo 回放后数据库的数据应该是一致性的状态，恢复SpaceT中usedSize，allocSize字段
    StatusInter ret = SpaceMount(seIns);
    if (ret != STATUS_OK_INTER) {
        return ret;
    }

    uint32_t uuid;
    ret = DbGetStatusInterErrno(SeLoadUuid(&uuid, seIns->instanceId));
    if (ret != STATUS_OK_INTER) {
        return ret;
    }
    recoveryCtx->cataSetUuidFunc(uuid, seIns->dbInstance);
    ret = SeRecoverTrxIds(seIns);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "recovery demand trxId");
        return ret;
    }
    ret = DbGetStatusInterErrno(SysTableStart(seIns->dbInstance));
    if (ret != STATUS_OK_INTER) {
        DB_LOG_ERROR(ret, "catalog systable init!");
        return ret;
    }
    ret = DbRecoveryUndo(seIns);
    if (ret != STATUS_OK_INTER) {
        DB_LOG_ERROR(ret, "recovery undo init!");
        return ret;
    }

    ret = RecoveryUpdateLabels(seIns, recoveryCtx->metaDiffMap, recoveryCtx->recoverUpgradeLabels);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "Recovery update labels!");
        return ret;
    }

    // 原地升级结束。后边流程使用到的数据都是最新版本的数据
    SePersistenceTranslateSetToEnd(seIns);
    ret = RebuildPlanCache(seIns, recoveryCtx->planCacheRebuildFunc);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "rebuild plan cache!");
        return ret;
    }

    ret = recoveryCtx->idxRebuildFunc(seIns->instanceId);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "rebuild index!");
        return ret;
    }

    // 重建索引之后进行自增ID的恢复
    ret = RecycleAutoIncId(seIns, recoveryCtx->autoIncIdRecycleFunc);
    if (ret != STATUS_OK_INTER) {
        SE_ERROR(ret, "recycle autoIncId!");
        return ret;
    }

    // 数据恢复完成，刷新leaflist，表id，cataget
    // 全表扫父亲节点，读pid
    return RecoveryUpdateLeafListLabels(seIns, recoveryCtx->metaDiffMap, recoveryCtx->recoverUpdateLeafListLabels);
}

SO_EXPORT_FOR_TS StatusInter DbRecoveryImpl(RecoveryCtxT *recoveryCtx, SeInstanceHdT seIns)
{
    if (SeGetStorageStatus(seIns) == SE_ON_DISK_CREATE) {
        return STATUS_OK_INTER;
    }

    StatusInter ret = STATUS_OK_INTER;
    SE_WARN(0, "recover begin!");
    if (SeGetPersistMode() == PERSIST_INCREMENT) {
        ret = RecoveryForIncrementMode(seIns, recoveryCtx);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
        // 同步副分区的不存在的redo文件
        ret = RedoSynchronizeOtherZone(seIns->redoMgr);
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
#ifdef FEATURE_TS
        // empty all in-memory pages can reduce memory consumption when recovery.
        ret = DbGetStatusInterErrno(BufpoolEmptyAllPagesInMem(seIns));
        if (ret != STATUS_OK_INTER) {
            return ret;
        }
#endif
    } else {
        DB_ASSERT(SeGetPersistMode() == PERSIST_ON_DEMAND);
        ret = RecoveryForOnDemandMode(seIns, recoveryCtx);
    }
    SE_WARN(0, "recover end!");
    return ret;
}
