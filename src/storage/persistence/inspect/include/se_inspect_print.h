/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: inspect打印时候工具函数
 * Author: yangyongji
 * Create: 2024-07-13
 */

#ifndef SE_INSPECT_PRINT_H
#define SE_INSPECT_PRINT_H

#include "se_inspect.h"
#include "se_inspect_am.h"
#include "se_replay.h"
#include "se_persistcap_page.h"
#include "se_space_inner.h"

#ifdef __cplusplus
extern "C" {
#endif

#define MAX_DUMP_MSG_INFO 256
#define GMINSPECT_PRINT_RATE 40

void DumpRedoPoint(RedoPointT *redoPoint, char *redoPointMsg);
void InspectDumpPageId(PageIdT *pageAddr, char *pageMsgInfo);
void InspectDumpPageRawData(PageHeadT *head, char *pageMsgInfo, int64_t fileOffset, uint32_t printSize);
void InspectDumpPageHead(PageHeadT *head, char *pageMsgInfo);
void InspectDumpSpaceHead(SpaceHeadT *head, char *pageMsgInfo);
void InspectDumpCtrlFileMagic(uint64_t checkMagic, char *msgInfo);
void InspectDumpValidIdxDataForUint8(const uint8_t *data, int32_t dataLen, const char *dataMsgInfo);
void InspectDumpValidIdxDataForUint32(const uint32_t *u32Data, int32_t dataLen, const char *dataMsgInfo);
void InspectDumpHexRawData(const uint8_t *data, int32_t dataLen, int32_t printOffset, const char *dataMsgInfo);

#ifdef __cplusplus
}
#endif

#endif  // SE_INSPECT_PRINT_H
