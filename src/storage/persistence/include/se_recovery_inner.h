/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: se_recovery_inner.h
 * Description: provide public interface for acquiring status of redo
 * Author: xujun
 * Create: 2022-09-28
 */

#ifndef SE_RECOVERY_INNER_H
#define SE_RECOVERY_INNER_H

#include "db_internal_error.h"
#include "db_instance.h"
#include "se_define.h"
#include "se_redo.h"
#include "se_replay.h"
#include "se_recovery.h"

#ifdef __cplusplus
extern "C" {
#endif

typedef struct {
    RedoLogTypeE logType;
    RedoLogReplayFuncT replayfunc;
} ReplayFuncMapT;

StatusInter RecoveryInit(SeInstanceT *seIns);
StatusInter DbRecoveryExec(SeInstanceT *seIns, RecoveryCtxT *recoveryCtx, RedoPointT *truncPoint, RedoPointT *lrpPoint);
StatusInter RecoveryRedo(RedoMgrT *redoMgr, const RedoPointT *startPoint, const RedoPointT *lrpPoint);
StatusInter MultiZoneSynRecoveryData(SeInstanceT *seIns);
StatusInter DbRecoveryImpl(RecoveryCtxT *recoveryCtx, SeInstanceHdT seIns);

#ifdef __cplusplus
}
#endif

#endif
