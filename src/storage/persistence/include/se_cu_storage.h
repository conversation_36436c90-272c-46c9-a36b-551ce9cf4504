/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: CU file management definition
 * Author: zhangtao
 * Create: 2024-01-02
 */

#ifndef SE_CU_STORAGE_H
#define SE_CU_STORAGE_H

#include <stdbool.h>
#include "adpt_types.h"
#include "adpt_define.h"
#include "db_mem_context.h"
#include "se_cstore.h"
#include "se_file.h"

#ifdef __cplusplus
extern "C" {
#endif

#define CU_ID_START (1000)
#define CU_POINTER_START (0)
#define INVALID_CU_FILE_ID (-1)
#define INVALID_CU_OFFSET (-1)
#define CU_FILE_BLOCK_ALIGN (8)
#define CU_FILE_MAX_SIZE (DB_GIBI)
#define INVALID_TBL_SPC_ID (0)
#define FATAL_MAX_TIME 10  // 分区handle能缓存的最多cuId信息，达到将会直接触发损坏数据的删除

typedef enum SaveCuStatus {
    CU_META_PREPARE,
    CU_ID_META_PREPARED,
    CU_PTR_META_PREPARED,
    CU_META_PERSISTED,
    CU_DATA_PERSISTED
} SaveCuStatusE;

typedef struct CuMetaT {
    uint32_t checkSum;
    uint32_t maxCuId;
    uint64_t maxCuPointer;
    uint32_t rowCnt;
} CuMetaT;

typedef enum CuFdStatus { CU_FD_PREPARE, CU_FD_BEGIN, CU_FD_END } CuFdStatusE;

struct CuStorageHdl {
    DbMemCtxT *memCtx;
    CuStorageKeyT cuStorageKey;
    uint32_t refCnt;
    uint32_t maxCuId;
    int64_t maxCuPointer;
    uint32_t rowCnt;
    int64_t upperBound;
    DbSpinLockT cuIdLock;
    DbSpinLockT cuPointerLock;
    DbSpinLockT cuMetaFileLock;
    DbSpinLockT cuFileLock;
    DbSpinLockT insertLock;
    DbRWSpinLockT cuMergeLock;
    // 故障恢复相关信息
    DbSpinLockT fatalLock;  // 对以下故障相关的成员操作，需要获取改锁，用于并发控制，防止多线程走入同批数据的异常处理
    bool isDelete;          // 标识着整个分区被删除，并且当 refCnt 减为0，触发标记删除
    uint32_t fatalNum;      // 对应cu数组
    uint32_t fatalCuIdArr[FATAL_MAX_TIME];
    uint64_t hollowSize;  // 此分区的文件空洞大小，由删除行存记录后生成
    uint32_t insertLockThreadId;
};

typedef struct CuFileSizeRecord {
    uint64_t size;
    uint32_t tblSpcId;
} CuFileSizeRecordT;

static inline int32_t GetCuFileId(int64_t cuPointer)
{
    if (cuPointer - (int64_t)CU_FILE_MAX_SIZE * DB_MAX_INT32 > 0) {
        return (int32_t)INVALID_CU_FILE_ID;
    }
    return (int32_t)(cuPointer / (int64_t)CU_FILE_MAX_SIZE);
}

static inline int32_t GetCuFileOffset(int64_t cuPointer)
{
    if (cuPointer - (int64_t)CU_FILE_MAX_SIZE * DB_MAX_INT32 > 0) {
        return (int32_t)INVALID_CU_OFFSET;
    }
    return (int32_t)(cuPointer % (int64_t)CU_FILE_MAX_SIZE);
}

SO_EXPORT_FOR_TS Status PrepareCuFile(CuStorageHdlT *cuStorageHdl, int64_t cuPointer, uint32_t cuSize);

SO_EXPORT_FOR_TS Status SaveCu(CuStorageHdlT *cuStorageHdl, DbBufT *buffer, int64_t cuPointer);

SO_EXPORT_FOR_TS Status LoadCu(DbMemCtxT *memCtx, CuStorageHdlT *cuStorageHdl, DbBufT *buffer, int64_t cuPointer);

SO_EXPORT_FOR_TS Status SaveCuMeta(CuStorageHdlT *cuStorageHdl, size_t *diskSizeIncrease);

SO_EXPORT_FOR_TS Status LoadCuMeta(CuStorageHdlT *cuStorageHdl);

SO_EXPORT_FOR_TS void RemoveCuDir(CuStorageKeyT *cuStorageKey, DbSpinLockT *cuDirLock, uint64_t *removedSize);

SO_EXPORT_FOR_TS void TrimCuDir(
    CuStorageKeyT *cuStorageKey, DbSpinLockT *cuDirLock, uint64_t cuPointer, uint64_t *removedSize);

SO_EXPORT_FOR_TS Status GetAllTableCuFileSizeByScanDir(
    uint32_t *count, CuFileSizeRecordT *recordArray, uint32_t maxTblNum);

SO_EXPORT_FOR_TS Status BuildCuStorageHdl(DbMemCtxT *memCtx, CuStorageKeyT *cuStorageKey, CuStorageHdlT **cuStorageHdl);

SO_EXPORT_FOR_TS void ResetThreadLocalCuFd(void);

SO_EXPORT_FOR_TS void ResetThreadLocalCuMetaFd(void);

SO_EXPORT_FOR_TS bool ThreadLocalCuFdValid(void);
Status BuildCuFilePath(char *pathBuf, uint32_t tblSpcId, uint32_t tblId, int64_t cuPointer);

#ifdef __cplusplus
}
#endif

#endif  // SE_CU_STORAGE_H
