/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * File Name: drt_schedule_manager.h
 * Description: header file for runtime schedule manager
 * Author:
 * Create: 2021-7-1
 */
#ifndef DRT_SCHEDULE_MANAGER_H
#define DRT_SCHEDULE_MANAGER_H

#include "db_rpc_msg_op.h"
#include "drt_schedule_list.h"
#include "drt_worker_manager.h"
#include "srv_manager_base.h"

#ifdef __cplusplus
extern "C" {
#endif

#define SCHE_PRIORITY_TOKEN_NORMAL 4

typedef struct SmOneOpStat {
    uint64_t timeAll;             // all long operation processe time
    uint64_t cpuTimeAll;          // all long operation cpu processe time
    uint32_t count;               // count of long operation
    uint32_t timeMax;             // max long operation processe time
    uint32_t cpuTimeMax;          // max long operation cpu processe time
    uint32_t executeCount;        // count of long operation in QE
    uint64_t executeTimeAll;      // all execute time of long operation in QE
    uint32_t executeTimeMax;      // max execute time of long operation in QE
    uint32_t largeObjCount;       // count of large object in long operation
    uint64_t largeObjTimeAll;     // total time of large object in long operation
    uint32_t largeObjTimeMax;     // max processe time of large object in long operation
    uint32_t largeObjCpuTimeMax;  // max cpu processe time of large object in long operation
    uint64_t largeObjCpuTimeAll;  // total cpu processe time of large object in long operation
} SmOneOpStatT;

typedef struct SmLongOpStatPara {
    SmOneOpStatT *allLongOpStat;
    int32_t longOpTimeThr;
    uint32_t procTimeUsec;
    uint32_t cpuProcTime;
    uint32_t opCode;
    uint16_t serviceId;
    bool isLargeObj;
} SmLongOpStatParaT;

struct DrtScheMgr {
    ServiceEntry serviceEntry;
    WorkerMgrT *workerMgr;
    DbMemCtxT *memctx;
    SmScheProcCtxT **scheProcCtx;
    DbRWSpinLockT lock;
    ScheduleModeE scheMode;
    uint16_t maxNum;
    bool enableResilienceStat;
    bool enableSchedulePerfStat;
    uint32_t workLoad;  // unit时间工作量
    uint32_t unitTime;
    int32_t longOpTimeThr;
    void *userCtx;
    SmOneOpStatT allLongOpStat[(uint32_t)REAL_OPCODE_SIZE];
    uint64_t workerStartTime;  // 最新一次worker创建时间
    uint64_t workerExitTime;   // 最新一次worker退出时间
};
// 带锁的变量，需要字节对齐
static_assert(
    (offsetof(struct DrtScheMgr, lock) % sizeof(uint32_t)) == 0, "field lock in DrtScheMgr must be aligned by 4 bytes");
typedef struct DrtInitScheMgrPara {
    WorkerMgrT *workerMgr;
    DbMemCtxT *memctx;
    uint16_t maxNum;
} DrtInitScheMgrParaT;

Status DrtInitScheduleMgr(DrtScheMgrT *scheMgr, const DrtInitScheMgrParaT *para);
void DrtDestroyScheduleMgr(DrtScheMgrT *scheMgr);
void DrtScheduleMgrRegister(DrtScheMgrT *scheMgr, ServiceEntry entry, void *userCtx);

Status SmPrepareSchedule(DrtScheMgrT *scheMgr, SmScheProcCtxT **ctx, DrtConnectionT *conn);
void SmScheProc(DrtScheMgrT *scheMgr, SmScheProcCtxT *ctx);
void SmScheProcWrapper(void *ctx, void *data);
void SmStopSchedule(DrtScheMgrT *scheMgr, uint16_t connId);

Status SmAllocProcCtx(const DrtScheMgrT *scheMgr, DrtConnectionT *conn, FixBufferT *msg, DrtProcCtxTypeE procCtxType,
    DrtProcCtxT **procCtxPtr);
Status SmPushMsgToScheList(DrtScheMgrT *scheMgr, DrtConnectionT *conn, FixBufferT *msg, bool insertHead);
Status SmScheduleOneMsg(DrtScheMgrT *scheMgr, DrtConnectionT *conn, FixBufferT *msg);
void DrtScheduleListSetWorkload(DrtScheMgrT *scheMgr, uint32_t unitTime, uint32_t workLoad);

inline static bool SmScheListIsEmpty(const DrtScheduleListT *scheList)
{
    return scheList == NULL || scheList->procCtxList.head == NULL;
}

inline static bool SmScheNeedSetBusyLoop(const DrtScheMgrT *scheMgr)
{
    DB_POINTER(scheMgr);
    return scheMgr->scheMode == SCHEDULE_DIRECTELY;
}

inline static bool SmScheListNotScheduled(const DrtScheduleListT *scheList)
{
    return scheList == NULL || scheList->procCtxPool.useNum == 0;
}

#ifdef __cplusplus
}
#endif

#endif /* DRT_SCHEDULE_MANAGER_H */
