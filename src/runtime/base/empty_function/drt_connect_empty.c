/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: drt_connect_empty.c
 * Description: 不开启runtime cs特性时候的空实现
 * Author: yangyongji
 * Create: 2023/7/24
 */

#include "gmc_errno.h"
#include "db_dyn_load.h"
#include "drt_base_def.h"
#include "drt_connection.h"
#include "drt_instance.h"
#include "drt_instance_inner.h"
#include "drt_schedule.h"
#include "drt_worker.h"

DrtConnectionT *DrtAttachConnById(DrtConnMgrT *connMgr, uint16_t id)
{
    DB_UNUSED(connMgr);
    DB_UNUSED(id);
    return NULL;
}

void DrtCheckConnAlarmForceRecover(const DrtConnectionT *conn)
{
    DB_UNUSED(conn);
}

Status DrtConnCheckToken(DrtConnectionT *conn, uint32_t uid, bool useReservedConn)
{
    DB_UNUSED(conn);
    DB_UNUSED(uid);
    DB_UNUSED(useReservedConn);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

void DrtConnMsgMemCtxSwitchTo(DrtConnectionT *conn, DbMemCtxT *dstCtx)
{
    DB_UNUSED(conn);
    DB_UNUSED(dstCtx);
}

void DrtDetachConnection(DrtConnectionT *conn)
{
    DB_UNUSED(conn);
}

void DrtFreeMsg(FixBufferT *msg)
{
    DB_UNUSED(msg);
}

Status DrtGetMsgSource(char *msgSource, size_t msgSourceLen, uint32_t pid)
{
    DB_UNUSED(msgSource);
    DB_UNUSED(msgSourceLen);
    DB_UNUSED(pid);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

void DrtSetUserOverLoadLevel(DrtConnectionT *conn, uint8_t level)
{
    DB_UNUSED(conn);
    DB_UNUSED(level);
}

void DrtUpdateConnSubStat(DrtConnectionT *conn, DrtSubStatTypeE subStatType, uint32_t addCnt)
{
    DB_UNUSED(conn);
    DB_UNUSED(subStatType);
    DB_UNUSED(addCnt);
}

// data send channel
void DrtCloseDataSendChannel(DrtDataSendChannelT *channel, bool forceDelete)
{
    DB_UNUSED(channel);
    DB_UNUSED(forceDelete);
}

Status DrtDataChanAllocOpMsg(DrtChanMsgSeqGeneratorHandleT generator, FixBufferT **opMsg, uint16_t opCode)
{
    DB_UNUSED(generator);
    DB_UNUSED(opMsg);
    DB_UNUSED(opCode);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status DrtDataChanAllocShareMsgNode(
    DrtChanMsgSeqGeneratorHandleT generator, uint32_t size, uint32_t *nodeId, FixBufferT **shareMsgBuff)
{
    DB_UNUSED(generator);
    DB_UNUSED(size);
    DB_UNUSED(nodeId);
    DB_UNUSED(shareMsgBuff);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

void DrtDataChanFinishCurOpMsg(DrtChanMsgSeqGeneratorHandleT generator)
{
    DB_UNUSED(generator);
}

void DrtDataChanFinishMsgSequence(DrtChanMsgSeqGeneratorHandleT generator)
{
    DB_UNUSED(generator);
}

void DrtDataChanFreeShareMsgNode(DrtShareMsgMgrT *drtShareMsgMgr, uint32_t nodeId)
{
    DB_UNUSED(drtShareMsgMgr);
    DB_UNUSED(nodeId);
}

uint8_t DrtDataChanGetOverloadRatio(const struct DrtDataChannelMsgSeqGenerator *generator)
{
    DB_UNUSED(generator);
    return 0;
}

Status DrtDataChanRefShareMsgNode(DrtChanMsgSeqGeneratorHandleT generator, uint32_t nodeId)
{
    DB_UNUSED(generator);
    DB_UNUSED(nodeId);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

void DrtDataChanRollBackAllShareMsgNode(DrtChanMsgSeqGeneratorHandleT generator)
{
    DB_UNUSED(generator);
}

void DrtDataChanRollBackCurOpMsg(DrtChanMsgSeqGeneratorHandleT generator)
{
    DB_UNUSED(generator);
}

Status DrtDataChanRollBackShareMsgNode(DrtChanMsgSeqGeneratorHandleT generator, uint32_t nodeId)
{
    DB_UNUSED(generator);
    DB_UNUSED(nodeId);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

void DrtDataChanStopMsgSequence(DrtChanMsgSeqGeneratorHandleT generator)
{
    DB_UNUSED(generator);
}

DrtDataSendChannelT *DrtGetChannelByConnAndId(const DrtConnectionT *conn, SaDataChannelIdT channelId)
{
    DB_UNUSED(conn);
    DB_UNUSED(channelId);
    return NULL;
}

// node
DrtNodeT *DrtAllocNodeWithName(DrtNodeMgrT *nodeMgr, uint8_t nodeType, const char *name)
{
    DB_UNUSED(nodeMgr);
    DB_UNUSED(nodeType);
    DB_UNUSED(name);
    return NULL;
}

void DrtFreeNode(DrtNodeMgrT *nodeMgr, uint16_t nodeId)
{
    DB_UNUSED(nodeMgr);
    DB_UNUSED(nodeId);
}

DrtNodeT *DrtGetNodeById(DrtNodeMgrT *nodeMgr, uint16_t nodeId)
{
    DB_UNUSED(nodeMgr);
    DB_UNUSED(nodeId);
    return NULL;
}

Status DrtGetNodeNameByNodeId(DrtNodeMgrT *nodeMgr, uint16_t nodeId, char *name, uint32_t nameLen)
{
    DB_UNUSED(nodeMgr);
    DB_UNUSED(nodeId);
    DB_UNUSED(name);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

uint8_t DrtGetFlagByNodeId(DrtNodeMgrT *nodeMgr, uint16_t nodeId)
{
    DB_UNUSED(nodeMgr);
    DB_UNUSED(nodeId);
    return 0;
}

uint16_t DrtGetConnIdByNodeName(DrtNodeMgrT *nodeMgr, const char *name)
{
    DB_UNUSED(nodeMgr);
    DB_UNUSED(name);
    return DB_INVALID_UINT16;
}

// send agent
DrtDataPlaneT *SaAllocDataPlane(DrtSendAgentT *sa, DrtDataPlaneChannelRangeT channelRange)
{
    DB_UNUSED(sa);
    DB_UNUSED(channelRange);
    return NULL;
}

void SaFreeDataPlane(DrtSendAgentT *sa, DrtDataPlaneT *plane)
{
    DB_UNUSED(sa);
    DB_UNUSED(plane);
}

Status SaGetAsyncMsgSeqGeneratorHandleByChannelId(
    SaDataChannelIdT channelId, DrtChanMsgSeqGeneratorHandleT *handle, DrtFreeNodeBuff freeNodeBuffFun)
{
    DB_UNUSED(channelId);
    DB_UNUSED(handle);
    DB_UNUSED(freeNodeBuffFun);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status SaGetSyncMsgSeqGeneratorHandleByChannelId(SaDataChannelIdT channelId, DrtChanMsgSeqGeneratorHandleT *handle,
    DrtFreeNodeBuff freeNodeBuffFun, DrtConnectionT *sourceConn, uint32_t sessionId)
{
    DB_UNUSED(channelId);
    DB_UNUSED(handle);
    DB_UNUSED(freeNodeBuffFun);
    DB_UNUSED(sourceConn);
    DB_UNUSED(sessionId);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status SaPushReservedCtrlMsgByChannelId(DrtSendAgentT *sa, SaDataChannelIdT channelId, const char *data, uint32_t len)
{
    DB_UNUSED(sa);
    DB_UNUSED(channelId);
    DB_UNUSED(data);
    DB_UNUSED(len);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

// share msg
Status DrtShareMsgMgrInit(DbMemCtxT *memCtx, DrtShareMsgMgrT *drtShareMsgMgr)
{
    DB_UNUSED(memCtx);
    DB_UNUSED(drtShareMsgMgr);
    return GMERR_FEATURE_NOT_SUPPORTED;
}

DrtDataSendChannelT *DrtPlaneGetChannelById(DrtDataPlaneT *plane, uint16_t channelId)
{
    DB_UNUSED(plane);
    DB_UNUSED(channelId);
    return NULL;
}

DrtDataSendChannelT *DrtPlaneAllocChannel(
    DrtDataPlaneT *plane, uint16_t channelId, DrtConnectionT *conn, const DrtDataPlaneInitParamsT *params)
{
    DB_UNUSED(plane);
    DB_UNUSED(channelId);
    DB_UNUSED(conn);
    DB_UNUSED(params);
    return NULL;
}
