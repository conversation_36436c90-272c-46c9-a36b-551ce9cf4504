/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: drt_schedule_empty.c
 * Description: 不开启runtime cs特性时候的空实现
 * Author: yangyongji
 * Create: 2023/7/24
 */

#include "gmc_errno.h"
#include "db_dyn_load.h"
#include "drt_base_def.h"
#include "drt_connection.h"
#include "drt_instance.h"
#include "drt_instance_inner.h"
#include "drt_schedule.h"
#include "drt_worker.h"

void DrtScheduleListPushProcCtx(SmScheProcCtxT *scheProcCtx, DrtProcCtxT *procCtx, bool insertHead)
{
    DB_UNUSED(scheProcCtx);
    DB_UNUSED(procCtx);
    DB_UNUSED(insertHead);
}

void SmUpdateLongOpExecuteStat(DrtScheMgrT *scheMgr, uint32_t procTimeUsec, uint32_t opCode)
{
    DB_UNUSED(scheMgr);
    DB_UNUSED(procTimeUsec);
    DB_UNUSED(opCode);
}

void DrtScheduleMgrRegister(DrtScheMgrT *scheMgr, ServiceEntry entry, void *userCtx)
{
    DB_UNUSED(scheMgr);
    DB_UNUSED(entry);
    DB_UNUSED(userCtx);
    DB_LOG_WARN(GMERR_FEATURE_NOT_SUPPORTED, "runtime not registered");
}
