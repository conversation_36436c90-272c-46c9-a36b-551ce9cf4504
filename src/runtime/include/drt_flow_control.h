/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: drt_flowctrl.h
 * Description: header file for runtime flow control
 * Author:
 * Create: 2023-6-12
 */

#ifndef DRT_FLOWCTRL_H
#define DRT_FLOWCTRL_H

#include "drt_connection_def.h"
#include "db_privileges.h"

#ifdef __cplusplus
extern "C" {
#endif

inline static uint8_t DrtGetGlobalFlowCtrlLevel(const DrtFlowCtrlInfoT *flowCtrlInfo)
{
    DB_POINTER(flowCtrlInfo);
    return (uint8_t)flowCtrlInfo->globalLevel;
}

#ifdef __cplusplus
}
#endif
#endif /* DRT_FLOWCTRL_H */
