/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * File Name: drt_worker.c
 * Description: source file for runtime worker
 * Author:
 * Create: 2021-6-3
 */
#include "drt_worker.h"
#include "adpt_sleep.h"
#include "db_signal.h"
#include "drt_log.h"
#include "drt_worker_inner.h"
#include "drt_instance.h"

#ifndef HPE
#include <limits.h>
#endif

#ifdef __cplusplus
extern "C" {
#endif

DB_THREAD_LOCAL uint16_t g_gmdbThisWorkerId = DB_INVALID_UINT16;

uint16_t GetThisWorkerId(void)
{
    return g_gmdbThisWorkerId;
}

void WorkerClearMonitorHandler(WorkerMonitorHandlerT *handler)
{
    DB_POINTER(handler);
    handler->startMonitorWorker = NULL;
    handler->stopMonitorWorker = NULL;
    handler->workerAlive = NULL;
    handler->handle = NULL;
}

void WorkerInit(WorkerT *worker, uint16_t workerId, WorkerMonitorHandlerT *monitorHandler)
{
    DB_POINTER2(worker, monitorHandler);
    worker->workerId = workerId;
    worker->status = DRT_WORKER_STATUS_STOPPED;
    worker->monitorHandler = monitorHandler;
    worker->workerStat = (WorkerStatT){0};
}

Status WorkerSwitchStatus(WorkerT *worker, uint16_t oldStatus, uint16_t newStatus)
{
    DB_POINTER(worker);
    Status ret = GMERR_OK;
    DbSpinLock(&worker->lock);
    if (newStatus == DRT_WORKER_STATUS_STOPPED) {
        worker->handle = 0;
    }
    if ((worker->status & oldStatus) == 0) {
        ret = GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
        DB_LOG_WARN(ret,
            "Changing worker stat. id %" PRIu16 ", name %s, currStatus %" PRIu16 ", oldStat %" PRIu16
            ", newStat %" PRIu16 ".",
            worker->workerId, worker->name, worker->status, oldStatus, newStatus);
    } else {
        DB_LOG_INFO("Changing worker stat. id %" PRIu16 ", name %s, currStat %" PRIu16 ", oldStat %" PRIu16
                    ", newStat %" PRIu16 ".",
            worker->workerId, worker->name, worker->status, oldStatus, newStatus);
        worker->status = newStatus;
        if (newStatus == DRT_WORKER_STATUS_RUNNING) {
            DB_ASSERT(worker->handle == 0);
            worker->handle = pthread_self();
        }
    }
    DbSpinUnlock(&worker->lock);
    return ret;
}

inline static void WorkerStartMonitor(const WorkerT *worker)
{
    DB_POINTER2(worker, worker->monitorHandler);
    if (worker->monitorHandler->startMonitorWorker != NULL) {
        worker->monitorHandler->startMonitorWorker(worker->monitorHandler->handle, worker->workerId);
    }
}

inline static void WorkerStopMonitor(const WorkerT *worker)
{
    DB_POINTER2(worker, worker->monitorHandler);
    if (worker->monitorHandler->stopMonitorWorker != NULL) {
        worker->monitorHandler->stopMonitorWorker(worker->monitorHandler->handle, worker->workerId);
    }
}

void *WorkerEntryWrapper(void *ctx)
{
    DB_POINTER(ctx);
    DbSetServerThreadFlag();
    WorkerT *worker = (WorkerT *)ctx;
    Status ret = WorkerSwitchStatus(worker, DRT_WORKER_STATUS_START, DRT_WORKER_STATUS_RUNNING);
    if (ret != GMERR_OK) {
        DB_LOG_WARN(ret, "set work stat: %s run", worker->name);
    }
    (void)DbThreadSetName(pthread_self(), worker->name);
    WorkerStartMonitor(worker);
    void *retEntry = NULL;
    if (worker->entry != NULL) {
        g_gmdbThisWorkerId = worker->workerId;
        retEntry = worker->entry(worker->ctx, worker->workerId);
    }
    WorkerStopMonitor(worker);
    ret = WorkerSwitchStatus(worker, DRT_WORKER_STATUS_RUNNING | DRT_WORKER_STATUS_STOPPING, DRT_WORKER_STATUS_STOPPED);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set work stat: %s stop", worker->name);
    }
    DbClearServerThreadFlag();
    return retEntry;
}

Status WorkerSetName(WorkerT *worker, const char *name, bool nameWithId)
{
    DB_POINTER(worker);
    uint16_t instanceId = DbGetProcGlobalId();
    int32_t ret;
    if (name != NULL) {
        size_t nameLen = strlen(name);
        if (nameLen >= WORKER_NAME_LEN) {
            DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "inv worker name %s max len:%" PRId32, name, WORKER_NAME_LEN);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        if (nameWithId) {
            DB_ASSERT(
                (nameLen + 4) < (WORKER_NAME_LEN - 1));  // workId最大长度为4位数,workerName总长不可超过 WORKER_NAME_LEN
            if (DbCfgIsTsInstance()) {
                // ts instance: WorkPl_<instId>_<workId>
                ret = snprintf_s(worker->name, WORKER_NAME_LEN, WORKER_NAME_LEN - 1, "%s_%" PRIu16 "_%" PRIu16 "", name,
                    instanceId, worker->workerId);
            } else {
                // v5 instance: WorkerPool_<workId>
                ret = snprintf_s(
                    worker->name, WORKER_NAME_LEN, WORKER_NAME_LEN - 1, "%s_%" PRIu16 "", name, worker->workerId);
            }
        } else {
            ret = snprintf_s(worker->name, WORKER_NAME_LEN, WORKER_NAME_LEN - 1, "%s_%" PRIu16 "", name, instanceId);
        }
        if (ret < 0) {
            DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, " copy worker name %s_%" PRIu16 ".", name, instanceId);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
    } else {
        ret = snprintf_s(worker->name, WORKER_NAME_LEN, WORKER_NAME_LEN - 1, "%s_%" PRIu16 "_%" PRIu16 "",
            DRT_WORKER_NAME_PRE, instanceId, worker->workerId);
        if (ret < 0) {
            DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, " copy worker name %s_%" PRIu16 "_%" PRIu16 ". system %" PRId32,
                DRT_WORKER_NAME_PRE, instanceId, worker->workerId, DbAptGetErrno());
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
    }
    return GMERR_OK;
}

// 线程或者协程退出时清理申请的资源（参数，内存等）
void *WorkExitClearRes(Handle param)
{
    DB_UNUSED(param);
    DbSetCurrMemCtxToNull();
    return NULL;
}

static Status WorkerSetThreadAttributes(ThreadAttrsT *attrs, const WorkerParaT *workerPara, WorkerT *worker)
{
    DB_POINTER3(attrs, workerPara, worker);
    errno_t ret = strcpy_s(attrs->name, sizeof(attrs->name), worker->name);
    if (ret != EOK) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "copy worker name, sys: %" PRId32, DbAptGetErrno());
        (void)WorkerSwitchStatus(worker, DRT_WORKER_STATUS_START, DRT_WORKER_STATUS_STOPPED);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    attrs->priority = (DbThreadPriorityE)workerPara->priority;  // 现在的优先级必须大于0，不然调度不起来
    attrs->type = DB_THREAD_DETACHABLE;
    attrs->entryFunc = WorkerEntryWrapper;
    attrs->exitFunc = WorkExitClearRes;
    attrs->exitArgs = NULL;
    attrs->entryArgs = worker;
    attrs->stackSize = DB_DEFAULT_THREAD_STACK_SIZE;
    attrs->userAttr = NULL;
    attrs->bindCpuFlag = workerPara->isBindCpu;
    attrs->cpu = workerPara->cpuNO;
    return GMERR_OK;
}

Status WorkerStart(WorkerT *worker, const WorkerParaT *workerPara)
{
    DB_POINTER2(worker, workerPara);
    Status ret = WorkerSetName(worker, workerPara->name, workerPara->nameWithId);
    if (ret != GMERR_OK) {
        return ret;
    }

    worker->workerStat.scheduleTime = workerPara->scheduleTime;  // 设置workerStat中worker的周期调度间隔
    worker->type = workerPara->type;
    ret = WorkerSwitchStatus(worker, DRT_WORKER_STATUS_STOPPED, DRT_WORKER_STATUS_START);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set work stat: %s start", worker->name);
    }
    worker->ctx = workerPara->ctx;
    worker->entry = workerPara->entry;
    ThreadAttrsT attrs;

    if ((ret = WorkerSetThreadAttributes(&attrs, workerPara, worker)) != GMERR_OK) {
        return ret;
    }
    DB_LOG_DEBUG("start create worker thread: %" PRIu16, worker->workerId);
    worker->workerStat.workerCreateBegin = DbRdtsc();
    DbThreadHandle handle = 0;
    ret = DbThreadCreate(&attrs, &handle);
    worker->workerStat.workerCreateEnd = DbRdtsc();
    // [worker创建开始, worker创建结束]，超过1000ms则记录日志
    uint64_t timeUsed = DbToMseconds(worker->workerStat.workerCreateEnd - worker->workerStat.workerCreateBegin);
    if (timeUsed > MSECONDS_IN_SECOND) {
        DB_LOG_WARN(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE,
            "create worker takes long time(ms): %" PRIu64 ".,workerId %" PRIu16, timeUsed, worker->workerId);
    }
    if (ret != GMERR_OK) {
        (void)WorkerSwitchStatus(worker, DRT_WORKER_STATUS_START, DRT_WORKER_STATUS_STOPPED);
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, " create new worker, worker name: %s, id: %" PRIu16 ", ret: %" PRId32,
            worker->name, worker->workerId, (int32_t)ret);
        return ret;
    }
    DB_LOG_DEBUG("create worker thread %" PRIu16 " comp", worker->workerId);
    return GMERR_OK;
}

void WorkerWaitExit(const WorkerT *worker)
{
    DB_POINTER(worker);
    while (worker->status != DRT_WORKER_STATUS_STOPPED) {
        DbUsleep(1);
    }
}

Status WorkerStopNoWait(WorkerT *worker)
{
    DB_POINTER(worker);
    uint16_t oldStatus = DRT_WORKER_STATUS_START | DRT_WORKER_STATUS_RUNNING;
    Status ret = WorkerSwitchStatus(worker, oldStatus, DRT_WORKER_STATUS_STOPPING);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Stop worker %" PRIu16 ", stat %" PRIu16, worker->workerId, worker->status);
    }
    return ret;
}

Status WorkerStop(WorkerT *worker)
{
    DB_POINTER(worker);
    Status ret = WorkerStopNoWait(worker);
    WorkerWaitExit(worker);
    return ret;
}

Status WorkerListInit(WorkerListT *workerList, DbMemCtxT *memCtx, uint16_t connMaxNum)
{
    DB_POINTER2(workerList, memCtx);
    workerList->maxNum = (uint16_t)(connMaxNum + (uint16_t)MAX_BG_WORKER_NUM);
    uint32_t workerSize = (uint32_t)sizeof(WorkerT *) * (uint32_t)workerList->maxNum;
    /* 持久化实例，服务端退出时，上层memctx保底机制释放 */
    workerList->workers = (WorkerT **)DbDynMemCtxAlloc(memCtx, workerSize);
    if (workerList->workers == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Alloc workers");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(workerList->workers, workerSize, 0, workerSize);
    workerList->memCtx = memCtx;
    workerList->isStoppingAllWorker = 0;
    workerList->hungWorkerTimes = 0;
    workerList->createThreadSuccessCnt = 0;
    workerList->createThreadFailureCnt = 0;
    return GMERR_OK;
}

void WorkerListRelease(WorkerListT *workerList)
{
    DB_POINTER(workerList);
    DbSpinLock(&workerList->lock);
    for (uint16_t i = 0; i < workerList->maxNum; ++i) {
        if (workerList->workers[i] != NULL) {
            DbDynMemCtxFree(workerList->memCtx, workerList->workers[i]);
            workerList->workers[i] = NULL;
        }
    }
    // DB退出流程调用，内存释放后可不置空，无UAF风险
    DbDynMemCtxFree(workerList->memCtx, workerList->workers);
    DbSpinUnlock(&workerList->lock);
    return;
}

bool WorkerIsStopping(const WorkerListT *workerList, uint16_t workerId)
{
    DB_POINTER(workerList);
    DB_ASSERT(workerId < workerList->maxNum && workerList->workers != NULL);  // 上层保证workerId一定合法
    WorkerT *worker = workerList->workers[workerId];
    bool workerIsRunning = (worker != NULL && worker->status == DRT_WORKER_STATUS_RUNNING);
    if (SECUREC_LIKELY(workerIsRunning && worker->monitorHandler->workerAlive != NULL)) {
        worker->monitorHandler->workerAlive(worker->monitorHandler->handle, worker->workerId);
    } else {
        DB_LOG_DEBUG("worker %" PRIu16 "stopping, stat: %" PRIu16 ".", workerId,
            (worker != NULL ? worker->status : DRT_WORKER_STATUS_STOPPED));
    }
    return !workerIsRunning;
}

uint16_t WorkerListGetValidWorkerId(WorkerListT *workerList, WorkerTypeE type)
{
    DB_POINTER(workerList);
    if (type >= DRT_WORKER_BUTT) {
        return DB_INVALID_ID16;
    }

    uint16_t startId = (type == DRT_WORKER_BGTASK) ? 0 : (uint16_t)MAX_BG_WORKER_NUM;
    uint16_t endId = (type == DRT_WORKER_BGTASK) ? (uint16_t)MAX_BG_WORKER_NUM : workerList->maxNum;
    for (uint16_t i = startId; i < endId; i++) {
        if (!WorkerIdIsValid(workerList, i)) {
            return i;
        }
    }
    return DB_INVALID_ID16;
}

Status WorkerListStartNewWorker(
    WorkerListT *workerList, WorkerMonitorHandlerT *monitorHandler, const WorkerParaT *workerPara, uint16_t *workerId)
{
    DB_POINTER3(workerList, workerPara, monitorHandler);
#ifdef SHUTDOWN
    if (DrtDbServerIsStopped()) {
        DB_LOG_ERROR(GMERR_CONNECTION_RESET_BY_PEER, "Db is closing when start new worker.");
        return GMERR_CONNECTION_RESET_BY_PEER;
    }
#endif
    DbSpinLock(&workerList->lock);

    if (workerList->isStoppingAllWorker) {
        DbSpinUnlock(&workerList->lock);
        DB_LOG_WARN(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "Db worker list is stopping when start new worker.");
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    uint16_t freeId = WorkerListGetValidWorkerId(workerList, workerPara->type);
    if (freeId == DB_INVALID_ID16) {
        DbSpinUnlock(&workerList->lock);
        DB_LOG_ERROR(GMERR_INSUFFICIENT_RESOURCES, "Worker init, workers num %" PRIu16 " exceeds max %" PRId32,
            workerList->maxNum, MAX_BG_WORKER_NUM);
        return GMERR_INSUFFICIENT_RESOURCES;
    }
    WorkerT *worker = workerList->workers[freeId];
    if (worker == NULL) {
        /* 持久化实例，服务端退出时，上层memctx保底机制释放 */
        worker = (WorkerT *)DbDynMemCtxAlloc(workerList->memCtx, sizeof(WorkerT));
        if (worker == NULL) {
            DbSpinUnlock(&workerList->lock);
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Alloc new worker");
            return GMERR_OUT_OF_MEMORY;
        }
        (void)memset_s(worker, sizeof(WorkerT), 0, sizeof(WorkerT));
        WorkerInit(worker, freeId, monitorHandler);
        workerList->workers[freeId] = worker;
    }
    if (workerId != NULL) {
        *workerId = freeId;
    }
    Status ret = WorkerStart(worker, workerPara);
    ret == GMERR_OK ? workerList->createThreadSuccessCnt++ : workerList->createThreadFailureCnt++;
    if (workerId != NULL && ret != GMERR_OK) {
        *workerId = (uint16_t)DB_INVALID_ID16;
    }
    DbSpinUnlock(&workerList->lock);
    return ret;
}

inline static uint16_t DrtWorkerGetStatus(const WorkerListT *workerList, uint16_t workerId)
{
    DB_POINTER(workerList);
    return (workerId < workerList->maxNum && workerList->workers[workerId] != NULL) ?
               workerList->workers[workerId]->status :
               (uint16_t)DRT_WORKER_STATUS_INVALID;
}

Status WorkerListStopOneWorker(WorkerListT *workerList, uint16_t workerId)
{
    DB_POINTER(workerList);
    DbSpinLock(&workerList->lock);
    if (!WorkerIdIsValid(workerList, workerId)) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "inv worker id: %" PRIu16 ", stat: %" PRIu16, workerId,
            DrtWorkerGetStatus(workerList, workerId));
        DbSpinUnlock(&workerList->lock);
        return GMERR_DATA_EXCEPTION;
    }

    Status ret = WorkerStop(workerList->workers[workerId]);
    DbSpinUnlock(&workerList->lock);
    return ret;
}

Status WorkerListStopOneWorkerNoWait(WorkerListT *workerList, uint16_t workerId)
{
    DB_POINTER(workerList);
    DbSpinLock(&workerList->lock);
    if (!WorkerIdIsValid(workerList, workerId)) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "inv worker id: %" PRIu16 ", stat: %" PRIu16, workerId,
            DrtWorkerGetStatus(workerList, workerId));
        DbSpinUnlock(&workerList->lock);
        return GMERR_DATA_EXCEPTION;
    }

    Status ret = WorkerStopNoWait(workerList->workers[workerId]);
    DbSpinUnlock(&workerList->lock);
    return ret;
}

#define DRT_WORKER_WAIT_TIME_10_US 10
#define DRT_WORKER_WAIT_TIME_1000_US 1000
void WorkerListStopAllWorker(WorkerListT *workerList)
{
    DB_POINTER(workerList);
    DbSpinLock(&workerList->lock);
    workerList->isStoppingAllWorker = true;
    for (uint16_t workerId = 0; workerId < workerList->maxNum; workerId++) {
        if (!WorkerIdIsValid(workerList, workerId)) {
            continue;
        }
        Status ret = WorkerStopNoWait(workerList->workers[workerId]);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Stop worker %" PRIu16 ",  when stop all", workerId);
        }
    }
    DbSpinUnlock(&workerList->lock);
    uint32_t stopWorkerCnt = 0;
    DbThreadHandle tid = DbThreadGetTid();
    do {
        DbSpinLock(&workerList->lock);
        for (uint16_t workerId = 0; workerId < workerList->maxNum; workerId++) {
            /* Don't wait for yourself */
            WorkerT *worker = workerList->workers[workerId];
            if (worker == NULL) {
                stopWorkerCnt++;
                continue;
            }
            if (worker->handle != tid && worker->status != DRT_WORKER_STATUS_STOPPED) {
                // 等待worker退出
                DbUsleep(DRT_WORKER_WAIT_TIME_10_US);
                if (worker->status != DRT_WORKER_STATUS_STOPPED) {
                    // worker未正常退出，可能存在死锁
                    break;
                }
            }
            stopWorkerCnt++;
        }
        DbSpinUnlock(&workerList->lock);
        if (stopWorkerCnt != workerList->maxNum) {
            // 解锁后sleep等待其他worker处理完成
            DbUsleep(DRT_WORKER_WAIT_TIME_1000_US);
            stopWorkerCnt = 0;
        }
    } while (stopWorkerCnt != workerList->maxNum);
}

WorkerT *WorkerListGetWorker(const WorkerListT *workerList, uint16_t workerId)
{
    DB_POINTER(workerList);
    if (!WorkerIdIsValid(workerList, workerId)) {
        DB_LOG_WARN(GMERR_DATA_EXCEPTION, "inv worker id: %" PRIu16 ", stat: %" PRIu16, workerId,
            DrtWorkerGetStatus(workerList, workerId));
        return NULL;
    }
    return workerList->workers[workerId];
}

uint16_t WorkerListGetTotalNum(WorkerListT *workerList)
{
    DB_POINTER(workerList);
    uint16_t num = 0;
    DbSpinLock(&workerList->lock);
    for (uint16_t workerId = 0; workerId < workerList->maxNum; workerId++) {
        if (WorkerIdIsValid(workerList, workerId)) {
            num++;
        }
    }
    DbSpinUnlock(&workerList->lock);
    return num;
}

#ifdef INJECT
uint16_t WorkerListGetValidNum()
{
    DrtInstanceT *drtIns = DrtGetInstance(NULL);
    if (drtIns == NULL) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Inject get instance unsucc when get worker num");
        return 0;
    }
    uint16_t num = 0;
    WorkerListT *workerList = &drtIns->workerMgr.workerList;
    DbSpinLock(&workerList->lock);
    for (uint16_t workerId = 0; workerId < workerList->maxNum; workerId++) {
        if (WorkerIdIsValid(workerList, workerId)) {
            num++;
        }
    }
    DbSpinUnlock(&workerList->lock);
    return num;
}
#endif

bool WorkerNeedUploadHungResult(void)
{
    return DbGetServerSameProcessStartFlag() != 0 && DbCfgGetBoolLite(DB_CFG_ENABLE_UPLOAD_HUNG_RESULT, NULL);
}

Status WorkerListKillWorker(WorkerListT *workerList, uint16_t workerId)
{
    DB_POINTER(workerList);
    DbSpinLock(&workerList->lock);
    if (!WorkerIdIsValid(workerList, workerId)) {
        DbSpinUnlock(&workerList->lock);
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "inv worker id: %" PRIu16 " stat: %" PRIu16, workerId,
            DrtWorkerGetStatus(workerList, workerId));
        return GMERR_INTERNAL_ERROR;
    }

    Status ret = GMERR_OK;

    if (SECUREC_UNLIKELY(WorkerNeedUploadHungResult())) {
        DbSetHungThreadId(workerList->workers[workerId]->handle);
    } else {
        ret = DbThreadKill(workerList->workers[workerId]->handle, DB_SIGABRT);
    }

    DbSpinUnlock(&workerList->lock);
    return ret;
}

void DrtWorkerUpdateStat(WorkerMgrT *workerMgr, uint16_t workerId, bool isProcessing)
{
    DB_POINTER(workerMgr);
    WorkerStatT *workerStat = &workerMgr->workerList.workers[workerId]->workerStat;
    workerStat->isProcessing = isProcessing;
    workerStat->nowCpuCycle = DbRdtsc();  // 记录调度时间
    if (isProcessing) {
        workerStat->procCount++;
    } else {
        workerStat->procTimeLastCycle = DbRdtsc() - workerStat->procTimeLastCycle;
        workerStat->procTimeAllCycle += workerStat->procTimeLastCycle;
    }
    return;
}

Status DrtWorkerBindCpuSet(WorkerMgrT *workerMgr, void *cpuSet)
{
    DB_POINTER2(workerMgr, cpuSet);

    Status status;
    WorkerListT *workerList = &workerMgr->workerList;
    for (uint16_t workerId = 0; workerId < workerList->maxNum; workerId++) {
        if (WorkerIdIsValid(workerList, workerId)) {
            status = DbThreadBindCpuSet(workerList->workers[workerId]->handle, cpuSet);
            if (status != GMERR_OK) {
                DB_LOG_ERROR(status, "drt bind cpu. WorkerId %" PRIu16 ", stat: %" PRIu16, workerId,
                    DrtWorkerGetStatus(workerList, workerId));
                return status;
            }
        }
    }

    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif
