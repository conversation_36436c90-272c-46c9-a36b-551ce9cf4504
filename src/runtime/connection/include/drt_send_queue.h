/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * File Name: drt_send_queue.h
 * Description: header file for runtime send queue, prefix 'sq' means send queue.
 * Author: chenjunyu
 * Create: 2021-5-29
 */
#ifndef DRT_SEND_QUEUE_H
#define DRT_SEND_QUEUE_H

#include "drt_data_channel_node.h"

#ifdef __cplusplus
extern "C" {
#endif

#define SQ_CLEAR_TIMEOUT_MS 100
#define SQ_CLEAR_LOG_TIMEOUT_MS 500

inline static bool SqIsEmpty(const SndQueueT *que)
{
    DB_POINTER(que);
    return que->nodeInQue == 0;
}

void SqInitQueue(SndQueueT *que, DrtFreeNodeBuff freeNodeBuffFun);
void SqAtomicCat(SndQueueT *src, SndQueueT *dst);
bool SqClear(SndQueueT *que, DrtChannelNodeSubPoolT *nodePool, bool isCleanBufContent, bool monitorTimeout);
void SqAtomicAppend(SndQueueT *que, DrtChannelNodeT *node);
DrtChannelNodeT *SqAtomicPop(SndQueueT *que);

#ifdef __cplusplus
}
#endif

#endif /* DRT_SEND_QUEUE_H */
