/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * File Name: drt_send_agent.h
 * Description: send agent head file
 * Author: chenjunyu
 * Create: 2021-6-9
 */
#ifndef DRT_SEND_AGENT_H
#define DRT_SEND_AGENT_H

#include "drt_send_queue.h"
#include "drt_data_plane.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define MAX_DATA_PLANE_BITMAP 0xF

typedef struct SaInitPara {
    DbMemCtxT *memCtx;
    WorkerMgrT *workerMgr;
} SaInitParaT;

void SaInit(DrtSendAgentT *sa, SaInitParaT *para);
void SaRelease(DrtSendAgentT *sa);

DrtDataPlaneT *SaAllocDataPlane(DrtSendAgentT *sa, DrtDataPlaneChannelRangeT channelRange);
void SaFreeDataPlane(DrtSendAgentT *sa, DrtDataPlaneT *plane);

Status SaGetAsyncMsgSeqGeneratorHandleByChannelId(
    SaDataChannelIdT channelId, DrtChanMsgSeqGeneratorHandleT *handle, DrtFreeNodeBuff freeNodeBuffFun);
Status SaGetSyncMsgSeqGeneratorHandleByChannelId(SaDataChannelIdT channelId, DrtChanMsgSeqGeneratorHandleT *handle,
    DrtFreeNodeBuff freeNodeBuffFun, DrtConnectionT *sourceConn, uint32_t sessionId);
Status SaPushReservedCtrlMsgByChannelId(DrtSendAgentT *sa, SaDataChannelIdT channelId, const char *data, uint32_t len);

void SaRefreshSubConnAlarmRatio(DrtSendAgentT *sa);

inline static DrtDataSendChannelT *SaFindDataSendChannelByConnId(DrtSendAgentT *sa, uint16_t connId)
{
    DB_POINTER(sa);
    for (uint16_t i = 0; i < MAX_DATA_PLANE_NUM; i++) {
        if (sa->dataPlane[i] != NULL && DrtPlaneChannelIdIsInRange(sa->dataPlane[i], connId)) {
            return DrtPlaneGetChannelById(sa->dataPlane[i], connId);
        }
    }
    return NULL;
}

inline static Status SaPushCtrlMsgByConnId(DrtSendAgentT *sa, uint16_t connId, FixBufferT *rsp)
{
    DB_POINTER2(sa, rsp);
    DrtDataSendChannelT *channel = SaFindDataSendChannelByConnId(sa, connId);
    if (channel == NULL) {
        return GMERR_DATA_EXCEPTION;
    }
    return DrtDataChanPushCtrlMsg(channel, rsp);
}

#ifdef __cplusplus
}
#endif
#endif /* DRT_SEND_AGENT_H */
