/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * Description: header file for runtime data channel node
 * Author:
 * Create: 2021-12-31
 */
#ifndef DRT_DATA_CHANNEL_NODE_H
#define DRT_DATA_CHANNEL_NODE_H

#include "drt_connection_inner.h"
#include "db_msg_buffer.h"
#include "db_mem_context.h"
#include "adpt_spinlock.h"

#ifdef __cplusplus
extern "C" {
#endif

#define CHANNEL_NODE_FLAG_NONE 0x0000
#define CHANNEL_NODE_FLAG_SND_DIRECTLY 0x0001
#define CHANNEL_NODE_FLAG_COMPLETE_MSG 0x0002

Status DrtInitChannelNodePool(
    DrtChannelNodePoolT *chanNodePool, DbMemCtxT *memctx, uint32_t nodeLineSize, uint32_t maxNodeNum);
void DrtDestroyChannelNodePool(DrtChannelNodePoolT *chanNodePool);
DrtChanNodeLineT *DrtChanNodePoolAllocNodeLine(DrtChannelNodePoolT *chanNodePool);
void DrtChanNodePoolFreeNodeLine(DrtChannelNodePoolT *chanNodePool, DrtChanNodeLineT *nodeLine);

void DrtInitChannelNodeSubPool(DrtChannelNodeSubPoolT *subPool, DrtChannelNodePoolT *pool, uint32_t maxNodeNum);
void DrtDestroyChannelNodeSubPool(DrtChannelNodeSubPoolT *subPool);
DrtChannelNodeT *DrtChanNodeSubPoolAllocNode(DrtChannelNodeSubPoolT *subPool);
void DrtChanNodeSubPoolFreeNode(DrtChannelNodeSubPoolT *subPool, DrtChannelNodeT *node);
void DrtChanNodeFreeFromPool(DrtChannelNodeSubPoolT *subPool, DrtChannelNodeT *node);

uint8_t DrtChanNodeUsage(const DrtChannelNodeSubPoolT *subPool);

inline static void DrtChanNodeSetFlag(DrtChannelNodeT *node, uint32_t flag)
{
    DB_POINTER(node);
    node->flags |= flag;
}

inline static bool DrtChanNodeIsCompleteMsg(const DrtChannelNodeT *node)
{
    DB_POINTER(node);
    return (node->flags & CHANNEL_NODE_FLAG_COMPLETE_MSG) == CHANNEL_NODE_FLAG_COMPLETE_MSG;
}

inline static bool DrtChanNodeIsSndDirectly(const DrtChannelNodeT *node)
{
    DB_POINTER(node);
    return (node->flags & CHANNEL_NODE_FLAG_SND_DIRECTLY) == CHANNEL_NODE_FLAG_SND_DIRECTLY;
}

#ifdef __cplusplus
}
#endif

#endif /* DRT_DATA_CHANNEL_NODE_H */
