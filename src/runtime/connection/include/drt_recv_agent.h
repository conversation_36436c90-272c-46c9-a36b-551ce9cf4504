/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: drt_recv_agent.h
 * Description: receive agent head file
 * Author: gaohaiyang
 * Create: 2020-08-11
 */
#ifndef DRT_RECV_AGENT_H
#define DRT_RECV_AGENT_H

#include "drt_connection.h"
#include "drt_node.h"
#include "drt_schedule_manager.h"
#include "drt_worker_manager.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#ifdef HPE
#define CONNECT_STAGE2_TIMEOUT_MSEC (120 * MSECONDS_IN_SECOND)
#else
#define CONNECT_STAGE2_TIMEOUT_MSEC (20 * MSECONDS_IN_SECOND)
#endif

struct ScheHandle {
    uint32_t dummy;
};

typedef struct RaInitPara {
    DbMemCtxT *memCtx;
    DrtScheMgrT *scheMgr;
    DrtNodeMgrT *nodeMgr;
    DrtConnMgrT *connMgr;
    WorkerMgrT *workerMgr;
    ScheduleCallback scheCb;
    ScheHandleT scheStartHandle;
    DbLctrT *selfLctr;
    uint32_t uid;
} RaInitParaT;

Status RaInit(DrtRecvAgentT *ra, const RaInitParaT *initPara);
void RaDestroy(DrtRecvAgentT *ra);
void RaRecvAndScheMsg(const DrtRecvAgentT *ra, DrtConnectionT *conn, DrtPipeT *pipe);
void RaLsnrProcess(DrtRecvAgentT *ra, const DrtPipeT *pipe);
void RaRequestEventProc(void *ctx, void *data, uint32_t event);
bool MonitorSendAllConnRemainingMsg(const DrtRecvAgentT *ra, DrtConnMgrT *connMgr);
void DrtProcessClosingConn(DrtRecvAgentT *ra);
void DrtHandleInvalidPipe(DrtScheMgrT *scheMgr, DrtConnectionT *conn);
#ifdef HPE_SIMULATION
void RaProcPeerReset(DrtRecvAgentT *ra, DrtPipeT *pipe, DrtConnectionT *conn);
#endif
#ifdef EXPERIMENTAL_NERGC
void DrtCheckTcpTimeout(const DrtRecvAgentT *ra, DrtConnMgrT *connMgr);
#endif

#ifdef __cplusplus
}
#endif
#endif /* DRT_RECV_AGENT_H */
