/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: drt_node.h
 * Description: header file for runtime node
 * Author:
 * Create: 2020-7-27
 */

#ifndef DRT_NODE_H
#define DRT_NODE_H

#include "drt_base_def.h"
#include "adpt_spinlock.h"
#include "db_dyn_array.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define NODE_ARRAY_SIZE 16

#define DRT_CLIENT_NODE_TYPE_MASKS 0x07

Status DrtInitNodeMgr(DrtNodeMgrT *nodeMgr, DbMemCtxT *memCtx, uint16_t maxSize, uint16_t instanceId);
void DrtDestroyNodeMgr(DrtNodeMgrT *nodeMgr);

DrtNodeT *DrtAllocNode(DrtNodeMgrT *nodeMgr, uint8_t nodeType, const char *nodeName, uint32_t nodeNameLen);
void DrtFreeNode(DrtNodeMgrT *nodeMgr, uint16_t nodeId);

DrtNodeT *DrtAllocNodeWithName(DrtNodeMgrT *nodeMgr, uint8_t nodeType, const char *name);
uint16_t DrtGetNodeId(const DrtNodeT *node);
DrtNodeT *DrtGetNodeById(DrtNodeMgrT *nodeMgr, uint16_t nodeId);
Status DrtGetNodeNameByNodeId(DrtNodeMgrT *nodeMgr, uint16_t nodeId, char *name, uint32_t nameLen);
uint8_t DrtGetFlagByNodeId(DrtNodeMgrT *nodeMgr, uint16_t nodeId);
uint16_t DrtGetConnIdByNodeName(DrtNodeMgrT *nodeMgr, const char *name);
DrtConnectionT *DrtGetAndAttachConnByNodeName(DrtNodeMgrT *nodeMgr, DrtConnMgrT *connMgr, const char *name);

typedef Status (*DrtNodeProcessFunc)(void *arg, DrtNodeT *node);

// 判断是否为订阅连接对应的订阅node，是则返回true，否则返回false
inline static bool DrtIsSubNode(NodeIdT nodeId)
{
    return (nodeId.flag == (uint8_t)GMC_CONN_TYPE_SUB);
}

/*
 * description: 将订阅推送DFX信息添加到 subsInfoMap，目前仅订阅推送的用户回调函数名
 * param {DrtNodeMgrT} *nodeMgr 入参：drt node manager
 * param {DrtNodeSubInfoT} nodeSubInfo 入参：drt node sub dfx info
 */
Status DrtNodeAddSubsInfo(DrtNodeMgrT *nodeMgr, DrtNodeSubInfoT nodeSubInfo);

/*
 * description: 将指定的订阅关系的DFX信息从 subsInfoMap 中删除
 * param {DrtNodeMgrT} *nodeMgr 入参：drt node manager
 * param {uint16_t} connId 入参：connection id
 * param {uint16_t} nodeId 入参：node id
 * param {uint32_t} namespaceId 入参：订阅关系所在的namespace id
 * param {char} *subsName 入参：map的key，指定的订阅关系
 */
void DrtNodeDelSubsInfo(
    DrtNodeMgrT *nodeMgr, uint16_t connId, uint16_t nodeId, uint32_t namespaceId, const char *subsName);

/*
 * description: 从指定node中获取订阅关系DFX字符串；dladdr获取的函数名形似 _Z11CallBackFuncP8GmcStmtTPK14GmcSubMsgInfoTPv
 * 通常前4个字符是前缀（第4个字符是数字），P8及其以后字符为后缀，中间CallBackFunc为函数名
 * param {DrtNodeMgrT} *nodeMgr 入参：drt node manager
 * param {uint16_t} nodeId 入参：指定的node id
 * param {char} *dfxStr 出参：订阅关系DFX字符串，由上层申请和释放
 * param {uint32_t} ret 入参：打印日志时的错误码
 */
void DrtNodeGetSubsInfo(DrtNodeMgrT *nodeMgr, uint16_t nodeId, char *dfxStr, uint32_t dfxStrSize, Status ret);

/*
 * description: 从指定node中获取订阅关系DFX字符串并打印日志
 * 通常前4个字符是前缀（第4个字符是数字），P8及其以后字符为后缀，中间CallBackFunc为函数名
 * param {DrtNodeMgrT} *nodeMgr 入参：drt node manager
 * param {uint16_t} nodeId 入参：指定的node id
 * param {uint32_t} ret 入参：打印日志时的错误码
 */
void DrtNodeLogSubsInfo(DrtNodeMgrT *nodeMgr, uint16_t nodeId, Status ret);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif /* DRT_NODE_H */
