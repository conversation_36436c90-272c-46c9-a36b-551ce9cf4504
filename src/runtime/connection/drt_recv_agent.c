/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: drt_recv_agent.c
 * Description: file content
 * Author: gaohaiyang
 * Create: 2020-08-18
 */
#include "drt_recv_agent.h"
#include "db_alarm.h"
#include "adpt_process_name.h"
#include "drt_log.h"
#include "db_perf_stat.h"
#include "db_msg_buffer.h"
#include "drt_connection_inner.h"
#include "drt_pipe.h"
#include "adpt_sleep.h"
#include "drt_common.h"
#include "drt_instance_inner.h"
#include "adpt_eventfd.h"
#ifdef PERF_SAMPLE_STAT
extern PerfDataT g_serverRecv[MAX_CONN_NUM];
#endif

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define REQUEST_RECV_MAX_TRY_TIMES 30

static void InitComResult(ComInfoT *comInfo, uint8_t result, uint32_t shmCtxId, uint16_t nd)
{
    DB_POINTER(comInfo);
    comInfo->result = result;
    comInfo->endian = CM_IS_BIGENDIUM ? COM_BIG_ENDIAN : COM_LITTLE_ENDIAN,
    comInfo->shmemCtxIdHigh = (uint8_t)(shmCtxId >> DB_8BIT);
    comInfo->shmemCtxIdLow = (uint8_t)shmCtxId;
    comInfo->instanceId = DbGetProcGlobalId();
    comInfo->nd = nd;
}

static void HandshakeError(DrtConnectionT *conn, const DrtPipeT *pipe, uint8_t result, Status ret, const char *errMsg)
{
    const char *auditUserInfo = (conn != NULL ? conn->auditUserInfo : "");
    DB_LOG_AUDIT(auditUserInfo, "connection", DB_AUDIT_DCL, ret, "Cli conn unsucc. send %s rsp.", errMsg);
    uint16_t connId = (conn == NULL) ? DB_INVALID_ID16 : conn->id;
    uint32_t pid = (conn == NULL) ? DB_INVALID_ID32 : conn->pid;
    DB_LOG_ERROR(ret,
        "stage1, srv send %s handshake rsp inv, result:%" PRIu8 ", peerInfo_connId_pipeId:%" PRIu32 "-%s_%" PRIu16
        "_%" PRIu16,
        errMsg, (uint8_t)result, pid, auditUserInfo, connId, pipe->pipeId);
    // 如果连接资源申请成功，需要回收
    if (result == (uint8_t)COM_RESULT_SUCCEED) {
        DB_POINTER(conn);
        DrtUnMonitorPipe(conn->drtPipe);
        DrtSetConnStatus(conn, CONN_STATUS_CLOSING);
    }
}

static void RaRespComResult(DrtConnectionT *conn, DrtPipeT *pipe, uint8_t result)
{
    DB_POINTER(pipe);
    uint32_t shmemCtxId = (conn == NULL) ? DB_INVALID_ID32 : conn->msgMemCtx->ctxId;
    ComInfoT comInfo;
    if (DbIsHpeEnv()) {
        uint16_t nd = (conn == NULL) ? DB_INVALID_ID16 : (uint16_t)conn->connMgr->msgPoolMgr->nd;
        if (SECUREC_UNLIKELY(conn != NULL && conn->connMgr->msgPoolMgr->nd > DB_MAX_UINT16)) {
            DB_LOG_WARN(GMERR_INTERNAL_ERROR, "Hpe nd exceed uint16, nd:%" PRIu32, conn->connMgr->msgPoolMgr->nd);
        }
        InitComResult(&comInfo, result, shmemCtxId, nd);
    } else {
        InitComResult(&comInfo, result, shmemCtxId, 0);
    }
    PipeBufferT pipeBuf = {0};
    pipeBuf.buf = (uint8_t *)&comInfo;
    pipeBuf.shmPtr = *(ShmemPtrT *)(void *)&comInfo;
    pipeBuf.validSize = (uint32_t)sizeof(ComInfoT);
#ifndef NDEBUG
    uint16_t connId = (conn == NULL) ? DB_INVALID_ID16 : conn->id;
    DB_LOG_DEBUG("connId %" PRIu16 " send rsp info, shmemCtxId %" PRIu32 " highCtxId %" PRIu8 " lowCtxId %" PRIu8
                 ", endian %" PRIu8 ", result %" PRIu8,
        connId, shmemCtxId, comInfo.shmemCtxIdHigh, comInfo.shmemCtxIdHigh, comInfo.endian, result);
#endif
    pipe->handshakeTimePoint.sendSecHandshakeRspBegin = DbRdtsc();
    Status ret = DbAdptPipeSend(&pipe->dbPipe, &pipeBuf);
    pipe->handshakeTimePoint.sendSecHandshakeRspEnd = DbRdtsc();
    if (ret != GMERR_OK) {
        HandshakeError(conn, pipe, result, ret, "second");
        return;
    }
#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
    if (result != (uint8_t)COM_RESULT_SUCCEED) {
        // 如果建连失败，不用回第二个包
        return;
    }
    ShmemPtrT msgPoolShmPtr = (conn == NULL) ? DB_INVALID_SHMPTR : conn->connMgr->msgPoolMgr->msgPools[conn->id];
    pipeBuf.shmPtr = (ShmemPtrT){.segId = msgPoolShmPtr.segId, .offset = msgPoolShmPtr.offset};
    pipe->handshakeTimePoint.sendThirdHandshakeRspBegin = DbRdtsc();
    ret = DbAdptPipeSend(&pipe->dbPipe, &pipeBuf);
    pipe->handshakeTimePoint.sendThirdHandshakeRspEnd = DbRdtsc();
    if (ret != GMERR_OK) {
        HandshakeError(conn, pipe, result, ret, "third");
    }
#endif
}

#if !defined(RTOSV2) && !defined(RTOSV2X) && !defined(HPE)
static Status RaRecvMsgHeader(DrtPipeT *pipe, MsgHeaderT **msgHeader)
{
    DB_POINTER2(pipe, msgHeader);
    *msgHeader = NULL;
    FixBufferT *request = &pipe->cacheBuf;
    uint32_t recvdSize = FixBufGetPos(request);
    MsgHeaderT *msgHdr = recvdSize < MSG_HEADER_ALIGN_SIZE ? NULL : RpcPeekMsgHeader(request);
    if (msgHdr != NULL) {
        *msgHeader = msgHdr;
        return GMERR_OK;
    }
    FixBufSetTotalLen(request, MSG_HEADER_ALIGN_SIZE);
    Status ret = DbPipeRecv(&pipe->dbPipe, request, pipe->dbPipe.nonBlock);
    if (ret != GMERR_OK) {
        return ret;
    }
#ifdef SHUTDOWN
    // Db处于停止状态，不再处理新消息
    if (DrtDbServerIsStopped()) {
        DB_LOG_WARN(GMERR_CONNECTION_RESET_BY_PEER, "DRT Db Stopping, refuse req.");
        return GMERR_CONNECTION_RESET_BY_PEER;
    }
#endif
    if (FixBufGetPos(request) != MSG_HEADER_ALIGN_SIZE) {
        return GMERR_REQUEST_TIME_OUT;
    }
    *msgHeader = RpcPeekMsgHeader(request);
    // 如果报文头检验异常，则此连接异常
    DB_POINTER(*msgHeader);
    if ((*msgHeader)->msgMagicNum != MSG_VERIFY_NUMBER) {
        DB_LOG_ERROR(
            GMERR_CONNECTION_RESET_BY_PEER, "msg header verify, msg magic num: %" PRIu16, (*msgHeader)->msgMagicNum);
        return GMERR_CONNECTION_RESET_BY_PEER;
    }
    return GMERR_OK;
}

static Status RaWorkerReadStreamMsg(DrtPipeT *pipe)
{
    DB_POINTER(pipe);
    MsgHeaderT *msgHeader = NULL;
    Status ret = RaRecvMsgHeader(pipe, &msgHeader);
    if (ret != GMERR_OK || msgHeader == NULL) {
        return ret;
    }
    uint32_t msgSize = msgHeader->size;
    FixBufferT *request = &pipe->cacheBuf;
    FixBufSetTotalLen(request, msgSize);
    if (msgSize > MSG_HEADER_ALIGN_SIZE) {
        ret = FixBufExtend(request, msgSize);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret,
                "Fixbuf ext, when recv msg body, pipe %" PRIu16 ", msgSize %" PRIu32 ", recvSize %" PRIu32,
                pipe->pipeId, msgSize, FixBufGetPos(request));
            // msgSize超过程序限制时回收资源
            return (ret == GMERR_PROGRAM_LIMIT_EXCEEDED) ? GMERR_CONNECTION_RESET_BY_PEER : ret;
        }
        ret = DbPipeRecv(&pipe->dbPipe, request, pipe->dbPipe.nonBlock);
        if (ret != GMERR_OK && ret != GMERR_REQUEST_TIME_OUT) {
            DB_LOG_ERROR(ret, "Recv msg body, pipe %" PRIu16 ", msgSize %" PRIu32 ", recvSize %" PRIu32 ".",
                pipe->pipeId, msgSize, FixBufGetPos(request));
        }
        if (ret == GMERR_OK && (FixBufGetPos(request) != FixBufGetTotalLength(request))) {
            ret = GMERR_REQUEST_TIME_OUT;
        }
    }
    return ret;
}
#endif

#if defined(RTOSV2) || defined(RTOSV2X) || defined(HPE)
static Status RaWorkerReadLobMsgLast(DrtPipeT *pipe, FixBufferT *buffer, FixBufferT *tmpBuffer)
{
    Status ret;
    MsgHeaderT *msgHeader = RpcPeekMsgHeader(tmpBuffer);
    uint32_t size = msgHeader->size - MSG_HEADER_ALIGN_SIZE;

    // tmpBuffer需要剥消息头
    uint8_t *srcBuf = FixBufGetBuf(tmpBuffer) + MSG_HEADER_ALIGN_SIZE;
    ret = FixBufPutData(buffer, srcBuf, size);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "copy data to lob msg pack.");
        return ret;
    }
    // 如果tmpBuffer中的isFinish等于true，需要修改buffer中的isFinish标志位
    pipe->recvSeq++;
    if (msgHeader->isFinish) {
        MsgHeaderT *msg = RpcPeekMsgHeader(buffer);
        msg->isFinish = true;
        pipe->recvSeq = 0;
        return GMERR_OK;
    }
    return GMERR_REQUEST_TIME_OUT;
}

static Status RaWorkerReadLobMsgInner(DrtPipeT *pipe, FixBufferT *tmpBuffer)
{
    // 1. 使用tmpBuffer收包
    Status ret = DbPipeRecvWithNotify(&pipe->dbPipe, tmpBuffer, pipe->dbPipe.nonBlock);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 2.拿到消息头、校验消息头序列
    MsgHeaderT *msgHeader = RpcPeekMsgHeader(tmpBuffer);
    if (pipe->recvSeq != msgHeader->seq) {
        // 校验失败，有可能是心跳消息，丢弃心跳。
        OpHeaderT *op = ProtocolPeekFirstOpHeader(tmpBuffer);
        if (op->opCode == MSG_OP_RPC_HEARTBEAT) {
            return GMERR_OK;
        }
        DB_LOG_ERROR(GMERR_CONNECTION_RESET_BY_PEER,
            "check lob msg recv seq, recv seq: %" PRIu16 " ,msg header seq: %" PRIu16, pipe->recvSeq, msgHeader->seq);
        return GMERR_CONNECTION_RESET_BY_PEER;
    }

    // 3.组包,这里拿到的cacheBuf理论上是空的，pos为0，seekpos也为0，buf为NULL.
    FixBufferT *buffer = &pipe->cacheBuf;
    // 3.1 非首包,走组非首包流程，然后返回
    if (pipe->recvSeq != 0) {
        ret = RaWorkerReadLobMsgLast(pipe, buffer, tmpBuffer);
        return ret;
    }
    // 3.2 首包，拷贝完整tmpbuffer到buffer
    uint32_t size = FixBufGetPos(tmpBuffer);
    ret = FixBufPutData(buffer, msgHeader, size);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "copy first recv pack data to lob buff");
        return ret;
    }
    // 4. 序列号增1并判断是否结束
    pipe->recvSeq++;
    if (msgHeader->isFinish) {
        pipe->recvSeq = 0;
        return GMERR_OK;
    }
    // 5. 未接收完一个大报文，返回GMERR_REQUEST_TIME_OUT
    return GMERR_REQUEST_TIME_OUT;
}

static Status RaWorkerReadLobMsg(DrtPipeT *pipe, DbMemCtxT *msgMemCtx)
{
    DB_POINTER2(pipe, msgMemCtx);
    // 创建临时fixbuffer，用于接收报文，接收完就释放,最终报文会放在pipe->cacheBuf上。
    FixBufferT tmpBuffer = {};
    FixBufInit(&tmpBuffer, NULL, 0, 0, FIX_BUF_FLAG_EXTEND_BUFFER, msgMemCtx);
    Status ret = RaWorkerReadLobMsgInner(pipe, &tmpBuffer);
    if (FixBufGetBuf(&tmpBuffer) != NULL) {
        FixBufRelease(&tmpBuffer);
    }
    return ret;
}
#endif

static ALWAYS_INLINE Status RaWorkerReadMsgNormalProc(const DrtRecvAgentT *ra, DrtConnectionT *conn, DrtPipeT *pipe)
{
    FixBufferT *request = &pipe->cacheBuf;
#if defined(MSG_DUMP)
    MsgDebugDump(request, conn->id, false);
#endif

#ifdef EXPERIMENTAL_NERGC
    if (DrtConnFlagConnComplete(conn)) {
        MsgHeaderT *msgHeader = RpcPeekMsgHeader(request);
        if (memcmp(&msgHeader->sessionId, &conn->tcpSessionId, sizeof(SessionIdT)) != 0) {
            char headerHex[SESSION_ID_LEN * SESSION_BINARY_PRINT_LEN + 1] = {0};
            char connHex[SESSION_ID_LEN * SESSION_BINARY_PRINT_LEN + 1] = {0};

            for (int i = 0; i < SESSION_ID_LEN; i++) {
                sprintf_s(headerHex + (i * SESSION_BINARY_PRINT_LEN),
                    sizeof(headerHex) - (i * SESSION_BINARY_PRINT_LEN), "%02x", msgHeader->sessionId.id[i]);
                sprintf_s(connHex + (i * SESSION_BINARY_PRINT_LEN), sizeof(connHex) - (i * SESSION_BINARY_PRINT_LEN),
                    "%02x", conn->tcpSessionId.id[i]);
            }

            OpHeaderT *reqOpHdr = ProtocolPeekFirstOpHeader(request);
            DB_LOG_ERROR(GMERR_CONNECTION_RESET_BY_PEER, "opcode %" PRIu32 ", inv sessionId: %s, conn sessionId: %s",
                reqOpHdr->opCode, headerHex, connHex);
            return GMERR_CONNECTION_RESET_BY_PEER;
        }
    }
#endif
    uint64_t currentCycle = DbGlobalRdtsc();
    conn->stat.connReqProcTime.recvOneReqOkTimestamp = currentCycle;
#ifdef PERF_SAMPLE_STAT
    if (request->buf != NULL) {
        OpHeaderT *reqOpHdr = ProtocolPeekFirstOpHeader(request);
        if (reqOpHdr->opCode == MSG_OP_RPC_REPLACE_VERTEX) {
            uint64_t tsc = DbGlobalRdtsc();
            DbPerfAppendData(g_serverRecv, tsc, conn->id);
        }
    }
#endif
    DrtPipeUpdateStat(pipe, FixBufGetPos(request), false, true, currentCycle);

    if (SECUREC_UNLIKELY(ra->scheMgr->enableResilienceStat)) {
        DrtPipeUpdateThreatStat(pipe, FixBufGetPos(request));
    }
    return GMERR_OK;
}

static ALWAYS_INLINE Status RaWorkerReadMsgFailedProc(
    const DrtRecvAgentT *ra, DrtConnectionT *conn, DrtPipeT *pipe, Status retProc)
{
    if (SECUREC_UNLIKELY(retProc == GMERR_REQUEST_TIME_OUT)) {
        DrtConnCheckOpTimeout(conn);
        return retProc;
    }
    FixBufferT *request = &pipe->cacheBuf;
    DrtConnCheckOpTimeout(conn);
    DrtPipeUpdateStat(pipe, FixBufGetPos(request), false, false, DbRdtsc());
    DB_LOG_ERROR(retProc,
        "Recv msg unsucc, pipe %" PRIu16 ", conn %" PRIu16 ", peer pid %" PRIu32 ", pos %" PRIu32 ", total %" PRIu32
        ", status %" PRId32 ".",
        pipe->pipeId, conn->id, conn->pid, FixBufGetPos(request), FixBufGetTotalLength(request), (int32_t)conn->status);
    if (SECUREC_UNLIKELY(ra->scheMgr->enableResilienceStat)) {
        DrtPipeUpdateThreatStat(pipe, FixBufGetPos(request));
    }
    return retProc;
}

Status RaWorkerReadMsg(const DrtRecvAgentT *ra, DrtConnectionT *conn, DrtPipeT *pipe, bool nonBlock)
{
    DB_POINTER2(conn, pipe);
    Status ret;
#if !defined(RTOSV2) && !defined(RTOSV2X) && !defined(HPE)
    ret = RaWorkerReadStreamMsg(pipe);
#else
    FixBufferT *request = &pipe->cacheBuf;
    if (SECUREC_UNLIKELY(DrtConnFlagIsAsyncLargeObj(conn))) {
        ret = RaWorkerReadLobMsg(pipe, conn->msgMemCtx);
    } else {
        ret = DbPipeRecv(&pipe->dbPipe, request, pipe->dbPipe.nonBlock && nonBlock);
    }
#endif
    if (SECUREC_LIKELY(ret == GMERR_OK)) {
        return RaWorkerReadMsgNormalProc(ra, conn, pipe);
    } else {
        return RaWorkerReadMsgFailedProc(ra, conn, pipe, ret);
    }
}

/* 修改RaWorkerGetRpcReq的返回值为ok，则在RaRecvAndScheMsg中会正常调度一个空消息，
 * 在ConnServiceEntry会将空消息解析成MSG_OP_RPC_DISCONNECT
 */
Status RaGenerateSelfScheDisconnectMsg(void)
{
    return GMERR_OK;
}

static void RaRecvRetry(const DrtConnectionT *conn, DrtPipeT *pipe, FixBufferT *request, Status ret)
{
    pipe->recvRetryTimes++;
    if (pipe->recvRetryTimes % REQUEST_RECV_MAX_TRY_TIMES == 0) {
        DB_LOG_WARN(ret,
            "conn %" PRIu16 " pipe %" PRIu16 " retry %" PRIu8 ", flag %" PRIu32 ", connStat %" PRIu16 ", pos %" PRIu32
            ".",
            conn->id, pipe->pipeId, pipe->recvRetryTimes, conn->flag, (uint16_t)conn->status, FixBufGetPos(request));
    }
}

inline static void ClientExitAbnWithPrepareConn(DrtConnectionT *conn)
{
    // exitStatus如果已有其他值则exitStatus保持不变
    if (conn->exitStatus == CONN_EXIT_NONE) {
        conn->exitStatus = (uint8_t)CONN_EXIT_ABNORMAL_WITH_PREPARE_STAT;
    }
}

Status RaWorkerHandleReadFail(
    Status readFailRet, DrtConnectionT *conn, DrtPipeT *pipe, FixBufferT *request, uint32_t pos)
{
    DB_POINTER2(conn, pipe);
    if (SECUREC_UNLIKELY(readFailRet == GMERR_CONNECTION_RESET_BY_PEER)) {
        DB_LOG_ERROR(readFailRet,
            "conn %" PRIu16 ", peer pid %" PRIu32 "-%s, pipe %" PRIu16 " broken, flag %" PRIu32 ", exitStat %" PRIu8
            ", connStat %" PRIu16 ", pos %" PRIu32 ".",
            conn->id, conn->pid, conn->auditUserInfo, pipe->pipeId, conn->flag, (uint8_t)conn->exitStatus,
            (uint16_t)conn->status, FixBufGetPos(request));
        if (conn->status == CONN_STATUS_PREPARE) {
            FixBufRelease(request);
            DrtUnMonitorPipe(conn->drtPipe);
            DrtSetConnStatus(conn, CONN_STATUS_CLOSING);
            ClientExitAbnWithPrepareConn(conn);
        } else {
            /* 触发自调度，走异常断连流程 */
            FixBufRelease(request);
            return RaGenerateSelfScheDisconnectMsg();
        }
    }

    if (SECUREC_UNLIKELY(readFailRet == GMERR_REQUEST_TIME_OUT && FixBufGetPos(request) == pos && pos > 0 &&
                         pos != FixBufGetTotalLength(request))) {
        RaRecvRetry(conn, pipe, request, GMERR_REQUEST_TIME_OUT);
    } else {
        pipe->recvRetryTimes = 0;
    }
    return readFailRet;
}

static inline uint32_t DrtAllowRecvMsgInThisRound(DrtScheMgrT *scheMgr, uint16_t connId)
{
    DB_POINTER(scheMgr);
    return NOT_RECV_PROC_CTX_MAX - DrtScheduleListGetUsedProcCtx(&scheMgr->scheProcCtx[connId]->scheList.procCtxPool);
}

static inline bool DrtScheduleNotRecvMsgInThisRound(DrtScheMgrT *scheMgr, uint16_t connId)
{
    DB_POINTER(scheMgr);
    // 不加锁，因为在调度执行逻辑里，scheProcCtx当前必然存在
    return DrtScheduleListGetUsedProcCtx(&scheMgr->scheProcCtx[connId]->scheList.procCtxPool) >= NOT_RECV_PROC_CTX_MAX;
}

Status RaWorkerGetRpcReq(const DrtRecvAgentT *ra, DrtConnectionT *conn, DrtPipeT *pipe)
{
    DB_POINTER3(conn, pipe, conn->connMgr);
    FixBufferT *request = &pipe->cacheBuf;
    uint32_t pos = FixBufGetPos(request);
    if (SECUREC_UNLIKELY(DbIsEulerEnv() && request->buf == NULL)) {
        uint32_t flags = DrtConnFlagIsLargeObj(conn) ? FIX_BUF_FLAG_LOB_BUFFER : FIX_BUF_FLAG_EXTEND_BUFFER;
        /* 动态通信内存申请，调度结束，发送应答时释放，或断连时msgMemCtx保底释放，DrtComplete */
        Status ret = FixBufCreate(request, conn->msgMemCtx, CS_PACK_SIZE, flags);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_ERROR(ret, "create fixbuf, conn %" PRIu16, conn->id);
            return ret;
        }
    }

    // channel通信下+大报文需要创建fixbuffer
    if (SECUREC_UNLIKELY(DbIsHpeEnv() && DrtConnFlagIsAsyncLargeObj(conn) && request->buf == NULL)) {
        /* 共享通信内存申请，客户端消费后释放，或断连时msgMemCtx保底释放，DrtComplete */
        Status ret = FixBufCreate(request, conn->connMgr->memctx, CS_PACK_SIZE, FIX_BUF_FLAG_LOB_BUFFER);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "create lob fixbuf, conn: %" PRIu16, conn->id);
            return ret;
        }
    }

    Status ret = RaWorkerReadMsg(ra, conn, pipe, false);
    if (SECUREC_LIKELY(ret == GMERR_OK)) {
        // 收消息成功，但消息非法，走正常断连流程，断连类型为异常报文断连
        if (SECUREC_LIKELY(!conn->connMgr->msgSecurityCheck)) {
            return ret;
        }
        if (RpcMsgIsValid(request)) {
            return ret;
        }

        (void)DbAtomicInc64(&conn->connMgr->connProbeData.invalidMsgNum);
        DrtConnCloseAbnormalPacket(conn);
        ret = GMERR_CONNECTION_RESET_BY_PEER;
    }

    return RaWorkerHandleReadFail(ret, conn, pipe, request, pos);
}

DB_DEF_PERF_STAT_POINT(ServerPorcCost);
static inline void RaScheMsg(const DrtRecvAgentT *ra, DrtConnectionT *conn, DrtPipeT *pipe)
{
    DB_POINTER3(ra, conn, pipe);
    DB_START_TEST_CPU_CYCLES(ServerPorcCost);
    FixBufferT request;
    FixBufMove(&pipe->cacheBuf, &request);
    DB_POINTER(ra->scheCb);
    // SmScheduleOneMsg
    ra->scheCb(ra->scheMgr, conn, &request);
}

void RaRecvAndScheMsg(const DrtRecvAgentT *ra, DrtConnectionT *conn, DrtPipeT *pipe)
{
    DB_POINTER5(ra, conn, pipe, ra->scheMgr, conn->connMgr);
    Status ret = RaWorkerGetRpcReq(ra, conn, pipe);
    if (SECUREC_LIKELY(ret == GMERR_OK)) {
        RaScheMsg(ra, conn, pipe);
    } else if (!DbIsEulerEnv() && ret == GMERR_REQUEST_TIME_OUT && conn->connMgr->msgPoolMgr != NULL &&
               SmScheListIsEmpty(&ra->scheMgr->scheProcCtx[conn->id]->scheList)) {
        if (!DrtConnFlagIsAsyncLargeObj(conn) && !DrtConnFlagIsResendRsp(conn)) {
            FixBufRelease(&conn->drtPipe->cacheBuf);
            FixBufRelease(&conn->rspCacheBuf);
        }
        SharedMsgPoolT *msgPool = DbShmPtrToAddr(conn->connMgr->msgPoolMgr->msgPools[conn->id]);
        if (msgPool != NULL) {
            SharedMsgPoolResetRespond(msgPool);
        }
    }
}

typedef struct RaConnectContext {
    DrtConnMgrT *connMgr;
    DrtConnectionT *conn;
    DrtPipeT *newPipe;
    uint16_t port;
    char peerInfoBuf[PIPE_PEER_INFO_LEN];
    DbCredT pipeCred;
    char ip[MAX_IP_ADDR_LEN];
    char auditUserInfo[DB_AUDIT_USER_INFO];
} RaConnectContextT;

static DrtConnectionT *RaPrepareConnection(const DrtRecvAgentT *ra, RaConnectContextT *connCtx)
{
    DB_POINTER2(ra, connCtx);
    DrtPipeT *newPipe = connCtx->newPipe;
    char *auditUserInfo = connCtx->auditUserInfo;
    DbCredT *credInfo = &connCtx->pipeCred;

    DrtConnectionT *conn = DrtAllocConnection(ra->connMgr, auditUserInfo, credInfo);
    if (conn == NULL) {
        DbAlarmUpdateFailCnt(DB_ALARM_CONNECTION_NUMBER, 1);
        char buf[PIPE_PEER_INFO_LEN] = {0};
        DbPipeGetPipeInfo(&newPipe->dbPipe, buf, PIPE_PEER_INFO_LEN);
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "stage1, alloc conn for new pipe: %s, peer info:%" PRIu32 "-%s.", buf,
            credInfo->pid, auditUserInfo);
        // 打印占用连接最多的TOP3进程及连接数
        DbRWSpinRLock(&ra->connMgr->lock);
        DrtPrintTopProcessMsg(ra->connMgr);
        DbRWSpinRUnlock(&ra->connMgr->lock);
        DB_LOG_AUDIT(
            auditUserInfo, "connection", DB_AUDIT_DCL, GMERR_NO_DATA, "Cli conn unsucc. Server not alloc conn.");
        return NULL;
    }
    DrtConnBindPipe(conn, newPipe);
    conn->port = connCtx->port;
    (void)memcpy_s(conn->ip, MAX_IP_ADDR_LEN, connCtx->ip, MAX_IP_ADDR_LEN);
#if defined(RTOSV2X) || defined(RTOSV2) || defined(HPE)
    Status ret = SharedMsgPoolCreate(ra->connMgr->msgPoolMgr, conn->id, conn->msgMemCtx);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "stage1, create share msg pool for conn %" PRIu16 ", cli %" PRIu32 "-%s with memctx %" PRIu32,
            conn->id, credInfo->pid, auditUserInfo, conn->msgMemCtx->ctxId);
        return NULL;
    }
#endif
    return conn;
}

ALWAYS_INLINE static Status RaReadRpcMsgNonBlock(const DrtRecvAgentT *ra, DrtConnectionT *conn, DrtPipeT *pipe)
{
    DB_POINTER4(conn, conn->msgMemCtx, conn->connMgr, pipe);

    FixBufferT *request = &pipe->cacheBuf;

#if !defined(RTOSV2) && !defined(RTOSV2X) && !defined(HPE)
    if (SECUREC_UNLIKELY(request->buf == NULL)) {
        uint32_t flags = DrtConnFlagIsLargeObj(conn) ? FIX_BUF_FLAG_LOB_BUFFER : FIX_BUF_FLAG_EXTEND_BUFFER;
        Status ret = FixBufCreate(request, conn->msgMemCtx, CS_PACK_SIZE, flags);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
#endif

    Status ret = RaWorkerReadMsg(ra, conn, pipe, true);
    if (SECUREC_LIKELY(ret == GMERR_OK && !conn->connMgr->msgSecurityCheck) || ret == GMERR_REQUEST_TIME_OUT) {
        return ret;
    } else if (ret == GMERR_OK && conn->connMgr->msgSecurityCheck) {
#ifdef HPE_SIMULATION
        if (FixBufIsFree(request)) {
            DB_LOG_EMRG(GMERR_REQUEST_TIME_OUT,
                "sim recv msg free, conn %" PRIu16 ", stat %" PRIu32 ", flag %" PRIu32 ", exitStat %" PRIu32
                ", ref %" PRIu32 "",
                conn->id, conn->status, conn->flag, conn->exitStatus, conn->ref);
            FixBufZeroOut(request);
            return GMERR_REQUEST_TIME_OUT;
        }
#endif
        if (!RpcMsgIsValid(request)) {
            DrtConnCloseAbnormalPacket(conn);
            ret = GMERR_CONNECTION_RESET_BY_PEER;
        }
    }

    return ret;
}

static void RaDispatcherRequest(const DrtRecvAgentT *ra, DrtConnectionT *conn, FixBufferT *request)
{
    DB_POINTER2(ra, conn);
    DrtConnUpdateTimeStamp(conn);
    Status ret;
    switch (conn->status) {
        case CONN_STATUS_NORMAL:
        case CONN_STATUS_PREPARE:
        case CONN_STATUS_REJECT:
        case CONN_STATUS_INVALID:
            DB_POINTER(ra->scheCb);
            // SmScheduleOneMsg
            ret = ra->scheCb(ra->scheMgr, conn, request);
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "Ra push msg to conn %" PRIu16 " sche list", conn->id);
                DrtFreeRequest(conn, request);
            }
            break;
        case CONN_STATUS_CLOSING:  // closing状态不可能从外部设置后，又进到service执行
            // fuzz场景下，事件挂到
            // workerMgr上以后，存在并发修改conn->status为closing的情况，这里正常处理即可，不用断言
            DrtUnMonitorPipe(conn->drtPipe);
            DrtFreeRequest(conn, request);
            DB_LOG_ERROR(
                GMERR_INTERNAL_ERROR, "conn %" PRIu16 " unexpected, stat %" PRId32, conn->id, (int32_t)conn->status);
            break;
        case CONN_STATUS_CLOSED:
        case CONN_STATUS_INIT:
        default:
            DB_ASSERT(0);  // 以上分支理论上不应该走到，debug版本提前看护
            DrtUnMonitorPipe(conn->drtPipe);
            DrtFreeRequest(conn, request);
            DB_LOG_ERROR(
                GMERR_INTERNAL_ERROR, "conn %" PRIu16 " unexpected, stat %" PRId32, conn->id, (int32_t)conn->status);
            break;
    }
}

// 当前该事件只需要唤醒后，在RaLsnrProcess走到DrtProcessClosingConn即可。
void DrtGcEventProc(void *ctx, void *data, uint32_t event)
{
    DB_POINTER2(ctx, data);
    DrtRecvAgentT *ra = (DrtRecvAgentT *)ctx;
    DrtConnMgrT *connMgr = (DrtConnMgrT *)data;
#if defined(HPE)
    DbNotifyFdRead(connMgr->fd);
#else
    eventfd_t value;
    DbAdptEventFdRead(connMgr->fd, &value);
#endif
    // 尽量早回收，链接满后gc event后面有新建链请求
    DrtProcessClosingConn(ra);
    DrtConnProcessClosedList(connMgr);
}

// 收到断连请求
static ALWAYS_INLINE bool RecvDisConnectRequest(const DrtRecvAgentT *ra, DrtPipeT *pipe, DrtConnectionT *conn)
{
    DB_POINTER(pipe);
    OpHeaderT *opHeader = ProtocolPeekFirstOpHeader(&pipe->cacheBuf);
    DB_POINTER(opHeader);
    // 第一次接收到断链请求后，del event，后续所有事件不再处理
    if (SECUREC_UNLIKELY(opHeader->opCode == MSG_OP_RPC_DISCONNECT)) {
        DrtUnMonitorPipe(pipe);
        if (!DrtConnFlagIsRecycle(conn)) {
            DbSpinLock(&conn->lock);
            if (!DrtConnFlagIsRecycle(conn)) {
                DrtConnFlagSetRecycle(conn);
                DB_ASSERT(pipe->monitor == NULL);
                DB_ASSERT(conn->status == CONN_STATUS_NORMAL || conn->status == CONN_STATUS_INVALID ||
                          conn->status == CONN_STATUS_REJECT);
                if (ra->scheMgr->scheMode == SCHEDULE_THREAD_POOL) {
                    DrtDetachConnection(conn);  // 3->2
                }
            }
            DbSpinUnlock(&conn->lock);
        }
        return true;
    }
    return false;
}

// 对端异常退出
static bool PeerAbnormalExit(const DrtRecvAgentT *ra, DrtPipeT *pipe, DrtConnectionT *conn)
{
    // 第一次收到reset by peer，del event，两种场景，Ra中二阶段建链、链接已经normal
    DB_LOG_ERROR(GMERR_CONNECTION_RESET_BY_PEER,
        "conn %" PRIu16 " peer(pid:%" PRIu32 ") unusual exit with stat %" PRId32 ", ref: %" PRIu32 ".", conn->id,
        conn->pid, (int32_t)conn->status, conn->ref);
    // fuzz 场景下，并发二阶段建连存在 status 为 closing 状态的情况，下面正常处理
    bool needDispatchRequest = false;
    DrtUnMonitorPipe(pipe);
    if (!DrtConnFlagIsRecycle(conn)) {
        DbSpinLock(&conn->lock);
        if (!DrtConnFlagIsRecycle(conn)) {
            needDispatchRequest = true;
            DrtConnFlagSetRecycle(conn);
            DB_ASSERT(pipe->monitor == NULL);
            if (ra->scheMgr->scheMode == SCHEDULE_THREAD_POOL) {
                DrtDetachConnection(conn);
            }
        }
        DbSpinUnlock(&conn->lock);
    }
    // 构造自调度任务，设置链接状态，清理workerRef
    FixBufRelease(&conn->drtPipe->cacheBuf);
    return needDispatchRequest;
}

void RaProcPeerReset(DrtRecvAgentT *ra, DrtPipeT *pipe, DrtConnectionT *conn)
{
    DB_POINTER3(ra, pipe, conn);
    if (!PeerAbnormalExit(ra, pipe, conn)) {
        return;
    }
    FixBufferT request = {0};
    DB_ASSERT(ra->scheMgr->scheProcCtx[conn->id]->conn != NULL);
    RaDispatcherRequest(ra, conn, &request);
}

#if (!defined(RTOSV2) && !defined(RTOSV2X) && !defined(HPE)) || defined(HPE_SIMULATION)
static ALWAYS_INLINE void RequestEventProc(DrtRecvAgentT *ra, DrtConnectionT *conn, uint32_t event)
{
    DB_POINTER3(ra, conn, ra->scheMgr);
    DrtPipeT *pipe = conn->drtPipe;
    DbPipeMonitorT *monitor = (DbPipeMonitorT *)conn->drtPipe->monitor;
    DB_ASSERT(ra == (DrtRecvAgentT *)monitor->userCtx);
    if (DrtScheduleNotRecvMsgInThisRound(ra->scheMgr, conn->id)) {
        DB_LOG_WARN(GMERR_PROGRAM_LIMIT_EXCEEDED, "conn %" PRIu16 " type %" PRIu32 " flag %" PRId32 " not recv msg.",
            conn->id, (uint32_t)conn->nodeId.flag, conn->flag);
        return;
    }
    Status ret = RaReadRpcMsgNonBlock(ra, conn, pipe);
    if (DrtConnFlagIsRecycle(conn)) {
        return;
    }
    if (SECUREC_LIKELY(ret == GMERR_OK)) {
        (void)RecvDisConnectRequest(ra, pipe, conn);
        FixBufferT request;
        FixBufMove(&conn->drtPipe->cacheBuf, &request);
        DB_ASSERT(ra->scheMgr->scheProcCtx[conn->id]->conn != NULL);
        RaDispatcherRequest(ra, conn, &request);
        return;
    }
    if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
        RaProcPeerReset(ra, pipe, conn);
    }
}
#else
// ra->proc closing, singleWorker->sm wrapper, lsnr 自己减connRef
static ALWAYS_INLINE void RequestEventProc(DrtRecvAgentT *ra, DrtConnectionT *conn, uint32_t event)
{
    DB_POINTER4(ra, conn, ra->scheMgr, conn->drtPipe);
    DrtPipeT *pipe = conn->drtPipe;
    if (DrtConnFlagIsRecycle(conn)) {
        return;
    }
    if (SECUREC_UNLIKELY(event == DB_PIPE_MONITOR_EVENT_ERR)) {
        DrtConnFlagSetHup(conn);
        bool needDispatchRequest = PeerAbnormalExit(ra, pipe, conn);
        if (!needDispatchRequest) {
            return;
        }
        FixBufferT request = {0};
        DB_ASSERT(ra->scheMgr->scheProcCtx[conn->id]->conn != NULL);
        RaDispatcherRequest(ra, conn, &request);
        return;
    }

    int32_t itemNum = DbChanGetItemNum(&pipe->dbPipe);
    int32_t recvNum = DB_MIN(itemNum, (int32_t)DrtAllowRecvMsgInThisRound(ra->scheMgr, conn->id));
    bool isDisconnect = false;
    for (int32_t i = 0; (i < recvNum && !isDisconnect); i++) {
        Status ret = RaReadRpcMsgNonBlock(ra, conn, pipe);
        // 大报文需要分批多次接收会报16004,不影响后续报文接收
        if (SECUREC_UNLIKELY(ret == GMERR_REQUEST_TIME_OUT)) {
            continue;
        } else if (SECUREC_UNLIKELY(ret != GMERR_OK || FixBufGetBuf(&pipe->cacheBuf) == NULL)) {
            DB_LOG_ERROR(ret,
                "conn %" PRIu16 " type %" PRIu32 " proc No.%" PRId32 " msg, recv %" PRId32 ", total %" PRId32
                ", stat %" PRIu32 ", exitStat %" PRIu32 ", flag %" PRIu32 ", ref %" PRIu32 ", buf %s, pos %" PRIu32 ".",
                conn->id, (uint32_t)conn->nodeId.flag, i, recvNum, itemNum, (uint32_t)conn->status,
                (uint32_t)conn->exitStatus, conn->flag, conn->ref,
                FixBufGetBuf(&pipe->cacheBuf) == NULL ? "is NULL" : "not NULL", FixBufGetPos(&pipe->cacheBuf));
            return;
        }
        isDisconnect = RecvDisConnectRequest(ra, pipe, conn);
        FixBufferT request;
        FixBufMove(&pipe->cacheBuf, &request);
        DB_ASSERT(ra->scheMgr->scheProcCtx[conn->id]->conn != NULL);
        RaDispatcherRequest(ra, conn, &request);
    }

    // 新消息肯定有事件唤醒，不调DbChanGetItemNum过度唤醒
    if (recvNum < itemNum && !isDisconnect) {
        // HPE下需要补偿事件，让monitor下次继续唤醒
        (void)DbVnotifySelf(DbPipeGetEventFd(&pipe->dbPipe));
    }
}
#endif

void RaRequestEventProc(void *ctx, void *data, uint32_t event)
{
    DB_POINTER2(ctx, data);
    DB_ASSERT(event != DB_PIPE_MONITOR_EVENT_WRITE);
    DrtRecvAgentT *ra = (DrtRecvAgentT *)ctx;
    DrtConnectionT *conn = (DrtConnectionT *)data;
    DrtPipeT *pipe = conn->drtPipe;
    DB_POINTER(pipe);  // RaRef未释放，因此conn及其上面的资源必然仍存在
    if (SECUREC_LIKELY(conn->status == CONN_STATUS_NORMAL || conn->status == CONN_STATUS_REJECT ||
                       conn->status == CONN_STATUS_INVALID)) {
        RequestEventProc(ra, conn, event);
        return;
    }
    if (DbIsHpeEnv() && conn->status == CONN_STATUS_PREPARE) {  // prepare状态需要补偿
        Status ret = DbVnotifySelf(DbPipeGetEventFd(&pipe->dbPipe));
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "conn %" PRIu16 " notify itself, stat %" PRId32 ", eventfd %" PRId32 "",
                conn->id, (int32_t)conn->status, (int32_t)DbPipeGetEventFd(&pipe->dbPipe));
        }
    }
}

static Status RaGetUserInfoFromPipe(const DbPipeT *dbPipe, DbCredT *cred, char *auditUserInfo, uint32_t len)
{
    DB_POINTER3(cred, auditUserInfo, dbPipe);

    Status ret = DbPipeGetCred(dbPipe, cred);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "stage1, get pipe cred.");
        return ret;
    }
    char procName[MAX_OS_USER_NAME_LENGTH] = {0};
    ret = DbGetProcessNameByPid(cred->pid, procName, sizeof(procName));
    if (ret != GMERR_OK) {
        DB_LOG_WARN(ret, "stage1, get proc name, uid-pid:%" PRIu32 "-%" PRIu32 ", ret: %" PRId32 ", os: %" PRId32,
            cred->uid, cred->pid, ret, DbAptGetErrno());
        return ret;
    }

    int32_t err = snprintf_s(auditUserInfo, len, len - 1, "%" PRIu32 "-%s", cred->uid, procName);
    if (err < 0) {
        DB_LOG_ERROR(GMERR_FIELD_OVERFLOW,
            "stage1, copy audit user info name, uid-pid-procName:%" PRIu32 "-%" PRIu32 "-%s, no%" PRId32, cred->uid,
            cred->pid, procName, err);
        return GMERR_FIELD_OVERFLOW;
    }
    return GMERR_OK;
}

static Status RaAcceptNewPipe(DrtRecvAgentT *ra, const DrtPipeT *lsnrPipe, RaConnectContextT *connCtx)
{
    DB_POINTER4(ra, lsnrPipe, connCtx, ra->connMgr);
    DbPipeT dbPipe;
    ra->recvCount++;
    PipeAcceptParamT param = {
        .nonBlock = false,
        .needSetLoop = SmScheNeedSetBusyLoop(ra->scheMgr),
        .recvCount = ra->recvCount,
    };
    Status ret = DbPipeAccept(&lsnrPipe->dbPipe, &dbPipe, param);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (ra->connMgr->type != LCTR_TYPE_TCP) {
        ret = RaGetUserInfoFromPipe(&dbPipe, &connCtx->pipeCred, connCtx->auditUserInfo, DB_AUDIT_USER_INFO);
        if (ret != GMERR_OK) {
            DB_LOG_AUDIT("", "pipe", DB_AUDIT_DCL, ret, "Cli conn unsucc. Srv not get pipe cred info.");
            DbPipeClose(&dbPipe);
            return ret;
        }
    } else {
        ret = DbGetPeerName(&dbPipe, connCtx->ip, sizeof(connCtx->ip), &connCtx->port);
    }
    DrtPipeT *pipe = DrtAllocPipe(&ra->connMgr->pipeList);
    if (pipe == NULL) {
        DbPipeClose(&dbPipe);
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "stage1, alloc new pipe %" PRIu32 "-%s", connCtx->pipeCred.pid,
            connCtx->auditUserInfo);
        DB_LOG_AUDIT(connCtx->auditUserInfo, "pipe", DB_AUDIT_DCL, GMERR_MEMORY_OPERATE_FAILED,
            "Cli connects unsucc. Srv not alloc pipe.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    pipe->dbPipe = dbPipe;
    ret = DrtPipeGetBuffsize(pipe);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "stage1, get pipe %" PRIu16 " bufSize. %" PRIu32 "-%s", pipe->pipeId, connCtx->pipeCred.pid,
            connCtx->auditUserInfo);
        DB_LOG_AUDIT(
            connCtx->auditUserInfo, "pipe", DB_AUDIT_DCL, ret, "Cli conn unsucc. Srv not get pipe buffer size.");
        DrtClosePipe(&ra->connMgr->pipeList, pipe);
        return ret;
    }
    connCtx->newPipe = pipe;
    return GMERR_OK;
}

static void RaProcessConnectFailed(
    DrtRecvAgentT *ra, RaConnectContextT *ctx, Status errcode, uint8_t result, const char *desc)
{
    DB_POINTER4(ctx, ctx->connMgr, ctx->newPipe, desc);
    DrtConnectionT *conn = ctx->conn;
    uint16_t connId = conn != NULL ? conn->id : (uint16_t)DB_INVALID_ID16;
    uint32_t pid = conn != NULL ? conn->pid : (uint16_t)DB_INVALID_ID32;
    const char *auditUserInfo = conn != NULL ? conn->auditUserInfo : "";
    RaRespComResult(conn, ctx->newPipe, result);
    DB_LOG_ERROR(
        errcode, "DRT conn %" PRIu16 " %s for new pipe: %" PRIu32 "-%s unsucc!.", connId, desc, pid, ctx->peerInfoBuf);
    DB_LOG_AUDIT(auditUserInfo, "connection", DB_AUDIT_DCL, errcode, "Conn %" PRIu16 " %s for new pipe: %s unsucc!.",
        connId, desc, ctx->peerInfoBuf);
    DrtClosePipe(&ctx->connMgr->pipeList, ctx->newPipe);
    ctx->newPipe = NULL;
    if (conn != NULL) {
        conn->drtPipe = NULL;
        DrtSetConnStatus(conn, CONN_STATUS_CLOSING);
        DrtProcessClosingConn(ra);
    }
}

// 从这个接口开始，所有失败都要unMonitor：recv->dispatch->service(conn/disconn/reset by peer)
void RaProcConnectStage2Event(void *ctx, void *data, uint32_t event)
{
    DB_POINTER2(ctx, data);
    DrtKeepThreadAlive(GetThisWorkerId());
    DrtRecvAgentT *ra = (DrtRecvAgentT *)ctx;
    DrtConnectionT *conn = (DrtConnectionT *)data;
    DrtPipeT *drtPipe = conn->drtPipe;
    DB_POINTER2(drtPipe, ra->scheMgr);  // 二阶段在Ra处理，此时ra对conn的ref = 1，因此必不为空
    DbPipeMonitorT *monitor = (DbPipeMonitorT *)drtPipe->monitor;
    DB_ASSERT(ctx == monitor->userCtx);
    DB_ASSERT(conn->ref == 1);
    DB_ASSERT(event != DB_PIPE_MONITOR_EVENT_WRITE);

    // 对端异常退出场景：需要停止调度，停止接受新链接，清理conn资源
    if (event == DB_PIPE_MONITOR_EVENT_ERR) {
        DrtConnFlagSetHup(conn);
        DB_LOG_ERROR(GMERR_CONNECTION_RESET_BY_PEER,
            "stage2, cli exit, connId-Type: %" PRIu16 "-%" PRIu8 ", client pid-uid-name: %" PRIu32 "-%s", conn->id,
            conn->nodeId.flag, conn->pid, conn->auditUserInfo);
        DrtUnMonitorPipe(drtPipe);  // del event from Ra
        DrtSetConnStatus(conn, CONN_STATUS_CLOSING);
        DB_ASSERT(conn->ref == 1);
        return;
    }

    // 非阻塞收二阶段建连消息
    Status ret = RaReadRpcMsgNonBlock(ra, conn, drtPipe);
    if (ret != GMERR_OK) {
        DrtUnMonitorPipe(drtPipe);  // del event from Ra
        DrtSetConnStatus(conn, CONN_STATUS_CLOSING);
        DB_LOG_ERROR(ret, "stage2, recv inv, connId-Type: %" PRIu16 "-%" PRIu8 ", cli pid-uid-name: %" PRIu32 "-%s",
            conn->id, conn->nodeId.flag, conn->pid, conn->auditUserInfo);
        return;
    }
    // 二阶段建连消息接收成功，对消息解析，如果从消息中获取数据，失败不应答
    FixBufferT request = {0, 0, 0, FIX_BUF_FLAG_EXTEND_BUFFER, NULL, conn->msgMemCtx};
    FixBufMove(&conn->drtPipe->cacheBuf, &request);
    // 调度并处理请求
    DB_ASSERT(ra->scheMgr->scheProcCtx[conn->id]->conn != NULL);
    // 设置线程变量存储位置并切环境变量
    DbSetGlobalTlsArrayAndSwap(conn->threadPoolTlsArray);
    RaDispatcherRequest(ra, conn, &request);
}

// 本接口内失败不detach，由RaProcessConnectFailed统一处理
static Status RaProcPreparePipe(DrtRecvAgentT *ra, RaConnectContextT *connCtx)
{
    DB_POINTER2(ra, connCtx);
    Status ret;
    DbPipeT *pipe = &connCtx->newPipe->dbPipe;
    if (ra->scheMgr->scheMode == SCHEDULE_DIRECTELY) {
        ret = DbPipeSetRecvTimeout(pipe, DRT_MAX_RECV_TIMEOUTMS);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "set recv timeout %" PRIu32 ", stage1, pipeId %" PRIu16, DRT_MAX_RECV_TIMEOUTMS,
                connCtx->newPipe->pipeId);
            return ret;
        }
        ret = DbPipeSetSendTimeout(pipe, CS_SEND_TIMEOUT_MS);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "set send timeout %" PRIu32 ", stage1, pipeId %" PRIu16, CS_SEND_TIMEOUT_MS,
                connCtx->newPipe->pipeId);
        }
        return ret;
    }
    ret = DbPipeSetNoBlock(pipe, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "set pipe no block mode %" PRIu32 ", stage1, pipeId %" PRIu16,
            (uint32_t)ra->scheMgr->scheMode, connCtx->newPipe->pipeId);
        return ret;
    }
    DbPipeMonitorEventParaT eventPara = {connCtx->conn, RaProcConnectStage2Event, DB_PIPE_MONITOR_EVENT_READ};
    DbPipeSetEtEvent(&eventPara);
    ret = DbPipeMonitorAddEvent(ra->monitor, pipe, &eventPara);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "pipe add event, stage1, pipeId %" PRIu16, connCtx->newPipe->pipeId);
        return ret;
    }
    connCtx->newPipe->monitor = (void *)ra->monitor;
    return ret;
}

inline static uint64_t CalHandshakeTimeUsed(uint64_t endCycle, uint64_t beginCycle)
{
    if (endCycle == 0 || beginCycle == 0 || endCycle < beginCycle) {
        return 0;
    }
    return DbToMseconds(endCycle - beginCycle);
}

// 建连握手阶段应答发送时间超过 timeThresholdMs 则记录日志
void RaCheckHandshakeTimeUsed(const DrtConnectionT *conn, DrtPipeT *drtPipe, uint64_t timeThresholdMs)
{
    DB_POINTER2(conn, drtPipe);
    ConnHandshakeDfxT *pipeDfx = &drtPipe->dbPipe.handshakeInfo;
    DrtConnHandshakeTimePointT *drtPipeDfx = &drtPipe->handshakeTimePoint;
    // [收到建连握手请求，发送第一次握手应答完成]
    uint64_t timeUsed1 = CalHandshakeTimeUsed(pipeDfx->sendHandshakeRsqTimestamp, pipeDfx->recvHandshakeReqTimestamp);

    // [发送第一次握手应答完成, 发送第二次握手应答开始]
    uint64_t timeUsed2 = CalHandshakeTimeUsed(drtPipeDfx->sendSecHandshakeRspBegin, pipeDfx->sendHandshakeRsqTimestamp);

    // [发送第二次握手应答开始, 发送第二次握手应答结束]
    uint64_t timeUsed3 = CalHandshakeTimeUsed(drtPipeDfx->sendSecHandshakeRspEnd, drtPipeDfx->sendSecHandshakeRspBegin);

    // [发送第三次握手应答开始, 发送第三次握手应答结束]
    uint64_t timeUsed4 =
        CalHandshakeTimeUsed(drtPipeDfx->sendThirdHandshakeRspEnd, drtPipeDfx->sendThirdHandshakeRspBegin);

    // [发送第一次握手应答完成, 发送第四次握手应答结束]
    uint64_t timeUsed5 = CalHandshakeTimeUsed(drtPipeDfx->sendThirdHandshakeRspEnd, pipeDfx->sendHandshakeRsqTimestamp);
    if (timeUsed1 > timeThresholdMs || timeUsed2 > timeThresholdMs || timeUsed3 > timeThresholdMs ||
        timeUsed4 > timeThresholdMs || timeUsed5 > timeThresholdMs) {
        DB_LOG_WARN(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE,
            "stage1, handshake too long. connId %" PRIu16 ", pipeId %" PRIu16 ", remain %" PRId32
            " handshake req, cliInfo: %" PRIu32 "-%s, time "
            "used(ms):(%" PRIu64 ",%" PRIu64 ",%" PRIu64 ",%" PRIu64 ",%" PRIu64 ")",
            conn->id, DrtGetPipeId(drtPipe), pipeDfx->remainHandshakeReq, conn->pid, conn->auditUserInfo, timeUsed1,
            timeUsed2, timeUsed3, timeUsed4, timeUsed5);
    }
    *pipeDfx = (ConnHandshakeDfxT){0};
    *drtPipeDfx = (DrtConnHandshakeTimePointT){0};
}

static void RaProcessConnectTimeout(DrtConnectionT *conn, const DrtRecvAgentT *ra)
{
    DB_POINTER(conn);
    uint64_t nowTime = DbRdtsc();
    uint64_t timeDuration = DbToMseconds(nowTime - conn->timeStamp);
    if (timeDuration >= CONNECT_STAGE2_TIMEOUT_MSEC) {
        char processName[MAX_OS_USER_NAME_LENGTH] = {0};
        (void)DbGetProcessNameByPid(conn->pid, processName, sizeof(processName));
        DB_LOG_ERROR(GMERR_REQUEST_TIME_OUT,
            "DRT conn %" PRIu16 " not recv cli(%s) pid %" PRIu16 "conn msg %" PRIu64 " ms, ctx %" PRIu32
            ", attch count %" PRIu32 ", conn stat %" PRIu32 ", this round epoll wakeup time %" PRIu64
            ", nowTime %" PRIu64,
            conn->id, processName, conn->pid, timeDuration,
            (conn->msgMemCtx == NULL ? DB_MAX_UINT32 : conn->msgMemCtx->ctxId),
            (conn->msgMemCtx == NULL ? DB_MAX_UINT32 : conn->msgMemCtx->attachCount), (uint32_t)conn->status,
            ra->thisRoundEpollWakeupTime, nowTime);
        DrtUnMonitorPipe(conn->drtPipe);
        DrtConnectionListT *destList = DrtGetConnListByConnStatus(conn->connMgr, CONN_STATUS_CLOSING);
        DbRWSpinWLock(&destList->lock);
        DrtSetConnStatusNoLock(conn, CONN_STATUS_CLOSING);
        DbRWSpinWUnlock(&destList->lock);
    }
}

static void RaProcessConnectClosing(DrtRecvAgentT *ra, DrtConnectionT *conn)
{
    DB_POINTER3(ra, conn, ra->workerMgr);
#ifndef NDEBUG
    // 将要被去除的调度，必定不再调度队列中
    WorkerTaskListT *taskList = ra->workerMgr->taskList;
    WorkerTaskCtxT *cur = taskList->head;
    while (cur != NULL) {
        DB_ASSERT(cur->task != ra->scheMgr->scheProcCtx[conn->id]);
        cur = cur->next;
    }
#endif
    SmStopSchedule(ra->scheMgr, conn->id);
    DrtDetachConnectionLastRef(conn);
}

void DrtProcessClosingConn(DrtRecvAgentT *ra)
{
    DB_POINTER2(ra, ra->connMgr);
    DrtConnMgrT *connMgr = ra->connMgr;
    connMgr->procGc = true;
    DrtConnectionT *conn = NULL;

    if (ra->scheMgr->scheMode != SCHEDULE_DIRECTELY) {
        DrtConnectionListT *prepareList = DrtGetConnListByConnStatus(connMgr, CONN_STATUS_PREPARE);
        DbRWSpinWLock(&prepareList->lock);
        conn = prepareList->head;
        while (conn != NULL) {
            DrtConnectionT *connNext = conn->next;
            RaProcessConnectTimeout(conn, ra);
            conn = connNext;
        }
        DbRWSpinWUnlock(&prepareList->lock);
    }

    DrtConnectionListT *closingList = DrtGetConnListByConnStatus(connMgr, CONN_STATUS_CLOSING);
    // 一次性把closingList取出来，提前释放锁，减少锁竞争
    DbRWSpinWLock(&closingList->lock);
    conn = closingList->head;
    closingList->head = NULL;
    closingList->tail = NULL;
    closingList->num = 0;
    DbRWSpinWUnlock(&closingList->lock);
    while (conn != NULL) {
        DrtConnectionT *connNext = conn->next;
        if (conn->ref == 1) {
            RaProcessConnectClosing(ra, conn);
        } else {
            DbRWSpinWLock(&closingList->lock);
            DrtConnListAppendNodeNoLock(closingList, conn);  // 还不满足回收条件，重新放回closingList
            DbRWSpinWUnlock(&closingList->lock);
        }
        conn = connNext;
    }
    connMgr->procGc = false;
}

static Status DrtStartSchedule(DrtRecvAgentT *ra, DrtConnectionT *conn)
{
    DB_POINTER2(ra, conn);
    DrtScheMgrT *scheMgr = ra->scheMgr;
    WorkerMgrT *workerMgr = ra->workerMgr;
    SmScheProcCtxT *scheCtx = NULL;
    Status ret = SmPrepareSchedule(scheMgr, &scheCtx, conn);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "stage1, prepare sche conn %" PRIu16 ", peerInfo: %" PRIu32 "-%s", conn->id, conn->pid,
            conn->auditUserInfo);
        // scheCtx 是局部变量，外部函数无法获得，未置NULL，无UAF风险
        return ret;
    }
    DB_ASSERT(scheCtx != NULL);
    DrtSetConnStatus(conn, CONN_STATUS_PREPARE);
    if (scheMgr->scheMode == SCHEDULE_DIRECTELY) {
        DrtAttachConnection(conn);  // 先给模式0的worker加上引用计数
        WorkerParaT workerPara = {NULL, DRT_WORKER_SERVICE, DrtServiceWorkerMain, (void *)scheCtx, false, 0,
            WORKER_PRIORITY_MIDDLE, false, .scheduleTime = DRT_MAX_RECV_TIMEOUTMS * SCHEDULE_TIMES};
        ret = DrtStartNewWorker(workerMgr, &workerPara, NULL);
        if (ret != GMERR_OK) {
            DrtSetConnStatus(conn, CONN_STATUS_CLOSING);
            // 仅对引用计数减一
            DrtDecreaseConnRef(conn);
            DB_LOG_ERROR(ret, "stage1 create worker, conn %" PRIu16 " peerInfo: %" PRIu32 "-%s", conn->id, conn->pid,
                conn->auditUserInfo);
            DB_LOG_AUDIT(conn->auditUserInfo, "worker", DB_AUDIT_DCL, COM_RESULT_START_WORKER_FAILED,
                "Cli conn. Srv not start new worker for conn");
        }
    }
    return ret;
}

static void RaProcConnectInternal(DrtRecvAgentT *ra, const DrtPipeT *pipe)
{
    DB_POINTER2(ra, pipe);
    DB_LOG_DEBUG("If log not refresh, lsrn worker is suspended.");
    DrtKeepThreadAlive(GetThisWorkerId());
    RaConnectContextT connCtx = {ra->connMgr, NULL, NULL, DB_INVALID_ID16, {0}, {0}, {0}, {0}};
    Status ret = RaAcceptNewPipe(ra, pipe, &connCtx);
    if (ret != GMERR_OK || connCtx.newPipe == NULL) {
        return;
    }

    DbPipeGetPipeInfo(&connCtx.newPipe->dbPipe, connCtx.peerInfoBuf, sizeof(connCtx.peerInfoBuf));
    DB_LOG_INFO("DRT Accept conn req, %s", connCtx.peerInfoBuf);
#ifdef SHUTDOWN
    // db处于停止状态，拒绝所有建连请求
    if (DrtDbServerIsStopped()) {
        DB_LOG_WARN(GMERR_CONNECTION_RESET_BY_PEER, "DRT Db Stopping, Refuse req, %s", connCtx.peerInfoBuf);
        RaProcessConnectFailed(
            ra, &connCtx, GMERR_CONNECTION_RESET_BY_PEER, COM_RESULT_START_WORKER_FAILED, "Db state check");
        return;
    }
#endif
    connCtx.conn = RaPrepareConnection(ra, &connCtx);
    if (connCtx.conn == NULL) {
        RaProcessConnectFailed(
            ra, &connCtx, GMERR_UNEXPECTED_NULL_VALUE, COM_RESULT_CONNECTION_USE_UP, "alloc connection");
        return;
    }
    DB_POINTER(ra->scheMgr);
    ret = RaProcPreparePipe(ra, &connCtx);  // add event
    if (ret != GMERR_OK) {
        RaProcessConnectFailed(ra, &connCtx, ret, COM_RESULT_START_WORKER_FAILED, "prepare pipe");
        return;
    }

    ret = DrtStartSchedule(ra, connCtx.conn);
    if (ret != GMERR_OK) {
        RaProcessConnectFailed(ra, &connCtx, ret, COM_RESULT_START_WORKER_FAILED, "start schedule");
        return;
    }
    RaRespComResult(connCtx.conn, connCtx.newPipe, COM_RESULT_SUCCEED);
    RaCheckHandshakeTimeUsed(connCtx.conn, connCtx.newPipe, MSECONDS_IN_SECOND);
}

/* pipe异常，conn进行断链操作，释放QE层资源 */
void DrtHandleInvalidPipe(DrtScheMgrT *scheMgr, DrtConnectionT *conn)
{
    DB_POINTER2(scheMgr, conn);
    FixBufferT msg = {0};
    Status ret = SmPushMsgToScheList(scheMgr, conn, &msg, true);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "conn %" PRIu16 " push msg when pipe inv", conn->id);
    }
}

static void MonitorCheckConnOpTimeout(const DrtRecvAgentT *ra, DrtConnectionT *conn)
{
    DB_POINTER2(ra, conn);
    if (DrtConnFlagIsRecycle(conn)) {
        return;
    }
    DrtConnCheckOpTimeout(conn);
    if (DrtConnTimeoutExit(conn)) {
        DB_LOG_WARN(GMERR_CONNECTION_TIMED_OUT,
            "Conn timeout to exit, conn %" PRIu16 ", peer pid %" PRIu32 ", pos %" PRIu32 ", seek %" PRIu32
            ", ref %" PRIu32 ".",
            conn->id, conn->pid, FixBufGetPos(&conn->rspCacheBuf), FixBufGetSeekPos(&conn->rspCacheBuf), conn->ref);
        DrtUnMonitorPipe(conn->drtPipe);

        if (!DrtConnFlagIsRecycle(conn)) {
            DbSpinLock(&conn->lock);
            if (DrtConnFlagIsRecycle(conn)) {
                DbSpinUnlock(&conn->lock);
                return;
            }
            DrtConnFlagSetRecycle(conn);
            if (ra->scheMgr->scheMode == SCHEDULE_THREAD_POOL) {
                // 连接是由RA释放的，此处detach不会走到connMgr加锁流程
                DrtDetachConnection(conn);
            }
            if (DrtConnFlagIsResendRsp(conn)) {
                FixBufRelease(&conn->rspCacheBuf);
                DrtConnFlagUnsetResendRsp(conn);
            }
            DrtHandleInvalidPipe(ra->scheMgr, conn);
            DbSpinUnlock(&conn->lock);
        }
    }
}

// 需要跟随normal以后在哪里，就在哪里调用
void DrtCheckOpTimeout(const DrtRecvAgentT *ra, DrtConnMgrT *connMgr)
{
    DB_POINTER(connMgr);
    DrtConnectionListT *normalList = DrtGetConnListByConnStatus(connMgr, CONN_STATUS_NORMAL);
    DbRWSpinWLock(&normalList->lock);
    DrtConnectionT *conn = normalList->head;
    while (conn != NULL) {
        DrtConnectionT *connNext = conn->next;
        MonitorCheckConnOpTimeout(ra, conn);
        conn = connNext;
    }
    DbRWSpinWUnlock(&normalList->lock);
}

#ifdef EXPERIMENTAL_NERGC
#define TCP_IP_ACCEPT 0
static void DrtTcpBanIpRecvory(DrtConnMgrT *connMgr)
{
    // 恢复重试超时连接
    BanIpT banIp;
    DbQueueT *ipBanQueue = &connMgr->ipBanMap.ipBanQueue;
    while (true) {
        if (DbQueueIsEmpty(ipBanQueue)) {
            return;
        }
        Status ret = DbQueueFront(ipBanQueue, &banIp);
        if (ret != GMERR_OK) {
            return;
        }
        uint32_t totalSec = (uint32_t)DbToSeconds(DbRdtsc() - banIp.timeStamp);
        if (SECUREC_LIKELY(totalSec >= connMgr->ipBanMap.retryTimeout)) {
            DbIpControl(banIp.ip, TCP_IP_ACCEPT);
            DbQueuePop(ipBanQueue, &banIp);
            DB_LOG_INFO("ip:%s allow", banIp.ip);
            DB_LOG_AUDIT("", "connection", DB_AUDIT_DCL, GMERR_CONNECTION_TIMED_OUT, "ip:%s allow", banIp.ip);
            DbOamapT *ipMap = &connMgr->ipBanMap.ipFailMap;
            if (ipMap == NULL) {
                DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "ipMap null.");
                return;
            }
            uint32_t *failCount = (uint32_t *)DbOamapLookup(ipMap, DbStrToHash32(banIp.ip), &banIp.ip, NULL);
            if (failCount == NULL) {
                return;
            }
            (*failCount) = 0;
        } else {
            break;
        }
    }
}
void DrtCheckTcpTimeout(const DrtRecvAgentT *ra, DrtConnMgrT *connMgr)
{
    DrtTcpBanIpRecvory(connMgr);
    if (connMgr->tcpConnTimeout == 0) {
        return;
    }
    DrtConnectionListT *normalList = DrtGetConnListByConnStatus(connMgr, CONN_STATUS_NORMAL);
    DbRWSpinWLock(&normalList->lock);
    DrtConnectionT *conn = normalList->head;
    while (conn != NULL) {
        DrtConnectionT *connNext = conn->next;
        DrtConnCheckTcpTimeout(conn);
        if (DrtConnTimeoutExit(conn)) {
            DB_LOG_WARN(GMERR_CONNECTION_TIMED_OUT,
                "Conn timeout to exit, conn %" PRIu16 ", peer pid %" PRIu32 ", pos %" PRIu32 ", seek %" PRIu32
                ", ref %" PRIu32 ".",
                conn->id, conn->pid, FixBufGetPos(&conn->rspCacheBuf), FixBufGetSeekPos(&conn->rspCacheBuf), conn->ref);
            DrtUnMonitorPipe(conn->drtPipe);
            if (!DrtConnFlagIsRecycle(conn)) {
                DbSpinLock(&conn->lock);
                if (DrtConnFlagIsRecycle(conn)) {
                    DbSpinUnlock(&conn->lock);
                    DbRWSpinWUnlock(&normalList->lock);
                    return;
                }
                DrtConnFlagSetRecycle(conn);
                if (ra->scheMgr->scheMode == SCHEDULE_THREAD_POOL) {
                    // 连接是由RA释放的，此处detach不会走到connMgr加锁流程
                    DrtDetachConnection(conn);
                }
                if (DrtConnFlagIsResendRsp(conn)) {
                    FixBufRelease(&conn->rspCacheBuf);
                    DrtConnFlagUnsetResendRsp(conn);
                }
                DrtHandleInvalidPipe(ra->scheMgr, conn);
                DbSpinUnlock(&conn->lock);
            }
        }
        conn = connNext;
    }
    DbRWSpinWUnlock(&normalList->lock);
}
#endif

#define CS_RECV_MIN_TIMEOUT_MS 10
void RaLsnrProcess(DrtRecvAgentT *ra, const DrtPipeT *pipe)
{
    DB_POINTER2(ra, ra->scheMgr);

    DrtKeepThreadAlive(GetThisWorkerId());
    // DbPipeMonitorProc 对应读事件回调函数 RaProcConnectEvent
    DbPipeMonitorProc(ra->monitor, CS_RECV_TIMEOUT_MS);
    // 客户端断链慢，gc event处理有残留场景，依赖这里最终回收
    DrtProcessClosingConn(ra);
    DrtConnProcessClosedList(ra->connMgr);
}

static void RaProcConnectEvent(void *ctx, void *data, uint32_t event)
{
    DB_POINTER2(ctx, data);
    DB_ASSERT(event == DB_PIPE_MONITOR_EVENT_READ);
    DrtRecvAgentT *ra = (DrtRecvAgentT *)ctx;
    ra->thisRoundEpollWakeupTime = DbRdtsc();
    const DrtPipeT *pipe = (const DrtPipeT *)data;
    RaProcConnectInternal(ra, pipe);
#if defined(RTOSV2) || defined(RTOSV2X) || defined(HPE)
    int32_t itemNum = DbPipeGetItemNum(&pipe->dbPipe);
    while (itemNum > 0) {
        RaProcConnectInternal(ra, pipe);
        itemNum--;
    }
    itemNum = DbPipeGetItemNum(&pipe->dbPipe);
    if (itemNum > 0) {
        (void)DbVnotifySelf(DbPipeGetEventFd(&pipe->dbPipe));
    }
#endif
}

static Status RaInitListener(DrtRecvAgentT *ra)
{
    DB_POINTER2(ra, ra->connMgr);
    DrtPipeListT *pipeList = &ra->connMgr->pipeList;
    ra->lsnrPipe = DrtAllocPipe(pipeList);
    if (ra->lsnrPipe == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Alloc pipe for lsnr");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    ra->lsnrPipe->connId = DB_INVALID_UINT16;

    bool isReuse = (bool)DbCfgGetInt32Lite(DB_CFG_ENABLE_CHANNEL_REUSE, NULL);
#ifdef HPE_SIMULATION
    DbPipeMemParasT pipeMemParas = {DbDynMemCtxAlloc, DbDynMemCtxFree, pipeList->memctx, DbGetProcGlobalId(), isReuse};
#else
    DbPipeMemParasT pipeMemParas = {DbDynMemCtxAlloc, DbDynMemCtxFree, pipeList->memctx, isReuse};
#endif
    Status ret = DbPipeOpenListen(&ra->lsnrPipe->dbPipe, ra->selfLctr, &pipeMemParas);
    if (ret != GMERR_OK) {
        DrtClosePipe(pipeList, ra->lsnrPipe);
        ra->lsnrPipe = NULL;
        DB_LOG_ERROR(ret, "Pipe open listen");
        return ret;
    }
    pipeList->lsnrPipeId = ra->lsnrPipe->pipeId;
    return ret;
}

static Status RaCreateMonitor(DrtRecvAgentT *ra)
{
    DB_POINTER(ra);
    DbPipeMonitorParasT monitorParas = {.memctx = ra->memCtx,
        .allocFunc = DbDynMemCtxAlloc,
        .freeFunc = DbDynMemCtxFree,
        .capacity = (uint16_t)(DbDynArrayGetMaxNum(&ra->connMgr->connArr) + 10u),
        .userCtx = ra};
    ra->monitor = DbPipeMonitorCreate(&monitorParas);
    if (ra->monitor == NULL) {
        DB_LOG_ERROR(GMERR_NO_DATA, "create monitor for ra");
        return GMERR_NO_DATA;
    }

    DbPipeMonitorEventParaT monitorEventPara = {ra->lsnrPipe, RaProcConnectEvent, DB_PIPE_MONITOR_EVENT_READ};
    DbPipeSetEtEvent(&monitorEventPara);
    Status ret = DbPipeMonitorAddEvent(ra->monitor, &ra->lsnrPipe->dbPipe, &monitorEventPara);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "add lsnrfd to ra monitor");
        return ret;
    }

    // 当前服务端只有这一个单独的event，所以id = 0
#if defined(HPE)
    ret = DbNotifyFdCreate(0, &ra->connMgr->fd);
#else
    ret = DbEventFdCreate(0, &ra->connMgr->fd);
#endif
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "create conn gc event");
        return ret;
    }
    DbPipeMonitorEventParaT para = {ra->connMgr, DrtGcEventProc, DB_PIPE_MONITOR_EVENT_READ};
    DbPipeSetEtEvent(&para);
    return DbMonitorAddEvent(ra->monitor, NULL, ra->connMgr->fd, &para, &ra->connMgr->eventId);
}

void DrtTimerEventProc(void *ctx, void *data, uint32_t event)
{
    DB_POINTER2(ctx, data);
    DrtRecvAgentT *ra = (DrtRecvAgentT *)ctx;
    WorkerMgrT *workerMgr = (WorkerMgrT *)data;
    WorkerPoolT *workerPool = &workerMgr->workerPool;

    uint64_t count;
    (void)DbAdptReadForTimeEvent(workerMgr->timerFd, &count, sizeof(uint64_t), NULL);

    Status ret;
    DbSpinLock(&workerPool->lock);
    (void)WorkerPoolJudgeAndCreateNewWorker(workerMgr, DbRdtsc(), &ret);
    DbSpinUnlock(&workerPool->lock);
    DrtCheckOpTimeout(ra, ra->connMgr);
}

static Status DrtMonitorAddTimerFd(WorkerMgrT *workerMgr, DbPipeMonitorT *monitor, int32_t *timerFd, uint16_t *eventId)
{
    DB_POINTER4(workerMgr, monitor, timerFd, eventId);
    *timerFd = DB_INVALID_FD;
    *eventId = DB_INVALID_ID16;

    Status ret = DbAdptTimerFdCreate(CLOCK_MONOTONIC, 0, timerFd);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "create timer for req monitor");
        return ret;
    }
    struct itimerspec time = {};
    time.it_value.tv_nsec = TASK_WAIT_TIME_WARN_MS * NSECONDS_IN_MSECOND;
    time.it_interval.tv_nsec = TASK_WAIT_TIME_WARN_MS * NSECONDS_IN_MSECOND;
    ret = DbAdptTimerFdSetTime(*timerFd, 0, &time, NULL);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "create timer for timeout.");
        DbAdptTimerFdClose(*timerFd);
        *timerFd = DB_INVALID_FD;
        return ret;
    }

    DbPipeMonitorEventParaT para = {workerMgr, DrtTimerEventProc, DB_PIPE_MONITOR_EVENT_READ};
    DbPipeSetEtEvent(&para);
    ret = DbMonitorAddEvent(monitor, NULL, *timerFd, &para, eventId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "create timer for add event.");
        DbAdptTimerFdClose(*timerFd);
        *timerFd = DB_INVALID_FD;
    }
    return ret;
}

Status DrtWorkerMgrCreateReqMonitor(
    WorkerMgrT *workerMgr, ScheduleModeE scheMode, uint32_t connMaxNum, DrtRecvAgentT *ra)
{
    DB_POINTER2(workerMgr, workerMgr->memCtx);
    if (scheMode != SCHEDULE_THREAD_POOL) {
        return GMERR_OK;
    }

    DB_ASSERT(workerMgr->reqMonitor == NULL);
    DbPipeMonitorParasT paras = {.memctx = workerMgr->memCtx,
        .allocFunc = DbDynMemCtxAlloc,
        .freeFunc = DbDynMemCtxFree,
        .capacity = (uint16_t)(connMaxNum + 10u),
        .userCtx = ra};
    DbPipeMonitorT *monitor = DbPipeMonitorCreate(&paras);
    if (monitor == NULL) {
        DB_LOG_ERROR(GMERR_NO_DATA, "create monitor for ra");
        return GMERR_NO_DATA;
    }

    Status ret = DrtMonitorAddTimerFd(workerMgr, monitor, &workerMgr->timerFd, &workerMgr->timerEventId);
    if (ret != GMERR_OK) {
        DbPipeMonitorDestroy(monitor);
        return ret;
    }
    workerMgr->reqMonitor = monitor;
    return ret;
}

Status RaInit(DrtRecvAgentT *ra, const RaInitParaT *initPara)
{
    DB_POINTER4(ra, initPara, initPara->memCtx, initPara->workerMgr);
    DB_POINTER5(initPara->connMgr, initPara->scheMgr, initPara->nodeMgr, initPara->scheCb, initPara->selfLctr);
    ra->memCtx = initPara->memCtx;
    ra->connMgr = initPara->connMgr;
    ra->scheMgr = initPara->scheMgr;
    ra->nodeMgr = initPara->nodeMgr;
    ra->scheCb = initPara->scheCb;
    ra->scheStartHandle = initPara->scheStartHandle;
    ra->selfLctr = initPara->selfLctr;
    ra->workerMgr = initPara->workerMgr;
    ra->uid = initPara->uid;
    ra->monitorTimeoutMs = CS_RECV_TIMEOUT_MS;
    ra->initComplete = false;
    ra->recvCount = 0;
    ra->thisRoundEpollWakeupTime = 0;
    DbSpinInit(&ra->lock);
    Status ret = RaInitListener(ra);
    if (ret != GMERR_OK) {
        RaDestroy(ra);
        return ret;
    }
    ret = RaCreateMonitor(ra);
    if (ret != GMERR_OK) {
        RaDestroy(ra);
        return ret;
    }
    ret = DrtWorkerMgrCreateReqMonitor(
        ra->workerMgr, ra->scheMgr->scheMode, (uint16_t)(DbDynArrayGetMaxNum(&ra->connMgr->connArr) + 1u), ra);
    if (ret != GMERR_OK) {
        RaDestroy(ra);
        return ret;
    }
    return GMERR_OK;
}

void RaDestroy(DrtRecvAgentT *ra)
{
    DB_POINTER2(ra, ra->memCtx);

    if (ra->monitor != NULL && ra->lsnrPipe != NULL) {
        if (ra->connMgr->msgPoolMgr != NULL) {
            DbMonitorDelEvent(ra->monitor, ra->connMgr->msgPoolMgr->nd, ra->connMgr->msgPoolMgr->eventId);
        }
        if (ra->connMgr->fd != DB_INVALID_FD) {
            DbMonitorDelEvent(ra->monitor, ra->connMgr->fd, ra->connMgr->eventId);
        }
        DbPipeMonitorDelEvent(ra->monitor, &ra->lsnrPipe->dbPipe);
        DbPipeMonitorDestroy(ra->monitor);
        ra->monitor = NULL;
    }
    if (ra->lsnrPipe != NULL) {
        DrtClosePipe(&ra->connMgr->pipeList, ra->lsnrPipe);
        ra->lsnrPipe = NULL;
    }
}

#ifdef __cplusplus
}
#endif
