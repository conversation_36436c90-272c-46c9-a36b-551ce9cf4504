/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * Description: source file for data model Vertex
 * Author: zhangxiaolei
 * Create: 2020-8-14
 */

#include "dm_data_prop.h"
#include <string.h>
#include "dm_data_record.h"
#include "dm_data_node_inner.h"
#include "dm_data_keybuf_opti.h"
#include "dm_meta_res_col_pool.h"
#include "dm_yang_interface.h"
#include "dm_yang_union.h"

#include "dm_data_prop_in.h"
#include "dm_log.h"

/*
 * create empty vertex
 */
Status CreateEmptyVertexByVertexDesc(DbMemCtxT *memCtx, DmVertexDescT *vertexDesc, DmVertexT **vertex);
Status CreateEmptyVertexInner(DmVertexT *vertex, uint8_t **bufCursor);

/*
 * process edge when create empty vertex
 */
Status CreateVertexProcessEdge(DmVertexT *vertex);

/*
 * create child node
 */
Status VertexCreateChildNodes(DmVertexT *vertex, uint8_t **bufCursor);

/*
 * 初始化vertex中的keyBuf信息，主要包括提前申请compareKeyBuf，secCompareKeyBuf，vertexKeyBuf所需buf大小
 */
void VertexInitKeyBuf(DmVertexT *vertex, uint8_t **bufCursor);

/*
 * free vertex inner memory
 */
void FreeVertexInnerMemory(DmVertexT *vertex);

/**
 * @brief vertex树模型中子节点如果有memberKey，对memberKey的size进行约束检查
 * @param vertex vertex顶点
 * @return Status 成功或错误码
 */
Status VertexMemberKeySizeCheck(const DmVertexT *vertex);

/*                Function Implements                 */
uint32_t DmVertexGetPropNum(const DmVertexT *vertex, const bool isSysPropeIncluded)
{
    DB_POINTER2(vertex, vertex->vertexDesc);
    return RecordDescGetPropNum(vertex->vertexDesc->recordDesc, isSysPropeIncluded);
}

void DmVertexGetDefaultValue(const DmVertexT *vertex, uint32_t propId, DmValueT **value)
{
    DB_POINTER2(vertex, vertex->record);
    *value = RecordGetDefaultValue(vertex->record, propId);
}

Status DmCreateEmptyVertexWithMemCtx(DbMemCtxT *memCtx, DmVertexLabelT *vertexLabel, DmVertexT **vertex)
{
    DB_POINTER3(memCtx, vertexLabel, vertex);
    Status ret = VertexLabelInitVertexDesc(vertexLabel);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    return CreateEmptyVertexByVertexDesc(memCtx, vertexLabel->vertexDesc, vertex);
}

Status DmCreateEmptyVertexByDescWithMemCtx(DbMemCtxT *memCtx, DmVertexDescT *vertexDesc, DmVertexT **vertex)
{
    DB_POINTER3(memCtx, vertexDesc, vertex);
    return CreateEmptyVertexByVertexDesc(memCtx, vertexDesc, vertex);
}

Status CreateEmptyVertexByVertexDesc(DbMemCtxT *memCtx, DmVertexDescT *vertexDesc, DmVertexT **vertex)
{
    uint32_t memSize = vertexDesc->memSizePreMalloc;
    // 内存释放逻辑：DmDestroyVertex
    uint8_t *totalBuf = (uint8_t *)DbDynMemCtxAlloc(memCtx, memSize);
    if (SECUREC_UNLIKELY(totalBuf == NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Create empty vertex, size: %" PRIu32 ".", memSize);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(totalBuf, memSize, 0x00, memSize);
    uint8_t *bufCursor = totalBuf;
    *vertex = (DmVertexT *)bufCursor;
    bufCursor += (uint32_t)sizeof(DmVertexT);
    (*vertex)->memCtx = memCtx;
    (*vertex)->vertexDesc = vertexDesc;
    Status ret = CreateEmptyVertexInner(*vertex, &bufCursor);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        goto EXIT;
    }
    if (SECUREC_UNLIKELY((uintptr_t)bufCursor - (uintptr_t)totalBuf != memSize)) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR,
            "Buf mistake create empty vertex. BufSize: %" PRIu32 ", Write size: %" PRIu32 ".", memSize,
            (uint32_t)((uintptr_t)bufCursor - (uintptr_t)totalBuf));
        ret = GMERR_INTERNAL_ERROR;
        goto EXIT;
    }
    return GMERR_OK;
EXIT:
    FreeVertexInnerMemory(*vertex);
    // 释放vertex内存，包含vertex结构体，三段keyBuf,vertex->nodes 内存
    // 此处释放的内存包含GetEmptyVertexBufLen4PreMalloc函数中计算的属性
    DbDynMemCtxFree((*vertex)->memCtx, totalBuf);
    *vertex = NULL;
    return ret;
}

void InitBasicVertexInfo(DmVertexT *vertex)
{
    vertex->hasBitMapPropPartialSet = false;
    vertex->isDeltaVertex = false;
    vertex->headMagicCode = DM_RUNNING_MAGIC_CODE;
    vertex->tailMagicCode = DM_RUNNING_MAGIC_CODE;
    vertex->state = DM_VERTEX_RUNNING;
    vertex->position = DM_LIST_STAY;
}

Status CreateEmptyVertexInner(DmVertexT *vertex, uint8_t **bufCursor)
{
    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    // 创建系统字段区
    vertex->sysPropeBuf = *bufCursor;
    *bufCursor += vertexDesc->sysPropeTotalLen;
    // NPABitmap
    if (vertexDesc->npaCount > 0) {
        vertex->npaBitmap = *bufCursor;
        *bufCursor += DM_NULL_INFO_SERI_BYTES(vertexDesc->npaCount);
    }
    DmRecordTypeE type = JudgeRecordType(vertexDesc->vertexLabelType);
    Status ret = CreateEmptyRecord(vertex->memCtx, type, vertexDesc->recordDesc, &vertex->record);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    InitBasicVertexInfo(vertex);
    vertex->vertexSeriBuf = NULL;
    VertexInitKeyBuf(vertex, bufCursor);

    ret = CreateVertexProcessEdge(vertex);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }

    return VertexCreateChildNodes(vertex, bufCursor);
}

void VertexInitKeyBuf(DmVertexT *vertex, uint8_t **bufCursor)
{
    uint32_t keyBufLen = vertex->vertexDesc->keyBufLen;
    vertex->compareKeyBuf = *bufCursor;
    *bufCursor += keyBufLen;
    vertex->vertexKeyBuf = *bufCursor;
    *bufCursor += keyBufLen;
}

Status CreateVertexProcessEdge(DmVertexT *vertex)
{
    vertex->edgeLabelNum = vertex->vertexDesc->edgeLabelNum;
    if (vertex->edgeLabelNum != 0) {
        size_t memSize = vertex->edgeLabelNum * sizeof(DmEdgeAddrsT);
        // 内存释放逻辑：DmDestroyVertex
        vertex->edgeAddrsEntry = (DmEdgeAddrsT *)DbDynMemCtxAlloc(vertex->memCtx, memSize);
        if (vertex->edgeAddrsEntry == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Create vertex edgeAddrsEntry size: %zu.", memSize);
            return GMERR_OUT_OF_MEMORY;
        }
        (void)memset_s(vertex->edgeAddrsEntry, memSize, 0x00, memSize);
    }
    return GMERR_OK;
}

Status VertexCreateChildNodes(DmVertexT *vertex, uint8_t **bufCursor)
{
    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    uint32_t nodeNum = vertexDesc->nodeNum;
    if (nodeNum == 0) {
        return GMERR_OK;
    }
    size_t memSize = sizeof(DmNodeT *) * nodeNum;
    vertex->nodes = (DmNodeT **)(*bufCursor);
    *bufCursor += memSize;
    bool needCreated = true;  // 句柄按需创建开关，暂未打开
    for (uint32_t i = 0; i < nodeNum; i++) {
        Status ret = DmCreateEmptyNode(vertex->memCtx, vertexDesc->nodeDescs[i], &vertex->nodes[i], needCreated);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            // 释放前面已经创建成功的node
            for (uint32_t j = 0; j < i; j++) {
                DestroyNode(&vertex->nodes[j]);
            }
            vertex->nodes = NULL;
            return ret;
        }
    }
    return GMERR_OK;
}

/*
 * 释放vertex指向的内存，vertex本身的处理（如置NULL），由调用方处理
 */
void DmDestroyVertex(DmVertexT *vertex)
{
    if (SECUREC_UNLIKELY(vertex == NULL)) {
        return;
    }
    FreeVertexInnerMemory(vertex);
    vertex->headMagicCode = DM_DESTROYED_MAGIC_CODE;
    vertex->tailMagicCode = DM_DESTROYED_MAGIC_CODE;
    vertex->state = DM_VERTEX_DESTROYED;
    // 释放vertex内存，包含vertex结构体，两段keyBuf,vertex->nodes, sysPropeBuf内存
    // 此处释放的内存包含GetEmptyVertexBufLen4PreMalloc函数中计算的属性
    DbDynMemCtxFree(vertex->memCtx, vertex);
}

void FreeVertexInnerMemory(DmVertexT *vertex)
{
    DestroyRecord(&(vertex->record));
    DmVertexDescT *vertexDesc = vertex->vertexDesc;

    DbDynMemCtxFree(vertex->memCtx, vertex->vertexSeriBuf);
    vertex->vertexSeriBuf = NULL;

    if (SECUREC_UNLIKELY(vertex->edgeLabelNum != 0)) {
        DbDynMemCtxFree(vertex->memCtx, vertex->edgeAddrsEntry);
        vertex->edgeAddrsEntry = NULL;
    }

    if (vertex->nodes != NULL) {
        uint32_t nodeNum = vertexDesc->nodeNum;
        for (uint32_t i = 0; i < nodeNum; i++) {
            DestroyNode(&vertex->nodes[i]);
        }
    }
}

/*
 * Reset vertex，将vertex恢复成仅含有默认值的状态
 */
Status DmResetVertex(DmVertexT *vertex)
{
    DB_POINTER(vertex);
    uint32_t sysPropeTotalLen = vertex->vertexDesc->sysPropeTotalLen;
    (void)memset_s(vertex->sysPropeBuf, sysPropeTotalLen, 0x00, sysPropeTotalLen);
    if (vertex->vertexDesc->npaCount > 0) {
        uint32_t npaBitmapLen = DM_NULL_INFO_SERI_BYTES(vertex->vertexDesc->npaCount);
        (void)memset_s(vertex->npaBitmap, npaBitmapLen, 0x00, npaBitmapLen);
    }
    Status ret = ResetRecord(vertex->record);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    vertex->record->hasLongVarPrope = vertex->record->recordDesc->hasLongDefault;

    // reset edgeAddrsEntry
    if (vertex->edgeLabelNum != 0) {
        (void)memset_s(vertex->edgeAddrsEntry, vertex->edgeLabelNum * sizeof(DmEdgeAddrsT), 0x00,
            vertex->edgeLabelNum * sizeof(DmEdgeAddrsT));
    }

    vertex->isDeltaVertex = false;  // 恢复成一般的vertex，对vector节点进行增量更新操作时会再置为true
    vertex->position = DM_LIST_STAY;

    // Reset所有子树节点
    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    for (uint32_t i = 0; i < vertexDesc->nodeNum; i++) {
        ret = DmResetNode(vertex->nodes[i]);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            return ret;
        }
    }
    return GMERR_OK;
}

/*
 * Clear vertex，默认值也被全部清除
 */
void DmClearVertex(DmVertexT *vertex)
{
    DB_POINTER(vertex);
    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    if (SECUREC_LIKELY(vertexDesc->sysPropeTotalLen == 1)) {
        *(uint8_t *)vertex->sysPropeBuf = 0;
    } else {
        (void)memset_s(vertex->sysPropeBuf, vertexDesc->sysPropeTotalLen, 0x00, vertexDesc->sysPropeTotalLen);
    }
    if (vertexDesc->npaCount > 0) {
        uint32_t npaBitmapLen = DM_NULL_INFO_SERI_BYTES(vertexDesc->npaCount);
        (void)memset_s(vertex->npaBitmap, npaBitmapLen, 0x00, npaBitmapLen);
    }
    ClearRecord(vertex->record);
    if (SECUREC_LIKELY(vertex->edgeLabelNum == 0 && vertexDesc->nodeNum == 0)) {
        return;
    }

    if (vertex->edgeLabelNum != 0) {
        (void)memset_s(vertex->edgeAddrsEntry, vertex->edgeLabelNum * sizeof(DmEdgeAddrsT), 0x00,
            vertex->edgeLabelNum * sizeof(DmEdgeAddrsT));
    }

    vertex->isDeltaVertex = false;  // 恢复成一般的vertex，对vector进行操作时会再置为true

    // Clear所有子树节点
    for (uint32_t i = 0; i < vertexDesc->nodeNum; i++) {
        DmClearNode(vertex->nodes[i]);
    }
}

Status VertexMemberKeyCheck(DmVertexT *vertex)
{
    // 不涉及yang，未适配按需创建句柄的场景
    DB_ASSERT(vertex->vertexDesc->vertexLabelType != VERTEX_TYPE_YANG);
    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    for (uint32_t i = 0; i < vertexDesc->nodeNum; i++) {
        Status ret = NodeMemberKeyCheck(vertex->nodes[i]);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status DmVertexMemberKeyCheckInUpdate(const DmVertexT *vertex, const DmVertexT *deltaVertex)
{
    DB_POINTER2(vertex, deltaVertex);
    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    // merge场景，不涉及yang，未适配按需创建句柄的场景
    DB_ASSERT(vertexDesc->vertexLabelType != VERTEX_TYPE_YANG);
    if (vertexDesc->labelLevel != VERTEX_LEVEL_GENERAL || !vertexDesc->checkValidity) {
        return GMERR_OK;
    }
    for (uint32_t i = 0; i < vertexDesc->nodeNum; i++) {
        // 对于vector型节点，如果opNum == 0则表示没有发生增量更新，此时不用校验member key
        if (deltaVertex->nodes[i]->nodeDesc->nodeType == DM_NODE_VECTOR && deltaVertex->nodes[i]->opNum == 0) {
            continue;
        }
        Status ret = NodeMemberKeyCheck(vertex->nodes[i]);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status VertexMemberKeySizeCheck(const DmVertexT *vertex)
{
    Status ret;
    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    for (uint32_t i = 0; i < vertexDesc->nodeNum; i++) {
        ret = NodeMemberKeySizeCheck(vertex->nodes[i]);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Check memkey size, nodeName: %s.", vertexDesc->nodeDescs[i]->name);
            return ret;
        }
    }
    return GMERR_OK;
}

/*
 * whether vertex key is set or not
 */
bool DmVertexPkPropeIsAllSet(const DmVertexT *vertex)
{
    DB_POINTER(vertex);
    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    if (vertexDesc->hasPkIdx == false) {
        return true;
    }
    DmPropertyInfoT *propeInfos = vertexDesc->recordDesc->propeInfos;
    int8_t *propeIsSetValue = vertex->record->propeIsSetValue;
    DmIndexKeyBufInfoT *pkInfo = GetKeyBufInfoForVertex(vertexDesc, 0);
    for (uint32_t i = 0; i < pkInfo->keyPropeNum; i++) {
        // 主键建立在自增列上，不进行非空校验
        if (propeInfos[pkInfo->keyPropeIds[i]].isAutoIncPrope) {
            continue;
        }
        if (propeIsSetValue[pkInfo->keyPropeIds[i]] == DM_PROPERTY_IS_NULL) {
            return false;
        }
    }
    return true;
}

bool DmVertexPkAnyPropeIsSet(const DmVertexT *vertex)
{
    DB_POINTER(vertex);
    int8_t *propeIsSetValue = vertex->record->propeIsSetValue;

    DmIndexKeyBufInfoT *pkInfo = GetKeyBufInfoForVertex(vertex->vertexDesc, 0);
    for (uint32_t i = 0; i < pkInfo->keyPropeNum; i++) {
        if (propeIsSetValue[pkInfo->keyPropeIds[i]] == DM_PROPERTY_IS_NOT_NULL) {
            return true;
        }
    }
    return false;
}

/*
 * whether vertex non-null property is set or not
 */
Status DmVertexNotNullPropeIsAllSet(const DmVertexT *vertex)
{
    DB_POINTER2(vertex, vertex->record);
    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    // 性能路径，不涉及yang，未适配按需创建句柄的场景
    DB_ASSERT(vertexDesc->vertexLabelType != VERTEX_TYPE_YANG);
    if (SECUREC_LIKELY(vertexDesc->vertexType == DM_FLAT_VERTEX || vertexDesc->vertexType == DM_FIXED_VERTEX)) {
        return RecordNotNullPropeIsAllSet(vertex->record);
    } else {
        // DM_TREE_VERTEX类型
        Status ret = RecordNotNullPropeIsAllSet(vertex->record);
        if (ret != GMERR_OK) {
            return ret;
        }
        for (uint32_t i = 0; i < vertexDesc->nodeNum; i++) {
            ret = NodeNotNullPropeIsAllSet(vertex->nodes[i]);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        return GMERR_OK;
    }
}

/*
 * whether primary key is AutoIncPrope
 */
bool DmAutoIncIsOnPk(const DmVertexT *vertex)
{
    DB_POINTER(vertex);
    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    if (vertexDesc->indexKeyBufInfos == NULL) {
        return false;
    }
    DmPropertyInfoT *propeInfos = vertexDesc->recordDesc->propeInfos;
    DmIndexKeyBufInfoT *pkInfo = GetKeyBufInfoForVertex(vertexDesc, 0);
    for (uint32_t i = 0; i < pkInfo->keyPropeNum; i++) {
        // 主键建立在自增列上，返回true
        if (propeInfos[pkInfo->keyPropeIds[i]].isAutoIncPrope) {
            return true;
        }
    }
    return false;
}

Status DmVertexConstraintMemberKeyCheck(DmVertexT *vertex)
{
    // 校验机制兼容v3，一般复杂表，且checkValidity字段为true，对节点中memberkey进行校验
    // 否则仅对memberKey的大小进行校验
    DB_POINTER2(vertex, vertex->vertexDesc);
    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    if (!vertexDesc->hasMemberKey) {
        return GMERR_OK;
    }
    if (vertexDesc->labelLevel == VERTEX_LEVEL_GENERAL && vertexDesc->checkValidity == true &&
        !DmIsYangVertexDesc(vertexDesc)) {
        Status ret = VertexMemberKeyCheck(vertex);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Check memKey");
            return ret;
        }
        return GMERR_OK;
    }
    return VertexMemberKeySizeCheck(vertex);
}

static bool PropInNotNullArrays(DmVertexT *vertex, uint32_t propId)
{
    uint16_t *notNullPropeIds = vertex->record->recordDesc->notNullPropeIds;
    for (uint16_t i = 0; i < vertex->record->recordDesc->notNullPropeNum; i++) {
        if (notNullPropeIds[i] == propId) {
            return true;
        }
    }
    return false;
}

static Status DmVertexCheckYangSecondIndex(DmVertexT *vertex)
{
    DB_POINTER2(vertex, vertex->vertexDesc);
    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    if (!DmIsYangVertexDesc(vertexDesc) || vertexDesc->indexNum == 0) {
        return GMERR_OK;
    }
    // 需要判断yang中的不允许为空的字段做索引时是否都写值，如若没写，则要报错
    int8_t *propeIsSetValue = vertex->record->propeIsSetValue;
    for (uint32_t i = 0; i < vertexDesc->secIdxNum; i++) {
        DmIndexKeyBufInfoT *secondIndexInfo =
            GetKeyBufInfoForVertex(vertexDesc, i + vertexDesc->indexNum - vertexDesc->secIdxNum);
        if (secondIndexInfo->indexKeyType == LIST_LOCALHASH_INDEX) {
            continue;
        }
        for (uint32_t j = 0; j < secondIndexInfo->keyPropeNum; j++) {
            if (propeIsSetValue[secondIndexInfo->keyPropeIds[j]] == DM_PROPERTY_IS_NULL &&
                PropInNotNullArrays(vertex, secondIndexInfo->keyPropeIds[j])) {
                DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Yang non-empty fields cant be empty when indexed");
                return GMERR_DATA_EXCEPTION;
            }
        }
    }
    return GMERR_OK;
}

Status DmVertexConstraintCheck(DmVertexT *vertex)
{
    DB_POINTER2(vertex, vertex->vertexDesc);
    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    Status ret = DmVertexConstraintMemberKeyCheck(vertex);
    if (ret != GMERR_OK) {
        return ret;
    }
    // yang表的非空校验，在mandatory校验中进行
    if (vertexDesc->labelLevel == VERTEX_LEVEL_GENERAL && vertexDesc->checkValidity == true &&
        !DmIsYangVertexDesc(vertexDesc)) {
        // 校验机制兼容v3，一般复杂表，且checkValidity字段为true，对所有属性进行非空校验，并对节点中member key进行校验
        return DmVertexNotNullPropeIsAllSet(vertex);
    }

    // 若vertex是：1、一般复杂表，且checkValidity字段为false，2、是简单表和特殊复杂表；
    // 仅对主键和资源字段进行非空检查，对memberKey的大小进行校验
    bool pkIsSet = DmVertexPkPropeIsAllSet(vertex);
    if (pkIsSet == false) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Primary key not all set");
        return GMERR_DATA_EXCEPTION;
    }
    ret = DmVertexCheckYangSecondIndex(vertex);
    if (ret != GMERR_OK) {
        return ret;
    }
    bool resIsSet = DmVertexResAndPartitionPropeIsAllSet(vertex);
    if (resIsSet == false) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Resource or Partition prop not all set");
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

void VertexGetBitMapPropeInfo(DmVertexT *vertex, DmNodeT *node)
{
    if (node == NULL) {
        // node不存在，则一定没有bitMapPrope
        return;
    }
    for (uint32_t i = 0; i < node->realElementNum; i++) {
        NodeSetElementIndexWithoutCheck(node, i);
        if (node->currRecord->hasBitMapPartialSet == true) {
            vertex->hasBitMapPropPartialSet = true;
        }
        for (uint32_t j = 0; j < node->nodeDesc->nodeNumPerElement; j++) {
            VertexGetBitMapPropeInfo(vertex, node->currNodes[j]);
        }
    }
}

// 通过record的bitmapProp, 获取vertex的bitmapProp
inline void DmGetBitMapPropInfo(DmVertexT *vertex)
{
    DB_POINTER(vertex);
    if (vertex->vertexDesc->hasBitMapProp == false) {
        return;
    }
    if (vertex->record->hasBitMapPartialSet == true) {
        vertex->hasBitMapPropPartialSet = true;
    }
    for (uint32_t i = 0; i < vertex->vertexDesc->nodeNum; i++) {
        VertexGetBitMapPropeInfo(vertex, vertex->nodes[i]);
    }
}

/*
 * set property by name for vertex
 * input: propertyName, propertyValue, vertex
 * output: null
 */
Status DmVertexSetPropeByName(const char *propeName, DmValueT propeValue, const DmVertexT *vertex)
{
    DB_POINTER3(propeName, vertex, vertex->record);
    return RecordSetPropeByName(propeName, propeValue, vertex->record);
}

/*
 * set property by id for vertex
 * input: id, propertyValue, vertex
 * output: null
 */
Status DmVertexSetPropeById(uint32_t propeId, DmValueT propeValue, const DmVertexT *vertex)
{
    DB_POINTER(vertex);
    return RecordSetPropeById(propeId, propeValue, vertex->record);
}

Status DmVertexGetPropeTypeById(uint32_t propeId, const DmVertexT *vertex, DbDataTypeE *type)
{
    DB_POINTER2(vertex, type);
    return RecordGetPropeTypeById(propeId, vertex->record, type);
}

/*
 * get property by name for vertex
 * input: propertyName, vertex
 * output: propertyValue
 * propertyValue (malloc and free by the invoker)
 */
Status DmVertexGetPropeByName(const DmVertexT *vertex, const char *propeName, DmValueT *propeValue)
{
    DB_POINTER3(vertex, propeName, propeValue);
    return RecordGetPropeByName(vertex->record, propeName, propeValue, true);
}

/*
 * get property by id for vertex
 * input: id, vertex
 * output: propertyValue
 * propertyValue (malloc and free by the invoker)
 */
Status DmVertexGetPropeById(const DmVertexT *vertex, uint32_t propeId, DmValueT *propeValue)
{
    DB_POINTER2(vertex, propeValue);
    return RecordGetPropeById(vertex->record, propeId, propeValue, true);
}

Status DmVertexGetRealPropeById(const DmVertexT *vertex, uint32_t propeId, DmValueT *propeValue)
{
#ifdef FEATURE_YANG
    return DmVertexGetYangRealPropeById(vertex, propeId, propeValue);
#else
    return DmVertexGetPropeById(vertex, propeId, propeValue);
#endif
}

Status DmVertexGetRealPropeByName(const DmVertexT *vertex, const char *propeName, DmValueT *propeValue)
{
#ifdef FEATURE_YANG
    return DmVertexGetYangRealPropeByName(vertex, propeName, propeValue);
#else
    return DmVertexGetPropeByName(vertex, propeName, propeValue);
#endif
}

Status DmVertexGetPropeByNameNoCopy(const DmVertexT *vertex, const char *propeName, DmValueT *propeValue)
{
    DB_POINTER3(vertex, propeName, propeValue);
    return RecordGetPropeByName(vertex->record, propeName, propeValue, false);
}

Status DmVertexGetPropeByIdNoCopy(const DmVertexT *vertex, uint32_t propeId, DmValueT *propeValue)
{
    DB_POINTER2(vertex, propeValue);
    return RecordGetPropeById(vertex->record, propeId, propeValue, false);
}

Status DmVertexGetRealPropeByIdNoCopy(const DmVertexT *vertex, uint32_t propeId, DmValueT *propeValue)
{
#ifdef FEATURE_YANG
    return DmVertexGetYangRealPropeByIdNoCopy(vertex, propeId, propeValue);
#else
    return DmVertexGetPropeByIdNoCopy(vertex, propeId, propeValue);
#endif
}

Status DmVertexGetRealPropeByNameNoCopy(const DmVertexT *vertex, const char *propeName, DmValueT *propeValue)
{
#ifdef FEATURE_YANG
    return DmVertexGetYangRealPropeByNameNoCopy(vertex, propeName, propeValue);
#else
    return DmVertexGetPropeByNameNoCopy(vertex, propeName, propeValue);
#endif
}

Status DmVertexGetPropNameById(const DmVertexT *vertex, uint32_t propId, char **propName)
{
    DB_POINTER2(vertex, propName);
    RecordDescT *recordDesc = vertex->record->recordDesc;
    if (propId >= recordDesc->propeNum) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PROPERTY, "Vertex get propName by id = %" PRIu32 "", propId);
        return GMERR_INVALID_PROPERTY;
    }
    *propName = recordDesc->propeInfos[propId].propeName;
    return GMERR_OK;
}

Status DmVertexGetPropTypeById(const DmVertexT *vertex, uint32_t propId, DbDataTypeE *dataType)
{
    DB_POINTER2(vertex, dataType);
    RecordDescT *recordDesc = vertex->record->recordDesc;
    if (propId >= recordDesc->propeNum) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PROPERTY, "Vertex get propType by id = %" PRIu32 "", propId);
        return GMERR_INVALID_PROPERTY;
    }
    *dataType = recordDesc->propeInfos[propId].dataType;
    return GMERR_OK;
}

/*
 * set property in batches by superfield name for vertex
 */
Status DmVertexSetSuperFieldByName(DmVertexT *vertex, const char *superFieldName, const DmValueT *superFieldValue)
{
    DB_POINTER3(vertex, superFieldName, superFieldValue);
    return RecordSetSuperFieldByName(vertex->record, superFieldName, superFieldValue);
}

// 通过seqNum（superField的脚标）设置对应superField的值信息
Status DmVertexSetSuperFieldById(const DmVertexT *vertex, uint32_t seqNum, const DmValueT *superFieldValue)
{
    DB_POINTER2(vertex, superFieldValue);
    return RecordSetSuperFieldById(vertex->record, seqNum, superFieldValue);
}

#ifdef FEATURE_GQL
// 若vertex label所有属性都是定长字段，可使用
Status DmVertexSetAll(DmVertexT *vertex, const uint8_t *value, const uint32_t length)
{
    DB_POINTER2(vertex, value);
    if (vertex->vertexDesc->labelLevel != VERTEX_LEVEL_SIMPLE) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Set all vertexProp, only simple vertex");
        return GMERR_DATA_EXCEPTION;
    }
    RecordDescT *recordDesc = vertex->record->recordDesc;
    if (!recordDesc->isFixed) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PROPERTY, "Set all only support fixLen");
        return GMERR_INVALID_PROPERTY;
    }

    // GmcExecGql 创建的vertex label不会有系统字段
    if (length != recordDesc->runningBufLen) {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Input DataLen(%" PRIu32 ") != running buf len(%" PRIu32 ")",
            length, recordDesc->runningBufLen);
        return GMERR_DATA_EXCEPTION;
    }

    errno_t err = memcpy_s(vertex->record->runningBuf, length, value, length);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Get prop by id.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    // GmcExecGql 创建的vertex label不会有系统字段
    (void)memset_s(
        vertex->record->propeIsSetValue, recordDesc->propeNum, DM_PROPERTY_IS_NOT_NULL, recordDesc->propeNum);
    return GMERR_OK;
}
#endif  // FEATURE_GQL

/*
 * get property in batches by superfield name from vertex
 */
Status DmVertexGetSuperFieldByName(DmVertexT *vertex, const char *superFieldName, DmValueT *superFieldValue)
{
    DB_POINTER3(vertex, superFieldName, superFieldValue);
    return RecordGetSuperFieldByName(vertex->record, superFieldName, superFieldValue);
}

// 通过seqNum（superField的脚标）获取对应superField的值信息
Status DmVertexGetSuperFieldById(const DmVertexT *vertex, uint32_t seqNum, DmValueT *superFieldValue)
{
    DB_POINTER2(vertex, superFieldValue);
    return RecordGetSuperFieldById(vertex->record, seqNum, superFieldValue);
}

/*
 * get value size by superfield name from vertex
 */
Status DmVertexGetSuperFieldLengthByName(DmVertexT *vertex, const char *superFieldName, uint32_t *length)
{
    DB_POINTER3(vertex, superFieldName, length);
    return RecordGetSuperFieldSizeByName(vertex->record, superFieldName, length);
}

Status DmVertexGetSuperFieldLengthById(DmVertexT *vertex, uint32_t seqNum, uint32_t *length)
{
    DB_POINTER2(vertex, length);
    return RecordGetSuperFieldSizeById(vertex->record, seqNum, length);
}

/*
 * get property size by name for vertex
 * input: propertyName, vertex
 * output: propertySize
 * for performance we advise to use the interface only when the type is unfixed, such as string.
 */
Status DmVertexGetPropeSizeByName(const DmVertexT *vertex, const char *propeName, uint32_t *propeSize)
{
    DB_POINTER3(vertex, propeName, propeSize);
    return RecordGetPropeSizeByName(vertex->record, propeName, propeSize);
}

SO_EXPORT Status DmVertexGetPropeSizeById(const DmVertexT *vertex, uint32_t propeId, uint32_t *propeSize)
{
    DB_POINTER2(vertex, propeSize);
    return RecordGetPropeSizeById(vertex->record, propeId, propeSize);
}

Status DmVertexGetRealPropeSizeById(const DmVertexT *vertex, uint32_t propeId, uint32_t *propeSize)
{
#ifdef FEATURE_YANG
    return DmVertexGetYangRealPropeSizeById(vertex, propeId, propeSize);
#else
    return DmVertexGetPropeSizeById(vertex, propeId, propeSize);
#endif
}

Status DmVertexGetPropeByIdPathNoCopy(
    const uint32_t *idPath, uint32_t depth, const DmVertexT *vertex, DmValueT *propeValue)
{
    DB_POINTER3(idPath, propeValue, vertex);
    if (depth > 1) {
        DmNodeT *node = NULL;
        Status ret = DmVertexGetNodeByIdPath(vertex, idPath, depth - 1, &node);
        if (ret != GMERR_OK) {
            return ret;
        }
        return DmNodeGetPropeByIdNoCopy(node, idPath[depth - 1], propeValue);
    } else if (depth == 1) {
        return DmVertexGetPropeByIdNoCopy(vertex, idPath[0], propeValue);
    } else {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PROPERTY, "Get prop by id path no copy, depth(%" PRIu32 ").", depth);
        return GMERR_INVALID_PROPERTY;
    }
}

Status DmVertexGetNodeByNameSilent(const DmVertexT *vertex, const char *nodeName, DmNodeT **node)
{
    DB_POINTER3(vertex, nodeName, node);
    for (uint32_t i = 0; i < vertex->vertexDesc->nodeNum; i++) {
        if (strcmp(nodeName, vertex->vertexDesc->nodeDescs[i]->name) == 0) {
            return DmVertexGetChildNodeByIndex(vertex, i, node);
        }
    }
    return GMERR_INVALID_NAME;
}

Status DmVertexGetNodeByName(const DmVertexT *vertex, const char *nodeName, DmNodeT **node)
{
    DB_POINTER3(vertex, nodeName, node);
    Status ret = DmVertexGetNodeByNameSilent(vertex, nodeName, node);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_NAME, "Vertex get node by inv name(%s).", nodeName);
    }

    return ret;
}

Status DmVertexGetNodeById(const DmVertexT *vertex, uint32_t id, DmNodeT **node)
{
    DB_POINTER2(vertex, node);
    for (uint32_t i = 0; i < vertex->vertexDesc->nodeNum; i++) {
        if (id == vertex->vertexDesc->nodeDescs[i]->nodeId) {
            return DmVertexGetChildNodeByIndex(vertex, i, node);
        }
    }
    DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "Vertex get node by inv id(%" PRIu32 ").", id);
    return GMERR_INVALID_PARAMETER_VALUE;
}

Status DmVertexGetChildNodeByIndex(const DmVertexT *vertex, uint32_t index, DmNodeT **childNode)
{
    DB_POINTER2(vertex, childNode);
    // 内部参数，不满足一定是编码问题，直接进行assert
    DB_ASSERT(index < vertex->vertexDesc->nodeNum);
    if (vertex->nodes[index] == NULL) {
        DmNodeDescT *nodeDesc = vertex->vertexDesc->nodeDescs[index];
        Status ret = DmCreateEmptyNode(vertex->memCtx, nodeDesc, &vertex->nodes[index], true);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "Get childNode by Index in %s, Index %" PRIu32 ".", nodeDesc->name, index);
            return ret;
        }
    }
    *childNode = vertex->nodes[index];
    return GMERR_OK;
}

Status DmVertexGetNodeByNamePath(const DmVertexT *vertex, char **namePath, uint32_t depth, DmNodeT **node)
{
    DB_POINTER3(vertex, namePath, node);
    DmNodeT *fatherNode = NULL;
    DmNodeT *childNode = NULL;
    Status ret = DmVertexGetNodeByName(vertex, namePath[0], &fatherNode);
    if (ret != GMERR_OK) {
        return ret;
    }
    for (uint32_t i = 1; i < depth; i++) {
        ret = DmNodeGetChildNodeByName(fatherNode, namePath[i], &childNode);
        if (ret != GMERR_OK) {
            return ret;
        }
        fatherNode = childNode;
    }
    *node = fatherNode;
    return GMERR_OK;
}

Status DmVertexGetNodeByIdPath(const DmVertexT *vertex, const uint32_t *idPath, uint32_t depth, DmNodeT **node)
{
    DB_POINTER3(vertex, idPath, node);
    DmNodeT *fatherNode = NULL;
    DmNodeT *childNode = NULL;
    Status ret = DmVertexGetNodeById(vertex, idPath[0], &fatherNode);
    if (ret != GMERR_OK) {
        return ret;
    }
    for (uint32_t i = 1; i < depth; i++) {
        ret = DmNodeGetChildNodeById(fatherNode, idPath[i], &childNode);
        if (ret != GMERR_OK) {
            return ret;
        }
        fatherNode = childNode;
    }
    *node = fatherNode;
    return GMERR_OK;
}
/*
 * get key buf efficiently
 */
Status DmGetKeyBufFromVertex(const DmVertexT *vertex, uint32_t indexId, uint8_t **keyBuf, uint32_t *length)
{
    DB_POINTER3(vertex, keyBuf, length);
    DmIndexKeyBufInfoT *indexKeyInfo = NULL;
    Status ret = DmVertexGetIndexKeyInfo(vertex, indexId, &indexKeyInfo);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t keyBufLen = 0;
    RecordGetKeyBuf(vertex->record, indexKeyInfo, vertex->vertexKeyBuf, &keyBufLen);

    *keyBuf = vertex->vertexKeyBuf;
    *length = keyBufLen;
    return GMERR_OK;
}

#ifdef FEATURE_GQL
/*
 * get key buf and put into user defined mem
 */
Status DmGetKeyBufFromVertex2ExistsBuf(const DmVertexT *vertex, uint32_t indexId, uint8_t *keyBuf, uint32_t *length)
{
    DB_POINTER3(vertex, keyBuf, length);
    DmIndexKeyBufInfoT *indexKeyInfo = NULL;
    Status ret = DmVertexGetIndexKeyInfo(vertex, indexId, &indexKeyInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    RecordGetKeyBuf(vertex->record, indexKeyInfo, keyBuf, length);
    return GMERR_OK;
}

void DmGetKeyBufFromVertexWithIndexInfo(
    const DmVertexT *vertex, DmIndexKeyBufInfoT *indexKeyInfo, uint8_t **keyBuf, uint32_t *keyBufLen)
{
    uint32_t len = 0;
    RecordGetKeyBuf(vertex->record, indexKeyInfo, vertex->vertexKeyBuf, &len);
    *keyBuf = vertex->vertexKeyBuf;
    *keyBufLen = len;
}
#endif

void DmGetKeyBufFromVertexWithKeyInfo(
    const DmVertexT *vertex, const DmIndexKeyBufInfoT *indexKeyInfo, uint8_t *keyBuf, uint32_t *length)
{
    DB_POINTER4(vertex, indexKeyInfo, keyBuf, length);
    *length = 0;
    RecordGetKeyBuf(vertex->record, indexKeyInfo, keyBuf, length);
}

void DmGetPrimaryKeyBufFromVertex(const DmVertexT *vertex, uint8_t *keyBuf, uint32_t *length)
{
    DB_POINTER3(vertex, keyBuf, length);
    DmIndexKeyBufInfoT *indexKeyInfo = NULL;
    indexKeyInfo = &vertex->vertexDesc->indexKeyBufInfos[0];

    *length = 0;
    RecordGetKeyBuf(vertex->record, indexKeyInfo, keyBuf, length);
}

Status DmVertexGetEdgeIndexByName(const DmVertexT *vertex, const char *edgeLabelName, uint32_t *index)
{
    DB_POINTER4(vertex, edgeLabelName, index, vertex->vertexDesc);
    DmVertexDescT *vertexdesc = vertex->vertexDesc;
    DmEdgeLabelInfoT *edgeLabelInfos = (DmEdgeLabelInfoT *)DbShmPtrToAddr(vertexdesc->commonInfo->relatedEdgeLabelsShm);
    if (edgeLabelInfos == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "EdgeLabelInfos shmPtr inv.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    for (uint32_t i = 0; i < vertexdesc->edgeLabelNum; i++) {
        if (strcmp(edgeLabelInfos[i].edgeName, edgeLabelName) == 0) {
            *index = i;
            return GMERR_OK;
        }
    }
    DB_LOG_AND_SET_LASERR(GMERR_UNDEFINED_OBJECT, "Find edge label, vertex get edgeIndex by name(%s).", edgeLabelName);
    return GMERR_UNDEFINED_OBJECT;
}

Status DmVertexGetEdgeIndexById(const DmVertexT *vertex, uint32_t edgeLabelId, uint32_t *index)
{
    DB_POINTER2(vertex, index);
    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    DmEdgeLabelInfoT *edgeLabelInfos = (DmEdgeLabelInfoT *)DbShmPtrToAddr(vertexDesc->commonInfo->relatedEdgeLabelsShm);
    if (edgeLabelInfos == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "EdgeLabelInfos shmPtr inv.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    for (uint32_t i = 0; i < vertexDesc->edgeLabelNum; i++) {
        if (edgeLabelInfos[i].edgeLabelId == edgeLabelId) {
            *index = i;
            return GMERR_OK;
        }
    }
    DB_LOG_AND_SET_LASERR(GMERR_UNDEFINED_OBJECT, "Find edge label, id(%" PRIu32 ").", edgeLabelId);
    return GMERR_UNDEFINED_OBJECT;
}

Status DmVertexSetFirstEdgeTopoAddrByName(DmVertexT *vertex, const char *edgeLabelName, uint64_t addr)
{
    DB_POINTER2(vertex, edgeLabelName);
    uint32_t index;
    Status ret = DmVertexGetEdgeIndexByName(vertex, edgeLabelName, &index);
    if (ret != GMERR_OK) {
        return ret;
    }
    vertex->edgeAddrsEntry[index].first = addr;
    return GMERR_OK;
}

Status DmVertexGetFirstEdgeTopoAddrByName(const DmVertexT *vertex, const char *edgeLabelName, uint64_t *addr)
{
    DB_POINTER3(vertex, edgeLabelName, addr);
    uint32_t index;
    Status ret = DmVertexGetEdgeIndexByName(vertex, edgeLabelName, &index);
    if (ret != GMERR_OK) {
        return ret;
    }
    *addr = vertex->edgeAddrsEntry[index].first;
    return GMERR_OK;
}

Status DmVertexSetFirstEdgeTopoAddrById(DmVertexT *vertex, uint32_t edgeLabelId, uint64_t addr, uint64_t *oldAddr)
{
    DB_POINTER2(vertex, oldAddr);

    uint32_t index = 0;
    Status ret = DmVertexGetEdgeIndexById(vertex, edgeLabelId, &index);
    if (ret != GMERR_OK) {
        return ret;
    }
    *oldAddr = vertex->edgeAddrsEntry[index].first;
    vertex->edgeAddrsEntry[index].first = addr;
    return GMERR_OK;
}

Status DmVertexGetFirstEdgeTopoAddrById(const DmVertexT *vertex, uint32_t edgeLabelId, uint64_t *addr)
{
    DB_POINTER2(vertex, addr);

    uint32_t index = 0;
    Status ret = DmVertexGetEdgeIndexById(vertex, edgeLabelId, &index);
    if (ret != GMERR_OK) {
        return ret;
    }
    *addr = vertex->edgeAddrsEntry[index].first;
    return GMERR_OK;
}

/*                          inner interface                      */
bool DmAutoIncValueIsValid(uint64_t autoIncMaxValue, uint64_t autoIncValue)
{
    return (autoIncValue > autoIncMaxValue || autoIncValue == 0) ? false : true;
}

Status DmGetAutoIncPropValue(DmAutoIncrPropInfoT *autoIncrInfo, DmValueT *propValue)
{
    DB_POINTER2(autoIncrInfo, propValue);
    // 业务要求，不对用户传进来的自增列值做校验，而是内部生成一个值去覆盖
    uint64_t autoIncMaxValue = autoIncrInfo->autoIncrMaxValue;
    // 对自增列的值进行原子自增
    uint64_t newAutoIncValue = DbAtomicFetchAndAdd64(&(autoIncrInfo->autoIncrValue), 1);
    // 检查经过原子自增后的自增列的值是否超过最大值
    if (!DmAutoIncValueIsValid(autoIncMaxValue, newAutoIncValue)) {
        (void)DbAtomicFetchAndSub64(&(autoIncrInfo->autoIncrValue), 1);
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Auto-inc value inv, new value:%" PRIu64 ".", newAutoIncValue);
        return GMERR_DATA_EXCEPTION;
    }
    DbDataTypeE dataType = autoIncrInfo->type;
    propValue->type = dataType;
    // 解析的时候就已经保证了，自增列属性的类型只能是uint32、uint64和int64
    if (dataType == DB_DATATYPE_UINT32) {
        propValue->value.uintValue = (uint32_t)newAutoIncValue;
    } else if (dataType == DB_DATATYPE_UINT64 || dataType == DB_DATATYPE_INT64) {
        propValue->value.ulongValue = newAutoIncValue;
    }
    return GMERR_OK;
}

#ifdef FEATURE_REPLICATION
Status AutoIncProcByOverwrite(const DmVertexT *vertex, uint32_t propId, DmAutoIncrPropInfoT *autoIncrInfo)
{
    DmValueT tempPropeValue;
    uint64_t curAutoIncValue;
    uint64_t autoIncMaxValue = autoIncrInfo->autoIncrMaxValue;
    Status ret = DmVertexGetPropeById(vertex, propId, &tempPropeValue);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Get vertex in slave auto inc proc, prop id = %" PRIu64 "", propId);
        return ret;
    }

    DbDataTypeE dataType = tempPropeValue.type;
    if (dataType == DB_DATATYPE_UINT32) {
        curAutoIncValue = (uint64_t)tempPropeValue.value.uintValue;
    } else if (dataType == DB_DATATYPE_UINT64) {
        curAutoIncValue = tempPropeValue.value.ulongValue;
    } else {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Unexpected autoincPropType=%" PRIu64 "", dataType);
        return GMERR_DATA_EXCEPTION;
    }

    if (!DmAutoIncValueIsValid(autoIncMaxValue, curAutoIncValue)) {
        DB_LOG_AND_SET_LASERR(
            GMERR_DATA_EXCEPTION, "autoinc limit exceeded, curAutoIncValue:%" PRIu64 ".", curAutoIncValue);
        return GMERR_DATA_EXCEPTION;
    }

    /* 考虑两种特殊情况：
    1）主机传入值=uint64_max，备机自增元数据应该被更新为0，自增列数据应该更新为uint64_max
    2）批备中，第一条数据传入值=uint64_max，备机自增元数据更新为0，第二条数据传入值=任意值时，备机自增元数据不能更新
    约束：修改自增元数据必须为原子操作，否则会有并发问题
    */
    uint64_t currAutoIncMeta;
    uint64_t oldAutoIncMeta = autoIncrInfo->autoIncrValue;
    while (curAutoIncValue > oldAutoIncMeta - 1) {
        currAutoIncMeta = DbAtomicValCAS64(&(autoIncrInfo->autoIncrValue), oldAutoIncMeta, curAutoIncValue + 1);
        if (currAutoIncMeta == oldAutoIncMeta) {
            break;
        }
        oldAutoIncMeta = currAutoIncMeta;
    }

    return GMERR_OK;
}
#endif

#ifdef FEATURE_REPLICATION
Status DmVertexSetAutoIncPropByOverwrite(const DmVertexT *vertex, const DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(vertex, vertexLabel);
    if (vertexLabel->commonInfo->autoIncrPropNum == 0) {
        return GMERR_OK;
    }
    Status ret;
    uint32_t propId = 0;
    uint32_t autoIncrPropNum = vertexLabel->commonInfo->autoIncrPropNum;
    DmAutoIncrPropInfoT *autoIncrInfo = MEMBER_PTR(vertexLabel->commonInfo, autoIncrPropInfo);
    for (uint32_t i = 0; i < autoIncrPropNum; ++i) {
        propId = autoIncrInfo[i].autoIncrPropId;
        ret = AutoIncProcByOverwrite(vertex, propId, &autoIncrInfo[i]);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "slave unable proc autoinc, prop id = %" PRIu64 "", propId);
            return ret;
        }
    }
    return GMERR_OK;
}
#endif

Status DmVertexSetAutoIncProp(const DmVertexT *vertex, const DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(vertex, vertexLabel);
    if (vertexLabel->commonInfo->autoIncrPropNum == 0) {
        return GMERR_OK;
    }
    Status ret;
    DmValueT propValue = {0};
    uint32_t propId = 0;
    uint32_t autoIncrPropNum = vertexLabel->commonInfo->autoIncrPropNum;
    bool isLeafList = DmIsLeafListVertexLabel(vertexLabel);
    DmAutoIncrPropInfoT *autoIncrInfo = MEMBER_PTR(vertexLabel->commonInfo, autoIncrPropInfo);
    for (uint32_t i = 0; i < autoIncrPropNum; ++i) {
        propId = autoIncrInfo[i].autoIncrPropId;
        // yang场景leaflist节点的id和pid可能被设置成自增列，但是id和pid不可自增，需略过处理
        if (isLeafList && (propId == DM_YANG_ID_PROPE_SUBSCRIPT || propId == DM_YANG_PID_PROPE_SUBSCRIPT)) {
            continue;
        }
        ret = DmGetAutoIncPropValue(&autoIncrInfo[i], &propValue);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = DmVertexSetPropeById(propId, propValue, vertex);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status DmVertexBufSetAutoIncProp(uint8_t *vertexBuf, const DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(vertexBuf, vertexLabel);
    // vertex不含有自增列
    if (vertexLabel->commonInfo->autoIncrPropNum == 0) {
        return GMERR_OK;
    }
    // vertex含有自增列
    Status ret;
    uint32_t autoIncPropNum = vertexLabel->commonInfo->autoIncrPropNum;
    uint32_t propId = 0;
    bool isLeafList = DmIsLeafListVertexLabel(vertexLabel);
    DmValueT propValue = {0};
    for (uint32_t i = 0; i < autoIncPropNum; ++i) {
        propId = vertexLabel->commonInfo->autoIncrPropInfo[i].autoIncrPropId;
        if (isLeafList && (propId == DM_YANG_PID_PROPE_SUBSCRIPT || propId == DM_YANG_ID_PROPE_SUBSCRIPT)) {
            continue;
        }
        ret = DmGetAutoIncPropValue(&vertexLabel->commonInfo->autoIncrPropInfo[i], &propValue);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = DmVertexBufSetFirstLevelFixedPropById(vertexBuf, vertexLabel->vertexDesc,
            vertexLabel->commonInfo->autoIncrPropInfo[i].autoIncrPropId, &propValue);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

// 只在插入流程中调用（Insert，Replace，以及无对应主键数据时的Merge操作）
Status DmCheckBitMapConstrict(DmVertexT *vertex)
{
    DB_POINTER(vertex);
    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    if (vertexDesc->hasBitMapProp == false) {
        return GMERR_OK;
    }
    // 为了兼容V3，一般复杂表在插入时允许局部设置bitmap的值；其他类型不支持在插入时局部设置bitmap的值
    if (vertexDesc->labelLevel != VERTEX_LEVEL_GENERAL && vertex->hasBitMapPropPartialSet == true) {
        DB_LOG_AND_SET_LASERR(
            GMERR_FEATURE_NOT_SUPPORTED, "Set bitmapValue partially only for general complex vertex label");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

bool DmVertexIsSetResColPrope(const DmVertexT *vertex)
{
    DB_POINTER(vertex);
    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    // vertex不含有资源列，返回false
    if (!vertexDesc->hasResCol) {
        return false;
    }
    // 遍历所有资源列，检查是否被更新
    for (uint32_t i = 0; i < vertexDesc->recordDesc->propeNum; i++) {
        if (vertexDesc->recordDesc->propeInfos[i].isResCol && vertex->record->propeIsSetValue[i] == 1) {
            return true;
        }
    }
    return false;
}

void DmVertexSetAutoIncProp2Null(const DmVertexT *vertex, const DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(vertex, vertexLabel);
    // vertex不含有自增列属性，直接返回
    if (vertexLabel->commonInfo->autoIncrPropNum == 0) {
        return;
    }
    DmAutoIncrPropInfoT *autoIncrInfo = MEMBER_PTR(vertexLabel->commonInfo, autoIncrPropInfo);
    for (uint32_t i = 0; i < vertexLabel->commonInfo->autoIncrPropNum; ++i) {
        // 将自增列属性置NULL
        vertex->record->propeIsSetValue[autoIncrInfo[i].autoIncrPropId] = DM_PROPERTY_IS_NULL;
    }
}

Status DmVertexSetResColPrope2Zero(DmVertexT *vertex)
{
    DB_POINTER(vertex);
    if (vertex->vertexDesc->resColNum == 0) {
        return GMERR_OK;
    }
    ResId resId[DM_RES_COL_MAX_COUNT] = {0};
    Status ret = DmVertexGetResColPrope(vertex, vertex->vertexDesc->resColNum, resId);
    if (ret != GMERR_OK) {
        return ret;
    }
    const DmResColInfoT *resColInfo = MEMBER_PTR(vertex->vertexDesc->commonInfo, resColInfo);
    for (uint32_t i = 0; i < vertex->vertexDesc->resColNum; ++i) {
        if (DmResColGetCountFromResId(resId[i]) > 0) {
            continue;
        }
        DmValueT propertyValue;
        propertyValue.type = DB_DATATYPE_RESOURCE;
        propertyValue.value.resValue = 0;  // 兼容V3，当资源字段的count为0时，将整个资源字段的值设为0
        ret = DmVertexSetPropeById(resColInfo->resPropeId[i], propertyValue, vertex);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status DmVertexSetResColPrope(const DmVertexT *vertex, uint32_t resColCount, const ResId *resId)
{
    DB_POINTER2(vertex, resId);
    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    if (!vertexDesc->hasResCol) {
        DB_ASSERT(vertexDesc->resColNum == 0);
        DB_LOG_AND_SET_LASERR(GMERR_RESOURCE_POOL_ERROR, "Inv resource column when set prop");
        return GMERR_RESOURCE_POOL_ERROR;
    }
    // 设置顶点中的资源字段必须一次全部设置完成
    if (vertexDesc->resColNum != resColCount) {
        DB_LOG_AND_SET_LASERR(GMERR_RESOURCE_POOL_ERROR,
            "Resource column count not equal. Input cnt: %" PRIu32 " inner cnt: %" PRIu32 ".", resColCount,
            vertexDesc->resColNum);
        return GMERR_RESOURCE_POOL_ERROR;
    }
    uint32_t resColId = 0;
    DmValueT propertyValue;
    Status ret = GMERR_OK;
    propertyValue.type = DB_DATATYPE_RESOURCE;
    for (uint32_t i = 0; i < vertexDesc->recordDesc->propeNum; i++) {
        if (vertexDesc->recordDesc->propeInfos[i].isResCol) {
            propertyValue.value.resValue = resId[resColId];
            ret = DmVertexSetPropeById(i, propertyValue, vertex);
            if (ret != GMERR_OK) {
                return ret;
            }
            resColId++;
        }
    }
    return GMERR_OK;
}

Status DmVertexGetResColPrope(DmVertexT *vertex, uint32_t resColCount, ResId *resId)
{
    DB_POINTER2(vertex, resId);
    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    // 该vertex label中没有资源字段
    if (!vertexDesc->hasResCol) {
        DB_ASSERT(vertexDesc->resColNum == 0);
        DB_LOG_AND_SET_LASERR(GMERR_RESOURCE_POOL_ERROR, "Vertex get resource col prop"
                                                         ", resource column not exist.");
        return GMERR_RESOURCE_POOL_ERROR;
    }
    // 获取定点中的资源字段必须一次全部获取完成
    if (resColCount != vertexDesc->resColNum) {
        DB_LOG_AND_SET_LASERR(GMERR_RESOURCE_POOL_ERROR,
            "Vertex get resource col prop,"
            " resource col count not equal, input cnt: %" PRIu32 ", inner cnt: %" PRIu32 ".",
            resColCount, vertexDesc->resColNum);
        return GMERR_RESOURCE_POOL_ERROR;
    }
    uint32_t resColId = 0;
    DmValueT propertyValue;
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < vertexDesc->recordDesc->propeNum; i++) {
        if (vertexDesc->recordDesc->propeInfos[i].isResCol) {
            ret = DmVertexGetPropeById(vertex, i, &propertyValue);
            if (ret != GMERR_OK) {
                return ret;
            }
            if (propertyValue.type != DB_DATATYPE_RESOURCE) {
                // 一个资源字段里的赋值类型无法预期，类型不符合时报错返回
                DB_LOG_AND_SET_LASERR(
                    GMERR_DATATYPE_MISMATCH, "Unexpected resource type: %" PRIu32 ".", (uint32_t)propertyValue.type);
                return GMERR_DATATYPE_MISMATCH;
            }
            resId[resColId] = propertyValue.value.resValue;
            resColId++;
        }
    }
    return GMERR_OK;
}

void DmVertexGetRunningbuf(
    DmVertexT *vertex, uint8_t **runningBuf, uint32_t *fixLen, int8_t **propeIsSetValue, uint32_t *propeNum)
{
    DB_POINTER5(vertex, runningBuf, fixLen, propeIsSetValue, propeNum);
    DmRecordT *record = vertex->record;
    *runningBuf = record->runningBuf;
    *fixLen = record->recordDesc->fixedPropertiesLen;
    *propeIsSetValue = record->propeIsSetValue;
    *propeNum = record->recordDesc->propeNum;
}

void DmVertexGetRecordbuf(DmVertexT *vertex, uint8_t **recordSeriBuf, uint32_t *recordSeriBufLen)
{
    DB_POINTER3(vertex, recordSeriBuf, recordSeriBufLen);
    DmRecordT *record = vertex->record;
    *recordSeriBuf = record->recordSeriBuf;
    *recordSeriBufLen = record->recordSeriBufLen;
}

Status DmVertexSetUint8PropeByName(const DmVertexT *vertex, const char *propeName, uint8_t value)
{
    DB_POINTER2(vertex, propeName);
    DmValueT propertyValue;
    propertyValue.type = DB_DATATYPE_UINT8;
    propertyValue.value.ushortValue = value;
    return DmVertexSetPropeByName(propeName, propertyValue, vertex);
}

Status DmVertexSetUint16PropeByName(const DmVertexT *vertex, const char *propeName, uint16_t value)
{
    DB_POINTER2(vertex, propeName);
    DmValueT propertyValue;
    propertyValue.type = DB_DATATYPE_UINT16;
    propertyValue.value.ushortValue = value;
    return DmVertexSetPropeByName(propeName, propertyValue, vertex);
}

Status DmVertexSetInt32PropeByName(const DmVertexT *vertex, const char *propeName, int32_t value)
{
    DB_POINTER2(vertex, propeName);
    DmValueT propertyValue;
    propertyValue.type = DB_DATATYPE_INT32;
    propertyValue.value.intValue = value;
    return DmVertexSetPropeByName(propeName, propertyValue, vertex);
}

Status DmVertexSetUint32PropeByName(const DmVertexT *vertex, const char *propeName, uint32_t value)
{
    DB_POINTER2(vertex, propeName);
    DmValueT propertyValue;
    propertyValue.type = DB_DATATYPE_UINT32;
    propertyValue.value.uintValue = value;
    return DmVertexSetPropeByName(propeName, propertyValue, vertex);
}

Status DmVertexSetUint64PropeByName(const DmVertexT *vertex, const char *propeName, uint64_t value)
{
    DB_POINTER2(vertex, propeName);
    DmValueT propertyValue;
    propertyValue.type = DB_DATATYPE_UINT64;
    propertyValue.value.ulongValue = value;
    return DmVertexSetPropeByName(propeName, propertyValue, vertex);
}

Status DmVertexSetInt64PropeByName(const DmVertexT *vertex, const char *propeName, int64_t value)
{
    DB_POINTER2(vertex, propeName);
    DmValueT propertyValue;
    propertyValue.type = DB_DATATYPE_INT64;
    propertyValue.value.longValue = value;
    return DmVertexSetPropeByName(propeName, propertyValue, vertex);
}

SO_EXPORT_FOR_TS Status DmVertexSetStrPropeByName(const DmVertexT *vertex, const char *propeName, const char *value)
{
    DB_POINTER3(vertex, propeName, value);
    DmValueT propertyValue;
    propertyValue.type = DB_DATATYPE_STRING;
    propertyValue.value.constStrAddr = value;
    propertyValue.value.length = DM_STR_LEN(value);
    return DmVertexSetPropeByName(propeName, propertyValue, vertex);
}

Status DmVertexSetFixedPropeByName(const DmVertexT *vertex, const char *propeName, char *value, uint32_t valueLen)
{
    DB_POINTER3(vertex, propeName, value);
    DmValueT propertyValue;
    propertyValue.type = DB_DATATYPE_FIXED;
    propertyValue.value.strAddr = value;
    propertyValue.value.length = valueLen;
    return DmVertexSetPropeByName(propeName, propertyValue, vertex);
}

bool DmVertexResAndPartitionPropeIsAllSet(const DmVertexT *vertex)
{
    DB_POINTER(vertex);
    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    DmPropertyInfoT *propeInfos = vertexDesc->recordDesc->propeInfos;
    int8_t *propeIsSetValue = vertex->record->propeIsSetValue;
    uint16_t *notNullPropeIds = vertexDesc->recordDesc->notNullPropeIds;
    uint16_t notNullNum = vertexDesc->recordDesc->notNullPropeNum;
    for (uint32_t i = 0; i < notNullNum; i++) {
        uint16_t propeId = notNullPropeIds[i];
        if ((propeInfos[propeId].isResCol || propeInfos[propeId].dataType == DB_DATATYPE_PARTITION) &&
            propeIsSetValue[propeId] == DM_PROPERTY_IS_NULL) {
            return false;
        }
    }
    return true;
}

uint32_t DmVertexGetEdgeLabelNum(DmVertexT *vertex)
{
    DB_POINTER(vertex);
    return vertex->edgeLabelNum;
}

uint32_t DmVertexDescGetDefaultValueNumById(const DmVertexDescT *vertexDesc, uint32_t id)
{
    DB_POINTER(vertexDesc);
    return vertexDesc->recordDesc->propeInfos[id].defaultValueNum;
}

bool DmVertexHasMemberKey(const DmVertexT *vertex)
{
    DB_POINTER(vertex);
    return vertex->vertexDesc->hasMemberKey;
}

bool DmVertexIsDelta(const DmVertexT *vertex)
{
    DB_POINTER(vertex);
    return vertex->isDeltaVertex;
}

bool DmVertexPropIsValidById(DmVertexT *vertex, uint32_t propId)
{
    DB_POINTER4(vertex, vertex->vertexDesc, vertex->vertexDesc->recordDesc, vertex->vertexDesc->recordDesc->propeInfos);
    RecordDescT *recordDesc = vertex->vertexDesc->recordDesc;
    DB_ASSERT(propId < recordDesc->propeNum);
    return recordDesc->propeInfos[propId].isValid;
}

DmEdgeAddrsT *DmVertexGetEdgeAddrs(DmVertexT *vertex)
{
    DB_POINTER(vertex);
    return vertex->edgeAddrsEntry;
}

Status DmVertexCheckValueIsChanged(
    uint32_t *idPath, uint32_t depth, const DmVertexT *deltaVertex, const DmVertexT *oldVertex, bool *propeIsChanged)
{
    Status ret;
    DB_POINTER4(idPath, deltaVertex, oldVertex, propeIsChanged);
    *propeIsChanged = false;
    DmNodeT *node = NULL;
    DmNodeT *oldNode = NULL;
    if (depth > 1) {  // depth 大于1 表示此时是子节点下的属性
        ret = DmVertexGetNodeById(deltaVertex, idPath[0], &node);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = DmVertexGetNodeById(oldVertex, idPath[0], &oldNode);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (depth == 2) {  // depth 等于2 表示此时根节点下的子节点正好是最终属性所在的节点
            if (deltaVertex->isDeltaVertex) {
                return LeafNodeCheckValueIsChangedInUpdate(node, idPath[depth - 1], propeIsChanged);
            } else {
                return LeafNodeCheckValueIsChangedInReplace(node, oldNode, idPath[depth - 1], propeIsChanged);
            }
        }
        if (deltaVertex->isDeltaVertex) {
            return CheckNodePropeIsChangedInUpdate(idPath, depth, node, 1, propeIsChanged);
        } else {
            DmIdPathT path = {.path = (uint32_t *)idPath, .depth = depth};
            return CheckNodePropeIsChangedInReplace(&path, node, oldNode, 1, propeIsChanged);
        }
    } else if (depth == 1) {
        if (deltaVertex->isDeltaVertex) {
            return RecordGetPropeIsChangedInUpdate(deltaVertex->record, idPath[0], propeIsChanged);
        } else {
            return RecordGetPropeIsChangedInReplace(deltaVertex->record, oldVertex->record, idPath[0], propeIsChanged);
        }
    } else {
        DB_LOG_AND_SET_LASERR(GMERR_DATA_EXCEPTION, "Check value in delta vertex. Inv tree depth(%" PRIu32 ").", depth);
        return GMERR_DATA_EXCEPTION;
    }
}

/*****************************************Last Edge Addr操作*************************************************/

Status DmVertexSetLastEdgeTopoAddrById(DmVertexT *vertex, uint32_t edgeLabelId, uint64_t addr, uint64_t *oldAddr)
{
    DB_POINTER2(vertex, oldAddr);

    uint32_t index = 0;
    Status ret = DmVertexGetEdgeIndexById(vertex, edgeLabelId, &index);
    if (ret != GMERR_OK) {
        return ret;
    }
    *oldAddr = vertex->edgeAddrsEntry[index].last;
    vertex->edgeAddrsEntry[index].last = addr;
    return GMERR_OK;
}

Status DmVertexGetLastEdgeTopoAddrById(const DmVertexT *vertex, uint32_t edgeLabelId, uint64_t *addr)
{
    DB_POINTER2(vertex, addr);

    uint32_t index = 0;
    Status ret = DmVertexGetEdgeIndexById(vertex, edgeLabelId, &index);
    if (ret != GMERR_OK) {
        return ret;
    }
    *addr = vertex->edgeAddrsEntry[index].last;
    return GMERR_OK;
}

Status DmVertexSetLastEdgeTopoAddrByName(DmVertexT *vertex, const char *edgeLabelName, uint64_t addr)
{
    DB_POINTER2(vertex, edgeLabelName);

    uint32_t index;
    Status ret = DmVertexGetEdgeIndexByName(vertex, edgeLabelName, &index);
    if (ret != GMERR_OK) {
        return ret;
    }
    vertex->edgeAddrsEntry[index].last = addr;
    return GMERR_OK;
}

Status DmVertexGetLastEdgeTopoAddrByName(const DmVertexT *vertex, const char *edgeLabelName, uint64_t *addr)
{
    DB_POINTER3(vertex, edgeLabelName, addr);

    uint32_t index;
    Status ret = DmVertexGetEdgeIndexByName(vertex, edgeLabelName, &index);
    if (ret != GMERR_OK) {
        return ret;
    }
    *addr = vertex->edgeAddrsEntry[index].last;
    return GMERR_OK;
}

// datalog专用函数，函数内部目前没有进行任何校验
// fromVertex和toVertex对应相同的schema，因此runningbuf直接进行拷贝，并额外处理大字段
Status DmVertexCopy(const DmVertexT *fromVertex, DmVertexT *toVertex)
{
    DmRecordT *fromRecord = fromVertex->record;
    DmRecordT *toRecord = toVertex->record;
    // 如果有大变长字段，那么需要将toVertex重置下，因为RecordLargePropCopy要重新申请内存
    if (toRecord->hasLongVarPrope) {
        FreeRecordLongVarProp(toRecord, toRecord->runningBuf);
        toRecord->hasLongVarPrope = 0;
    }
    RecordDescT *fromRecordDesc = fromRecord->recordDesc;
    RecordDescT *toRecordDesc = toRecord->recordDesc;
    DB_ASSERT(fromRecordDesc->propeNum == toRecordDesc->propeNum);
    DB_ASSERT(fromRecordDesc->runningBufLen == toRecordDesc->runningBufLen);

    uint32_t len = fromRecordDesc->propeNum * (uint32_t)sizeof(int8_t);
    errno_t err = memcpy_s(toRecord->propeIsSetValue, len, fromRecord->propeIsSetValue, len);
    DB_ASSERT(err == EOK);

    uint32_t runningBufLen = fromRecordDesc->runningBufLen;
    err = memcpy_s(toRecord->runningBuf, runningBufLen, fromRecord->runningBuf, runningBufLen);
    DB_ASSERT(err == EOK);

    if (fromRecord->hasLongVarPrope) {
        toRecord->hasLongVarPrope = 1;
        return RecordLargePropCopy(fromRecord, toRecord);
    }
    return GMERR_OK;
}

Status DmProjectProperty(DmVertexT *fromVertex, DmVertexT *toVertex, DmPairT *pair, uint32_t len)
{
    DB_POINTER3(fromVertex, toVertex, pair);
    uint8_t *fromRuningBuf = fromVertex->record->runningBuf;
    uint8_t *toRuningBuf = toVertex->record->runningBuf;
    Status ret;
    for (uint32_t i = 0; i < len; ++i) {
        uint32_t fromId = pair[i].from;
        uint32_t toId = pair[i].to;
        // 保证id不越界
        DB_ASSERT(fromId < (RecordGetPropeNumExcludeSysPrope(fromVertex->record->recordDesc)));
        DB_ASSERT(toId < (RecordGetPropeNumExcludeSysPrope(toVertex->record->recordDesc)));

        DmPropertyInfoT fromPropeInfos = fromVertex->record->recordDesc->propeInfos[fromId];
        DmPropertyInfoT toPropeInfos = toVertex->record->recordDesc->propeInfos[toId];
        uint32_t size = fromPropeInfos.propeMaxLen;
        // 调用方需要保证具有映射关系的属性相同，并且已赋值
        DB_ASSERT(size == toPropeInfos.propeMaxLen);
        DB_ASSERT(fromPropeInfos.dataType == toPropeInfos.dataType);
        DB_ASSERT(fromVertex->record->propeIsSetValue[fromId] == DM_PROPERTY_IS_NOT_NULL);
        DB_ASSERT(fromPropeInfos.isValid == true);
        DB_ASSERT(toPropeInfos.isValid == true);
        // 暂不支持bitmap字段
        DB_ASSERT(fromPropeInfos.dataType != DB_DATATYPE_BITMAP);

        uint8_t *fromBufCursor = fromRuningBuf + fromPropeInfos.offset;
        uint8_t *toBufCursor = toRuningBuf + toPropeInfos.offset;

        if (DmIsBitFieldType(fromPropeInfos.dataType)) {
            DmValueT value = {0};
            value.type = fromPropeInfos.dataType;
            (void)GetBitfieldValueFromBuf(fromBufCursor, &fromPropeInfos, &value);
            ret = GetMergedBitFieldPropeValue(toBufCursor, &toPropeInfos, &value);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                return ret;
            }
            ret = SetPropeValueIntoBuf(toVertex->memCtx, toPropeInfos.propeMaxLen, &value, toBufCursor);
            if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
                return ret;
            }
        } else if (!DmIsFixedType(fromPropeInfos.dataType)) {
            bool isLongVarPrope = false;
            ret = CopyVarPropeValue(toVertex->memCtx, fromBufCursor, toBufCursor, &isLongVarPrope);
            if (ret != GMERR_OK) {
                return ret;
            }
            if (isLongVarPrope) {
                toVertex->record->hasLongVarPrope = 1;
            }
        } else {
            errno_t err = memcpy_s(toBufCursor, size, fromBufCursor, size);
            DB_ASSERT(err == EOK);
        }
        toVertex->record->propeIsSetValue[toId] = DM_PROPERTY_IS_NOT_NULL;
    }
    return GMERR_OK;
}

Status DmProjectPropertyToArray(
    DmVertexT *fromVertex, DmValueT *toArray, uint32_t arrlen, DmPairT *pair, uint32_t pairlen)
{
    DB_POINTER3(fromVertex, toArray, pair);
    uint8_t *fromRuningBuf = fromVertex->record->runningBuf;
    Status ret;
    for (uint32_t i = 0; i < pairlen; ++i) {
        uint32_t fromId = pair[i].from;
        uint32_t toId = pair[i].to;
        // 保证id不越界
        DB_ASSERT(fromId < (RecordGetPropeNumExcludeSysPrope(fromVertex->record->recordDesc)));
        DB_ASSERT(toId < arrlen);

        DmPropertyInfoT fromPropeInfos = fromVertex->record->recordDesc->propeInfos[fromId];
        uint32_t size = fromPropeInfos.propeMaxLen;
        DB_ASSERT(fromVertex->record->propeIsSetValue[fromId] == DM_PROPERTY_IS_NOT_NULL);
        DB_ASSERT(fromPropeInfos.isValid == true);

        DB_ASSERT(fromPropeInfos.dataType != DB_DATATYPE_BITMAP);
        DB_ASSERT(toArray[toId].type == fromPropeInfos.dataType);

        uint8_t *fromBufCursor = fromRuningBuf + fromPropeInfos.offset;
        if (DmIsBitFieldType(fromPropeInfos.dataType)) {
            ret = GetBitfieldValueFromBuf(fromBufCursor, &fromPropeInfos, &toArray[toId]);
            if (ret != GMERR_OK) {
                DB_LOG_AND_SET_LASERR(ret, "Project bitfield prop %s to array.", fromPropeInfos.propeName);
                return ret;
            }
            continue;
        }

        if (DM_TYPE_NEED_MALLOC(fromPropeInfos.dataType)) {
            toArray[toId].value.length = size;
        }
        ret = GetPropeValueFromBuf(fromBufCursor, size, &toArray[toId], true);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "Project prop %s to array.", fromPropeInfos.propeName);
            return ret;
        }
    }
    return GMERR_OK;
}

bool DmProjectPropertyIsSame(DmVertexT *fromVertex, DmVertexT *toVertex, DmPairT *pair, uint32_t len)
{
    DB_POINTER3(fromVertex, toVertex, pair);
    uint8_t *fromRuningBuf = fromVertex->record->runningBuf;
    uint8_t *toRuningBuf = toVertex->record->runningBuf;
    for (uint32_t i = 0; i < len; ++i) {
        uint32_t toId = pair[i].to;
        uint32_t fromId = pair[i].from;
        // 保证id不越界
        DB_ASSERT(toId < (RecordGetPropeNumExcludeSysPrope(toVertex->record->recordDesc)));
        DB_ASSERT(fromId < (RecordGetPropeNumExcludeSysPrope(fromVertex->record->recordDesc)));

        DmPropertyInfoT fromPropeInfos = fromVertex->record->recordDesc->propeInfos[fromId];
        DmPropertyInfoT toPropeInfos = toVertex->record->recordDesc->propeInfos[toId];

        // 调用方需要保证具有映射关系的属性相同，并且已赋值
        DB_ASSERT(fromPropeInfos.propeMaxLen == toPropeInfos.propeMaxLen);
        DB_ASSERT(fromPropeInfos.dataType == toPropeInfos.dataType);
        DB_ASSERT(fromVertex->record->propeIsSetValue[fromId] == DM_PROPERTY_IS_NOT_NULL);
        DB_ASSERT(toVertex->record->propeIsSetValue[toId] == DM_PROPERTY_IS_NOT_NULL);
        DB_ASSERT(fromPropeInfos.isValid == true);
        DB_ASSERT(toPropeInfos.isValid == true);

        // 暂不支持bitmap字段
        DB_ASSERT(fromPropeInfos.dataType != DB_DATATYPE_BITMAP);
        // 默认字段均已设值
        uint8_t *fromBufCursor = fromRuningBuf + fromPropeInfos.offset;
        uint8_t *toBufCursor = toRuningBuf + toPropeInfos.offset;
        if (DmIsBitFieldType(fromPropeInfos.dataType)) {
            DmValueT fromValue = {0};
            DmValueT toValue = {0};
            fromValue.type = fromPropeInfos.dataType;
            toValue.type = toPropeInfos.dataType;
            (void)GetBitfieldValueFromBuf(fromBufCursor, &fromPropeInfos, &fromValue);
            (void)GetBitfieldValueFromBuf(toBufCursor, &toPropeInfos, &toValue);
            if (!DmValueIsEqual(&fromValue, &toValue)) {
                return false;
            }
        } else if (!DmIsFixedType(fromPropeInfos.dataType)) {
            if (!VarPropeValueIsSame(fromBufCursor, toBufCursor)) {
                return false;
            }
        } else {
            if (memcmp(fromBufCursor, toBufCursor, fromPropeInfos.propeMaxLen) != 0) {
                return false;
            }
        }
    }
    return true;
}

// fromVertex和toVertex对应相同的schema
Status DmVertexIsSame(DmVertexT *fromVertex, DmVertexT *toVertex, bool *isSame)
{
    DB_POINTER3(fromVertex, toVertex, isSame);
    *isSame = true;
    DmVertexDescT *vertexDesc = fromVertex->vertexDesc;
    Status ret = RecordIsSame(fromVertex->record, toVertex->record, isSame);
    if (!(*isSame) || ret != GMERR_OK || vertexDesc->vertexType != DM_TREE_VERTEX) {
        return ret;
    }
    // 比较node
    for (uint32_t i = 0; i < vertexDesc->nodeNum; i++) {
        ret = NodeIsSame(fromVertex->nodes[i], toVertex->nodes[i], isSame);
        if (!(*isSame) || ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

bool DmVertexBufIsSecIndexChanged(uint8_t *vertexBuf, DmVlIndexLabelT *indexLabel)
{
    DB_POINTER2(vertexBuf, indexLabel);
    if (SECUREC_UNLIKELY(indexLabel->idxLabelBase.indexConstraint == PRIMARY)) {
        DB_ASSERT(false);
        return false;
    }
    uint8_t *nullInfoBuf = vertexBuf;
    VertexBufJumpToNullInfo(&nullInfoBuf);

    for (uint32_t i = 0; i < indexLabel->propeNum; ++i) {
        if (DmGetBitValueFromUint8Arr(nullInfoBuf, indexLabel->propIds[i]) == DM_PROPERTY_IS_NOT_NULL) {
            return true;
        }
    }
    return false;
}

#ifdef FEATURE_GQL
static void FreeVertexInnerMemoryBatch(DmVertexT *vertex)
{
    if (vertex->record != NULL) {
        DmRecordT *record = vertex->record;
        if (record->hasLongVarPrope == 1) {
            FreeRecordLongVarProp(record, record->runningBuf);
        }
    }

    DbDynMemCtxFree(vertex->memCtx, vertex->vertexSeriBuf);
    vertex->vertexSeriBuf = NULL;
}

static Status CreateEmptyVertexInnerBatch(DmVertexT *vertex, uint8_t **vertexBufCursor, uint8_t **recordBufCursor)
{
    DmVertexDescT *vertexDesc = vertex->vertexDesc;
    Status ret = CreateEmptyRecordBatch(
        vertex->memCtx, DM_RECORD_IN_VERTEX, vertexDesc->recordDesc, &vertex->record, recordBufCursor);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        return ret;
    }
    InitBasicVertexInfo(vertex);
    vertex->vertexSeriBuf = NULL;
    VertexInitKeyBuf(vertex, vertexBufCursor);

    // 当前batch场景主要是path在使用，当前无边实例和nodes，这两类成员初始化流程skip
    return GMERR_OK;
}

static Status CreateEmptyVertexByVertexDescBatch(
    DbMemCtxT *memCtx, DmVertexDescT *vertexDesc, uint8_t **vertexBuf, uint8_t **recordBuf)
{
    uint32_t memSize = vertexDesc->memSizePreMalloc;

    uint8_t *bufCursor = *vertexBuf;
    DmVertexT *vertex = (DmVertexT *)(void *)bufCursor;
    bufCursor += (uint32_t)sizeof(DmVertexT);
    vertex->memCtx = memCtx;
    vertex->vertexDesc = (DmVertexDescT *)vertexDesc;
    Status ret = CreateEmptyVertexInnerBatch(vertex, &bufCursor, recordBuf);
    if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
        FreeVertexInnerMemoryBatch(vertex);
        return ret;
    }
    if (SECUREC_UNLIKELY(bufCursor - *vertexBuf != memSize)) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "Size of alloc buf %" PRIu32 ", Write size is %" PRIu32 ". ",
            memSize, (uint32_t)(bufCursor - *vertexBuf));
        ret = GMERR_INTERNAL_ERROR;
        FreeVertexInnerMemoryBatch(vertex);
        return ret;
    }
    *vertexBuf = bufCursor;
    return GMERR_OK;
}

/*
 * 批量创建空点接口，批量数据处理，可以减少malloc调用，cache友好；出参为vertex，批量空点的头指针，
 * 空点间跨度为vertexDesc中的memSizePreMalloc
 */
Status DmCreateEmptyVertexWithMemCtxBatch(DbMemCtxT *memCtx, PreAllocMemParaT *preAllocMemPara,
    DmVertexLabelT *vertexLabel, uint8_t **vertexes, uint32_t batchCnt)
{
    DB_POINTER3(memCtx, vertexLabel, vertexes);
    Status ret = VertexLabelInitVertexDesc(vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }
    // vertex有两段较大的内存可以提前准备好：DmVertexT，以及DmVertexT成员：DmRecordT
    uint32_t vertexesMemSize = vertexLabel->vertexDesc->memSizePreMalloc * batchCnt;
    uint32_t recordsMemSize = vertexLabel->vertexDesc->recordDesc->memSizePreMalloc * batchCnt;
    uint8_t *dmVertexBuf = NULL;
    uint8_t *recordBuf = NULL;
    if (preAllocMemPara != NULL &&
        IsPreAllocBufferSuffice(preAllocMemPara->preAllocBuffer, vertexesMemSize + recordsMemSize)) {
        dmVertexBuf = preAllocMemPara->preAllocBuffer->bufferCursor;
        preAllocMemPara->preAllocBuffer->bufferCursor += vertexesMemSize;
        recordBuf = preAllocMemPara->preAllocBuffer->bufferCursor;
        preAllocMemPara->preAllocBuffer->bufferCursor += recordsMemSize;
        preAllocMemPara->isUsed = true;
    } else {
        dmVertexBuf = (uint8_t *)DbDynMemCtxAlloc(memCtx, vertexesMemSize);
        if (SECUREC_UNLIKELY(dmVertexBuf == NULL)) {
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Create empty vertex batch.");
            return GMERR_OUT_OF_MEMORY;
        }
        recordBuf = (uint8_t *)DbDynMemCtxAlloc(memCtx, recordsMemSize);
        if (SECUREC_UNLIKELY(recordBuf == NULL)) {
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Create vertex record batch");
            DbDynMemCtxFree(memCtx, dmVertexBuf);
            return GMERR_OUT_OF_MEMORY;
        }
    }
    (void)memset_s(dmVertexBuf, vertexesMemSize, 0x00, vertexesMemSize);
    (void)memset_s(recordBuf, recordsMemSize, 0x00, recordsMemSize);
    *vertexes = dmVertexBuf;

    for (uint32_t i = 0; i < batchCnt; i++) {
        ret = CreateEmptyVertexByVertexDescBatch(memCtx, vertexLabel->vertexDesc, &dmVertexBuf, &recordBuf);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            if (preAllocMemPara != NULL && !preAllocMemPara->isUsed) {
                DbDynMemCtxFree(memCtx, recordBuf);
                DbDynMemCtxFree(memCtx, dmVertexBuf);
            }
            return ret;
        }
    }

    return GMERR_OK;
}

void DmDestroyEmptyVertexBatch(uint8_t *batchBufHead, uint32_t batchCnt)
{
    if (batchBufHead == NULL) {
        return;
    }
    // 取出record头
    DmVertexT *firstVertex = (DmVertexT *)(void *)batchBufHead;
    DmRecordT *recordHead = firstVertex->record;
    uint32_t vertexSlotSize = firstVertex->vertexDesc->memSizePreMalloc;

    uint8_t *tmpHead = batchBufHead;
    DmVertexT *tmpVertex = NULL;
    for (uint32_t i = 0; i < batchCnt; i++) {
        tmpVertex = (DmVertexT *)(void *)tmpHead;
        FreeVertexInnerMemoryBatch(tmpVertex);
        tmpVertex->headMagicCode = 0;
        tmpVertex->state = DM_VERTEX_DESTROYED;
        tmpHead += vertexSlotSize;
    }

    // 此处释放批量申请的两段内存：批量record，批量vertex
    DbDynMemCtxFree(firstVertex->memCtx, recordHead);
    DbDynMemCtxFree(firstVertex->memCtx, batchBufHead);
}
#endif
