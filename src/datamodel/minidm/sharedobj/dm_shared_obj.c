/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: Implementation of data model related functions of distribution module
 * Author: huanghe
 * Create: 2024-02-06
 */

#include "dm_data_define.h"
#include "dm_shared_obj.h"

inline static uint32_t DmGetBaseAttributeLength(DmBaseAttributeT baseAttributes)
{
    uint32_t length = (uint32_t)sizeof(uint16_t);
    length += baseAttributes.keyLen;
    length += (uint32_t)sizeof(uint8_t);
    length += (uint32_t)sizeof(uint16_t);
    length += baseAttributes.valueLen;
    return length;
}

inline static uint32_t DmGetBaseEmbedLength(DmBaseEmbedT baseEmbed)
{
    uint32_t length = (uint32_t)sizeof(uint16_t);
    length += baseEmbed.typeLen;
    length += (uint32_t)sizeof(uint16_t);
    length += baseEmbed.pathLen;
    return length;
}

inline static void BufLenSafeCalculate(size_t size, uint8_t **buf, uint32_t *bufLen)
{
    DB_ASSERT((*bufLen) >= size);
    *buf += size;
    *bufLen -= size;
}

static uint32_t GetBaseContentSerializeLen(DmStorageElementT *storageElement)
{
    uint32_t length = 0u;
    if ((storageElement->flag & ELEMENT_STRING_TYPE) == ELEMENT_STRING_TYPE) {
        length += sizeof(uint32_t);
        length += storageElement->string.byteLength;
    } else if ((storageElement->flag & ELEMENT_ATTRIBUTE_TYPE) == ELEMENT_ATTRIBUTE_TYPE) {
        length += sizeof(uint16_t);
        length += storageElement->attribute.keyLen;
        length += sizeof(uint8_t);
        // value len.
        length += sizeof(uint16_t);
        if (storageElement->attribute.valueType == DM_ATTRIBUTE_VALUE_STRING_START ||
            storageElement->attribute.valueType == DM_ATTRIBUTE_VALUE_STRING_END) {
            // String have value.
            length += storageElement->attribute.valueLen;
        }
    } else if ((storageElement->flag & ELEMENT_EMBED_TYPE) == ELEMENT_EMBED_TYPE) {
        length += sizeof(uint16_t);
        length += storageElement->embed.typeLen;
        length += sizeof(uint16_t);
        length += storageElement->embed.pathLen;
    } else {
        DB_ASSERT(false);
    }

    return length;
}

static uint32_t DmGetStorageElementSerializeLen(DmStorageElementT *storageElement, uint32_t *valueLen)
{
    DB_POINTER(storageElement);
    *valueLen += sizeof(uint32_t);  // flag

    // Only store clock for now.
    if (storageElement->prevId != NULL) {
        if (storageElement->prevId->equipId != NULL) {
            *valueLen += sizeof(uint8_t);
            *valueLen += DM_STR_LEN(storageElement->prevId->equipId);
        }

        *valueLen += sizeof(uint32_t);
    }
    if (storageElement->nextId != NULL) {
        if (storageElement->nextId->equipId != NULL) {
            *valueLen += sizeof(uint8_t);
            *valueLen += DM_STR_LEN(storageElement->nextId->equipId);
        }

        *valueLen += sizeof(uint32_t);
    }
    if (storageElement->originPrev != NULL) {
        if (storageElement->originPrev->equipId != NULL) {
            *valueLen += sizeof(uint8_t);
            *valueLen += DM_STR_LEN(storageElement->originPrev->equipId);
        }

        *valueLen += sizeof(uint32_t);
    }
    if (storageElement->originNext != NULL) {
        if (storageElement->originNext->equipId != NULL) {
            *valueLen += sizeof(uint8_t);
            *valueLen += DM_STR_LEN(storageElement->originNext->equipId);
        }

        *valueLen += sizeof(uint32_t);
    }
    *valueLen += GetBaseContentSerializeLen(storageElement);
    // keyLen + key + valueLen + value
    return sizeof(uint16_t) + storageElement->keyLen + sizeof(uint32_t) + (*valueLen);
}

static Status DmCopyDataToBuf(const uint32_t *data, uint8_t **bufCursor, uint32_t *bufLen)
{
    errno_t err = memcpy_s(*bufCursor, *bufLen, data, sizeof(uint32_t));
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "copy data to buf get wrong");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    BufLenSafeCalculate(sizeof(uint32_t), (uint8_t **)bufCursor, bufLen);

    return GMERR_OK;
}

static Status DmSerializeElementKey(uint32_t keyLen, char *key, uint8_t **bufCursor, uint32_t *bufLen)
{
    errno_t err = memcpy_s(*bufCursor, *bufLen, &keyLen, sizeof(uint16_t));
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "copy keyLen to buf get wrong");
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    BufLenSafeCalculate(sizeof(uint16_t), (uint8_t **)bufCursor, bufLen);

    err = memcpy_s(*bufCursor, *bufLen, key, keyLen);
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "copy key to buf get wrong");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    BufLenSafeCalculate(keyLen, (uint8_t **)bufCursor, bufLen);

    return GMERR_OK;
}

/* | valueLength |  value  | */
/* |  uint32_t   |  char*  | */
Status DmSerializeStorageDeletedImpl(const DmStorageDeletedT *storageDeleted, uint8_t *buf, uint32_t bufLen)
{
    DB_POINTER2(storageDeleted, buf);
    uint8_t *bufCursor = buf;
    uint32_t bufLength = bufLen;
    Status ret = DmSerializeElementKey(storageDeleted->keyLen, storageDeleted->key, &bufCursor, &bufLength);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = DmCopyDataToBuf(&storageDeleted->valueLength, &bufCursor, &bufLength);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    errno_t err = memcpy_s(bufCursor, bufLength, storageDeleted->value, storageDeleted->valueLength);
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "copy value to buf get wrong");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    bufLength -= storageDeleted->valueLength;

EXIT:
    return bufLength == 0 ? GMERR_OK : GMERR_MEMORY_OPERATE_FAILED;
}

static Status DmCopyElementIdToBuf(DmBaseElementIdT *elementId, uint8_t **bufCursor, uint32_t *bufLen)
{
    if (elementId == NULL) {
        return GMERR_OK;
    }

    if (elementId->equipId != NULL) {
        // Ensure that the conversion is correct.
        uint8_t len = (uint8_t)DM_STR_LEN(elementId->equipId);
        *(uint8_t *)(*bufCursor) = len;

        BufLenSafeCalculate(sizeof(uint8_t), (uint8_t **)bufCursor, bufLen);

        errno_t err = memcpy_s(*bufCursor, *bufLen, elementId->equipId, len);
        if (err != EOK) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "copy equip id in element id to buf get wrong");
            return GMERR_MEMORY_OPERATE_FAILED;
        }

        BufLenSafeCalculate(len, (uint8_t **)bufCursor, bufLen);
    }

    return DmCopyDataToBuf(&elementId->incrClock, bufCursor, bufLen);
}

static Status DmSerializeElementStringContent(DmStorageElementT *storageElement, uint8_t **bufCursor, uint32_t *bufLen)
{
    Status ret = DmCopyDataToBuf(&storageElement->string.byteLength, bufCursor, bufLen);
    if (ret != GMERR_OK) {
        return ret;
    }

    errno_t err = memcpy_s(*bufCursor, *bufLen, storageElement->string.content, storageElement->string.byteLength);
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "copy content to buf get wrong");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    BufLenSafeCalculate(storageElement->string.byteLength, (uint8_t **)bufCursor, bufLen);

    return GMERR_OK;
}

static Status DmSerializeElementEmbedContent(DmStorageElementT *storageElement, uint8_t **bufCursor, uint32_t *bufLen)
{
    Status ret = DmSerializeElementKey(storageElement->embed.typeLen, storageElement->embed.type, bufCursor, bufLen);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "copy embed type to buf get wrong");
        return ret;
    }

    ret = DmSerializeElementKey(storageElement->embed.pathLen, storageElement->embed.path, bufCursor, bufLen);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "copy embed path to buf get wrong");
    }
    return ret;
}

static Status SerializeBaseContentToBuf(DmStorageElementT *storageElement, uint8_t **bufCursor, uint32_t *bufLen)
{
    if ((storageElement->flag & ELEMENT_STRING_TYPE) == ELEMENT_STRING_TYPE) {
        Status ret = DmSerializeElementStringContent(storageElement, bufCursor, bufLen);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else if ((storageElement->flag & ELEMENT_ATTRIBUTE_TYPE) == ELEMENT_ATTRIBUTE_TYPE) {
        Status ret =
            DmSerializeElementKey(storageElement->attribute.keyLen, storageElement->attribute.key, bufCursor, bufLen);
        if (ret != GMERR_OK) {
            return ret;
        }

        errno_t err = memcpy_s(*bufCursor, *bufLen, &storageElement->attribute.valueType, sizeof(uint8_t));
        if (err != EOK) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "copy valueType to buf get wrong");
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        BufLenSafeCalculate(sizeof(uint8_t), (uint8_t **)bufCursor, bufLen);

        // value len.
        err = memcpy_s(*bufCursor, *bufLen, &storageElement->attribute.valueLen, sizeof(uint16_t));
        if (err != EOK) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "copy valueLen to buf get wrong");
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        BufLenSafeCalculate(sizeof(uint16_t), (uint8_t **)bufCursor, bufLen);

        if (storageElement->attribute.valueType == DM_ATTRIBUTE_VALUE_STRING_START ||
            storageElement->attribute.valueType == DM_ATTRIBUTE_VALUE_STRING_END) {
            // String have value.
            err = memcpy_s(*bufCursor, *bufLen, storageElement->attribute.value, storageElement->attribute.valueLen);
            if (err != EOK) {
                DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "copy value to buf get wrong");
                return GMERR_MEMORY_OPERATE_FAILED;
            }
            BufLenSafeCalculate(storageElement->attribute.valueLen, (uint8_t **)bufCursor, bufLen);
        }
    } else if ((storageElement->flag & ELEMENT_EMBED_TYPE) == ELEMENT_EMBED_TYPE) {
        return DmSerializeElementEmbedContent(storageElement, bufCursor, bufLen);
    } else {
        DB_ASSERT(false);
    }

    return GMERR_OK;
}

/* |  keyLen  |  key  | valueLen |   flag   | prevId | nextId | originPrev | originNext | byteLength | content | */
/* | uint32_t | char* | uint32_t | uint32_t |  char* equipId + uint32_t incrClock       |  uint32_t  |  char*  | */
static Status DmSerializeStorageElementToBuf(
    DmStorageElementT *storageElement, uint8_t *buf, uint32_t bufLen, uint32_t valueLen)
{
    DB_POINTER2(storageElement, buf);
    uint8_t *bufCursor = buf;
    Status ret = DmSerializeElementKey(storageElement->keyLen, storageElement->key, &bufCursor, &bufLen);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = DmCopyDataToBuf(&valueLen, &bufCursor, &bufLen);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = DmCopyDataToBuf(&storageElement->flag, &bufCursor, &bufLen);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = DmCopyElementIdToBuf(storageElement->prevId, &bufCursor, &bufLen);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = DmCopyElementIdToBuf(storageElement->nextId, &bufCursor, &bufLen);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = DmCopyElementIdToBuf(storageElement->originPrev, &bufCursor, &bufLen);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = DmCopyElementIdToBuf(storageElement->originNext, &bufCursor, &bufLen);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    ret = SerializeBaseContentToBuf(storageElement, &bufCursor, &bufLen);
EXIT:
    return ret;
}

// Callers should free buf if serialize success.
Status DmSerializeStorageElementImpl(DmStorageElementT *storageElement, uint8_t **buf, uint32_t *bufLen)
{
    DB_POINTER3(storageElement, buf, bufLen);
    uint32_t valueLen = 0u;
    *bufLen = DmGetStorageElementSerializeLen(storageElement, &valueLen);
    *buf = (uint8_t *)DbDynMemCtxAlloc(storageElement->memCtx, *bufLen);
    if (*buf == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong when serialize storage element");
        return GMERR_OUT_OF_MEMORY;
    }

    Status ret = DmSerializeStorageElementToBuf(storageElement, *buf, *bufLen, valueLen);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(storageElement->memCtx, *buf);
        *buf = NULL;
    }

    return ret;
}

static Status DmCreateElementIdT(
    DbMemCtxT *memCtx, bool isEquipIdExist, uint8_t **bufCursor, uint32_t *bufLen, DmBaseElementIdT **element)
{
    *element = (DmBaseElementIdT *)DbDynMemCtxAlloc(memCtx, sizeof(DmBaseElementIdT));
    if (SECUREC_UNLIKELY(*element == NULL)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for base element");
        return GMERR_OUT_OF_MEMORY;
    }

    (void)memset_s(*element, sizeof(DmBaseElementIdT), 0, sizeof(DmBaseElementIdT));
    if (isEquipIdExist) {
        uint8_t equipIdLen = **bufCursor;
        BufLenSafeCalculate(sizeof(uint8_t), (uint8_t **)bufCursor, bufLen);
        (*element)->equipId = (char *)DbDynMemCtxAlloc(memCtx, equipIdLen);
        if ((*element)->equipId == NULL) {
            DbDynMemCtxFree(memCtx, *element);
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for deserialize element equipId.");
            return GMERR_OUT_OF_MEMORY;
        }

        errno_t iret = memcpy_s((*element)->equipId, equipIdLen, (char *)(*bufCursor), equipIdLen);
        if (SECUREC_UNLIKELY(iret != EOK)) {
            DbDynMemCtxFree(memCtx, (*element)->equipId);
            DbDynMemCtxFree(memCtx, *element);
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "wrong operate mem for deserialize element equipId.");
            return GMERR_MEMORY_OPERATE_FAILED;
        }

        BufLenSafeCalculate(equipIdLen, (uint8_t **)bufCursor, bufLen);
    }

    (*element)->incrClock = *(uint32_t *)(*bufCursor);
    BufLenSafeCalculate(sizeof(uint32_t), (uint8_t **)bufCursor, bufLen);

    return GMERR_OK;
}

static Status DmDeSerializeBaseElement(uint8_t **bufCursor, uint32_t *bufLen, DmStorageElementT *storageElement)
{
    Status ret = GMERR_OK;
    if ((storageElement->flag & ELEMENT_PREV_ID_EXIST) == ELEMENT_PREV_ID_EXIST) {
        bool isEquipIdExist = !(storageElement->flag & ELEMENT_PREV_ID_EQUIP_ID_NULL);
        ret = DmCreateElementIdT(storageElement->memCtx, isEquipIdExist, bufCursor, bufLen, &storageElement->prevId);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for previous id");
            return ret;
        }
    }

    if ((storageElement->flag & ELEMENT_NEXT_ID_EXIST) == ELEMENT_NEXT_ID_EXIST) {
        bool isEquipIdExist = !(storageElement->flag & ELEMENT_NEXT_ID_EQUIP_ID_NULL);
        ret = DmCreateElementIdT(storageElement->memCtx, isEquipIdExist, bufCursor, bufLen, &storageElement->nextId);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for next id");
            return ret;
        }
    }

    if ((storageElement->flag & ELEMENT_ORIGIN_PREV_EXIST) == ELEMENT_ORIGIN_PREV_EXIST) {
        bool isEquipIdExist = !(storageElement->flag & ELEMENT_ORIGIN_PREV_EQUIP_ID_NULL);
        ret =
            DmCreateElementIdT(storageElement->memCtx, isEquipIdExist, bufCursor, bufLen, &storageElement->originPrev);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for origin previous");
            return ret;
        }
    }

    if ((storageElement->flag & ELEMENT_ORIGIN_NEXT_EXIST) == ELEMENT_ORIGIN_NEXT_EXIST) {
        bool isEquipIdExist = !(storageElement->flag & ELEMENT_ORIGIN_NEXT_EQUIP_ID_NULL);
        ret =
            DmCreateElementIdT(storageElement->memCtx, isEquipIdExist, bufCursor, bufLen, &storageElement->originNext);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for origin next");
            return ret;
        }
    }
    return GMERR_OK;
}

static Status DmDeSerializeElementKey(
    DbMemCtxT *memCtx, uint8_t **bufCursor, uint32_t *bufLen, uint16_t *keyLen, char **key)
{
    *keyLen = *(uint16_t *)(*bufCursor);
    BufLenSafeCalculate(sizeof(uint16_t), (uint8_t **)bufCursor, bufLen);

    *key = (char *)DbDynMemCtxAlloc(memCtx, *keyLen);
    if (*key == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for deserialize element key");
        return GMERR_OUT_OF_MEMORY;
    }

    errno_t iret = memcpy_s(*key, *keyLen, (char *)(*bufCursor), *keyLen);
    if (SECUREC_UNLIKELY(iret != EOK)) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "wrong operate mem for deserialize element key");
        DbDynMemCtxFree(memCtx, *key);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    BufLenSafeCalculate(*keyLen, (uint8_t **)bufCursor, bufLen);
    return GMERR_OK;
}

static Status AllocAndCopyString(
    DbMemCtxT *memCtx, uint8_t **bufCursor, uint32_t *bufLen, uint32_t byteLength, char **target)
{
    char *content = (char *)DbDynMemCtxAlloc(memCtx, byteLength);
    if (content == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for string copy");
        return GMERR_OUT_OF_MEMORY;
    }

    errno_t errCode = memcpy_s(content, *bufLen, *bufCursor, byteLength);
    if (SECUREC_UNLIKELY(errCode != EOK)) {
        DbDynMemCtxFree(memCtx, content);
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "wrong operate mem for string copy");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    BufLenSafeCalculate(byteLength, (uint8_t **)bufCursor, bufLen);
    *target = content;
    return GMERR_OK;
}

static Status DmDeSerializeElementEmbed(
    DbMemCtxT *memCtx, uint8_t **bufCursor, uint32_t *bufLen, uint16_t *keyLen, char **key)
{
    *keyLen = *(uint16_t *)(*bufCursor);
    BufLenSafeCalculate(sizeof(uint16_t), (uint8_t **)bufCursor, bufLen);

    return AllocAndCopyString(memCtx, bufCursor, bufLen, *keyLen, key);
}

static Status DeSerializeBaseContentToEmbed(DmStorageElementT *storageElement, uint8_t **bufCursor, uint32_t *bufLen)
{
    Status ret = DmDeSerializeElementEmbed(
        storageElement->memCtx, bufCursor, bufLen, &storageElement->embed.typeLen, &storageElement->embed.type);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "wrong operate mem for embed type copy");
        return ret;
    }

    ret = DmDeSerializeElementEmbed(
        storageElement->memCtx, bufCursor, bufLen, &storageElement->embed.pathLen, &storageElement->embed.path);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "wrong operate mem for embed path copy");
    }
    return ret;
}

static Status DeSerializeBaseContent(uint8_t **bufCursor, uint32_t *bufLen, DmStorageElementT *storageElement)
{
    if ((storageElement->flag & ELEMENT_STRING_TYPE) == ELEMENT_STRING_TYPE) {
        storageElement->string.byteLength = *(uint32_t *)(*bufCursor);
        BufLenSafeCalculate(sizeof(uint32_t), (uint8_t **)bufCursor, bufLen);

        Status ret = AllocAndCopyString(storageElement->memCtx, (uint8_t **)bufCursor, bufLen,
            storageElement->string.byteLength, &storageElement->string.content);
        if (ret != GMERR_OK) {
            return ret;
        }

        // check content length. bufLen contains \0, strlen do not contain \0
        DB_ASSERT(*bufLen == 0);
    } else if ((storageElement->flag & ELEMENT_ATTRIBUTE_TYPE) == ELEMENT_ATTRIBUTE_TYPE) {
        Status ret = DmDeSerializeElementKey(storageElement->memCtx, bufCursor, bufLen,
            &storageElement->attribute.keyLen, &storageElement->attribute.key);
        if (ret != GMERR_OK) {
            return ret;
        }

        storageElement->attribute.valueType = *(uint8_t *)(*bufCursor);
        BufLenSafeCalculate(sizeof(uint8_t), (uint8_t **)bufCursor, bufLen);

        // value len.
        storageElement->attribute.valueLen = *(uint16_t *)(*bufCursor);
        BufLenSafeCalculate(sizeof(uint16_t), (uint8_t **)bufCursor, bufLen);

        if (storageElement->attribute.valueType == DM_ATTRIBUTE_VALUE_STRING_START ||
            storageElement->attribute.valueType == DM_ATTRIBUTE_VALUE_STRING_END) {
            // String have value.
            ret = AllocAndCopyString(storageElement->memCtx, bufCursor, bufLen, storageElement->attribute.valueLen,
                &storageElement->attribute.value);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        DB_ASSERT(*bufLen == 0);
    } else if ((storageElement->flag & ELEMENT_EMBED_TYPE) == ELEMENT_EMBED_TYPE) {
        Status ret = DeSerializeBaseContentToEmbed(storageElement, bufCursor, bufLen);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "wrong operate mem for embed copy");
            return ret;
        }

        DB_ASSERT(*bufLen == 0);
    } else {
        DB_ASSERT(false);
    }

    return GMERR_OK;
}

Status DmDeSerializeStorageElementImpl(uint8_t *buf, uint32_t bufLen, DmStorageElementT *storageElement)
{
    DB_POINTER3(storageElement, storageElement->memCtx, buf);
    uint8_t *bufCursor = buf;
    // 1. deserialize key
    Status ret = DmDeSerializeElementKey(
        storageElement->memCtx, &bufCursor, &bufLen, &storageElement->keyLen, &storageElement->key);
    if (ret != GMERR_OK) {
        return ret;
    }
    BufLenSafeCalculate(sizeof(uint32_t), (uint8_t **)&bufCursor, &bufLen);  // valueLen

    // 2. deserialize flag
    storageElement->flag = *(uint32_t *)bufCursor;
    BufLenSafeCalculate(sizeof(uint32_t), (uint8_t **)&bufCursor, &bufLen);

    // 3. deserialize prevId, nextId, originPrev and originNext
    ret = DmDeSerializeBaseElement(&bufCursor, &bufLen, storageElement);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 4. deserialize base content
    return DeSerializeBaseContent(&bufCursor, &bufLen, storageElement);
}

Status DmDeSerializeStorageDeletedImpl(uint8_t *buf, uint32_t bufLen, DmStorageDeletedT *storageDelElement)
{
    DB_POINTER3(storageDelElement, storageDelElement->memCtx, buf);
    uint8_t *bufCursor = buf;
    // 1. deserialize key
    Status ret = DmDeSerializeElementKey(
        storageDelElement->memCtx, &bufCursor, &bufLen, &storageDelElement->keyLen, &storageDelElement->key);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 2. deserialize value length and value
    storageDelElement->valueLength = *(uint32_t *)bufCursor;
    BufLenSafeCalculate(sizeof(uint32_t), (uint8_t **)&bufCursor, &bufLen);

    storageDelElement->value = (char *)DbDynMemCtxAlloc(storageDelElement->memCtx, storageDelElement->valueLength);
    if (storageDelElement->value == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for del deserialize value");
        return GMERR_OUT_OF_MEMORY;
    }

    errno_t errCode =
        memcpy_s(storageDelElement->value, storageDelElement->valueLength, bufCursor, storageDelElement->valueLength);
    if (SECUREC_UNLIKELY(errCode != EOK)) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "wrong operate mem for del deserialize value");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    // check value length. bufLen is length of element
    DB_ASSERT(bufLen == sizeof(uint32_t));
    return GMERR_OK;
}

Status DmGetStorageKeyFromBufImpl(uint8_t *buf, uint32_t bufLen, char **key)
{
    if (bufLen < sizeof(uint16_t)) {
        DB_LOG_ERROR(GMERR_INVALID_BUFFER, "wrong buffer for sequence get from DB");
        return GMERR_INVALID_BUFFER;
    }
    uint8_t *bufCursor = buf;
    bufCursor += sizeof(uint16_t);
    *key = (char *)bufCursor;
    return GMERR_OK;
}

static uint32_t DmGetBaseLogBodyLen(const DmBaseLogBodyT *baseLogBody)
{
    DB_POINTER(baseLogBody);
    uint32_t length = sizeof(uint8_t);           // sharedObjType
    length += sizeof(uint8_t);                   // sharedObjNameLength
    length += baseLogBody->sharedObjNameLength;  // sharedObjName

    return length;
}

/* |  type   |  length   |  name  | */
/* | uint8_t |  uint8_t  |  char* | */
static Status DmSerializeBaseLogBodyToBuf(const DmBaseLogBodyT *baseLogBody, uint8_t **buf, uint32_t *bufLen)
{
    DB_POINTER3(baseLogBody, buf, bufLen);
    errno_t err = memcpy_s(*buf, *bufLen, &baseLogBody->sharedObjType, sizeof(uint8_t));
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "copy shared obj type to buf get wrong");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    BufLenSafeCalculate(sizeof(uint8_t), (uint8_t **)buf, bufLen);

    *(uint8_t *)(*buf) = baseLogBody->sharedObjNameLength;
    BufLenSafeCalculate(sizeof(uint8_t), (uint8_t **)buf, bufLen);

    err = memcpy_s(*buf, *bufLen, baseLogBody->sharedObjName, baseLogBody->sharedObjNameLength);
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "copy shared obj name length to buf get wrong");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    BufLenSafeCalculate(baseLogBody->sharedObjNameLength, (uint8_t **)buf, bufLen);

    return GMERR_OK;
}

static uint32_t DmGetOperationElementLen(DmOperationElementT *operationElement)
{
    DB_POINTER(operationElement);
    uint32_t length = sizeof(uint32_t);  // flag

    if (operationElement->originPrev != NULL) {
        if (operationElement->originPrev->equipId != NULL) {
            length += sizeof(uint8_t);
            length += DM_STR_LEN(operationElement->originPrev->equipId);
        }
        length += sizeof(uint32_t);
    }
    if (operationElement->originNext != NULL) {
        if (operationElement->originNext->equipId != NULL) {
            length += sizeof(uint8_t);
            length += DM_STR_LEN(operationElement->originNext->equipId);
        }

        length += sizeof(uint32_t);
    }
    if ((operationElement->flag & ELEMENT_STRING_TYPE) == ELEMENT_STRING_TYPE) {
        length += sizeof(uint32_t);
        length += operationElement->string.byteLength;
    } else if ((operationElement->flag & ELEMENT_ATTRIBUTE_TYPE) == ELEMENT_ATTRIBUTE_TYPE) {
        length += DmGetBaseAttributeLength(operationElement->attribute);
    } else if ((operationElement->flag & ELEMENT_EMBED_TYPE) == ELEMENT_EMBED_TYPE) {
        length += DmGetBaseEmbedLength(operationElement->embed);
    } else {
        DB_ASSERT(false);
    }
    return length;
}

static uint32_t DmGetOperationDelElemLen(DmOperationDeletedT *operationDelElement)
{
    DB_POINTER2(operationDelElement, operationDelElement->equipId);
    uint32_t length = 0u;
    length += sizeof(uint8_t);                           // equipIdLen length
    length += DM_STR_LEN(operationDelElement->equipId);  // equipId length, include \0
    length += sizeof(uint32_t);                          // startClock
    length += sizeof(uint32_t);                          // deleteLength
    return length;
}

static uint32_t DmGetOperationLogBodyLen(const DmOperationLogBodyT *operationLogBody)
{
    DB_POINTER(operationLogBody);
    uint32_t length = DmGetBaseLogBodyLen(&operationLogBody->sharedObjInfo);
    length += sizeof(uint8_t);                  // equipIdLength
    length += operationLogBody->equipIdLength;  // equipId
    length += sizeof(uint32_t);                 // elementSize
    if (operationLogBody->elementSize != 0) {
        length += sizeof(uint32_t);  // startClock
        const TagLinkedListT *operationElements = &operationLogBody->operationElements;
        DB_ASSERT(!DbLinkedListEmpty(operationElements));

        DmOperationElementT *operationElement = NULL;
        DmOperationElementT *tmpElement = NULL;
        uint32_t elementSize = operationLogBody->elementSize;
        LIST_FOR_EACH_ENTRY_SAFE(operationElement, tmpElement, operationElements, linkedNode)
        {
            length += DmGetOperationElementLen(operationElement);
            elementSize--;
            if (elementSize == 0) {
                break;
            }
        }
    }

    length += sizeof(uint32_t);  // deleteSize
    if (operationLogBody->deleteSize != 0) {
        const TagLinkedListT *operationDeletes = &operationLogBody->operationDeletes;
        DB_ASSERT(!DbLinkedListEmpty(operationDeletes));

        DmOperationDeletedT *operationDelElement = NULL;
        DmOperationDeletedT *tmpElement = NULL;
        uint32_t deleteSize = operationLogBody->deleteSize;
        LIST_FOR_EACH_ENTRY_SAFE(operationDelElement, tmpElement, operationDeletes, linkedNode)
        {
            length += DmGetOperationDelElemLen(operationDelElement);
            if (--deleteSize == 0) {
                break;
            }
        }
    }

    return length;
}

static Status DmCopyUint16DataToBuf(const uint16_t *data, uint8_t **bufCursor, uint32_t *bufLen)
{
    errno_t err = memcpy_s(*bufCursor, *bufLen, data, sizeof(uint16_t));
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "copy uint16_t data to buf get wrong");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    BufLenSafeCalculate(sizeof(uint16_t), (uint8_t **)bufCursor, bufLen);
    return GMERR_OK;
}

static Status DmCopyUint8DataToBuf(const uint8_t *data, uint8_t **bufCursor, uint32_t *bufLen)
{
    errno_t err = memcpy_s(*bufCursor, *bufLen, data, sizeof(uint8_t));
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "copy uint16_t data to buf get wrong");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    BufLenSafeCalculate(sizeof(uint8_t), (uint8_t **)bufCursor, bufLen);
    return GMERR_OK;
}

static Status DmAddBaseAttributeToBuf(DmOperationElementT *operationElement, uint8_t **buf, uint32_t *bufLen)
{
    Status ret = DmCopyUint16DataToBuf((const uint16_t *)&operationElement->attribute.keyLen, buf, bufLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    errno_t err = memcpy_s(*buf, *bufLen, operationElement->attribute.key, operationElement->attribute.keyLen);
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "copy attribute key to buf get wrong");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    BufLenSafeCalculate(operationElement->attribute.keyLen, (uint8_t **)buf, bufLen);

    ret = DmCopyUint8DataToBuf((const uint8_t *)&operationElement->attribute.valueType, buf, bufLen);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = DmCopyUint16DataToBuf((const uint16_t *)&operationElement->attribute.valueLen, buf, bufLen);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (operationElement->attribute.valueType == DM_ATTRIBUTE_VALUE_STRING_START ||
        operationElement->attribute.valueType == DM_ATTRIBUTE_VALUE_STRING_END) {
        err = memcpy_s(*buf, *bufLen, operationElement->attribute.value, operationElement->attribute.valueLen);
        if (err != EOK) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "copy attribute value to buf get wrong");
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        BufLenSafeCalculate(operationElement->attribute.valueLen, (uint8_t **)buf, bufLen);
    }

    return GMERR_OK;
}

static Status DmAddBaseEmbedToBuf(DmOperationElementT *operationElement, uint8_t **buf, uint32_t *bufLen)
{
    Status ret = DmCopyUint16DataToBuf((const uint16_t *)&operationElement->embed.typeLen, buf, bufLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    errno_t err = memcpy_s(*buf, *bufLen, operationElement->embed.type, operationElement->embed.typeLen);
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "copy embed type to buf get wrong");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    BufLenSafeCalculate(operationElement->embed.typeLen, (uint8_t **)buf, bufLen);

    ret = DmCopyUint16DataToBuf((const uint16_t *)&operationElement->embed.pathLen, buf, bufLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    err = memcpy_s(*buf, *bufLen, operationElement->embed.path, operationElement->embed.pathLen);
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "copy embed path to buf get wrong");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    BufLenSafeCalculate(operationElement->embed.pathLen, (uint8_t **)buf, bufLen);

    return GMERR_OK;
}

static Status DmSerializeOperationElementToBuf(DmOperationElementT *operationElement, uint8_t **buf, uint32_t *bufLen)
{
    DB_POINTER3(operationElement, buf, bufLen);
    Status ret = DmCopyDataToBuf(&operationElement->flag, buf, bufLen);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = DmCopyElementIdToBuf(operationElement->originPrev, buf, bufLen);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = DmCopyElementIdToBuf(operationElement->originNext, buf, bufLen);
    if (ret != GMERR_OK) {
        return ret;
    }

    if ((operationElement->flag & ELEMENT_STRING_TYPE) == ELEMENT_STRING_TYPE) {
        ret = DmCopyDataToBuf(&operationElement->string.byteLength, buf, bufLen);
        if (ret != GMERR_OK) {
            return ret;
        }

        errno_t err = memcpy_s(*buf, *bufLen, operationElement->string.content, operationElement->string.byteLength);
        if (err != EOK) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "copy content to buf get wrong");
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        BufLenSafeCalculate(operationElement->string.byteLength, (uint8_t **)buf, bufLen);
    } else if ((operationElement->flag & ELEMENT_ATTRIBUTE_TYPE) == ELEMENT_ATTRIBUTE_TYPE) {
        ret = DmAddBaseAttributeToBuf(operationElement, buf, bufLen);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else if ((operationElement->flag & ELEMENT_EMBED_TYPE) == ELEMENT_EMBED_TYPE) {
        ret = DmAddBaseEmbedToBuf(operationElement, buf, bufLen);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        DB_ASSERT(false);
    }
    return GMERR_OK;
}

static Status CopyContentFromBuf(DbMemCtxT *memCtx, uint32_t contentLength, uint8_t **bufCursor, char **targetContent)
{
    char *content = (char *)DbDynMemCtxAlloc(memCtx, contentLength);
    if (SECUREC_UNLIKELY(content == NULL)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for content copy from buffer.");
        return GMERR_OUT_OF_MEMORY;
    }

    errno_t iret = memcpy_s(content, contentLength, (char *)(*bufCursor), contentLength);
    if (SECUREC_UNLIKELY(iret != EOK)) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "wrong operate mem for content copy.");
        DbDynMemCtxFree(memCtx, content);
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    *targetContent = content;
    return GMERR_OK;
}

/*
 * |   byteLength  |  content  |
 * |    uint32_t   |   char *  |
 */
static Status DmDeserializeOpElemStringContent(
    uint8_t **bufCursor, uint32_t *bufLen, DbMemCtxT *memCtx, DmOperationElementT *operationElement)
{
    // deserialize contentLength
    operationElement->string.byteLength = *(uint32_t *)(*bufCursor);
    BufLenSafeCalculate(sizeof(uint32_t), (uint8_t **)bufCursor, bufLen);

    // deserialize content
    Status ret =
        CopyContentFromBuf(memCtx, operationElement->string.byteLength, bufCursor, &operationElement->string.content);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "wrong copy OperationElement content from buffer.");
        return ret;
    }

    BufLenSafeCalculate(operationElement->string.byteLength, (uint8_t **)bufCursor, bufLen);
    return GMERR_OK;
}

static Status DmDeserializeOpElemContentKey(
    uint8_t **bufCursor, uint32_t *bufLen, DbMemCtxT *memCtx, uint16_t *keyLen, char **key)
{
    // deserialize key length
    *keyLen = *(uint16_t *)(*bufCursor);
    BufLenSafeCalculate(sizeof(uint16_t), (uint8_t **)bufCursor, bufLen);

    // deserialize key
    Status ret = CopyContentFromBuf(memCtx, *keyLen, bufCursor, key);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "wrong copy key content from buffer.");
        return ret;
    }

    BufLenSafeCalculate(*keyLen, (uint8_t **)bufCursor, bufLen);
    return GMERR_OK;
}

/*
 * |   keyLen   |   key    |   valueType  |  valueLen  |   value  |
 * |   uint16_t |   char * |    uint8_t   |  uint16_t  |   char * |
 */
static Status DmDeserializeOpElemAttributesContent(
    uint8_t **bufCursor, uint32_t *bufLen, DbMemCtxT *memCtx, DmOperationElementT *operationElement)
{
    Status ret = DmDeserializeOpElemContentKey(
        bufCursor, bufLen, memCtx, &operationElement->attribute.keyLen, &operationElement->attribute.key);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "wrong malloc or operate mem for deserialize OperationElement attribute key.");
        return ret;
    }

    // deserialize value type
    operationElement->attribute.valueType = *(uint8_t *)(*bufCursor);
    BufLenSafeCalculate(sizeof(uint8_t), (uint8_t **)bufCursor, bufLen);

    // deserialize value length
    operationElement->attribute.valueLen = *(uint16_t *)(*bufCursor);
    BufLenSafeCalculate(sizeof(uint16_t), (uint8_t **)bufCursor, bufLen);

    if (operationElement->attribute.valueType == DM_ATTRIBUTE_VALUE_STRING_START ||
        operationElement->attribute.valueType == DM_ATTRIBUTE_VALUE_STRING_END) {
        // deserialize value
        ret = CopyContentFromBuf(
            memCtx, operationElement->attribute.valueLen, bufCursor, &operationElement->attribute.value);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "wrong copy value content from buffer.");
            return ret;
        }
        BufLenSafeCalculate(operationElement->attribute.valueLen, (uint8_t **)bufCursor, bufLen);
    }

    return GMERR_OK;
}

/*
 * |   typeLen   |   type   |   pathLen  |   path   |
 * |   uint16_t  |   char * |   uint16_t |   char * |
 */
static Status DmDeserializeOpElemEmbedContent(
    uint8_t **bufCursor, uint32_t *bufLen, DbMemCtxT *memCtx, DmOperationElementT *operationElement)
{
    Status ret = DmDeserializeOpElemContentKey(
        bufCursor, bufLen, memCtx, &operationElement->embed.typeLen, &operationElement->embed.type);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "wrong malloc or operate mem for deserialize OperationElement embed type.");
        return ret;
    }

    ret = DmDeserializeOpElemContentKey(
        (uint8_t **)bufCursor, bufLen, memCtx, &operationElement->embed.pathLen, &operationElement->embed.path);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "wrong malloc or operate mem for deserialize OperationElement embed path.");
    }
    return ret;
}

/*
 * |   flag   |    originPrev    |     originNext    |           string/attribute            |
 * | uint32_t | DmBaseElementIdT |  DmBaseElementIdT |     DmBaseStringT/DmBaseAttributeT    |
 */
static Status DmDeSerializeOperationElement(
    uint8_t **bufCursor, uint32_t *bufLen, DbMemCtxT *memCtx, DmOperationElementT *operationElement)
{
    // deserialize flag
    operationElement->flag = *(uint32_t *)(*bufCursor);
    BufLenSafeCalculate(sizeof(uint32_t), (uint8_t **)bufCursor, bufLen);

    // deserialize originPrev
    Status ret = GMERR_OK;
    if ((operationElement->flag & ELEMENT_ORIGIN_PREV_EXIST) == ELEMENT_ORIGIN_PREV_EXIST) {
        bool isEquipIdExist = !(operationElement->flag & ELEMENT_ORIGIN_PREV_EQUIP_ID_NULL);
        ret = DmCreateElementIdT(memCtx, isEquipIdExist, bufCursor, bufLen, &operationElement->originPrev);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for origin previous");
            return ret;
        }
    }

    // deserialize originNext
    if ((operationElement->flag & ELEMENT_ORIGIN_NEXT_EXIST) == ELEMENT_ORIGIN_NEXT_EXIST) {
        bool isEquipIdExist = !(operationElement->flag & ELEMENT_ORIGIN_NEXT_EQUIP_ID_NULL);
        ret = DmCreateElementIdT(memCtx, isEquipIdExist, bufCursor, bufLen, &operationElement->originNext);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for origin next");
            return ret;
        }
    }

    if ((operationElement->flag & ELEMENT_STRING_TYPE) == ELEMENT_STRING_TYPE) {
        ret = DmDeserializeOpElemStringContent(bufCursor, bufLen, memCtx, operationElement);
    } else if ((operationElement->flag & ELEMENT_ATTRIBUTE_TYPE) == ELEMENT_ATTRIBUTE_TYPE) {
        ret = DmDeserializeOpElemAttributesContent(bufCursor, bufLen, memCtx, operationElement);
    } else if ((operationElement->flag & ELEMENT_EMBED_TYPE) == ELEMENT_EMBED_TYPE) {
        ret = DmDeserializeOpElemEmbedContent(bufCursor, bufLen, memCtx, operationElement);
    } else {
        DB_ASSERT(false);
    }

    return ret;
}

static Status DmSerializeOperationElementsToBuf(
    const TagLinkedListT *operationElements, uint32_t elementSize, uint8_t **buf, uint32_t *bufLen)
{
    DB_POINTER3(operationElements, buf, bufLen);
    DB_ASSERT(!DbLinkedListEmpty(operationElements));

    DmOperationElementT *operationElement = NULL;
    DmOperationElementT *tmpElement = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(operationElement, tmpElement, operationElements, linkedNode)
    {
        Status ret = DmSerializeOperationElementToBuf(operationElement, buf, bufLen);
        if (ret != GMERR_OK) {
            return ret;
        }
        elementSize--;
        if (elementSize == 0) {
            break;
        }
    }

    return GMERR_OK;
}

static Status DmSerializeOperationDeletedToBuf(DmOperationDeletedT *operationDelete, uint8_t **buf, uint32_t *bufLen)
{
    DB_POINTER3(operationDelete, buf, bufLen);
    Status ret = DmCopyUint8DataToBuf((const uint8_t *)&operationDelete->equipIdLen, buf, bufLen);
    if (ret != GMERR_OK) {
        return ret;
    }

    errno_t err = memcpy_s(*buf, *bufLen, operationDelete->equipId, operationDelete->equipIdLen);
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "copy equipId to buf go wrong");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    BufLenSafeCalculate(operationDelete->equipIdLen, (uint8_t **)buf, bufLen);

    ret = DmCopyDataToBuf(&operationDelete->startClock, buf, bufLen);
    if (ret != GMERR_OK) {
        return ret;
    }

    return DmCopyDataToBuf(&operationDelete->deleteLength, buf, bufLen);
}

/*
 * | startClock |  deleteLength |
 * |  uint32_t  |   uint32_t    |
 */
static Status DmDeSerializeOperationDeleted(
    DbMemCtxT *memCtx, uint8_t **bufCursor, uint32_t *bufLen, DmOperationDeletedT *operationDelElement)
{
    // deserialize equipIdLen
    operationDelElement->equipIdLen = *(uint8_t *)(*bufCursor);
    BufLenSafeCalculate(sizeof(uint8_t), (uint8_t **)bufCursor, bufLen);

    // deserialize equipId
    char *equipId = (char *)DbDynMemCtxAlloc(memCtx, operationDelElement->equipIdLen);
    if (SECUREC_UNLIKELY(equipId == NULL)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for deserialize equipId");
        return GMERR_OUT_OF_MEMORY;
    }

    errno_t iret = memcpy_s(equipId, operationDelElement->equipIdLen, *bufCursor, operationDelElement->equipIdLen);
    if (SECUREC_UNLIKELY(iret != EOK)) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "wrong operate mem for deserialize equipId");
        DbDynMemCtxFree(memCtx, equipId);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    operationDelElement->equipId = equipId;
    BufLenSafeCalculate(operationDelElement->equipIdLen, (uint8_t **)bufCursor, bufLen);

    // deserialize startClock
    operationDelElement->startClock = *(uint32_t *)(*bufCursor);

    BufLenSafeCalculate(sizeof(uint32_t), (uint8_t **)bufCursor, bufLen);

    // deserialize deleteLength
    operationDelElement->deleteLength = *(uint32_t *)(*bufCursor);
    BufLenSafeCalculate(sizeof(uint32_t), (uint8_t **)bufCursor, bufLen);
    return GMERR_OK;
}

static Status DmSerializeOperationDeletesToBuf(
    const TagLinkedListT *operationDeletes, uint32_t deleteSize, uint8_t **buf, uint32_t *bufLen)
{
    DB_POINTER3(operationDeletes, buf, bufLen);
    DB_ASSERT(!DbLinkedListEmpty(operationDeletes));

    DmOperationDeletedT *operationDelete = NULL;
    DmOperationDeletedT *tmpDelete = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(operationDelete, tmpDelete, operationDeletes, linkedNode)
    {
        Status ret = DmSerializeOperationDeletedToBuf(operationDelete, buf, bufLen);
        if (ret != GMERR_OK) {
            return ret;
        }
        deleteSize--;
        if (deleteSize == 0) {
            break;
        }
    }

    return GMERR_OK;
}

/* See struct DmOperationLogBody */
static Status DmSerializeOperationLogBodyToBuf(
    const DmOperationLogBodyT *operationLogBody, uint8_t *buf, uint32_t bufLen)
{
    DB_POINTER2(operationLogBody, buf);
    uint8_t *bufCursor = buf;
    Status ret = DmSerializeBaseLogBodyToBuf(&operationLogBody->sharedObjInfo, &bufCursor, &bufLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmCopyUint8DataToBuf((const uint8_t *)&operationLogBody->equipIdLength, &bufCursor, &bufLen);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "copy equip id length to buf get wrong");

        return ret;
    }

    errno_t err = memcpy_s(bufCursor, bufLen, operationLogBody->equipId, operationLogBody->equipIdLength);
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "copy equip id to buf get wrong");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    BufLenSafeCalculate(operationLogBody->equipIdLength, (uint8_t **)&bufCursor, &bufLen);

    ret = DmCopyDataToBuf(&operationLogBody->elementSize, &bufCursor, &bufLen);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (operationLogBody->elementSize != 0) {
        ret = DmCopyDataToBuf(&operationLogBody->startClock, &bufCursor, &bufLen);
        if (ret != GMERR_OK) {
            return ret;
        }

        const TagLinkedListT *operationElements = &operationLogBody->operationElements;
        ret = DmSerializeOperationElementsToBuf(operationElements, operationLogBody->elementSize, &bufCursor, &bufLen);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    ret = DmCopyDataToBuf(&operationLogBody->deleteSize, &bufCursor, &bufLen);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (operationLogBody->deleteSize != 0) {
        const TagLinkedListT *operationDeletes = &operationLogBody->operationDeletes;
        ret = DmSerializeOperationDeletesToBuf(operationDeletes, operationLogBody->deleteSize, &bufCursor, &bufLen);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    return GMERR_OK;
}

Status DmSerializeBaseLogBodyImpl(const DmBaseLogBodyT *baseLogBody, uint8_t **buf, uint32_t *bufLen)
{
    DB_POINTER3(baseLogBody, buf, bufLen);
    *bufLen = DmGetBaseLogBodyLen(baseLogBody);
    *buf = (uint8_t *)DbDynMemCtxAlloc(baseLogBody->memCtx, *bufLen);
    if (*buf == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong when serialize base log body");
        return GMERR_OUT_OF_MEMORY;
    }

    uint8_t *bufCursor = *buf;
    uint32_t tmpLen = *bufLen;
    Status ret = DmSerializeBaseLogBodyToBuf(baseLogBody, &bufCursor, &tmpLen);
    DB_ASSERT(tmpLen == 0);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(baseLogBody->memCtx, *buf);
        *buf = NULL;
    }

    return ret;
}

Status DmSerializeOperationLogBodyImpl(const DmOperationLogBodyT *operationLogBody, uint8_t **buf, uint32_t *bufLen)
{
    DB_POINTER3(operationLogBody, buf, bufLen);
    *bufLen = DmGetOperationLogBodyLen(operationLogBody);
    *buf = (uint8_t *)DbDynMemCtxAlloc(operationLogBody->sharedObjInfo.memCtx, *bufLen);
    if (*buf == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong when serialize storage element");
        return GMERR_OUT_OF_MEMORY;
    }

    Status ret = DmSerializeOperationLogBodyToBuf(operationLogBody, *buf, *bufLen);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree(operationLogBody->sharedObjInfo.memCtx, *buf);
        *buf = NULL;
    }

    return ret;
}

/* |  sharedObjType |  nameLength |  name  | */
/* |     uint8_t    |    uint8_t  |  char* | */
Status DmDeSerializeBaseLogBodyImpl(uint8_t **bufCursor, uint32_t *bufLen, DmBaseLogBodyT *baseLogBody)
{
    // deserialize sharedObjType
    baseLogBody->sharedObjType = *(uint8_t *)(*bufCursor);
    BufLenSafeCalculate(sizeof(uint8_t), (uint8_t **)bufCursor, bufLen);
    // deserialize sharedObjNameLength
    uint8_t nameLength = *(*bufCursor);
    baseLogBody->sharedObjNameLength = nameLength;
    BufLenSafeCalculate(sizeof(uint8_t), (uint8_t **)bufCursor, bufLen);

    // deserialize sharedObjName
    Status ret = CopyContentFromBuf(baseLogBody->memCtx, nameLength, bufCursor, &baseLogBody->sharedObjName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "wrong copy sharedObjName content from buffer.");
        return ret;
    }

    BufLenSafeCalculate(nameLength, (uint8_t **)bufCursor, bufLen);
    return GMERR_OK;
}

static Status DmDeserializeOperationElements(
    uint8_t **bufCursor, uint32_t *bufLen, DmOperationLogBodyT *operationLogBody)
{
    for (uint32_t i = 0; i < operationLogBody->elementSize; i++) {
        DmOperationElementT *opElement = (DmOperationElementT *)DbDynMemCtxAlloc(
            operationLogBody->sharedObjInfo.memCtx, sizeof(DmOperationElementT));
        if (SECUREC_UNLIKELY(opElement == NULL)) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for Operation element");
            return GMERR_OUT_OF_MEMORY;
        }
        (void)memset_s(opElement, sizeof(DmOperationElementT), 0, sizeof(DmOperationElementT));
        Status ret =
            DmDeSerializeOperationElement(bufCursor, bufLen, operationLogBody->sharedObjInfo.memCtx, opElement);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(operationLogBody->sharedObjInfo.memCtx, opElement);
            return ret;
        }
        DbLinkedListInsert(&operationLogBody->operationElements, &opElement->linkedNode);
    }
    return GMERR_OK;
}

static Status DmDeserializeOperationDeletes(
    uint8_t **bufCursor, uint32_t *bufLen, DmOperationLogBodyT *operationLogBody)
{
    for (uint32_t i = 0; i < operationLogBody->deleteSize; i++) {
        DmOperationDeletedT *opDelElement = (DmOperationDeletedT *)DbDynMemCtxAlloc(
            operationLogBody->sharedObjInfo.memCtx, sizeof(DmOperationDeletedT));
        if (SECUREC_UNLIKELY(opDelElement == NULL)) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for Operation deleted element");
            return GMERR_OUT_OF_MEMORY;
        }

        (void)memset_s(opDelElement, sizeof(DmOperationDeletedT), 0, sizeof(DmOperationDeletedT));
        Status ret =
            DmDeSerializeOperationDeleted(operationLogBody->sharedObjInfo.memCtx, bufCursor, bufLen, opDelElement);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(operationLogBody->sharedObjInfo.memCtx, opDelElement);
            return ret;
        }
        DbLinkedListInsert(&operationLogBody->operationDeletes, &opDelElement->linkedNode);
    }
    return GMERR_OK;
}

static Status DmDeserializeEquipId(
    uint8_t **bufCursor, uint32_t *bufLen, uint8_t equipIdLen, DmOperationLogBodyT *operationLogBody)
{
    Status ret =
        CopyContentFromBuf(operationLogBody->sharedObjInfo.memCtx, equipIdLen, bufCursor, &operationLogBody->equipId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "wrong copy equip Id content from buffer.");
        return ret;
    }

    BufLenSafeCalculate(equipIdLen, (uint8_t **)bufCursor, bufLen);
    return GMERR_OK;
}

/* |     baseLogBody    | equipIdLength  |        equipId      |  elementSize  |  startClock  |  */
/* |    DmBaseLogBodyT  |    uint8_t      |          char *      |    uint32_t   |    uint32_t  |  */
/* |  operationElements |    deleteSize   |    operationDeletes  |  */
/* |   sizeofElements   |     uint32_t    |  DmOperationDeletedT |  */
Status DmDeSerializeOperationLogBodyImpl(uint8_t *buf, uint32_t bufLen, DmOperationLogBodyT *operationLogBody)
{
    DB_POINTER3(buf, operationLogBody, operationLogBody->sharedObjInfo.memCtx);
    uint8_t *bufCursor = buf;
    // deserialize BaseLogBody
    Status ret = DmDeSerializeBaseLogBody(&bufCursor, &bufLen, &operationLogBody->sharedObjInfo);
    if (ret != GMERR_OK) {
        return ret;
    }

    // deserialize equipIdLength
    uint8_t equipIdLen = *bufCursor;
    BufLenSafeCalculate(sizeof(uint8_t), &bufCursor, &bufLen);
    operationLogBody->equipIdLength = equipIdLen;

    // deserialize equipId
    ret = DmDeserializeEquipId(&bufCursor, &bufLen, equipIdLen, operationLogBody);
    if (ret != GMERR_OK) {
        return ret;
    }

    // deserialize elementSize
    operationLogBody->elementSize = *(uint32_t *)bufCursor;
    BufLenSafeCalculate(sizeof(uint32_t), (uint8_t **)&bufCursor, &bufLen);

    if (operationLogBody->elementSize != 0) {
        // deserialize startClock
        operationLogBody->startClock = *(uint32_t *)bufCursor;
        BufLenSafeCalculate(sizeof(uint32_t), &bufCursor, &bufLen);
    }

    // deserialize operationElements
    ret = DmDeserializeOperationElements(&bufCursor, &bufLen, operationLogBody);
    if (ret != GMERR_OK) {
        return ret;
    }

    // deserialize deleteSize
    operationLogBody->deleteSize = *(uint32_t *)bufCursor;
    BufLenSafeCalculate(sizeof(uint32_t), &bufCursor, &bufLen);

    // deserialize operationDeletes
    return DmDeserializeOperationDeletes(&bufCursor, &bufLen, operationLogBody);
}

static void FreeStorageElementId(DbMemCtxT *memCtx, DmBaseElementIdT *elementId)
{
    if (elementId->equipId != NULL) {
        DbDynMemCtxFree(memCtx, elementId->equipId);
        elementId->equipId = NULL;
    }

    DbDynMemCtxFree(memCtx, elementId);
}

static void FreeStorageElementEmbed(DmStorageElementT *storageElement)
{
    if (storageElement->embed.type != NULL) {
        DbDynMemCtxFree(storageElement->memCtx, storageElement->embed.type);
        storageElement->embed.type = NULL;
    }
    if (storageElement->embed.path != NULL) {
        DbDynMemCtxFree(storageElement->memCtx, storageElement->embed.path);
        storageElement->embed.path = NULL;
    }
}

void FreeStorageElementImpl(DmStorageElementT *storageElement)
{
    if (storageElement == NULL) {
        return;
    }

    if (storageElement->key != NULL) {
        DbDynMemCtxFree(storageElement->memCtx, storageElement->key);
        storageElement->key = NULL;
    }
    if (storageElement->prevId != NULL) {
        FreeStorageElementId(storageElement->memCtx, storageElement->prevId);
        storageElement->prevId = NULL;
    }
    if (storageElement->nextId != NULL) {
        FreeStorageElementId(storageElement->memCtx, storageElement->nextId);
        storageElement->nextId = NULL;
    }
    if (storageElement->originPrev != NULL) {
        FreeStorageElementId(storageElement->memCtx, storageElement->originPrev);
        storageElement->originPrev = NULL;
    }
    if (storageElement->originNext != NULL) {
        FreeStorageElementId(storageElement->memCtx, storageElement->originNext);
        storageElement->originNext = NULL;
    }

    if ((storageElement->flag & ELEMENT_STRING_TYPE) == ELEMENT_STRING_TYPE) {
        if (storageElement->string.content != NULL) {
            DbDynMemCtxFree(storageElement->memCtx, storageElement->string.content);
            storageElement->string.content = NULL;
        }
    } else if ((storageElement->flag & ELEMENT_ATTRIBUTE_TYPE) == ELEMENT_ATTRIBUTE_TYPE) {
        if (storageElement->attribute.key != NULL) {
            DbDynMemCtxFree(storageElement->memCtx, storageElement->attribute.key);
            storageElement->attribute.key = NULL;
        }
        if (storageElement->attribute.value != NULL) {
            DbDynMemCtxFree(storageElement->memCtx, storageElement->attribute.value);
            storageElement->attribute.value = NULL;
        }
    } else if ((storageElement->flag & ELEMENT_EMBED_TYPE) == ELEMENT_EMBED_TYPE) {
        FreeStorageElementEmbed(storageElement);
    } else {
        DB_ASSERT(false);
    }

    DbDynMemCtxFree(storageElement->memCtx, storageElement);
}

void FreeStorageDeletedImpl(DbMemCtxT *memCtx, DmStorageDeletedT *storageDelElement)
{
    if (memCtx == NULL || storageDelElement == NULL) {
        return;
    }

    if (storageDelElement->key != NULL) {
        DbDynMemCtxFree(memCtx, storageDelElement->key);
        storageDelElement->key = NULL;
    }

    if (storageDelElement->value != NULL) {
        DbDynMemCtxFree(memCtx, storageDelElement->value);
        storageDelElement->value = NULL;
    }

    DbDynMemCtxFree(memCtx, storageDelElement);
}

static void FreeBaseLogBodyImpl(DmBaseLogBodyT *baseLogBody)
{
    DbMemCtxT *memCtx = baseLogBody->memCtx;

    if (baseLogBody->sharedObjName != NULL) {
        DbDynMemCtxFree(memCtx, baseLogBody->sharedObjName);
        baseLogBody->sharedObjName = NULL;
    }
    DbDynMemCtxFree(memCtx, baseLogBody);
}

static void FreeOperationElementContent(DmOperationElementT *OpElement, DbMemCtxT *memCtx)
{
    if ((OpElement->flag & ELEMENT_STRING_TYPE) == ELEMENT_STRING_TYPE) {
        if (OpElement->string.content != NULL) {
            DbDynMemCtxFree(memCtx, OpElement->string.content);
            OpElement->string.content = NULL;
        }
        return;
    }

    if ((OpElement->flag & ELEMENT_ATTRIBUTE_TYPE) == ELEMENT_ATTRIBUTE_TYPE) {
        if (OpElement->attribute.key != NULL) {
            DbDynMemCtxFree(memCtx, OpElement->attribute.key);
            OpElement->attribute.key = NULL;
        }
        if (OpElement->attribute.value != NULL) {
            DbDynMemCtxFree(memCtx, OpElement->attribute.value);
            OpElement->attribute.value = NULL;
        }
        return;
    }

    if ((OpElement->flag & ELEMENT_EMBED_TYPE) == ELEMENT_EMBED_TYPE) {
        if (OpElement->embed.type != NULL) {
            DbDynMemCtxFree(memCtx, OpElement->embed.type);
            OpElement->embed.type = NULL;
        }
        if (OpElement->embed.path != NULL) {
            DbDynMemCtxFree(memCtx, OpElement->embed.path);
            OpElement->embed.path = NULL;
        }
        return;
    }
    // Invalid Type
    DB_ASSERT(false);
}

void FreeOperationLogBodyImpl(DmOperationLogBodyT *operationLogBody)
{
    if (operationLogBody == NULL) {
        return;
    }

    DbMemCtxT *memCtx = operationLogBody->sharedObjInfo.memCtx;
    if (operationLogBody->sharedObjInfo.sharedObjName != NULL) {
        DbDynMemCtxFree(memCtx, operationLogBody->sharedObjInfo.sharedObjName);
        operationLogBody->sharedObjInfo.sharedObjName = NULL;
    }

    if (operationLogBody->equipId != NULL) {
        DbDynMemCtxFree(memCtx, operationLogBody->equipId);
        operationLogBody->equipId = NULL;
    }

    // the process below already covers the situation of empty dataList
    DmOperationElementT *currOpElement = NULL;
    DmOperationElementT *tmpOpElement = NULL;
    DB_ASSERT(!(NULL == &operationLogBody->operationElements));
    LIST_FOR_EACH_ENTRY_SAFE(currOpElement, tmpOpElement, &operationLogBody->operationElements, linkedNode)
    {
        if (currOpElement->originPrev != NULL) {
            if (currOpElement->originPrev->equipId != NULL) {
                DbDynMemCtxFree(memCtx, currOpElement->originPrev->equipId);
                currOpElement->originPrev->equipId = NULL;
            }
            DbDynMemCtxFree(memCtx, currOpElement->originPrev);
            currOpElement->originPrev = NULL;
        }
        if (currOpElement->originNext != NULL) {
            if (currOpElement->originNext->equipId != NULL) {
                DbDynMemCtxFree(memCtx, currOpElement->originNext->equipId);
                currOpElement->originNext->equipId = NULL;
            }
            DbDynMemCtxFree(memCtx, currOpElement->originNext);
            currOpElement->originNext = NULL;
        }
        FreeOperationElementContent(currOpElement, memCtx);
        DbLinkedListRemove(&currOpElement->linkedNode);
        DbDynMemCtxFree(memCtx, currOpElement);
    }

    // the process below already covers the situation of empty dataList
    DmOperationDeletedT *currOpDeElement = NULL;
    DmOperationDeletedT *tmpOpDeElement = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(currOpDeElement, tmpOpDeElement, &operationLogBody->operationDeletes, linkedNode)
    {
        DbLinkedListRemove(&currOpDeElement->linkedNode);
        DbDynMemCtxFree(memCtx, currOpDeElement->equipId);
        DbDynMemCtxFree(memCtx, currOpDeElement);
    }

    DbDynMemCtxFree(memCtx, operationLogBody);
}

void FreeBaseStringImpl(DbMemCtxT *memCtx, DmBaseStringT *baseString)
{
    if (memCtx == NULL || baseString == NULL) {
        return;
    }

    if (baseString->content != NULL) {
        DbDynMemCtxFree(memCtx, baseString->content);
        baseString->content = NULL;
    }

    DbDynMemCtxFree(memCtx, baseString);
}

void FreeBaseAttributeImpl(DbMemCtxT *memCtx, DmBaseAttributeT *baseAttribute)
{
    if (memCtx == NULL || baseAttribute == NULL) {
        return;
    }

    if (baseAttribute->key != NULL) {
        DbDynMemCtxFree(memCtx, baseAttribute->key);
        baseAttribute->key = NULL;
    }

    if (baseAttribute->value != NULL) {
        DB_ASSERT(baseAttribute->valueType == DM_ATTRIBUTE_VALUE_STRING_START ||
                  baseAttribute->valueType == DM_ATTRIBUTE_VALUE_STRING_END);
        DbDynMemCtxFree(memCtx, baseAttribute->value);
        baseAttribute->value = NULL;
    }

    DbDynMemCtxFree(memCtx, baseAttribute);
}

Status AllocBaseStringImpl(DbMemCtxT *memCtx, DmBaseStringT *source, DmBaseStringT **target)
{
    DB_POINTER3(memCtx, source, target);

    DmBaseStringT *string = (DmBaseStringT *)DbDynMemCtxAlloc(memCtx, sizeof(DmBaseStringT));
    if (SECUREC_UNLIKELY(string == NULL)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for new base string");
        return GMERR_OUT_OF_MEMORY;
    }

    string->byteLength = source->byteLength;
    string->content = (char *)DbDynMemCtxAlloc(memCtx, string->byteLength);
    if (SECUREC_UNLIKELY(string->content == NULL)) {
        DbDynMemCtxFree(memCtx, string);
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for new base string content");
        return GMERR_OUT_OF_MEMORY;
    }

    errno_t err = memcpy_s(string->content, string->byteLength, source->content, string->byteLength);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DbDynMemCtxFree(memCtx, string->content);
        DbDynMemCtxFree(memCtx, string);
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "wrong string copy for new base string content");
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    *target = string;
    return GMERR_OK;
}

Status AllocBaseAttributeImpl(DbMemCtxT *memCtx, DmBaseAttributeT *source, DmBaseAttributeT **target)
{
    DB_POINTER3(memCtx, source, target);
    Status ret = GMERR_OK;
    DmBaseAttributeT *arttribute = (DmBaseAttributeT *)DbDynMemCtxAlloc(memCtx, sizeof(DmBaseAttributeT));
    if (SECUREC_UNLIKELY(arttribute == NULL)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for new base arttribute");
        return GMERR_OUT_OF_MEMORY;
    }

    arttribute->keyLen = source->keyLen;
    arttribute->key = (char *)DbDynMemCtxAlloc(memCtx, arttribute->keyLen);
    if (SECUREC_UNLIKELY(arttribute->key == NULL)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for new base arttribute key");
        ret = GMERR_OUT_OF_MEMORY;
        goto EXIT;
    }

    DB_ASSERT(source->key != NULL);
    errno_t err = memcpy_s(arttribute->key, arttribute->keyLen, source->key, arttribute->keyLen);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "wrong string copy for new base arttribute key");
        ret = GMERR_MEMORY_OPERATE_FAILED;
        goto EXIT;
    }

    arttribute->valueType = source->valueType;
    arttribute->valueLen = source->valueLen;
    if (source->value != NULL) {
        arttribute->value = (char *)DbDynMemCtxAlloc(memCtx, arttribute->valueLen);
        if (SECUREC_UNLIKELY(arttribute->value == NULL)) {
            DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for new base arttribute value");
            ret = GMERR_OUT_OF_MEMORY;
            goto EXIT;
        }

        err = memcpy_s(arttribute->value, arttribute->valueLen, source->value, arttribute->valueLen);
        if (SECUREC_UNLIKELY(err != EOK)) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "wrong string copy for new base arttribute key");
            ret = GMERR_MEMORY_OPERATE_FAILED;
            goto EXIT;
        }
    } else {
        arttribute->value = NULL;
    }

    *target = arttribute;
    return GMERR_OK;
EXIT:
    FreeBaseAttributeImpl(memCtx, arttribute);
    return ret;
}

void FreeBaseEmbedImpl(DbMemCtxT *memCtx, DmBaseEmbedT *baseEmbed)
{
    if (memCtx == NULL || baseEmbed == NULL) {
        return;
    }

    if (baseEmbed->type != NULL) {
        DbDynMemCtxFree(memCtx, baseEmbed->type);
        baseEmbed->type = NULL;
    }

    if (baseEmbed->path != NULL) {
        DbDynMemCtxFree(memCtx, baseEmbed->path);
        baseEmbed->path = NULL;
    }

    DbDynMemCtxFree(memCtx, baseEmbed);
}

Status AllocBaseEmbedImpl(DbMemCtxT *memCtx, DmBaseEmbedT *source, DmBaseEmbedT **target)
{
    DB_POINTER3(memCtx, source, target);

    DmBaseEmbedT *embed = (DmBaseEmbedT *)DbDynMemCtxAlloc(memCtx, sizeof(DmBaseEmbedT));
    if (SECUREC_UNLIKELY(embed == NULL)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for new base embed object.");
        return GMERR_OUT_OF_MEMORY;
    }

    Status ret = GMERR_OK;
    (void)memset_s(embed, sizeof(DmBaseEmbedT), 0, sizeof(DmBaseEmbedT));
    embed->typeLen = source->typeLen;
    embed->type = (char *)DbDynMemCtxAlloc(memCtx, embed->typeLen);
    if (SECUREC_UNLIKELY(embed->type == NULL)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for new base embed object type.");
        ret = GMERR_OUT_OF_MEMORY;
        goto EXIT;
    }

    DB_ASSERT(source->type != NULL);
    errno_t err = memcpy_s(embed->type, embed->typeLen, source->type, embed->typeLen);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "wrong string copy for new base embed object type.");
        ret = GMERR_MEMORY_OPERATE_FAILED;
        goto EXIT;
    }

    embed->pathLen = source->pathLen;
    embed->path = (char *)DbDynMemCtxAlloc(memCtx, embed->pathLen);
    if (SECUREC_UNLIKELY(embed->path == NULL)) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "wrong malloc for new base embed object path.");
        ret = GMERR_OUT_OF_MEMORY;
        goto EXIT;
    }

    DB_ASSERT(source->path != NULL);
    err = memcpy_s(embed->path, embed->pathLen, source->path, embed->pathLen);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "wrong string copy for new base embed object path.");
        ret = GMERR_MEMORY_OPERATE_FAILED;
        goto EXIT;
    }

    *target = embed;
    return GMERR_OK;
EXIT:
    FreeBaseEmbedImpl(memCtx, embed);
    return ret;
}

void DmSharedObjAmFuncInit(void)
{
    DmSharedObjAmFuncT dmSharedObjAmFunc = {
        .dmSerializeStorageElementFunc = DmSerializeStorageElementImpl,
        .dmDeSerializeStorageElementFunc = DmDeSerializeStorageElementImpl,
        .dmSerializeStorageDeletedFunc = DmSerializeStorageDeletedImpl,
        .dmDeSerializeStorageDeletedFunc = DmDeSerializeStorageDeletedImpl,
        .dmGetStorageKeyFromBufFunc = DmGetStorageKeyFromBufImpl,
        .dmSerializeBaseLogBodyFunc = DmSerializeBaseLogBodyImpl,
        .dmSerializeOperationLogBodyFunc = DmSerializeOperationLogBodyImpl,
        .dmDeSerializeBaseLogBodyFunc = DmDeSerializeBaseLogBodyImpl,
        .dmDeSerializeOperationLogBodyFunc = DmDeSerializeOperationLogBodyImpl,
        .freeStorageElementFunc = FreeStorageElementImpl,
        .freeStorageDeletedFunc = FreeStorageDeletedImpl,
        .freeBaseLogBodyFunc = FreeBaseLogBodyImpl,
        .freeOperationLogBodyFunc = FreeOperationLogBodyImpl,
        .freeBaseStringFunc = FreeBaseStringImpl,
        .freeBaseAttributeFunc = FreeBaseAttributeImpl,
        .freeBaseEmbedFunc = FreeBaseEmbedImpl,
        .allocBaseStringFunc = AllocBaseStringImpl,
        .allocBaseAttributeFunc = AllocBaseAttributeImpl,
        .allocBaseEmbedFunc = AllocBaseEmbedImpl,
    };
    SetDmSharedObjAmFunc(&dmSharedObjAmFunc);
}
