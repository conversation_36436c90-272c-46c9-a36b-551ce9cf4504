/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: subscription label struct implement
 * 本文件中主要实现subs结构体中缓存一致性代码
 * Author: hufangzhou
 * Create: 2020-11-30
 */

#include <securec.h>
#include "dm_meta_log.h"
#include "adpt_string.h"
#include "dm_data_basic.h"
#include "dm_meta_basic.h"
#include "dm_meta_basic_in.h"
#include "dm_cache_multi_ver_mgr.h"
#include "dm_cache_single_ver_mgr.h"
#include "dm_meta_subscription.h"
#include "dm_meta_user.h"
#include "se_subs_status_merge.h"
#include "dm_meta_namespace.h"
#include "dm_meta_key_oper.h"
#include "dm_meta_kv_label.h"
#include "dm_meta_subs_in.h"
#include "cpl_base_pub.h"
#include "dm_log.h"
#ifdef FEATURE_GQL
#include "dm_meta_complex_path.h"
#endif

#ifdef __cplusplus
extern "C" {
#endif

/*
 * 用途：Catalog中已创建的订阅数量
 * 是否并发初始化：否
 * 是否并发读写：是，通过锁看护
 * 并发方案：无
 */
uint32_t g_gmdbSubscriptionNum = 0;  // 当前保存的总订阅数，不包含逻辑删除和实际删除的

#ifdef FEATURE_GQL
Status SubsCheckComplexPathIdAndType(
    uint32_t labelId, DmLabelTypeE labelType, DmSubsTypeE subsType, DbInstanceHdT dbInstance)
{
    if (subsType == SUB_ONESHOT_FAST || subsType == SUB_ONESHOT_SLOW) {
        return GMERR_OK;
    }
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    if (labelType == COMPLEX_PATH) {
        DmComplexPathInfoT *label = NULL;
        Status ret =
            SingleVersionGetById(cataCacheMgr->metaCache[CATA_COMPLEX_PATHINFO], labelId, (DmMetaCommonT **)&label);
        if (ret != GMERR_OK) {
            return GMERR_UNDEFINED_OBJECT;
        }
    }
    return GMERR_OK;
}

Status UpdateComplexPathSubsNum(const DmSubscriptionT *subs, bool isAdd)
{
    Status ret;
    DmComplexPathInfoT *label = NULL;
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(DbGetInstanceByMemCtx(subs->memCtx));
    ret = SingleVersionGetById(cataCacheMgr->metaCache[CATA_COMPLEX_PATHINFO], subs->labelId, (DmMetaCommonT **)&label);
    if (ret != GMERR_OK) {
        return GMERR_UNDEFINED_OBJECT;
    }
    label->subscriptionNum = isAdd ? label->subscriptionNum + 1u : label->subscriptionNum - 1u;

    /* 复杂path支持的订阅事件刚好是通用事件前四个，所以可以直接遍历，若支持事件有新增需要重构 */
    for (uint32_t i = 0; i < DM_SUBS_COMPLEXPATH_CEIL; i++) {
        if (subs->subMsgTypes[i] != 0) {
            label->subEventNums[i] =
                (uint16_t)(isAdd ? label->subEventNums[i] + (uint16_t)1 : label->subEventNums[i] - (uint16_t)1);
        }
    }
    return GMERR_OK;
}
#endif  // FEATURE_GQL

Status SubsCheckKvIdAndType(uint32_t labelId, DmSubsTypeE subsType, DbInstanceHdT dbInstance)
{
    DmKvLabelT *kvLabel = NULL;
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    Status ret = SingleVersionGetById(cataCacheMgr->metaCache[CATA_KV], labelId, (DmMetaCommonT **)&kvLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "KvLabel id is %" PRIu32, labelId);
        return ret;
    }
    // kv label只支持MESSAGE_QUEUE型subs
    if (subsType != MESSAGE_QUEUE) {
        DB_LOG_AND_SET_LASERR(
            GMERR_FEATURE_NOT_SUPPORTED, "subs type is STATUS_MERGE. Label Id: %" PRIu32 ".", labelId);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

Status SubsCheckLabelIdAndType(
    uint32_t labelId, uint32_t labelVersion, DmLabelTypeE labelType, DmSubsTypeE subsType, DbInstanceHdT dbInstance)
{
    Status ret;
#ifdef FEATURE_GQL
    if (subsType == SUB_ONESHOT_FAST || subsType == SUB_ONESHOT_SLOW || labelType == COMPLEX_PATH) {
        return SubsCheckComplexPathIdAndType(labelId, labelType, subsType, dbInstance);
    }
#endif
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    if (labelType == VERTEX_LABEL) {
        DmVertexLabelT *label = NULL;
        ret = MultiVersionGetByIdAndVersion(
            cataCacheMgr->metaCache[CATA_VL], labelId, labelVersion, (DmMetaCommonT **)&label);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "VertexLabel id is %" PRIu32 ", version is %" PRIu32 "", labelId, labelVersion);
            return ret;
        }
        // MESSAGE_QUEUE型label只支持MESSAGE_QUEUE型subs
        if (label->metaVertexLabel->labelSubsType == LABEL_MESSAGE_QUEUE && subsType != MESSAGE_QUEUE) {
            DB_LOG_AND_SET_LASERR(
                GMERR_FEATURE_NOT_SUPPORTED, "subs type is STATUS_MERGE. Label Id: %" PRIu32 ".", labelId);
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
        if (label->metaVertexLabel->labelSubsType == LABEL_STATUS_MERGE && subsType != STATUS_MERGE) {
            DB_LOG_AND_SET_LASERR(
                GMERR_FEATURE_NOT_SUPPORTED, "subs type is MESSAGE_QUEUE. Label Id: %" PRIu32 ".", labelId);
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
        if (!label->commonInfo->canSub) {
            DB_LOG_AND_SET_LASERR(GMERR_RESTRICT_VIOLATION, "label is dropping. Label Id: %" PRIu32 ".", labelId);
            return GMERR_RESTRICT_VIOLATION;
        }
        if (DmVertexLabelIsDatalogLabel(label) && label->metaVertexLabel->labelSubsType == LABEL_MESSAGE_QUEUE &&
            label->commonInfo->metaInfoAddr->subscriptionNum > 0) {
            DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED, "subscription exceeded for table %s, Id: %" PRIu32 ".",
                label->metaCommon.metaName, labelId);
            return GMERR_PROGRAM_LIMIT_EXCEEDED;
        }
        return GMERR_OK;
    }
    if (labelType == KV_TABLE) {
        return SubsCheckKvIdAndType(labelId, subsType, dbInstance);
    }
    DB_LOG_AND_SET_LASERR(
        GMERR_UNDEFINED_OBJECT, "Check label id: %" PRIu32 ", type %" PRIu32 "", labelId, (uint32_t)labelType);
    return GMERR_UNDEFINED_OBJECT;
}

Status UpdateKvLabelSubsNum(const DmSubscriptionT *subs, bool isAdd)
{
    DmKvLabelT *kvLabel = NULL;
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(DbGetInstanceByMemCtx(subs->memCtx));
    Status ret = SingleVersionGetById(cataCacheMgr->metaCache[CATA_KV], subs->labelId, (DmMetaCommonT **)&kvLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "KvLabel id is %" PRIu32, subs->labelId);
        return ret;
    }
    if (isAdd) {
        kvLabel->subscriptionNum += 1u;
        if (subs->subsType == STATUS_MERGE) {
            kvLabel->stmgSubNum += 1u;
        }
    } else {
        kvLabel->subscriptionNum -= 1u;
        if (subs->subsType == STATUS_MERGE) {
            kvLabel->stmgSubNum -= 1u;
        }
    }
    if (subs->subMsgTypes[DM_SUBS_EVENT_KV_SET] != 0) {
        kvLabel->subEventNums[DM_SUBS_KV_SET] =
            (isAdd ? (uint16_t)(kvLabel->subEventNums[DM_SUBS_KV_SET] + (uint16_t)1) :
                     (uint16_t)(kvLabel->subEventNums[DM_SUBS_KV_SET] - (uint16_t)1));
    }
    if (subs->subMsgTypes[DM_SUBS_EVENT_DELETE] != 0) {
        kvLabel->subEventNums[DM_SUBS_KV_REMOVE] =
            (isAdd ? (uint16_t)(kvLabel->subEventNums[DM_SUBS_KV_REMOVE] + (uint16_t)1) :
                     (uint16_t)(kvLabel->subEventNums[DM_SUBS_KV_REMOVE] - (uint16_t)1));
    }
    return GMERR_OK;
}

Status UpdateVertexLabelSubsNum(const DmSubscriptionT *subs, bool isAdd)
{
    DmVertexLabelT *vertexLabel = NULL;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(subs->memCtx);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    Status ret = MultiVersionGetById(cataCacheMgr->metaCache[CATA_VL], subs->labelId, (DmMetaCommonT **)&vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "label id is %" PRIu32 "", subs->labelId);
        return ret;
    }

    if (isAdd) {
        vertexLabel->commonInfo->metaInfoAddr->subscriptionNum += 1u;
        if (subs->subsType == STATUS_MERGE) {
            vertexLabel->commonInfo->metaInfoAddr->stmgSubNum += 1u;
        }
    } else {
        vertexLabel->commonInfo->metaInfoAddr->subscriptionNum -= 1u;
        if (subs->subsType == STATUS_MERGE) {
            vertexLabel->commonInfo->metaInfoAddr->stmgSubNum -= 1u;
        }
    }
    for (uint32_t i = 0; i < DM_SUBS_EVENT_CEIL; i++) {
        if (subs->subMsgTypes[i] != 0) {
            vertexLabel->commonInfo->metaInfoAddr->subEventNums[i] =
                (isAdd ? (uint16_t)(vertexLabel->commonInfo->metaInfoAddr->subEventNums[i] + (uint16_t)1) :
                         (uint16_t)(vertexLabel->commonInfo->metaInfoAddr->subEventNums[i] - (uint16_t)1));
        }
    }
    return GMERR_OK;
}

Status UpdateLabelSubsNum(const DmSubscriptionT *subs, bool isAdd)
{
#ifdef FEATURE_GQL
    if (subs->labelType == COMPLEX_PATH) {
        return UpdateComplexPathSubsNum(subs, isAdd);
    }
    if (subs->subsType == SUB_ONESHOT_FAST || subs->subsType == SUB_ONESHOT_SLOW) {
        return GMERR_OK;
    }
#endif
    if (subs->labelType != VERTEX_LABEL && subs->labelType != KV_TABLE) {
        DB_LOG_AND_SET_LASERR(GMERR_UNDEFINED_OBJECT, "Update label subs num, labelId: %" PRIu32 ".", subs->labelId);
        return GMERR_UNDEFINED_OBJECT;
    }
    if (subs->labelType == VERTEX_LABEL) {
        return UpdateVertexLabelSubsNum(subs, isAdd);
    } else {
        return UpdateKvLabelSubsNum(subs, isAdd);
    }
}

// 内部函数，出错时申请资源在函数外统一回收
Status CopySubs2NewCacheArr(
    DmSubscriptionT *subs, DmEventSubsCacheT *oldCache, DmEventSubsCacheT **newCache, bool isAdd)
{
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(DbGetInstanceByMemCtx(subs->memCtx));
    Status ret = DmCreateSubsCacheWithMemCtx((DbMemCtxT *)cataCacheMgr->dynMemCtx, newCache);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "subs name: %s.", subs->metaCommon.metaName);
        return ret;
    }
    if (isAdd) {
        if (oldCache == NULL) {  // 旧的subsCache不存在，则将subs加入新的subsCache
            ret = DmPutSubs2SubsCache(*newCache, subs);
            if (ret != GMERR_OK) {
                DB_LOG_AND_SET_LASERR(ret, "subs name: %s.", subs->metaCommon.metaName);
                return ret;
            }
        } else {  // 存在旧的subsCache，将旧的subsCache中所有的订阅复制给新的subsCache并将subs加入新的subsCache
            ret = DmCopyAndUpdateSubsCache(oldCache, *newCache, subs, isAdd);
            if (ret != GMERR_OK) {
                DB_LOG_AND_SET_LASERR(ret, "subs name: %s.", subs->metaCommon.metaName);
                return ret;
            }
        }
    } else {
        if (oldCache == NULL) {  // 旧的subsCache不存在，返回错误
            DB_LOG_AND_SET_LASERR(GMERR_UNDEFINED_OBJECT, "subs name: %s.", subs->metaCommon.metaName);
            return GMERR_UNDEFINED_OBJECT;
        }
        // 将旧的subsCache中除了指定subs的所有的订阅复制给新的subsCache
        ret = DmCopyAndUpdateSubsCache(oldCache, *newCache, subs, isAdd);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "subs name: %s.", subs->metaCommon.metaName);
            return ret;
        }
    }
    return GMERR_OK;
}

Status SaveSubs2SubsCacheList(DmSubscriptionT *subs, DmSubsCacheListT *cacheList)
{
    Status ret = GMERR_OK;
    uint32_t i;
    DmEventSubsCacheT *newCacheArr[(uint32_t)DM_SUBS_EVENT_CEIL] = {0};
    for (i = (uint32_t)DM_SUBS_EVENT_INSERT; i < (uint32_t)DM_SUBS_EVENT_CEIL; ++i) {
        if (subs->subMsgTypes[i] == 0) {  // 该subs的eventType不匹配
            continue;
        }
        ret = CopySubs2NewCacheArr(subs, cacheList->eventSubsCache[i], &newCacheArr[i], true);
        if (ret != GMERR_OK) {
            // 回收0-i个cache
            for (uint32_t j = 0; j <= i; j++) {
                DmDestroySubsCache(&newCacheArr[j]);
            }
            return ret;
        }
    }
    for (i = (uint32_t)DM_SUBS_EVENT_INSERT; i < (uint32_t)DM_SUBS_EVENT_CEIL; ++i) {
        if (subs->subMsgTypes[i] == 0) {  // 该subs的eventType不匹配
            continue;
        }
        DmEventSubsCacheT *oldCache = cacheList->eventSubsCache[i];
        cacheList->eventSubsCache[i] = newCacheArr[i];
        if (oldCache == NULL) {
            continue;
        }
        if (oldCache->refCount == 0) {  // 删除旧的subsCache
            DmDestroySubsCache(&oldCache);
        } else {  // refCount != 0表示正在引用，先标记删除，在release时真实删除
            oldCache->isDeleted = true;
        }
    }
    DbLinkedListAppend(&cacheList->totalSubsList, &subs->linkedNode);
    cacheList->subsNum++;
    return GMERR_OK;
}

Status RemoveSubsFromSubsCacheList(DmSubscriptionT *subs, DmSubsCacheListT *cacheList)
{
    uint32_t i;
    DmEventSubsCacheT *newCacheArr[(uint32_t)DM_SUBS_EVENT_CEIL] = {0};
    for (i = (uint32_t)DM_SUBS_EVENT_INSERT; i < (uint32_t)DM_SUBS_EVENT_CEIL; ++i) {
        if (subs->subMsgTypes[i] == 0) {  // 该subs的eventType不匹配
            continue;
        }
        Status ret = CopySubs2NewCacheArr(subs, cacheList->eventSubsCache[i], &newCacheArr[i], false);
        if (ret != GMERR_OK) {
            // 回收0-i个cache
            for (uint32_t k = 0; k <= i; k++) {
                DmDestroySubsCache(&newCacheArr[k]);
            }
            return ret;
        }
    }

    for (i = (uint32_t)DM_SUBS_EVENT_INSERT; i < (uint32_t)DM_SUBS_EVENT_CEIL; ++i) {
        if (subs->subMsgTypes[i] == 0) {  // 该subs的eventType不匹配
            continue;
        }
        DmEventSubsCacheT *oldCache = cacheList->eventSubsCache[i];
        if (oldCache->refCount == 0) {  // 删除旧的subsCache
            DmDestroySubsCache(&oldCache);
        } else {  // refCount != 0表示正在引用，先标记删除，在release时真实删除
            oldCache->isDeleted = true;
        }
        if (newCacheArr[i]->subsNum == 0) {  // 新的subsCache的subs数量为0，删除
            DmDestroySubsCache(&newCacheArr[i]);
            newCacheArr[i] = NULL;
        }
        cacheList->eventSubsCache[i] = newCacheArr[i];
    }

    DbLinkedListRemove(&subs->linkedNode);
    cacheList->subsNum--;
    return GMERR_OK;
}

void ClearSubsCacheList(DmSubsCacheListT *cacheList)
{
    if (cacheList->subsNum == 0) {
        return;
    }
    uint32_t i;
    for (i = (uint32_t)DM_SUBS_EVENT_INSERT; i < (uint32_t)DM_SUBS_EVENT_CEIL; ++i) {
        DmEventSubsCacheT *subsCache = cacheList->eventSubsCache[i];
        if (subsCache == NULL) {
            continue;
        }
        if (subsCache->refCount != 0) {
            subsCache->isDeleted = true;
        } else {
            DmDestroySubsCache(&subsCache);
        }
        cacheList->eventSubsCache[i] = NULL;
    }
}

#ifdef FEATURE_STMG_SUBS
Status SaveSubs2StatusMergeSubsList(DmSubscriptionT *subs)
{
    CataCacheMgrT *cata = DmGetCataCache(DbGetInstanceByMemCtx(subs->memCtx));
    DmSubsCacheListT *cacheList = DbOamapLookup(cata->labelId2SmSubs, subs->labelId, &subs->labelId, NULL);
    if (cacheList != NULL) {
        DbLinkedListAppend(&cacheList->totalSubsList, &subs->linkedNode);
        cacheList->subsNum++;
        return GMERR_OK;
    }
    // label对应的subsCacheList不存在，创建一个新的subsCacheList
    // 此函数为创建并存储元数据函数，如果创建过程中失败，会在函数中释放;
    // 创建成功后，需调用CataRemoveSubscriptionById释放 并发方案：支持并发，catalog模块内有锁看护
    cacheList = (DmSubsCacheListT *)DbDynMemCtxAlloc((DbMemCtxT *)cata->dynMemCtx, sizeof(DmSubsCacheListT));
    if (cacheList == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "cataCacheMgr memctx, size %zu", sizeof(DmSubsCacheListT));
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(cacheList, sizeof(DmSubsCacheListT), 0, sizeof(DmSubsCacheListT));
    cacheList->labelId = subs->labelId;
    Status ret = DbOamapInsert(cata->labelId2SmSubs, cacheList->labelId, &cacheList->labelId, cacheList, NULL);
    if (ret != GMERR_OK) {
        DbDynMemCtxFree((DbMemCtxT *)cata->dynMemCtx, cacheList);
        DB_LOG_AND_SET_LASERR(ret, "Subs id is %" PRIu32, subs->labelId);
        return ret;
    }
    DbLinkedListInit(&cacheList->totalSubsList);
    DbLinkedListAppend(&cacheList->totalSubsList, &subs->linkedNode);
    cacheList->subsNum++;
    return GMERR_OK;
}
#endif

Status SaveSubs2MessageQueueSubsList(DmSubscriptionT *subs)
{
    Status ret = GMERR_OK;
    CataCacheMgrT *cata = DmGetCataCache(DbGetInstanceByMemCtx(subs->memCtx));
    DmSubsCacheListT *cacheList = DbOamapLookup(cata->labelId2MqSubs, subs->labelId, &subs->labelId, NULL);
    if (cacheList != NULL) {
        return SaveSubs2SubsCacheList(subs, cacheList);
    }
    // label对应的subsCacheList不存在，创建一个新的subsCacheList
    // 此函数为创建并存储元数据函数，如果创建过程中失败，会在函数中释放;
    // 创建成功后，需调用CataRemoveSubscriptionById释放 并发方案：支持并发，catalog模块内有锁看护
    cacheList = (DmSubsCacheListT *)DbDynMemCtxAlloc((DbMemCtxT *)cata->dynMemCtx, sizeof(DmSubsCacheListT));
    if (cacheList == NULL) {
        DB_LOG_AND_SET_LASERR(
            GMERR_OUT_OF_MEMORY, "DmGetCataCache(dbInstance) memCtx, size %zu", sizeof(DmSubsCacheListT));
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t err = memset_s(cacheList, sizeof(DmSubsCacheListT), 0, sizeof(DmSubsCacheListT));
    if (err != EOK) {
        ret = GMERR_OUT_OF_MEMORY;
        goto EXIT2;
    }
    DbLinkedListInit(&cacheList->totalSubsList);
    cacheList->labelId = subs->labelId;
    ret = SaveSubs2SubsCacheList(subs, cacheList);
    if (ret != GMERR_OK) {
        goto EXIT1;
    }
    ret = DbOamapInsert(cata->labelId2MqSubs, cacheList->labelId, &cacheList->labelId, cacheList, NULL);
    if (ret != GMERR_OK) {
        goto EXIT1;
    }
    return GMERR_OK;
EXIT1:
    ClearSubsCacheList(cacheList);
EXIT2:
    DbDynMemCtxFree((DbMemCtxT *)cata->dynMemCtx, cacheList);
    DB_LOG_ERROR(ret, "Subs id is %" PRIu32, subs->labelId);
    return ret;
}

#ifdef FEATURE_STMG_SUBS
Status RemoveSubsFromStatusMergeSubsList(DmSubscriptionT *subs)
{
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(subs->memCtx);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DmSubsCacheListT *cacheList = DbOamapLookup(cataCacheMgr->labelId2SmSubs, subs->labelId, &subs->labelId, NULL);
    if (cacheList == NULL) {  // label对应的subsCacheList不存在，返回错误
        DB_LOG_AND_SET_LASERR(GMERR_UNDEFINED_OBJECT, "Remove subs label, id %" PRIu32 ".", subs->labelId);
        return GMERR_UNDEFINED_OBJECT;
    }
    DbLinkedListRemove(&subs->linkedNode);
    cacheList->subsNum--;
    if (cacheList->subsNum == 0) {  // subsCacheList中的subsNum为0，删除subsCacheList
        (void)DbOamapRemove(cataCacheMgr->labelId2SmSubs, subs->labelId, &subs->labelId);
        DbDynMemCtxFree((DbMemCtxT *)cataCacheMgr->dynMemCtx, cacheList);
    }

    return GMERR_OK;
}
#endif

Status RemoveSubsFromMessageQueueSubsList(DmSubscriptionT *subs)
{
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(subs->memCtx);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DmSubsCacheListT *cacheList = DbOamapLookup(cataCacheMgr->labelId2MqSubs, subs->labelId, &subs->labelId, NULL);
    if (cacheList == NULL) {  // label对应的subsCacheList不存在，返回错误
        DB_LOG_AND_SET_LASERR(GMERR_UNDEFINED_OBJECT, "Remove subs label, id %" PRIu32 ".", subs->labelId);
        return GMERR_UNDEFINED_OBJECT;
    }
    Status ret = RemoveSubsFromSubsCacheList(subs, cacheList);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (cacheList->subsNum == 0) {  // subsCacheList中的subsNum为0，删除subsCacheList
        (void)DbOamapRemove(cataCacheMgr->labelId2MqSubs, subs->labelId, &subs->labelId);
        DbDynMemCtxFree((DbMemCtxT *)cataCacheMgr->dynMemCtx, cacheList);
    }
    return GMERR_OK;
}

Status SaveSubs2LabelIdMap(DmSubscriptionT *subs)
{
    if (subs->subsType == STATUS_MERGE) {
#ifdef FEATURE_STMG_SUBS
        return SaveSubs2StatusMergeSubsList(subs);
#else
        return GMERR_FEATURE_NOT_SUPPORTED;
#endif
    } else {
        return SaveSubs2MessageQueueSubsList(subs);
    }
}

Status RemoveSubsFromLabelIdMap(DmSubscriptionT *subs)
{
    if (subs->subsType == STATUS_MERGE) {
#ifdef FEATURE_STMG_SUBS
        return RemoveSubsFromStatusMergeSubsList(subs);
#else
        return GMERR_FEATURE_NOT_SUPPORTED;
#endif
    } else {
        return RemoveSubsFromMessageQueueSubsList(subs);
    }
}

Status DeleteSubs(DmSubscriptionT *subs)
{
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(DbGetInstanceByMemCtx(subs->memCtx));
    Status ret = SingleVersionRemove(cataCacheMgr->metaCache[CATA_SUB], (DmMetaCommonT *)subs);
    DbDynMemCtxFree(cataCacheMgr->dynMemCtx, subs);
    return ret;
}

uint32_t GetSubsCopyLen(const DmSubscriptionT *originSubs)
{
    size_t len = sizeof(DmSubscriptionT);
    // channel
    if (originSubs->channelCount != 0) {
        DmChannelNodeT *chanNode = NULL;
        DmChannelNodeT *chanNodeTmp = NULL;
        LIST_FOR_EACH_ENTRY_SAFE(chanNode, chanNodeTmp, &originSubs->channelList, linkedNode)
        {
            len += sizeof(DmChannelNodeT) + chanNode->channelName.len;
        }
    }
    // subsConstraint
    len += sizeof(DmSubsConditionT) * originSubs->subsConstraint.conditionNum;

    for (uint32_t i = 0; i < originSubs->subsConstraint.conditionNum; i++) {
        // property不计算，指针用vertexlabel中的指针
        DmValueT *subsValue = &(originSubs->subsConstraint.conditions[i].subsValue);
        uint32_t namePathDepth = originSubs->subsConstraint.conditions[i].pathInfo.namePathDepth;
        for (uint32_t j = 0; j < namePathDepth; j++) {
            len += DM_STR_LEN(originSubs->subsConstraint.conditions[i].pathInfo.namePath[j]);
        }
        if (DM_TYPE_NEED_MALLOC(subsValue->type)) {
            len += subsValue->value.length;
        }
    }
    // var len string
    len += DM_STR_LEN(originSubs->metaCommon.metaName);
    len += originSubs->creator == NULL ? 0 : DM_STR_LEN(originSubs->creator);
    len += originSubs->commentLen;
    len += originSubs->labelNameLen;
    len += originSubs->subsJson.len;
#ifdef FEATURE_GQL
    // sr pathIdList
    if (originSubs->srPathIdList != NULL) {
        len += sizeof(DbListT);
        uint32_t cnt = DbListGetItemCnt(originSubs->srPathIdList);
        len += cnt * originSubs->srPathIdList->itemSize;
        for (uint32_t i = 0; i < cnt; ++i) {
            DbListT *pathList = (DbListT *)DbListItem(originSubs->srPathIdList, i);
            len += sizeof(DbListT);
            len += pathList->count * pathList->itemSize;
        }
    }
#endif
#ifdef MERGE_QUEUE
    if (originSubs->mergeCond != NULL) {
        len += sizeof(DmMergeConditionT);
        len += sizeof(uint32_t) * originSubs->mergeCond->mergeKeyNum;
    }
#endif
    return (uint32_t)len;
}

Status CreateAndCopySubscription(const DmSubscriptionT *originSubs, DmSubscriptionT **subs, DbMemCtxT *memCtx)
{
    DmSubscriptionT *newSubs = NULL;
    uint32_t subsLen = GetSubsCopyLen(originSubs);
    // 函数成功后由调用者释放，函数内失败时本函数内释放
    uint8_t *buf = (uint8_t *)DbDynMemCtxAlloc(memCtx, subsLen);
    if (buf == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "size is %" PRIu32, subsLen);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(buf, subsLen, 0, subsLen);
    uint8_t *bufCursor = buf;
    MetaCopy(originSubs, sizeof(DmSubscriptionT), (void **)&newSubs, &bufCursor);
    newSubs->memCtx = memCtx;
    CopySubscription(originSubs, newSubs, &bufCursor);
    if (buf + subsLen != bufCursor) {
        DbDynMemCtxFree(memCtx, buf);
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "subs len %" PRIu32 ", copy size %" PRIu32, subsLen,
            (uint32_t)((uintptr_t)bufCursor - (uintptr_t)buf));
        return GMERR_INTERNAL_ERROR;
    }
    *subs = newSubs;
    return GMERR_OK;
}

Status CheckSubsForCreate(const DmSubscriptionT *subs)
{
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(subs->memCtx);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    // 判断namespace是否存在
    if (!IsNamespaceIdExist(dbInstance, subs->metaCommon.namespaceId)) {
        DB_LOG_AND_SET_LASERR(
            GMERR_UNDEFINED_OBJECT, "Create subs, namespaceId: %" PRIu32 ".", subs->metaCommon.namespaceId);
        return GMERR_UNDEFINED_OBJECT;
    }
    // 判断subsType是否有效
    if (subs->subsType >= SUBS_TYPE_BOTTOM) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "Create subs, subs name: %s, subs type:%" PRIu32 ".",
            subs->metaCommon.metaName, (uint32_t)subs->subsType);
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    // 判断subs label 是否存在, type是否符合label要求
    Status ret =
        SubsCheckLabelIdAndType(subs->labelId, subs->labelVersion, subs->labelType, subs->subsType, dbInstance);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Check label id: %" PRIu32 ", type is %" PRIu32 "", subs->labelId, (uint32_t)subs->labelType);
        return ret;
    }

    // 判断subsName是否重复
    CataKeyT cataKey = {0};
    CataSetKeyForLabel(&cataKey, subs->metaCommon.dbId, subs->metaCommon.namespaceId, subs->metaCommon.metaName);
    if (SingleVersionLabelNameExist(cataCacheMgr->metaCache[CATA_SUB], &cataKey)) {
        DB_LOG_AND_SET_LASERR(GMERR_DUPLICATE_OBJECT, "Create subs, subsName: %s.", subs->metaCommon.metaName);
        return GMERR_DUPLICATE_OBJECT;
    }

    // 判断订阅总数是否超过阈值
    // 判断idMap中有多少个key，是否超过阈值。因为idMap中不存在已经删除的结构体
    if (g_gmdbSubscriptionNum >= (uint32_t)DbCfgGetInt32Lite(DB_CFG_MAX_SUBSCRIPTION_NUM, NULL)) {
        DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED, "Create subs, subsName: %s.", subs->metaCommon.metaName);
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }

    return CheckPrivEntityMetaLimit(dbInstance, subs->creator, subs->isGroupCreate, USER_META_SUBS);
}

Status CataCheckSubsForCreate(const DmSubscriptionT *subs)
{
    DB_POINTER(subs);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(DbGetInstanceByMemCtx(subs->memCtx));
    DbRWSpinRLock(&cataCacheMgr->cataLock);
    Status ret = CheckSubsForCreate(subs);
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    return ret;
}

Status CheckAndCopySubsInner(const DmSubscriptionT *subs, DmSubscriptionT **newSubs, DbInstanceHdT dbInstance)
{
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DmVertexLabelT *vertexLabel = NULL;
    Status ret = MultiVersionGetById(cataCacheMgr->metaCache[CATA_VL], subs->labelId, (DmMetaCommonT **)&vertexLabel);
    if (ret != GMERR_OK) {
        DmKvLabelT *kvTabel = NULL;
        ret = SingleVersionGetById(cataCacheMgr->metaCache[CATA_KV], subs->labelId, (DmMetaCommonT **)&kvTabel);
        if (ret != GMERR_OK) {
            return ret;
        }
    }

    ret = CheckSubsForCreate(subs);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 深拷贝一份subscription元数据
    return CreateAndCopySubscription(subs, newSubs, cataCacheMgr->dynMemCtx);
}

#if defined FEATURE_STMG_SUBS && !defined DATAMODEL_STATIC
static Status CreateStatusMergeCursor(DmSubscriptionT *sub)
{
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(DbGetInstanceByMemCtx(sub->memCtx));
    // get label
    DmVertexLabelT *vertexLabel;
    Status ret = MultiVersionGetById(cataCacheMgr->metaCache[CATA_VL], sub->labelId, (DmMetaCommonT **)&vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    // create cursor
    ShmemPtrT cursor;
    ret = CreateStatusMergeSubCursorNode(&cursor);
    if (ret != GMERR_OK) {
        return ret;
    }

    // insert cursor
    ShmemPtrT listPtr = vertexLabel->commonInfo->statusMergeList;
    if (sub->subMsgTypes[DM_SUBS_EVENT_INIT] != 0) {
        ret = DbServerCreateFullSub(listPtr, cursor);
    } else {
        ret = DbServerCreateIncSub(listPtr, cursor);
    }
    sub->statusMergeCursor = cursor;

    if (ret != GMERR_OK) {
        DbShmemCtxFree(GetSubsStmgListShmCtx(), cursor);
        DB_LOG_AND_SET_LASERR(ret, "Save subs, subsId: %" PRIu32 ".", sub->metaCommon.metaId);
    }
    return ret;
}

static Status RemoveStatusMergeCursor(const DmSubscriptionT *sub, bool isZombie)
{
    // get label
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(sub->memCtx);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DmVertexLabelT *vertexLabel = NULL;
    Status ret = MultiVersionGetById(cataCacheMgr->metaCache[CATA_VL], sub->labelId, (DmMetaCommonT **)&vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    // remove cursor
    ShmemPtrT listPtr = vertexLabel->commonInfo->statusMergeList;
    return DbServerDeleteSub(listPtr, sub->statusMergeCursor, isZombie);
}
#else
static Status CreateStatusMergeCursor(DmSubscriptionT *sub)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}

static Status RemoveStatusMergeCursor(const DmSubscriptionT *sub, bool isZombie)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}
#endif

#ifndef DATAMODEL_STATIC
Status CataSaveSubscription(const DmSubscriptionT *subs)
{
    DB_POINTER(subs);
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(subs->memCtx);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinWLock(&cataCacheMgr->cataLock);
    DmSubscriptionT *newSubs = NULL;
    Status ret = CheckAndCopySubsInner(subs, &newSubs, dbInstance);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    DB_ASSERT(newSubs != NULL);
    if (newSubs->subsType == STATUS_MERGE) {
        ret = CreateStatusMergeCursor(newSubs);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(cataCacheMgr->dynMemCtx, newSubs);
            goto EXIT;
        }
    }
    ret = SingleVersionPut(dbInstance, cataCacheMgr->metaCache[CATA_SUB], (DmMetaCommonT *)newSubs);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(
            ret, "subsName: %s, subId: %" PRIu32 ".", newSubs->metaCommon.metaName, newSubs->metaCommon.metaId);
        if (newSubs->subsType == STATUS_MERGE) {
            (void)DbMarkUnusedSubCursor(newSubs->statusMergeCursor);
            (void)RemoveStatusMergeCursor(newSubs, false);
        }
        DbDynMemCtxFree(cataCacheMgr->dynMemCtx, newSubs);
        goto EXIT;
    }
    // 将拷贝的subscription指针插入到labelId对应的subs指针链表中
    ret = SaveSubs2LabelIdMap(newSubs);
    if (ret != GMERR_OK) {
        if (newSubs->subsType == STATUS_MERGE) {
            (void)RemoveStatusMergeCursor(newSubs, false);
        }
        // 前置步骤已经put，此处一定能够删除成功
        newSubs->metaCommon.isDeleted = true;
        DB_ASSERT(DeleteSubs(newSubs) == GMERR_OK);
        DB_LOG_AND_SET_LASERR(ret, "Save subs, subsName: %s.", subs->metaCommon.metaName);
        goto EXIT;
    }
    g_gmdbSubscriptionNum++;
    (void)ModifyPrivEntityMetaCreatedNum(dbInstance, newSubs->creator, newSubs->isGroupCreate, USER_META_SUBS, true);
    ret = UpdateNspLabelCountNoLock(subs->metaCommon.namespaceId, true, dbInstance);
    DB_ASSERT(ret == GMERR_OK);
    // 更新label上的订阅数信息，在SaveSubs2LabelIdMap函数中已经校验了该ID一定会存在，因此该函数一定会成功
    ret = UpdateLabelSubsNum(subs, true);
    DB_ASSERT(ret == GMERR_OK);
EXIT:
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
    return ret;
}
#else
Status CataSaveSubscription(const DmSubscriptionT *subs)
{
    (void)subs;
    (void)CreateStatusMergeCursor(NULL);  // 避免unused function编译问题
    return GMERR_OK;
}
#endif

#if defined FEATURE_STMG_SUBS && !defined DATAMODEL_STATIC
static Status ZombieRetire(const DmSubscriptionT *sub)
{
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(sub->memCtx);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    // get label
    DmVertexLabelT *vertexLabel;
    Status ret = MultiVersionGetById(cataCacheMgr->metaCache[CATA_VL], sub->labelId, (DmMetaCommonT **)&vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    // remove cursor
    ShmemPtrT listPtr = vertexLabel->commonInfo->statusMergeList;
    ret = DbServerZombieRetire(listPtr, sub->statusMergeCursor);
    // 这里向gc发消息激活一下回收节点。
    return ret;
}
#else
static Status ZombieRetire(const DmSubscriptionT *sub)
{
    return GMERR_FEATURE_NOT_SUPPORTED;
}
#endif

Status RemoveSubscription(DmSubscriptionT *subs)
{
    Status ret = GMERR_OK;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(subs->memCtx);
    if (!subs->metaCommon.isDeleted) {
        // remove cursor
        if (subs->subsType == STATUS_MERGE) {
            if (subs->channelCount == 0) {
                ret = ZombieRetire(subs);
            } else {
                ret = RemoveStatusMergeCursor(subs, false);
            }
            if (ret != GMERR_OK) {
                DB_LOG_ERROR(ret, "cannot handle sub cursor, name %s", subs->metaCommon.metaName);
                return ret;
            }
            subs->statusMergeCursor = DB_INVALID_SHMPTR;
        }

        ret = RemoveSubsFromLabelIdMap(subs);
        if (ret != GMERR_OK) {
            return ret;
        }
        // 设置标记删除，更新nameSpace labelCount
        subs->metaCommon.isDeleted = true;
        ret = UpdateNspLabelCountNoLock(subs->metaCommon.namespaceId, false, dbInstance);
        DB_ASSERT(ret == GMERR_OK);  // ret != GMERR_OK表示subs已经被删除，函数入口加锁，理论上不可能发生，下同
        g_gmdbSubscriptionNum--;
        (void)ModifyPrivEntityMetaCreatedNum(dbInstance, subs->creator, subs->isGroupCreate, USER_META_SUBS, false);
        ret = UpdateLabelSubsNum(subs, false);
        DB_ASSERT(ret == GMERR_OK);
    }
    if (subs->metaCommon.refCount == 0) {
        ret = DeleteSubs(subs);
        DB_ASSERT(ret == GMERR_OK);
    }
    return ret;
}

Status CataRemoveSubscriptionByName(DbInstanceHdT dbInstance, const CataKeyT *cataKey)
{
    DB_POINTER(cataKey);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinWLock(&cataCacheMgr->cataLock);

    DmSubscriptionT *subs = NULL;
    Status ret = SingleVersionGetByName(cataCacheMgr->metaCache[CATA_SUB], cataKey, (DmMetaCommonT **)&subs);
    if (ret != GMERR_OK) {
        DbRWSpinWUnlock(&cataCacheMgr->cataLock);
        DB_LOG_AND_SET_LASERR(GMERR_UNDEFINED_OBJECT, "Remove subs by name:%s.", cataKey->labelName);
        return GMERR_UNDEFINED_OBJECT;
    }
    ret = RemoveSubscription(subs);
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
    return ret;
}

Status CataRemoveSubscriptionById(DbInstanceHdT dbInstance, uint32_t subId)
{
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinWLock(&cataCacheMgr->cataLock);

    DmSubscriptionT *subs = NULL;
    Status ret = SingleVersionGetById(cataCacheMgr->metaCache[CATA_SUB], subId, (DmMetaCommonT **)&subs);
    if (ret != GMERR_OK) {
        DbRWSpinWUnlock(&cataCacheMgr->cataLock);
        DB_LOG_AND_SET_LASERR(ret, "Remove subs by id:%" PRIu32 ".", subId);
        return ret;
    }
    ret = RemoveSubscription(subs);
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
    return ret;
}

Status CataGetSubsByName(const CataKeyT *cataKey, DmSubscriptionT **subs, DbInstanceHdT dbInstance)
{
    DB_POINTER2(cataKey, subs);
    DbRWSpinRLock(&DmGetCataCache(dbInstance)->cataLock);

    DmSubscriptionT *retSubs = NULL;
    Status ret =
        SingleVersionGetByName(DmGetCataCache(dbInstance)->metaCache[CATA_SUB], cataKey, (DmMetaCommonT **)&retSubs);
    if (ret != GMERR_OK) {  // 获取不到或者获取到的subs已被标记删除
        DbRWSpinRUnlock(&DmGetCataCache(dbInstance)->cataLock);
        DB_LOG_AND_SET_LASERR(GMERR_UNDEFINED_OBJECT, "Get subs by name:%s.", cataKey->labelName);
        return GMERR_UNDEFINED_OBJECT;
    }
    (void)DbAtomicInc(&retSubs->metaCommon.refCount);
    *subs = retSubs;
    DbRWSpinRUnlock(&DmGetCataCache(dbInstance)->cataLock);
    return GMERR_OK;
}

Status CataGetSubsById(uint32_t subId, DmSubscriptionT **subs, DbInstanceHdT dbInstance)
{
    DB_POINTER(subs);
    DbRWSpinRLock(&DmGetCataCache(dbInstance)->cataLock);

    DmSubscriptionT *retSubs = NULL;
    Status ret =
        SingleVersionGetById(DmGetCataCache(dbInstance)->metaCache[CATA_SUB], subId, (DmMetaCommonT **)&retSubs);
    if (ret != GMERR_OK) {  // 获取不到或者获取到的subs已被标记删除
        DbRWSpinRUnlock(&DmGetCataCache(dbInstance)->cataLock);
        DB_LOG_AND_SET_LASERR(GMERR_UNDEFINED_OBJECT, "Get subs by id:%" PRIu32 ".", subId);
        return GMERR_UNDEFINED_OBJECT;
    }
    (void)DbAtomicInc(&retSubs->metaCommon.refCount);
    *subs = retSubs;
    DbRWSpinRUnlock(&DmGetCataCache(dbInstance)->cataLock);
    return GMERR_OK;
}

static void RemoveChannelNodeBySubs(DmSubscriptionT *subs, CataRemoveChanItemParamT removePara)
{
    if (removePara.removeFunc == NULL) {
        return;
    }
    DmChannelNodeT *chanNode = NULL;
    DmChannelNodeT *chanNodeTmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(chanNode, chanNodeTmp, &subs->channelList, linkedNode)
    {
        removePara.removeFunc(removePara.memCtx, removePara.channel2ChannelItemT, chanNode->channelName.str);
    }
}

Status ReleaseSubscription(DmSubscriptionT *subs, CataRemoveChanItemParamT removePara)
{
    Status ret = GMERR_OK;
    if (subs->metaCommon.refCount > 0) {
        subs->metaCommon.refCount--;
        if (subs->metaCommon.refCount != 0) {
            return ret;
        }
        // 如果引用计数为0并且已经被删除，则将其真正删除
        if (subs->metaCommon.isDeleted) {
            RemoveChannelNodeBySubs(subs, removePara);
            return DeleteSubs(subs);
        }
    } else {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "Release subs, refcount: %" PRIu32 ".", subs->metaCommon.refCount);
        ret = GMERR_INTERNAL_ERROR;
    }
    return ret;
}

Status CataReleaseSubscription(DmSubscriptionT *subs)
{
    DB_POINTER(subs);
    // 在未标记删除的情况下加读锁，原子操作减 refCount，提升 release 操作性能
    bool hasReleased = false;
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(subs->memCtx);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    Status ret = ReleaseMetaQuickly(dbInstance, &subs->metaCommon, CATA_SUB, &hasReleased);
    if (ret != GMERR_OK || hasReleased) {
        return ret;
    }

    DbRWSpinWLock(&cataCacheMgr->cataLock);
    CataRemoveChanItemParamT removePara = {NULL, NULL, NULL};
    ret = ReleaseSubscription(subs, removePara);
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
    return ret;
}

#ifdef FEATURE_STMG_SUBS
Status CataGetStatusMergeSubsByLabelId(uint32_t labelId, DmSubsArrayT **subsArray, DbInstanceHdT dbInstance)
{
    DB_POINTER(subsArray);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinWLock(&cataCacheMgr->cataLock);
    DmSubsCacheListT *cacheList = DbOamapLookup(cataCacheMgr->labelId2SmSubs, labelId, &labelId, NULL);
    if (cacheList == NULL) {
        DbRWSpinWUnlock(&cataCacheMgr->cataLock);
        CATA_WARN(GMERR_UNDEFINED_OBJECT, "Illegal label id or label does not have status merge subs.",
            "label id %" PRIu32 ".", labelId);
        return GMERR_UNDEFINED_OBJECT;
    }
    // 此处为出参的内存申请，供外层函数读，需调用CataReleaseStateMergeArray释放
    // 并发方案：不涉及并发，资源只读
    uint32_t arraySize = (uint32_t)(cacheList->subsNum * sizeof(DmSubscriptionT *));
    uint8_t *buf = (uint8_t *)DbDynMemCtxAlloc(
        (DbMemCtxT *)DmGetCataCache(dbInstance)->dynMemCtx, sizeof(DmSubsArrayT) + arraySize);
    if (buf == NULL) {
        DbRWSpinWUnlock(&DmGetCataCache(dbInstance)->cataLock);
        CATA_LASTERR(GMERR_OUT_OF_MEMORY, "Unable to alloc memory when get status merge list by id.",
            "MemCtx is DmGetCataCache(dbInstance) memCtx, Memory size is %zu", sizeof(DmSubsArrayT));
        return GMERR_OUT_OF_MEMORY;
    }
    DmSubsArrayT *retSubsArray = (DmSubsArrayT *)buf;
    retSubsArray->arraySize = cacheList->subsNum;
    // 此处为出参的内存申请，供外层函数读，需调用CataReleaseStateMergeArray释放
    // 并发方案：不涉及并发，资源只读
    retSubsArray->subsArray = (DmSubscriptionT **)(buf + sizeof(DmSubsArrayT));

    DmSubscriptionT *subs = NULL;
    DmSubscriptionT *tempSubs = NULL;
    uint32_t index = 0;
    LIST_FOR_EACH_ENTRY_SAFE(subs, tempSubs, &cacheList->totalSubsList, linkedNode)
    {
        subs->metaCommon.refCount++;
        retSubsArray->subsArray[index++] = subs;
    }
    *subsArray = retSubsArray;
    DbRWSpinWUnlock(&DmGetCataCache(dbInstance)->cataLock);
    return GMERR_OK;
}

Status CataReleaseStatusMergeSubs(DmSubsArrayT *subsArray, DbInstanceHdT dbInstance)
{
    DB_POINTER(subsArray);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinWLock(&cataCacheMgr->cataLock);
    uint32_t arraySize = subsArray->arraySize;
    for (uint32_t i = 0; i < arraySize; i++) {
        DmSubscriptionT *subs = subsArray->subsArray[i];
        CataRemoveChanItemParamT removePara = {NULL, NULL, NULL};
        Status ret = ReleaseSubscription(subs, removePara);
        if (ret != GMERR_OK) {
            DB_ASSERT(false);
            CATA_ERROR(ret, "Release status merge subscription unsucc.", "Subscription's id: %" PRIu32 ", name: %s.",
                subs->metaCommon.metaId, subs->metaCommon.metaName);
        }
    }
    DbDynMemCtxFree(cataCacheMgr->dynMemCtx, subsArray);
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
    return GMERR_OK;
}
#endif

bool IsSubsBindTargetChannel(DmSubscriptionT *subs, char *channelName)
{
    if (subs->channelCount == 0) {
        return false;
    }
    DmChannelNodeT *chanNode = NULL;
    DmChannelNodeT *chanNodeTmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(chanNode, chanNodeTmp, &subs->channelList, linkedNode)
    {
        if (DbStrCmp(chanNode->channelName.str, channelName, false) == 0) {
            return true;
        }
    }
    return false;
}

uint32_t CataGetSubCountByChannelName(char *channelName, DbInstanceHdT dbInstance)
{
    DB_POINTER(channelName);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinRLock(&cataCacheMgr->cataLock);
    uint64_t startTime = DbRdtsc();
    bool isTimeOut = false;
    uint64_t timeout = CATA_HOLD_LOCK_TIMEOUT_US;
    uint32_t *id = NULL;
    DmMetaCommonT *value = NULL;
    DmSubscriptionT *tmpSubs = NULL;
    DbOamapIteratorT iter = 0;
    uint32_t count = 0;
    while (SingleVersionFetch(cataCacheMgr->metaCache[CATA_SUB], &iter, NULL, &id, &value) == GMERR_OK) {
        if (IsNowTimeout(startTime, timeout)) {
            DB_LOG_WARN(GMERR_INTERNAL_ERROR, "Time: %" PRIu64 " us, channel name: %s.", timeout, channelName);
            isTimeOut = true;
            timeout += timeout;
        }
        tmpSubs = (DmSubscriptionT *)value;
        if (tmpSubs->metaCommon.isDeleted) {
            continue;
        }
        if (IsSubsBindTargetChannel(tmpSubs, channelName)) {
            count++;
        }
    }
    if (isTimeOut) {
        DB_LOG_WARN(GMERR_OK, "Holding time: %" PRIu64 " us, channel name: %s.", DbToUseconds(DbRdtsc() - startTime),
            channelName);
    }
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    return count;
}

#ifndef DATAMODEL_STATIC
Status UnBindChannelFromSubs(const char *channelName, DmSubscriptionT *subs)
{
    if (subs->channelCount == 0) {
        return GMERR_OK;
    }
    Status ret = GMERR_OK;
    DmChannelNodeT *chanNode = NULL;
    DmChannelNodeT *chanNodeTmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(chanNode, chanNodeTmp, &subs->channelList, linkedNode)
    {
        if (DbStrCmp(chanNode->channelName.str, channelName, false) != 0) {
            continue;
        }
        chanNode->isUnBind = true;
        subs->channelCount--;

        if (subs->subsType == STATUS_MERGE && !IsShmemPtrEqual(subs->statusMergeCursor, DB_INVALID_SHMPTR)) {
            (void)DbMarkUnusedSubCursor(subs->statusMergeCursor);
            (void)RemoveStatusMergeCursor(subs, true);
        }

        if (subs->channelCount == 0) {
            ret = RemoveSubscription(subs);
        }
        break;
    }
    return ret;
}
#else
Status UnBindChannelFromSubs(const char *channelName, DmSubscriptionT *subs)
{
    return GMERR_OK;
}
#endif

Status CataUnBindChannelByName(const char *channelName, DbInstanceHdT dbInstance)
{
    DB_POINTER(channelName);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DbRWSpinWLock(&cataCacheMgr->cataLock);
    uint64_t startTime = DbRdtsc();
    bool isTimeOut = false;
    uint64_t timeout = CATA_HOLD_LOCK_TIMEOUT_US;
    uint32_t *id = NULL;
    DmMetaCommonT *value = NULL;
    DbOamapIteratorT iter = 0;
    DmSubscriptionT *subs = NULL;
    Status ret = GMERR_OK;
    while (true) {
        if (IsNowTimeout(startTime, timeout)) {
            DB_LOG_WARN(GMERR_INTERNAL_ERROR, "Time: %" PRIu64 " us, channel name: %s.", timeout, channelName);
            isTimeOut = true;
            timeout += timeout;
        }
        ret = SingleVersionFetch(cataCacheMgr->metaCache[CATA_SUB], &iter, NULL, &id, &value);
        if (ret != GMERR_OK) {  // fetch结束
            break;
        }
        subs = (DmSubscriptionT *)value;
        if (subs->metaCommon.isDeleted) {  // fetch到标记删除的数据，略过
            continue;
        }
        // fetch到标记删除的数据，删除对应的channel
        ret = UnBindChannelFromSubs(channelName, subs);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "unbind channel, subs id %" PRIu32 ".", subs->labelId);
            break;
        }
    }
    if (isTimeOut) {
        DB_LOG_WARN(GMERR_OK, "Holding time: %" PRIu64 " us, channel name: %s.", DbToUseconds(DbRdtsc() - startTime),
            channelName);
    }
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
    return ret;
}

DmEventSubsCacheT *GetSubsCacheByEventType(DmSubsCacheListT *subsCacheList, DmSubsEventE subsEvent)
{
    if ((uint32_t)subsEvent >= (uint32_t)DM_SUBS_EVENT_CEIL) {
        DB_ASSERT(false);
        return NULL;
    }
    if (subsCacheList->eventSubsCache[(uint32_t)subsEvent] != NULL) {
        return subsCacheList->eventSubsCache[(uint32_t)subsEvent];
    }
    if (subsEvent == DM_SUBS_EVENT_REPLACE_INSERT || subsEvent == DM_SUBS_EVENT_REPLACE_UPDATE) {
        if (subsCacheList->eventSubsCache[(uint32_t)DM_SUBS_EVENT_REPLACE] != NULL) {
            return subsCacheList->eventSubsCache[(uint32_t)DM_SUBS_EVENT_REPLACE];
        }
    }
    if (subsEvent == DM_SUBS_EVENT_MERGE_INSERT || subsEvent == DM_SUBS_EVENT_MERGE_UPDATE) {
        if (subsCacheList->eventSubsCache[(uint32_t)DM_SUBS_EVENT_MERGE] != NULL) {
            return subsCacheList->eventSubsCache[(uint32_t)DM_SUBS_EVENT_MERGE];
        }
    }
    return NULL;
}

void AtomicIncreaseSubsRefCountInSubsCache(DmEventSubsCacheT *subsCache)
{
    if (subsCache->subsNum == 0) {
        return;
    }
    uint32_t i;
    for (i = 0; i < subsCache->reliableSubs.condSubs.arraySize; ++i) {
        (void)DbAtomicInc(&subsCache->reliableSubs.condSubs.subsArray[i]->metaCommon.refCount);
    }
    for (i = 0; i < subsCache->reliableSubs.uncondSubs.arraySize; ++i) {
        (void)DbAtomicInc(&subsCache->reliableSubs.uncondSubs.subsArray[i]->metaCommon.refCount);
    }
    for (i = 0; i < subsCache->unreliableSubs.condSubs.arraySize; ++i) {
        (void)DbAtomicInc(&subsCache->unreliableSubs.condSubs.subsArray[i]->metaCommon.refCount);
    }
    for (i = 0; i < subsCache->unreliableSubs.uncondSubs.arraySize; ++i) {
        (void)DbAtomicInc(&subsCache->unreliableSubs.uncondSubs.subsArray[i]->metaCommon.refCount);
    }
}

void AtomicDecreaseSubsRefCountInSubsCache(DmEventSubsCacheT *subsCache)
{
    if (subsCache->subsNum == 0) {
        return;
    }
    uint32_t i;
    for (i = 0; i < subsCache->reliableSubs.condSubs.arraySize; ++i) {
        (void)DbAtomicDec(&subsCache->reliableSubs.condSubs.subsArray[i]->metaCommon.refCount);
    }
    for (i = 0; i < subsCache->reliableSubs.uncondSubs.arraySize; ++i) {
        (void)DbAtomicDec(&subsCache->reliableSubs.uncondSubs.subsArray[i]->metaCommon.refCount);
    }
    for (i = 0; i < subsCache->unreliableSubs.condSubs.arraySize; ++i) {
        (void)DbAtomicDec(&subsCache->unreliableSubs.condSubs.subsArray[i]->metaCommon.refCount);
    }
    for (i = 0; i < subsCache->unreliableSubs.uncondSubs.arraySize; ++i) {
        (void)DbAtomicDec(&subsCache->unreliableSubs.uncondSubs.subsArray[i]->metaCommon.refCount);
    }
}

void IncreaseSubsRefCountInSubsCache(DmEventSubsCacheT *subsCache)
{
    if (subsCache->subsNum == 0) {
        return;
    }
    uint32_t i;
    for (i = 0; i < subsCache->reliableSubs.condSubs.arraySize; ++i) {
        ++subsCache->reliableSubs.condSubs.subsArray[i]->metaCommon.refCount;
    }
    for (i = 0; i < subsCache->reliableSubs.uncondSubs.arraySize; ++i) {
        ++subsCache->reliableSubs.uncondSubs.subsArray[i]->metaCommon.refCount;
    }
    for (i = 0; i < subsCache->unreliableSubs.condSubs.arraySize; ++i) {
        ++subsCache->unreliableSubs.condSubs.subsArray[i]->metaCommon.refCount;
    }
    for (i = 0; i < subsCache->unreliableSubs.uncondSubs.arraySize; ++i) {
        ++subsCache->unreliableSubs.uncondSubs.subsArray[i]->metaCommon.refCount;
    }
}

void ReleaseSubsInSubsCache(DmEventSubsCacheT *subsCache, CataRemoveChanItemParamT removePara)
{
    if (subsCache->subsNum == 0) {
        return;
    }
    uint32_t i;
    for (i = 0; i < subsCache->reliableSubs.condSubs.arraySize; ++i) {
        (void)ReleaseSubscription(subsCache->reliableSubs.condSubs.subsArray[i], removePara);
    }
    for (i = 0; i < subsCache->reliableSubs.uncondSubs.arraySize; ++i) {
        (void)ReleaseSubscription(subsCache->reliableSubs.uncondSubs.subsArray[i], removePara);
    }
    for (i = 0; i < subsCache->unreliableSubs.condSubs.arraySize; ++i) {
        (void)ReleaseSubscription(subsCache->unreliableSubs.condSubs.subsArray[i], removePara);
    }
    for (i = 0; i < subsCache->unreliableSubs.uncondSubs.arraySize; ++i) {
        (void)ReleaseSubscription(subsCache->unreliableSubs.uncondSubs.subsArray[i], removePara);
    }
}

bool SubsCacheContainDeletedSubs(DmEventSubsCacheT *subsCache)
{
    if (subsCache->isDeleted) {
        return true;
    }
    if (subsCache->subsNum == 0) {
        return false;
    }
    uint32_t i;
    for (i = 0; i < subsCache->reliableSubs.condSubs.arraySize; ++i) {
        if (subsCache->reliableSubs.condSubs.subsArray[i]->metaCommon.isDeleted) {
            return true;
        }
    }
    for (i = 0; i < subsCache->reliableSubs.uncondSubs.arraySize; ++i) {
        if (subsCache->reliableSubs.uncondSubs.subsArray[i]->metaCommon.isDeleted) {
            return true;
        }
    }
    for (i = 0; i < subsCache->unreliableSubs.condSubs.arraySize; ++i) {
        if (subsCache->unreliableSubs.condSubs.subsArray[i]->metaCommon.isDeleted) {
            return true;
        }
    }
    for (i = 0; i < subsCache->unreliableSubs.uncondSubs.arraySize; ++i) {
        if (subsCache->unreliableSubs.uncondSubs.subsArray[i]->metaCommon.isDeleted) {
            return true;
        }
    }
    return false;
}

void ReleaseEventSubsCache(DmEventSubsCacheT **subsCache, CataRemoveChanItemParamT removePara)
{
    DB_ASSERT((*subsCache)->refCount > 0);
    (*subsCache)->refCount--;
    ReleaseSubsInSubsCache(*subsCache, removePara);
    if ((*subsCache)->refCount == 0 && (*subsCache)->isDeleted) {
        DmDestroySubsCache(subsCache);
    }
}

bool ReleaseEventSubsCacheQuickly(CataCacheMgrT *cataCacheMgr, DmEventSubsCacheT *subsCache)
{
    DbRWSpinRLock(&cataCacheMgr->cataLock);
    if (SubsCacheContainDeletedSubs(subsCache)) {
        DbRWSpinRUnlock(&cataCacheMgr->cataLock);
        return false;
    }
    (void)DbAtomicDec(&subsCache->refCount);
    AtomicDecreaseSubsRefCountInSubsCache(subsCache);
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    return true;
}

bool GetSubsCacheByEventQuickly(
    CataCacheMgrT *cataCacheMgr, CataGetSubsCacheParamT getParam, CataRemoveChanItemParamT removePara)
{
    uint32_t labelId = getParam.labelId;
    DmSubsEventE subsEvent = getParam.subsEvent;
    DmEventSubsCacheT **subsCache = getParam.subsCache;
    DbRWSpinRLock(&cataCacheMgr->cataLock);
    DmSubsCacheListT *subsCacheList = DbOamapLookup(cataCacheMgr->labelId2MqSubs, labelId, &labelId, NULL);
    if (subsCacheList == NULL) {
        if (*subsCache != NULL && SubsCacheContainDeletedSubs(*subsCache)) {
            DbRWSpinRUnlock(&cataCacheMgr->cataLock);
            return false;
        }
        if (*subsCache != NULL) {
            ReleaseEventSubsCache(subsCache, removePara);
            *subsCache = NULL;
        }
        DbRWSpinRUnlock(&cataCacheMgr->cataLock);
        return true;
    }

    DmEventSubsCacheT *srcCache = GetSubsCacheByEventType(subsCacheList, subsEvent);
    if (srcCache != *subsCache) {
        if (*subsCache != NULL && SubsCacheContainDeletedSubs(*subsCache)) {
            DbRWSpinRUnlock(&cataCacheMgr->cataLock);
            return false;
        }
        if (*subsCache != NULL) {
            ReleaseEventSubsCache(subsCache, removePara);
        }
        if (srcCache != NULL) {
            (void)DbAtomicInc(&srcCache->refCount);
            AtomicIncreaseSubsRefCountInSubsCache(srcCache);
        }
        *subsCache = srcCache;
    }
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    return true;
}

Status CataGetSubsCacheByEvent(
    CataGetSubsCacheParamT getParam, CataRemoveChanItemParamT removePara, DbInstanceHdT dbInstance)
{
    uint32_t labelId = getParam.labelId;
    DmSubsEventE subsEvent = getParam.subsEvent;
    DmEventSubsCacheT **subsCache = getParam.subsCache;
    DB_POINTER(subsCache);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    if (GetSubsCacheByEventQuickly(cataCacheMgr, getParam, removePara)) {
        return GMERR_OK;
    }

    DbRWSpinWLock(&cataCacheMgr->cataLock);
    DmSubsCacheListT *subsCacheList = DbOamapLookup(cataCacheMgr->labelId2MqSubs, labelId, &labelId, NULL);
    if (subsCacheList == NULL) {
        if (*subsCache != NULL) {
            ReleaseEventSubsCache(subsCache, removePara);
            *subsCache = NULL;
        }
        DbRWSpinWUnlock(&cataCacheMgr->cataLock);
        return GMERR_OK;
    }

    DmEventSubsCacheT *srcCache = GetSubsCacheByEventType(subsCacheList, subsEvent);
    if (srcCache != *subsCache) {
        if (*subsCache != NULL) {
            ReleaseEventSubsCache(subsCache, removePara);
        }
        if (srcCache != NULL) {
            ++srcCache->refCount;
            IncreaseSubsRefCountInSubsCache(srcCache);
        }
        *subsCache = srcCache;
    }
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
    return GMERR_OK;
}

void CataReleaseEventSubsCache(DmEventSubsCacheT *subsCache)
{
    DB_POINTER(subsCache);
    DbInstanceHdT dbInstance = DbGetInstanceByMemCtx(subsCache->memCtx);
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    if (ReleaseEventSubsCacheQuickly(cataCacheMgr, subsCache)) {
        return;
    }

    DbRWSpinWLock(&cataCacheMgr->cataLock);
    CataRemoveChanItemParamT removePara = {NULL, NULL, NULL};
    ReleaseEventSubsCache(&subsCache, removePara);
    DbRWSpinWUnlock(&cataCacheMgr->cataLock);
}

Status CheckSubsLabelIdExistAndClear(DbInstanceHdT dbInstance, uint32_t labelId, DmLabelTypeE labelType)
{
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    if (labelType == VERTEX_LABEL) {
        DmVertexLabelT *label = NULL;
        Status ret = MultiVersionGetById(cataCacheMgr->metaCache[CATA_VL], labelId, (DmMetaCommonT **)&label);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "label id is %" PRIu32, labelId);
            return ret;
        }
        label->commonInfo->metaInfoAddr->subscriptionNum = 0;
        label->commonInfo->metaInfoAddr->stmgSubNum = 0;
        (void)memset_s(label->commonInfo->metaInfoAddr->subEventNums,
            sizeof(label->commonInfo->metaInfoAddr->subEventNums), 0,
            sizeof(label->commonInfo->metaInfoAddr->subEventNums));
        return GMERR_OK;
    }
    if (labelType == KV_TABLE) {
        DmKvLabelT *label = NULL;
        Status ret = SingleVersionGetById(cataCacheMgr->metaCache[CATA_KV], labelId, (DmMetaCommonT **)&label);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "KvLabel id is %" PRIu32, labelId);
            return ret;
        }
        label->subscriptionNum = 0;
        label->stmgSubNum = 0;
        (void)memset_s(label->subEventNums, sizeof(label->subEventNums), 0, sizeof(label->subEventNums));

        return GMERR_OK;
    }
#ifdef FEATURE_GQL
    if (labelType == COMPLEX_PATH) {
        DmComplexPathInfoT *label = NULL;
        Status ret =
            SingleVersionGetById(cataCacheMgr->metaCache[CATA_COMPLEX_PATHINFO], labelId, (DmMetaCommonT **)&label);
        if (ret != GMERR_OK) {
            return GMERR_UNDEFINED_OBJECT;
        }
        label->subscriptionNum = 0;
        (void)memset_s(label->subEventNums, sizeof(label->subEventNums), 0, sizeof(label->subEventNums));
        return GMERR_OK;
    }
#endif
    DB_LOG_AND_SET_LASERR(GMERR_UNDEFINED_OBJECT, "Check label id: %" PRIu32 ".", labelId);
    return GMERR_UNDEFINED_OBJECT;
}

void RemoveAllSubsByLabelId(DbInstanceHdT dbInstance, uint32_t labelId, DmLabelTypeE labelType)
{
    Status ret = GMERR_OK;
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    DmSubsCacheListT *subsCacheList = DbOamapLookup(cataCacheMgr->labelId2MqSubs, labelId, &labelId, NULL);
    if (subsCacheList == NULL || subsCacheList->subsNum == 0) {
        return;
    }
    // 遍历eventSubsCache，销毁所有SubsCache
    uint32_t i;
    for (i = (uint32_t)DM_SUBS_EVENT_INSERT; i < (uint32_t)DM_SUBS_EVENT_CEIL; ++i) {
        DmEventSubsCacheT *subsCache = subsCacheList->eventSubsCache[i];
        if (subsCache == NULL) {
            continue;
        }
        if (subsCache->refCount == 0) {
            DmDestroySubsCache(&subsCache);
        } else {
            subsCache->isDeleted = true;
        }
        subsCacheList->eventSubsCache[i] = NULL;
    }

    DmSubscriptionT *subs = NULL;
    DmSubscriptionT *tempSubs = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(subs, tempSubs, &subsCacheList->totalSubsList, linkedNode)
    {
        // labelId2SubsList中保存的subs的isDeleted一定不为true
        subs->metaCommon.isDeleted = true;
        ret = UpdateNspLabelCountNoLock(subs->metaCommon.namespaceId, false, dbInstance);
        DB_ASSERT(ret == GMERR_OK);
        g_gmdbSubscriptionNum--;
        (void)ModifyPrivEntityMetaCreatedNum(dbInstance, subs->creator, subs->isGroupCreate, USER_META_SUBS, false);
        // 判断是否refCount为0 为0直接删除掉
        if (subs->metaCommon.refCount == 0) {
            ret = DeleteSubs(subs);
            DB_ASSERT(ret == GMERR_OK);
        }
    }
    ret = CheckSubsLabelIdExistAndClear(dbInstance, labelId, labelType);
    DB_ASSERT(ret == GMERR_OK);  // 该函数仅在catalog内部删除label前调用，故一定能找到label
    // 删除subsCacheList并从map中移除
    (void)DbOamapRemove(cataCacheMgr->labelId2MqSubs, labelId, &labelId);
    DbDynMemCtxFree((DbMemCtxT *)cataCacheMgr->dynMemCtx, subsCacheList);
}

uint64_t CataGetLabelSubsMemById(uint32_t id, DbInstanceHdT dbInstance)
{
    uint64_t size = 0;
    DmSubscriptionT *subs = NULL;
    DmSubscriptionT *tempSubs = NULL;
    CataCacheMgrT *cataCacheMgr = DmGetCataCache(dbInstance);
    // 计算message queue型订阅的内存
    DbRWSpinRLock(&cataCacheMgr->cataLock);
    DmSubsCacheListT *mqCacheList = DbOamapLookup(cataCacheMgr->labelId2MqSubs, id, &id, NULL);
    if (mqCacheList != NULL) {
        size += DmGetSubsCacheListLength(mqCacheList, MESSAGE_QUEUE);
        LIST_FOR_EACH_ENTRY_SAFE(subs, tempSubs, &mqCacheList->totalSubsList, linkedNode)
        {
            size += DmGetSubscriptionLength(subs);
        }
    }

    subs = NULL;
    tempSubs = NULL;
    // 计算status merge型订阅的内存
    DmSubsCacheListT *smCacheList = DbOamapLookup(cataCacheMgr->labelId2SmSubs, id, &id, NULL);
    if (smCacheList != NULL) {
        size += DmGetSubsCacheListLength(smCacheList, STATUS_MERGE);
        LIST_FOR_EACH_ENTRY_SAFE(subs, tempSubs, &smCacheList->totalSubsList, linkedNode)
        {
            size += DmGetSubscriptionLength(subs);
        }
    }
    DbRWSpinRUnlock(&cataCacheMgr->cataLock);
    return size;
}

#ifdef __cplusplus
}
#endif
