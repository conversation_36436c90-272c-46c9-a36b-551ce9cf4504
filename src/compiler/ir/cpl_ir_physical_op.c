/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Implementation of IR physical operator
 * Author: xuxin
 * Create: 2022-07-15
 */

#include "cpl_ir_physical_op.h"
#include "cpl_ir_utils.h"
#include "cpl_log.h"
#include "cpl_ir_op_common.h"
#ifdef FEATURE_STREAM
#include "dm_meta_prop_stream_label.h"
#endif

#include <cpl_ir_explain.h>
#ifdef FEATURE_TS
#include "dm_cu.h"
#include "dm_data_ts.h"
#include "ee_expression.h"
#endif

#ifdef __cplusplus
extern "C" {
#endif

Status CopyPhyOpScan(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
#ifdef FEATURE_TS
    DB_ASSERT(srcOp->type == IR_PHYOP_SEQSCAN || srcOp->type == IR_PHYOP_WORKLABELSCAN ||
              srcOp->type == IR_PHYOP_TIMEPARTITION_SCAN);
#else
    DB_ASSERT(srcOp->type == IR_PHYOP_SEQSCAN || srcOp->type == IR_PHYOP_WORKLABELSCAN);
#endif
    (void)memCtx;
    const OpPhyScanT *src = (const OpPhyScanT *)(const void *)srcOp;
    OpPhyScanT *dst = (OpPhyScanT *)(void *)dstOp;
    dst->label = src->label;
    return GMERR_OK;
}

#ifdef FEATURE_STREAM
Status CopyPhyOpStreamScan(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
    DB_ASSERT(srcOp->type == IR_PHYOP_STREAMSCAN || srcOp->type == IR_PHYOP_STREAMSCAN);
    (void)memCtx;
    const OpPhyStreamScanT *src = (const OpPhyStreamScanT *)(const void *)srcOp;
    OpPhyStreamScanT *dst = (OpPhyStreamScanT *)(void *)dstOp;
    dst->scan.label = src->scan.label;
    return GMERR_OK;
}
#endif

Status CopyPhyOpIndexScan(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
#ifndef FEATURE_SQL
    DB_ASSERT(srcOp->type == IR_PHYOP_INDEXSCAN || srcOp->type == IR_PHYOP_INDEXFILTERSCAN);
#endif
    (void)memCtx;
    const OpPhyIndexScanT *src = (const OpPhyIndexScanT *)(const void *)srcOp;
    OpPhyIndexScanT *dst = (OpPhyIndexScanT *)(void *)dstOp;
    dst->scan.label = src->scan.label;
    dst->index = src->index;
    return GMERR_OK;
}

Status CopyPhyOpJoin(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
    DB_ASSERT(srcOp->type == IR_PHYOP_NESTLOOPJOIN || srcOp->type == IR_PHYOP_INDEXLOOPJOIN ||
              srcOp->type == IR_PHYOP_HASHJOIN);

    const OpPhyJoinT *src = (const OpPhyJoinT *)(const void *)srcOp;
    OpPhyJoinT *dst = (OpPhyJoinT *)(void *)dstOp;

    dst->joinType = src->joinType;
    dst->joinKeyNum = src->joinKeyNum;
    dst->leftKeyIds = NULL;
    dst->rightKeyIds = NULL;
#ifdef FEATURE_SQL
    dst->onExpr = src->onExpr;
#endif

    uint32_t size = (uint32_t)(src->joinKeyNum * sizeof(uint32_t));
    // memCtx：由外层传入，并由外层管理释放；并发：不支持，外部模块保证线程安全
    Status ret = DmCopyBufWithMemCtx(
        memCtx, (const uint8_t *)(const void *)src->leftKeyIds, size, (uint8_t **)&(dst->leftKeyIds));
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "copy physical join leftKeyIds, size:%" PRIu32, size);
        return ret;
    }
    // memCtx：由外层传入，并由外层管理释放；并发：不支持，外部模块保证线程安全
    ret = DmCopyBufWithMemCtx(
        memCtx, (const uint8_t *)(const void *)src->rightKeyIds, size, (uint8_t **)&(dst->rightKeyIds));
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "copy of physical join rightKeyIds, size:%" PRIu32, size);
        return ret;
    }

    return GMERR_OK;
}

Status CopyPhyOpProject(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
    DB_ASSERT(srcOp->type == IR_PHYOP_PROJECT);

    const OpPhyProjectT *src = (const OpPhyProjectT *)(const void *)srcOp;
    OpPhyProjectT *dst = (OpPhyProjectT *)(void *)dstOp;

    dst->rowIdxNum = src->rowIdxNum;
    dst->rowIndex = NULL;
    dst->colIdxNum = src->colIdxNum;
    dst->colIndex = NULL;
    dst->isAllProjection = src->isAllProjection;

    uint32_t size = (uint32_t)(src->rowIdxNum * sizeof(uint32_t));
    // memCtx：由外层传入，并由外层管理释放；并发：不支持，外部模块保证线程安全
    Status ret =
        DmCopyBufWithMemCtx(memCtx, (const uint8_t *)(const void *)src->rowIndex, size, (uint8_t **)&(dst->rowIndex));
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "copy of physical project rowIndex, size:%" PRIu32, size);
        return ret;
    }

    size = (uint32_t)(src->colIdxNum * sizeof(uint32_t));
    // memCtx：由外层传入，并由外层管理释放；并发：不支持，外部模块保证线程安全
    ret = DmCopyBufWithMemCtx(memCtx, (const uint8_t *)(const void *)src->colIndex, size, (uint8_t **)&(dst->colIndex));
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "copy physical project colIndex, size:%" PRIu32, size);
        return ret;
    }

    return GMERR_OK;
}

Status CopyPhyOpExtendProject(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
    DB_ASSERT(srcOp->type == IR_PHYOP_EXTEND_PROJECT);

    const OpPhyExtendProjectT *src = (const OpPhyExtendProjectT *)(const void *)srcOp;
    OpPhyExtendProjectT *dst = (OpPhyExtendProjectT *)(void *)dstOp;

    dst->rowIdxNum = src->rowIdxNum;
    dst->rowIndex = NULL;
    dst->colIdxNum = src->colIdxNum;
    dst->colIndex = NULL;

    uint32_t size = (uint32_t)(src->rowIdxNum * sizeof(uint32_t));
    // memCtx：由外层传入，并由外层管理释放；并发：不支持，外部模块保证线程安全
    Status ret =
        DmCopyBufWithMemCtx(memCtx, (const uint8_t *)(const void *)src->rowIndex, size, (uint8_t **)&(dst->rowIndex));
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "copy physical extend project rowIndex, size:%" PRIu32, size);
        return ret;
    }

    size = src->colIdxNum * (uint32_t)sizeof(IRExtractIndexT);
    dst->colIndex = (IRExtractIndexT *)DbDynMemCtxAlloc(memCtx, size);
    if (dst->colIndex == NULL) {
        // when copy physical extend project op
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "malloc for colIndex, size:%" PRIu32, size);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(dst->colIndex, size, 0x00, size);

    for (uint32_t i = 0; i < src->colIdxNum; i++) {
        if (src->colIndex[i].type == IR_EXTRACT_CONST) {
            dst->colIndex[i].type = IR_EXTRACT_CONST;
            ret = DmValueCopy(memCtx, &src->colIndex[i].value.constValue, &dst->colIndex[i].value.constValue);
            if (ret != GMERR_OK) {
                // for physical extend project
                DB_LOG_AND_SET_LASERR(ret, "copy variable const value.");
                return ret;
            }
        } else {
            dst->colIndex[i] = src->colIndex[i];
        }
    }

    return GMERR_OK;
}

Status CopyPhyOpMerge(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
    DB_ASSERT(srcOp->type == IR_PHYOP_MERGE);
    (void)memCtx;
    const OpPhyMergeT *src = (const OpPhyMergeT *)(const void *)srcOp;
    OpPhyMergeT *dst = (OpPhyMergeT *)(void *)dstOp;
    dst->label = src->label;
    return GMERR_OK;
}

Status CopyPhyOpModify(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
    DB_ASSERT(srcOp->type == IR_PHYOP_MODIFYWORKLABEL);
    (void)memCtx;
    const OpPhyWorkLabelModifyT *src = (const OpPhyWorkLabelModifyT *)(const void *)srcOp;
    OpPhyWorkLabelModifyT *dst = (OpPhyWorkLabelModifyT *)(void *)dstOp;
    dst->label = src->label;
    dst->dmlType = src->dmlType;
    return GMERR_OK;
}

bool IsPhyOpModifyEqual(const OpBaseT *op1, const OpBaseT *op2)
{
    DB_ASSERT(op1->type == IR_PHYOP_MODIFYWORKLABEL && op2->type == IR_PHYOP_MODIFYWORKLABEL);
    const OpPhyWorkLabelModifyT *modify1 = (const OpPhyWorkLabelModifyT *)(const void *)op1;
    const OpPhyWorkLabelModifyT *modify2 = (const OpPhyWorkLabelModifyT *)(const void *)op2;
    return modify1->label == modify2->label && modify1->dmlType == modify2->dmlType;
}

void DestroyPhyOpJoin(DbMemCtxT *memCtx, OpBaseT *op)
{
    DB_ASSERT(op->type == IR_PHYOP_NESTLOOPJOIN || op->type == IR_PHYOP_INDEXLOOPJOIN || op->type == IR_PHYOP_HASHJOIN);
    OpPhyJoinT *join = (OpPhyJoinT *)(void *)op;
    if (join->leftKeyIds != NULL) {
        DbDynMemCtxFree(memCtx, join->leftKeyIds);
        join->leftKeyIds = NULL;
    }
    if (join->rightKeyIds != NULL) {
        DbDynMemCtxFree(memCtx, join->rightKeyIds);
        join->rightKeyIds = NULL;
    }
}

void DestroyPhyOpProject(DbMemCtxT *memCtx, OpBaseT *op)
{
    DB_ASSERT(op->type == IR_PHYOP_PROJECT);
    OpPhyProjectT *prj = (OpPhyProjectT *)(void *)op;
    if (prj->rowIndex != NULL) {
        DbDynMemCtxFree(memCtx, prj->rowIndex);
        prj->rowIndex = NULL;
    }
    if (prj->colIndex != NULL) {
        DbDynMemCtxFree(memCtx, prj->colIndex);
        prj->colIndex = NULL;
    }
}

void DestroyPhyOpExtendProject(DbMemCtxT *memCtx, OpBaseT *op)
{
    DB_ASSERT(op->type == IR_PHYOP_EXTEND_PROJECT);
    OpPhyExtendProjectT *prj = (OpPhyExtendProjectT *)(void *)op;
    if (prj->rowIndex != NULL) {
        DbDynMemCtxFree(memCtx, prj->rowIndex);
        prj->rowIndex = NULL;
    }

    if (prj->colIndex == NULL) {
        return;
    }

    for (uint32_t i = 0; i < prj->colIdxNum; i++) {
        if (prj->colIndex[i].type == IR_EXTRACT_CONST) {
            if (DM_TYPE_NEED_MALLOC(prj->colIndex[i].value.constValue.type) &&
                prj->colIndex[i].value.constValue.value.strAddr != NULL) {
                DbDynMemCtxFree(memCtx, (void *)prj->colIndex[i].value.constValue.value.strAddr);
                prj->colIndex[i].value.constValue.value.strAddr = NULL;
            }
        }
    }
    DbDynMemCtxFree(memCtx, prj->colIndex);
    prj->colIndex = NULL;
}

bool IsPhyOpScanEqual(const OpBaseT *op1, const OpBaseT *op2)
{
#ifdef FEATURE_TS
    DB_ASSERT(op1->type == IR_PHYOP_TIMEPARTITION_SCAN && op2->type == IR_PHYOP_TIMEPARTITION_SCAN);
#else
    DB_ASSERT((op1->type == IR_PHYOP_SEQSCAN && op2->type == IR_PHYOP_SEQSCAN) ||
              (op1->type == IR_PHYOP_WORKLABELSCAN && op2->type == IR_PHYOP_WORKLABELSCAN));
#endif
    const OpPhyScanT *scan1 = (const OpPhyScanT *)(const void *)op1;
    const OpPhyScanT *scan2 = (const OpPhyScanT *)(const void *)op2;
    return scan1->label == scan2->label;
}

#ifdef FEATURE_STREAM
bool IsPhyOpStreamScanEqual(const OpBaseT *op1, const OpBaseT *op2)
{
    DB_ASSERT(op1->type == IR_PHYOP_STREAMSCAN && op1->type == IR_PHYOP_STREAMSCAN);
    const OpPhyStreamScanT *scan1 = (const OpPhyStreamScanT *)(const void *)op1;
    const OpPhyStreamScanT *scan2 = (const OpPhyStreamScanT *)(const void *)op2;
    return (scan1->scan.label == scan2->scan.label);
}
#endif

bool IsPhyOpIndexScanEqual(const OpBaseT *op1, const OpBaseT *op2)
{
#ifndef FEATURE_SQL
    DB_ASSERT((op1->type == IR_PHYOP_INDEXSCAN && op2->type == IR_PHYOP_INDEXSCAN) ||
              (op1->type == IR_PHYOP_INDEXFILTERSCAN && op2->type == IR_PHYOP_INDEXFILTERSCAN));
#endif
    const OpPhyIndexScanT *scan1 = (const OpPhyIndexScanT *)(const void *)op1;
    const OpPhyIndexScanT *scan2 = (const OpPhyIndexScanT *)(const void *)op2;
    return (scan1->scan.label == scan2->scan.label) && (scan1->index == scan2->index);
}

bool IsPhyOpJoinEqual(const OpBaseT *op1, const OpBaseT *op2)
{
    DB_ASSERT((op1->type == IR_PHYOP_NESTLOOPJOIN && op2->type == IR_PHYOP_NESTLOOPJOIN) ||
              (op1->type == IR_PHYOP_INDEXLOOPJOIN && op2->type == IR_PHYOP_INDEXLOOPJOIN) ||
              (op1->type == IR_PHYOP_HASHJOIN && op2->type == IR_PHYOP_HASHJOIN));

    const OpPhyJoinT *phyJoin1 = (const OpPhyJoinT *)(const void *)op1;
    const OpPhyJoinT *phyJoin2 = (const OpPhyJoinT *)(const void *)op2;

    if (phyJoin1->joinType != phyJoin2->joinType) {
        return false;
    }
    if (phyJoin1->joinKeyNum != phyJoin2->joinKeyNum) {
        return false;
    }
    for (uint32_t i = 0; i < phyJoin1->joinKeyNum; ++i) {
        uint32_t j = 0;
        for (; j < phyJoin1->joinKeyNum; ++j) {
            if (phyJoin2->leftKeyIds[j] == phyJoin1->leftKeyIds[i]) {
                break;
            }
        }
        if (j == phyJoin1->joinKeyNum) {
            return false;
        }
    }
    for (uint32_t i = 0; i < phyJoin1->joinKeyNum; ++i) {
        uint32_t j = 0;
        for (; j < phyJoin1->joinKeyNum; ++j) {
            if (phyJoin2->rightKeyIds[j] == phyJoin1->rightKeyIds[i]) {
                break;
            }
        }
        if (j == phyJoin1->joinKeyNum) {
            return false;
        }
    }

    return true;
}

bool IsPhyOpProjectEqual(const OpBaseT *op1, const OpBaseT *op2)
{
    DB_ASSERT(op1->type == IR_PHYOP_PROJECT && op2->type == IR_PHYOP_PROJECT);

    const OpPhyProjectT *prj1 = (const OpPhyProjectT *)(const void *)op1;
    const OpPhyProjectT *prj2 = (const OpPhyProjectT *)(const void *)op2;

    if (prj1->rowIdxNum != prj2->rowIdxNum) {
        return false;
    }
    uint32_t size = prj1->rowIdxNum * (uint32_t)sizeof(uint32_t);
    if (size != 0 && memcmp(prj1->rowIndex, prj2->rowIndex, size) != 0) {
        return false;
    }

    if (prj1->colIdxNum != prj2->colIdxNum) {
        return false;
    }
    size = prj1->colIdxNum * (uint32_t)sizeof(uint32_t);
    if (size != 0 && memcmp(prj1->colIndex, prj2->colIndex, size) != 0) {
        return false;
    }

    return true;
}

bool IsPhyOpExtendProjectEqual(const OpBaseT *op1, const OpBaseT *op2)
{
    DB_ASSERT(op1->type == IR_PHYOP_EXTEND_PROJECT && op2->type == IR_PHYOP_EXTEND_PROJECT);

    const OpPhyExtendProjectT *prj1 = (const OpPhyExtendProjectT *)(const void *)op1;
    const OpPhyExtendProjectT *prj2 = (const OpPhyExtendProjectT *)(const void *)op2;

    if (prj1->rowIdxNum != prj2->rowIdxNum) {
        return false;
    }
    uint32_t size = prj1->rowIdxNum * (uint32_t)sizeof(IRExtractIndexT);
    if (size != 0 && memcmp(prj1->rowIndex, prj2->rowIndex, size) != 0) {
        return false;
    }

    if (prj1->colIdxNum != prj2->colIdxNum) {
        return false;
    }

    for (uint32_t i = 0; i < prj1->colIdxNum; i++) {
        if (prj1->colIndex[i].type != prj2->colIndex[i].type) {
            return false;
        }

        if (prj1->colIndex[i].type == IR_EXTRACT_CONST) {
            if (!DmValueIsEqual(&prj1->colIndex[i].value.constValue, &prj2->colIndex[i].value.constValue)) {
                return false;
            }
            continue;
        }

        if (prj1->colIndex[i].value.index != prj2->colIndex[i].value.index) {
            return false;
        }
    }

    return true;
}

bool IsPhyOpMergeEqual(const OpBaseT *op1, const OpBaseT *op2)
{
    DB_ASSERT(op1->type == IR_PHYOP_MERGE && op2->type == IR_PHYOP_MERGE);
    const OpPhyMergeT *merge1 = (const OpPhyMergeT *)(const void *)op1;
    const OpPhyMergeT *merge2 = (const OpPhyMergeT *)(const void *)op2;
    return merge1->label == merge2->label;
}

Status CheckPhyOpScan(const OpBaseT *op, const OpBaseT **children)
{
    (void)children;
    const DmVertexLabelT *label = ((const OpPhyScanT *)(const void *)op)->label;
    const AASchemaT *schema = &((const OpPhyScanT *)(const void *)op)->schema;

    // 1. schema中property的个数必须和DmVertexLabel一致
    // 2. schema中property和DmVertexLabel中property必须按顺序一一对应，内容相同
    Status ret = IRCheckAAVertexDescWithLabel(&schema->propAA, label);
    if (ret != GMERR_OK) {
        // property of the op does not match the label
        DB_LOG_ERROR(ret, "Illegal IR operator (%" PRIu32 ").", (uint32_t)op->type);
        return ret;
    }

    return GMERR_OK;
}

Status CheckPhyOpIndexScan(const OpBaseT *op, const OpBaseT **children)
{
#ifndef FEATURE_SQL
    DB_ASSERT(op->type == IR_PHYOP_INDEXSCAN || op->type == IR_PHYOP_INDEXFILTERSCAN);
#endif

    // 1. schema中property的个数必须和DmVertexLabel一致
    // 2. schema中property和DmVertexLabel中property必须按顺序一一对应，内容相同
    Status ret = CheckPhyOpScan(op, children);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 3. index必须为label中索引的引用
    const DmVertexLabelT *label = ((const OpPhyIndexScanT *)(const void *)op)->scan.label;
    const DmVlIndexLabelT *index = ((const OpPhyIndexScanT *)(const void *)op)->index;
#ifdef FEATURE_SQL
    // 向标混合场景 index 可能为 NULL
    if ((op->type == IR_PHYOP_VECTOR_INDEXSCAN && index == NULL) || index == label->metaVertexLabel->pkIndex) {
        return GMERR_OK;
    }
#else
    if (index == label->metaVertexLabel->pkIndex) {
        return GMERR_OK;
    }
#endif
    for (uint32_t i = 0; i < label->metaVertexLabel->secIndexNum; i++) {
        if (index == &label->metaVertexLabel->secIndexes[i]) {
            return GMERR_OK;
        }
    }
    // the index does not belong to the label
    DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "Illegal operator (%" PRIu32 ").", (uint32_t)op->type);
    return GMERR_INTERNAL_ERROR;
}

Status CheckPhyOpIndexFilterScan(const OpBaseT *op, const OpBaseT **children)
{
    DB_ASSERT(op->type == IR_PHYOP_INDEXFILTERSCAN);

    // 1. schema中property的个数必须和DmVertexLabel一致
    // 2. schema中property和DmVertexLabel中property必须按顺序一一对应，内容相同
    // 3. index必须为label中索引的引用
    Status ret = CheckPhyOpIndexScan(op, NULL);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 4. 孩子节点必须为Item算子
    if (!IRIsItemOp(children[0]->type)) {
        // child is not Item op
        DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "Illegal operator IndexFilterScan.");
        return GMERR_DATATYPE_MISMATCH;
    }

    // 待校验Index是否选择正确

    return GMERR_OK;
}

Status CheckPhyOpJoin(const OpBaseT *op, const OpBaseT **children)
{
    DB_ASSERT(op->type == IR_PHYOP_NESTLOOPJOIN || op->type == IR_PHYOP_INDEXLOOPJOIN || op->type == IR_PHYOP_HASHJOIN);
    DB_ASSERT(op->arity == IR_OPARITY_TWO);  // join算子一定有两个孩子节点

    // 1. 左右孩子不能为Item算子(因为Item算子没有AASchema, 不会向上输出中间结果集)
    if (IRIsItemOp(children[0]->type) || IRIsItemOp(children[1]->type)) {
        // Illegal IR physical operator Join
        DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH,
            "left(type:%" PRIu32 ") or right(type:%" PRIu32 ") child is non-expected.", (uint32_t)children[0]->type,
            (uint32_t)children[1]->type);
        return GMERR_DATATYPE_MISMATCH;
    }

    const OpPhyJoinT *phyJoin = (const OpPhyJoinT *)(const void *)op;
#ifndef FEATURE_SQL
    if (phyJoin->joinKeyNum == 0) {
        // 2. 笛卡尔积的joinType必须为FULL
        if (phyJoin->joinType != IR_JOIN_FULL) {
            // Illegal IR physical operator Join
            DB_LOG_AND_SET_LASERR(
                GMERR_DATATYPE_MISMATCH, "joinType(%" PRIu32 ") is not FULL", (uint32_t)phyJoin->joinType);
            return GMERR_DATATYPE_MISMATCH;
        }

        // 笛卡尔积, 不用校验joinKey
        return GMERR_OK;
    }

    // 2. 非笛卡尔积的joinType当前只支持INNER、ANTI和给datalog定制的NOT、SQL的left join
    if (phyJoin->joinType != IR_JOIN_INNER && phyJoin->joinType != IR_JOIN_ANTI && phyJoin->joinType != IR_JOIN_NOT &&
        phyJoin->joinType != IR_JOIN_LEFT) {
        DB_LOG_AND_SET_LASERR(
            GMERR_FEATURE_NOT_SUPPORTED, "unsupported joinType %" PRIu32, (uint32_t)phyJoin->joinType);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
#endif

    // 3. joinKey的个数不能超过上限
    if (phyJoin->joinKeyNum > IR_OP_JOINKEY_MAX_NUM) {
        DB_LOG_AND_SET_LASERR(
            GMERR_PROGRAM_LIMIT_EXCEEDED, "joinKey num(%" PRIu32 ") exceeds the limit.", phyJoin->joinKeyNum);
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }

#ifndef FEATURE_SQL
    const AASchemaT *selfSchema = &phyJoin->schema;
    const AASchemaT *leftSchema = IRGetOpAASchemaConst(children[0]);
    const AASchemaT *rightSchema = IRGetOpAASchemaConst(children[1]);
    AACommonPropIdsParaT para = {
        .commonPropsNum = phyJoin->joinKeyNum,
        .leftCommonPropIds = phyJoin->leftKeyIds,
        .rightCommonPropIds = phyJoin->rightKeyIds,
    };

    // 4. joinKey个数不能大于左孩子schema中property的个数
    // 5. joinKey个数不能大于右孩子schema中property的个数
    // 6. PhyJoin的左边部分property为左孩子property的引用, 并按join key下标一一对应
    // 7. PhyJoin的右边部分property为右孩子property的引用, 并按join key下标一一对应
    // 8. 左右孩子join key对应的property name必须相同
    // 9. 除了join key, 左右孩子schema中不能再有同名的property
    Status ret = IRCheckJoinedAASchema(selfSchema, leftSchema, rightSchema, &para, phyJoin->joinType == IR_JOIN_FULL);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "check joined AASchema.");
        return ret;
    }
#endif

    return GMERR_OK;
}

Status CheckPhyOpProject(const OpBaseT *op, const OpBaseT **children)
{
    DB_ASSERT(op->type == IR_PHYOP_PROJECT);

    // 1. 孩子不能为Item算子(因为Item算子没有AASchema, 不会向上输出中间结果集)
    if (IRIsItemOp(children[0]->type)) {
        DB_LOG_AND_SET_LASERR(
            GMERR_DATATYPE_MISMATCH, "child(type:%" PRIu32 ") is non-expected.", (uint32_t)children[0]->type);
        return GMERR_DATATYPE_MISMATCH;
    }

    // 2. schema中属性的个数必须等于投影列的个数
    // 3. 投影列下标不能超过孩子schema属性数组范围
    // 4. schema中property必须和孩子schema中property的数据类型一致，并按投影列一一对应
    const OpPhyProjectT *prj = (const OpPhyProjectT *)(const void *)op;
    const AASchemaT *childSchema = IRGetOpAASchemaConst(children[0]);
    Status ret = IRCheckExtractedAASchema(&prj->schema, childSchema, prj->colIndex, prj->colIdxNum);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "check extracted AASchema.");
        return ret;
    }

    // 5. 当前不支持行键投影
    if (prj->rowIdxNum != 0 || prj->rowIndex != NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "rowIdex is not NULL.");
        return GMERR_INTERNAL_ERROR;
    }

    return GMERR_OK;
}

Status CheckPhyOpExtendProject(const OpBaseT *op, const OpBaseT **children)
{
    DB_ASSERT(op->type == IR_PHYOP_EXTEND_PROJECT);

    // 1. 孩子不能为Item算子(因为Item算子没有AASchema, 不会向上输出中间结果集)
    if (IRIsItemOp(children[0]->type)) {
        DB_LOG_AND_SET_LASERR(
            GMERR_DATATYPE_MISMATCH, "child(type:%" PRIu32 ") is non-expected.", (uint32_t)children[0]->type);
        return GMERR_DATATYPE_MISMATCH;
    }

    const OpPhyExtendProjectT *prj = (const OpPhyExtendProjectT *)(const void *)op;
    const AASchemaT *childSchema = IRGetOpAASchemaConst(children[0]);

    // 2. 投影列不能为NULL
    // 3. schema中属性的个数必须等于投影列的个数
    // 4. 非常量投影列个数不能超过孩子schema属性的个数
    // 5. 非常量投影列下标不能超过孩子schema属性数组范围
    // 6. schema中property必须和孩子schema中property一致，并按投影列一一对应
    Status ret = IRCheckExtendExtractedAASchema(&prj->schema, childSchema, prj->colIndex, prj->colIdxNum);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "check extend project AASchema.");
        return ret;
    }

    // 7. 当前不支持行键投影
    if (prj->rowIdxNum != 0 || prj->rowIndex != NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "rowIdx is not NULL.");
        return GMERR_INTERNAL_ERROR;
    }

    return GMERR_OK;
}

Status CheckPhyOpFilter(const OpBaseT *op, const OpBaseT **children)
{
    DB_ASSERT(op->type == IR_PHYOP_FILTER);

    // 1. 左孩子不能为Item算子(因为Item算子没有AASchema, 不会向上输出中间结果集)
    if (IRIsItemOp(children[0]->type)) {
        DB_LOG_AND_SET_LASERR(
            GMERR_DATATYPE_MISMATCH, "left child(type:%" PRIu32 ") is non-expected.", (uint32_t)children[1]->type);
        return GMERR_DATATYPE_MISMATCH;
    }

    // 2. 右孩子必须为Item算子
    if (!IRIsItemOp(children[1]->type)) {
        DB_LOG_AND_SET_LASERR(
            GMERR_DATATYPE_MISMATCH, "right child(type:%" PRIu32 ") is not item op.", (uint32_t)children[1]->type);
        return GMERR_DATATYPE_MISMATCH;
    }

    // 3. Filter的schema必须和左孩子节点的schema相同
    const OpPhyFilterT *filter = (const OpPhyFilterT *)(const void *)op;
    const AASchemaT *leftChildSchema = IRGetOpAASchemaConst(children[0]);
    if (!IRIsAASchemaEqual(&filter->schema, leftChildSchema)) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "schema of filter != schema of left child.");
        return GMERR_INTERNAL_ERROR;
    }

    return GMERR_OK;
}

Status CheckPhyOpDML(const OpBaseT *op, const OpBaseT **children)
{
    DB_ASSERT(op->type == IR_PHYOP_MERGE || op->type == IR_PHYOP_MODIFYWORKLABEL);

    return GMERR_OK;
}

Status GenAASchema4PhyScan(DbMemCtxT *memCtx, const AASchemaT **children, OpBaseT *op)
{
#ifdef FEATURE_TS
    DB_ASSERT(op->type == IR_PHYOP_SEQSCAN || op->type == IR_PHYOP_WORKLABELSCAN || op->type == IR_PHYOP_INDEXSCAN ||
              op->type == IR_PHYOP_INDEXFILTERSCAN || op->type == IR_PHYOP_TIMEPARTITION_SCAN);
#elif !defined(FEATURE_SQL)
    DB_ASSERT(op->type == IR_PHYOP_SEQSCAN || op->type == IR_PHYOP_WORKLABELSCAN || op->type == IR_PHYOP_INDEXSCAN ||
              op->type == IR_PHYOP_INDEXFILTERSCAN);
#endif

    OpPhyScanT *scan = (OpPhyScanT *)(void *)op;
    // 在线反序列化时，为了减少内存开销，执行计划将复用物理计划的schema，即直接引用物理计划的schema
    // 入参的memctx在生成执行计划完成后会统一释放，若使用其拷贝label的schema，执行计划将引用野指针，故这里不copy
    Status ret = IRInitAASchemaWithLabelNoCopy(memCtx, scan->label, &scan->schema);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "generate aaschema for physical scan(type:%" PRIu32 ")", (uint32_t)op->type);
        return ret;
    }

    return GMERR_OK;
}

#ifdef FEATURE_STREAM
Status GenAASchema4StreamPhyScan(DbMemCtxT *memCtx, const AASchemaT **children, OpBaseT *op)
{
    DB_ASSERT(op->type == IR_PHYOP_STREAMSCAN);

    OpPhyStreamScanT *scan = (OpPhyStreamScanT *)(void *)op;
    // 在线反序列化时，为了减少内存开销，执行计划将复用物理计划的schema，即直接引用物理计划的schema
    // 入参的memctx在生成执行计划完成后会统一释放，若使用其拷贝label的schema，执行计划将引用野指针，故这里不copy
    Status ret = IRInitAASchemaWithLabelNoCopy(memCtx, scan->scan.label, &scan->scan.schema);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "generate aaschema for physical scan (type:%" PRIu32 ")", (uint32_t)op->type);
        return ret;
    }

    return GMERR_OK;
}
#endif

Status GenAASchema4PhyJoin(DbMemCtxT *memCtx, const AASchemaT **children, OpBaseT *op)
{
    DB_ASSERT(op->type == IR_PHYOP_NESTLOOPJOIN || op->type == IR_PHYOP_INDEXLOOPJOIN || op->type == IR_PHYOP_HASHJOIN);
    DB_ASSERT(children != NULL);  // join算子一定有孩子节点

    Status ret;
    OpPhyJoinT *join = (OpPhyJoinT *)(void *)op;
    if (join->joinKeyNum == 0) {
        // 笛卡尔积
        ret = IRUnionAASchema(memCtx, children[0], children[1], &join->schema);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "generate aaschema for physical join.");
            return ret;
        }

        return GMERR_OK;
    }

    AACommonPropIdsParaT para = {
        .commonPropsNum = join->joinKeyNum,
        .leftCommonPropIds = join->leftKeyIds,
        .rightCommonPropIds = join->rightKeyIds,
    };

    // 校验joinKey相关信息是否合法
    // datalog规则定义里, prop名称和table定义中的prop名称不一致, 以规则中定义的prop名称判断是否为commonProp
    // plan反序列化过程中, 以table里的prop生成AASchema, 所以不能校验common prop的名称
    ret = IRCheckCommonProps(children[0], children[1], &para, join->joinType == IR_JOIN_FULL, false);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "join key is illegal.");
        return ret;
    }

    ret = IRJoinAASchemaByCommonPropIdx(memCtx, children[0], children[1], &para, &join->schema);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "generate aaschema for physical join.");
        return ret;
    }

    return GMERR_OK;
}

Status GenAASchema4PhyProject(DbMemCtxT *memCtx, const AASchemaT **children, OpBaseT *op)
{
    DB_ASSERT(op->type == IR_PHYOP_PROJECT);
    DB_ASSERT(children != NULL);  // 投影算子一定有孩子节点

    OpPhyProjectT *prj = (OpPhyProjectT *)(void *)op;
    Status ret = IRCheckExtractIndex(children[0], prj->colIndex, prj->colIdxNum);
    if (ret != GMERR_OK) {
        // Unable to generate aaschema for physical project op
        DB_LOG_ERROR(ret, "check extract index.");
        return ret;
    }

    ret = IRExtractAASchema(memCtx, children[0], prj->colIndex, prj->colIdxNum, &prj->schema);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "generate aaschema for physical project.");
        return ret;
    }

    return GMERR_OK;
}

Status GenAASchema4PhyExtendProject(DbMemCtxT *memCtx, const AASchemaT **children, OpBaseT *op)
{
    // 扩展投影的schema被序列化进文件中，并在反序列化时生成，反序列化出来schema需要用planCache.memctx拷贝一份，以免被释放
    return GMERR_OK;
}

Status GenAASchema4PhyFilter(DbMemCtxT *memCtx, const AASchemaT **children, OpBaseT *op)
{
    DB_ASSERT(op->type == IR_PHYOP_FILTER);
    DB_ASSERT(children != NULL);  // 过滤算子一定有孩子节点

    OpPhyFilterT *filter = (OpPhyFilterT *)(void *)op;
    Status ret = IRCopyAASchema(memCtx, children[0], &filter->schema);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "generate aaschema for physical filter.");
        return ret;
    }

    return GMERR_OK;
}

Status GenAASchema4PhyMerge(DbMemCtxT *memCtx, const AASchemaT **children, OpBaseT *op)
{
    DB_ASSERT(op->type == IR_PHYOP_MERGE);
    DB_ASSERT(children == NULL);  // merge算子一定没有孩子节点

    OpPhyMergeT *merge = (OpPhyMergeT *)(void *)op;
    Status ret = IRInitAASchemaWithLabel(memCtx, merge->label, &merge->schema);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "generate aaschema for physical merge.");
        return ret;
    }

    return GMERR_OK;
}

Status GenAASchema4PhyWorkLabelModify(DbMemCtxT *memCtx, const AASchemaT **children, OpBaseT *op)
{
    DB_ASSERT(op->type == IR_PHYOP_MODIFYWORKLABEL);
    DB_ASSERT(children != NULL);  // modify算子一定有孩子节点

    return GMERR_OK;
}

#if defined(FEATURE_TS) || defined(FEATURE_SQL)
Status CopyPhyOpOrderBy(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
    DB_ASSERT(srcOp->type == IR_PHYOP_ORDER_BY && srcOp->type == dstOp->type);

    const OpPhyOrderByT *src = (const OpPhyOrderByT *)(const void *)srcOp;
    OpPhyOrderByT *dst = (OpPhyOrderByT *)(void *)dstOp;

    dst->orderByKeyNum = src->orderByKeyNum;
    dst->orderByKeyIds = NULL;
    dst->directions = NULL;
    dst->nullPoses = NULL;

    uint32_t size = (uint32_t)(src->orderByKeyNum * sizeof(uint32_t));
    Status ret = DmCopyBufWithMemCtx(
        memCtx, (uint8_t *)(void *)src->orderByKeyIds, size, (uint8_t **)(void **)&(dst->orderByKeyIds));
    if (ret != GMERR_OK) {
        // Unable to copy physical op orderBy
        DB_LOG_ERROR(ret, "copy orderBy key id.");
        return ret;
    }

    size = (uint32_t)(src->orderByKeyNum * sizeof(IROrderByDirectionE));
    ret =
        DmCopyBufWithMemCtx(memCtx, (uint8_t *)(void *)src->directions, size, (uint8_t **)(void **)&(dst->directions));
    if (ret != GMERR_OK) {
        // Unable to copy physical op orderBy
        DB_LOG_ERROR(ret, "copy directions.");
        return ret;
    }

    size = (uint32_t)(src->orderByKeyNum * sizeof(IROrderByNULLPosE));
    ret = DmCopyBufWithMemCtx(memCtx, (uint8_t *)(void *)src->nullPoses, size, (uint8_t **)(void **)&dst->nullPoses);
    if (ret != GMERR_OK) {
        // Unable to copy physical op orderBy
        DB_LOG_ERROR(ret, "copy null positions.");
    }
    return ret;
}

void DestroyPhyOpOrderBy(DbMemCtxT *memCtx, OpBaseT *op)
{
    DB_ASSERT(op->type == IR_PHYOP_ORDER_BY);
    OpPhyOrderByT *orderBy = (OpPhyOrderByT *)(void *)op;
    if (orderBy->orderByKeyIds != NULL) {
        DbDynMemCtxFree(memCtx, orderBy->orderByKeyIds);
        orderBy->orderByKeyIds = NULL;
    }
    if (orderBy->directions != NULL) {
        DbDynMemCtxFree(memCtx, orderBy->directions);
        orderBy->directions = NULL;
    }
    if (orderBy->nullPoses != NULL) {
        DbDynMemCtxFree(memCtx, orderBy->nullPoses);
        orderBy->nullPoses = NULL;
    }
}

bool IsPhyOpOrderByEqual(const OpBaseT *op1, const OpBaseT *op2)
{
    DB_ASSERT(op1->type == IR_PHYOP_ORDER_BY && op2->type == IR_PHYOP_ORDER_BY);
    return IsOpOrderByEqual(op1, op2);
}

Status CheckPhyOpOrderBy(const OpBaseT *op, const OpBaseT **children)
{
    DB_ASSERT(op->type == IR_PHYOP_ORDER_BY);

    // 1. 子节点必须不为 NULL
    if (children == NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "operator (%" PRIu32 "), children is NULL.", (uint32_t)op->type);
        return GMERR_INTERNAL_ERROR;
    }

    const OpPhyOrderByT *orderBy = (const OpPhyOrderByT *)(const void *)op;
    const AASchemaT *childSchema = IRGetOpAASchemaConst(children[0]);

    // 2. orderBy 的 schema 必须和左孩子节点的 schema 相同
    if (!IRIsAASchemaEqual(&orderBy->schema, childSchema)) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "orderBy schema != left child schema.");
        return GMERR_INTERNAL_ERROR;
    }

    // 3. order by 的 id 范围不超过 children schema 的范围
    for (uint32_t i = 0; i < orderBy->orderByKeyNum; i++) {
        if (i >= childSchema->propAA.propNum) {
            DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "keyId exceeds left child propNum.");
            return GMERR_INTERNAL_ERROR;
        }
    }
    return GMERR_OK;
}

Status GenAASchema4PhyOrderBy(DbMemCtxT *memCtx, const AASchemaT **children, OpBaseT *op)
{
    DB_ASSERT(op->type == IR_PHYOP_ORDER_BY);
    DB_ASSERT(children != NULL);

    OpPhyOrderByT *orderBy = (OpPhyOrderByT *)(void *)op;
    Status ret = IRCopyAASchema(memCtx, children[0], &orderBy->schema);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "generate AASchema for physical orderBy.");
    }
    return ret;
}

Status CopyPhyOpDistinct(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
    DB_ASSERT(srcOp->type == IR_PHYOP_DISTINCT);

    const OpPhyDistinctT *src = (const OpPhyDistinctT *)(const void *)srcOp;
    OpPhyDistinctT *dst = (OpPhyDistinctT *)(void *)dstOp;

    dst->distinctType = src->distinctType;
    return GMERR_OK;
}

bool IsPhyOpDistinctEqual(const OpBaseT *op1, const OpBaseT *op2)
{
    DB_ASSERT(op1->type == IR_PHYOP_DISTINCT && op2->type == IR_PHYOP_DISTINCT);
    const OpPhyDistinctT *distinct1 = (const OpPhyDistinctT *)(const void *)op1;
    const OpPhyDistinctT *distinct2 = (const OpPhyDistinctT *)(const void *)op2;
    return distinct1->distinctType == distinct2->distinctType;
}

Status CheckPhyOpDistinct(const OpBaseT *op, const OpBaseT **children)
{
    DB_POINTER2(op, children);
    DB_ASSERT(op->type == IR_PHYOP_DISTINCT);

    // distinct 的 schema 必须和子节点的 schema 相同
    const OpPhyDistinctT *distinct = (const OpPhyDistinctT *)(const void *)op;
    const AASchemaT *childSchema = IRGetOpAASchemaConst((const OpBaseT *)children[0]);
    if (!IRIsAASchemaEqual(&distinct->schema, childSchema)) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "schema of distinct != schema of child.");
        return GMERR_DATA_EXCEPTION;
    }

    return GMERR_OK;
}

Status GenAASchema4PhyDistinct(DbMemCtxT *memCtx, const AASchemaT **children, OpBaseT *op)
{
    DB_ASSERT(op->type == IR_PHYOP_DISTINCT);
    DB_POINTER(children);

    OpPhyDistinctT *distinct = (OpPhyDistinctT *)(void *)op;
    Status ret = IRCopyAASchema(memCtx, children[0], &distinct->schema);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "generate AASchema for physical distinct.");
    }
    return ret;
}

Status CopyPhyOpLimit(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
    DB_ASSERT(srcOp->type == IR_PHYOP_LIMIT && srcOp->type == dstOp->type);

    const OpPhyLimitT *src = (const OpPhyLimitT *)(const void *)srcOp;
    OpPhyLimitT *dst = (OpPhyLimitT *)(void *)dstOp;

    dst->limitNum = src->limitNum;
    dst->offset = src->offset;
    return GMERR_OK;
}

bool IsPhyOpLimitEqual(const OpBaseT *op1, const OpBaseT *op2)
{
    DB_ASSERT(op1->type == IR_PHYOP_LIMIT && op2->type == IR_PHYOP_LIMIT);
    const OpPhyLimitT *limit1 = (const OpPhyLimitT *)(const void *)op1;
    const OpPhyLimitT *limit2 = (const OpPhyLimitT *)(const void *)op2;
    return (limit1->offset == limit2->offset) && (limit1->limitNum == limit2->limitNum);
}

Status CheckPhyOpLimit(const OpBaseT *op, const OpBaseT **children)
{
    DB_ASSERT(op->type == IR_PHYOP_LIMIT);

    // 1. 子节点必须不为 NULL
    if (children == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "operator (%" PRIu32 "), children is NULL.", (uint32_t)op->type);
        return GMERR_INTERNAL_ERROR;
    }

    // 2. limit 的 schema 必须和左孩子节点的 schema 相同
    const OpPhyLimitT *limit = (const OpPhyLimitT *)(const void *)op;
    const AASchemaT *childSchema = IRGetOpAASchemaConst(children[0]);
    if (!IRIsAASchemaEqual(&limit->schema, childSchema)) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "schema of limit != schema of left child.");
        return GMERR_INTERNAL_ERROR;
    }

    return GMERR_OK;
}

Status GenAASchema4PhyLimit(DbMemCtxT *memCtx, const AASchemaT **children, OpBaseT *op)
{
    DB_ASSERT(op->type == IR_PHYOP_LIMIT);
    DB_ASSERT(children != NULL);

    OpPhyLimitT *limit = (OpPhyLimitT *)(void *)op;
    Status ret = IRCopyAASchema(memCtx, children[0], &limit->schema);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "generate AASchema for physical limit.");
    }
    return ret;
}
#endif

#ifdef FEATURE_SQL
Status CopyPhyOpVecIdxScan(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
    const OpPhyVectorIndexScanT *src = (const OpPhyVectorIndexScanT *)(const void *)srcOp;
    OpPhyVectorIndexScanT *dst = (OpPhyVectorIndexScanT *)(void *)dstOp;

    Status ret = CopyPhyOpIndexScan(memCtx, (const OpBaseT *)(const void *)&src->scan, (OpBaseT *)(void *)&dst->scan);
    if (ret != GMERR_OK) {
        IR_ERROR_LOG_PRINT(ret, "Unable to copy IR Op %s, copying base scan op incorrectly.", GetOpName(srcOp->type));
        return ret;
    }
    dst->limit = src->limit;
    dst->offset = src->offset;
    dst->orderByExpr = src->orderByExpr;
    dst->distanceUpperBound = src->distanceUpperBound;
    dst->scalarScan = src->scalarScan;
    dst->pqVecScan = src->pqVecScan;
    dst->isCombine = src->isCombine;
    dst->preFilterMax = src->preFilterMax;
    if (src->scalarScan != NULL) {
        ret = IRCopyExprTree(memCtx, src->scalarScan, &dst->scalarScan);
        if (ret != GMERR_OK) {
            IR_ERROR_LOG_PRINT(ret, "Unable to copy IR Op %s, copying scalarScan incorrectly.", GetOpName(srcOp->type));
            return ret;
        }
    }

    if (src->pqVecScan != NULL) {
        ret = IRCopyExprTree(memCtx, src->pqVecScan, &dst->pqVecScan);
        if (ret != GMERR_OK) {
            IR_ERROR_LOG_PRINT(ret, "Unable to copy IR Op %s, copying pqVecScan incorrectly.", GetOpName(srcOp->type));
            return ret;
        }
    }

    return GMERR_OK;
}

void DestroyPhyOpVecIdxScan(DbMemCtxT *memCtx, OpBaseT *op)
{
    OpPhyVectorIndexScanT *vecIdxScan = (OpPhyVectorIndexScanT *)(void *)op;
    if (vecIdxScan->orderByExpr != NULL) {
        ExprDestroy(memCtx, vecIdxScan->orderByExpr);
    }
}

bool IsPhyOpVecIdxScanEqual(const OpBaseT *op1, const OpBaseT *op2)
{
    const OpPhyVectorIndexScanT *vecIdxScan1 = (const OpPhyVectorIndexScanT *)(const void *)op1;
    const OpPhyVectorIndexScanT *vecIdxScan2 = (const OpPhyVectorIndexScanT *)(const void *)op2;

    // 先比较基类
    if (!IsPhyOpIndexScanEqual(
            (const OpBaseT *)(const void *)&vecIdxScan1->scan, (const OpBaseT *)(const void *)&vecIdxScan2->scan)) {
        return false;
    }
    if (vecIdxScan1->limit != vecIdxScan2->limit) {
        return false;
    }
    if (vecIdxScan1->offset != vecIdxScan2->offset) {
        return false;
    }
    if (vecIdxScan1->isCombine != vecIdxScan2->isCombine) {
        return false;
    }
    if (vecIdxScan1->preFilterMax != vecIdxScan2->preFilterMax) {
        return false;
    }
    return ExprEqual(vecIdxScan1->orderByExpr, vecIdxScan2->orderByExpr);
}

static Status CheckPhyOpVecIdxScan(const OpBaseT *op, const OpBaseT **children)
{
    Status ret = CheckPhyOpIndexScan(op, children);
    if (ret != GMERR_OK) {
        return ret;
    }
    const OpPhyVectorIndexScanT *vecIdxScan = (const OpPhyVectorIndexScanT *)(const void *)op;
    // orderByExpr 不能为空
    if (vecIdxScan->orderByExpr == NULL) {
        IR_LAST_ERROR_LOG_PRINT(
            GMERR_UNEXPECTED_NULL_VALUE, "Illegal IR Op OpPhyVectorIndexScanT, orderByExpr is NULL.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    return GMERR_OK;
}

#ifndef IDS_HAOTIAN
Status CopyPhyOpUnion(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
    const OpPhyUnionT *src = (const OpPhyUnionT *)(const void *)srcOp;
    OpPhyUnionT *dst = (OpPhyUnionT *)(void *)dstOp;
    dst->isUnionAll = src->isUnionAll;
    dst->isDistinctByProps = src->isDistinctByProps;
    return GMERR_OK;
}

bool IsPhyOpCompoundEqual(const OpBaseT *op1, const OpBaseT *op2)
{
    const AAVertexDescT *propAA1 = &IRGetOpAASchemaConst(op1)->propAA;
    const AAVertexDescT *propAA2 = &IRGetOpAASchemaConst(op2)->propAA;
    // 当前的比较方式会导致 Multi-Index-OR 中的每个 Compound类 算子都被视作相同.
    // 目前来看没有副作用, 因为它们确实可以视为同一个.
    return IRIsAAVertexDescEqual(propAA1, propAA2);
}

bool IsPhyOpUnionEqual(const OpBaseT *op1, const OpBaseT *op2)
{
    const OpPhyUnionT *union1 = (const OpPhyUnionT *)(const void *)op1;
    const OpPhyUnionT *union2 = (const OpPhyUnionT *)(const void *)op2;
    if (union1->isUnionAll != union2->isUnionAll) {
        return false;
    }
    if (union1->isDistinctByProps != union2->isDistinctByProps) {
        return false;
    }
    // Union 算子要比较其 propAA 中的属性是否相同

    return IsPhyOpCompoundEqual(op1, op2);
}

Status CheckPhyOpCompound(const OpBaseT *op, const OpBaseT **children)
{
    if (children == NULL) {  // 孩子节点不为空
        IR_ERROR_LOG_PRINT(
            GMERR_INTERNAL_ERROR, "Illegal IR physical operator (%" PRIu32 "), wrong children.", (uint32_t)op->type);
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

Status GenAASchema4PhyCompound(DbMemCtxT *memCtx, const AASchemaT **children, OpBaseT *op)
{
    OpPhyCompoundT *compoundOp = (OpPhyCompoundT *)(void *)op;
    Status ret = IRCopyAASchema(memCtx, children[0], &compoundOp->schema);
    if (ret != GMERR_OK) {
        IR_ERROR_LOG_PRINT(ret, "Unable to generate AASchema for physical operator %s.", GetOpName(op->type));
        return ret;
    }
    return GMERR_OK;
}

Status CopyPhyOpSubQueryScan(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
    const OpPhySubQueryScanT *src = (const OpPhySubQueryScanT *)(const void *)srcOp;
    OpPhySubQueryScanT *dst = (OpPhySubQueryScanT *)(void *)dstOp;
    dst->subQueryId = src->subQueryId;
    return GMERR_OK;
}

bool IsPhyOpSubQueryScanEqual(const OpBaseT *op1, const OpBaseT *op2)
{
    DB_ASSERT(op1->type == IR_PHYOP_SUB_QUERY_SCAN && op2->type == IR_PHYOP_SUB_QUERY_SCAN);
    const OpPhySubQueryScanT *subQuery1 = (const OpPhySubQueryScanT *)(const void *)op1;
    const OpPhySubQueryScanT *subQuery2 = (const OpPhySubQueryScanT *)(const void *)op2;
    return subQuery1->subQueryId == subQuery2->subQueryId;
}

Status CheckPhyOpSubQueryScan(const OpBaseT *op, const OpBaseT **children)
{
    if (children != NULL) {  // 孩子节点必为空
        IR_ERROR_LOG_PRINT(
            GMERR_DATA_EXCEPTION, "Illegal IR physical operator %s, children isn't NULL.", GetOpName(op->type));
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}
#endif

#endif

#if defined(FEATURE_TS) || defined(FEATURE_SQL)
Status CopyPhyOpHashAgg(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
    DB_ASSERT(srcOp->type == IR_PHYOP_HASH_AGG && srcOp->type == dstOp->type);

    Status ret = CopyOpGroupBy(memCtx, srcOp, dstOp);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "copy physical hashAgg operator.");
    }
    return ret;
}

void DestroyPhyOpHashAgg(DbMemCtxT *memCtx, OpBaseT *op)
{
    DB_ASSERT(op->type == IR_PHYOP_HASH_AGG);
    DestroyOpGroupBy(memCtx, op);
}

bool IsPhyOpHashAggEqual(const OpBaseT *op1, const OpBaseT *op2)
{
    DB_ASSERT(op1->type == IR_PHYOP_HASH_AGG && op2->type == IR_PHYOP_HASH_AGG);
    return IsOpGroupByEqual(op1, op2);
}

Status CheckPhyOpHashAgg(const OpBaseT *op, const OpBaseT **children)
{
    DB_ASSERT(op->type == IR_PHYOP_HASH_AGG);
    Status ret = CheckOpGroupBy(op, children);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Illegal IR physical operator %" PRIu32, (uint32_t)IR_PHYOP_HASH_AGG);
    }
    return ret;
}

Status GenAASchema4PhyHashAgg(DbMemCtxT *memCtx, const AASchemaT **children, OpBaseT *op)
{
    DB_ASSERT(op->type == IR_PHYOP_HASH_AGG);
    Status ret = GenAASchema4GroupBy(memCtx, children, op);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "generate AASchema for physical hashAgg.");
    }
    return ret;
}
#endif
#ifdef FEATURE_STREAM
Status CopyPhyOpStreamUpsertIntoRef(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
    DB_ASSERT(srcOp->type == IR_PHYOP_STREAM_UPSERT_INTO_REF || srcOp->type == dstOp->type);
    const OpPhyStreamUpsertIntoRefT *src = (const OpPhyStreamUpsertIntoRefT *)(const void *)srcOp;
    OpPhyStreamUpsertIntoRefT *dst = (OpPhyStreamUpsertIntoRefT *)(void *)dstOp;
    dst->label = src->label;
    return DbCopyListWithMemCtx(memCtx, src->insertDataList, &dst->insertDataList);
}

void DestroyPhyOpStreamUpsertIntoRef(DbMemCtxT *memCtx, OpBaseT *op)
{
    DB_UNUSED(memCtx);
    DB_ASSERT(op->type == IR_PHYOP_STREAM_UPSERT_INTO_REF);
    OpPhyStreamUpsertIntoRefT *insert = (OpPhyStreamUpsertIntoRefT *)(void *)op;
    if (insert->insertDataList != NULL) {
        // List 中存放是待插入数据 <SqlExprList>, 直接释放 List 即可
        DbDestroyList(insert->insertDataList);
        insert->insertDataList = NULL;
    }
}

bool IsPhyOpStreamUpsertIntoRefEqual(const OpBaseT *op1, const OpBaseT *op2)
{
    DB_ASSERT(op1->type == IR_PHYOP_STREAM_UPSERT_INTO_REF && op2->type == IR_PHYOP_STREAM_UPSERT_INTO_REF);
    const OpPhyStreamUpsertIntoRefT *insert1 = (const OpPhyStreamUpsertIntoRefT *)(const void *)op1;
    const OpPhyStreamUpsertIntoRefT *insert2 = (const OpPhyStreamUpsertIntoRefT *)(const void *)op2;
    if (insert1->label != insert2->label) {
        return false;
    }
    return DbIsListEqual(insert1->insertDataList, insert2->insertDataList, NULL);
}

Status CheckPhyOpStreamUpsertIntoRef(const OpBaseT *op, const OpBaseT **children)
{
    DB_ASSERT(op->type == IR_PHYOP_STREAM_UPSERT_INTO_REF);
    if (children != NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "AABulkInsert, child should be null.");
        return GMERR_INTERNAL_ERROR;
    }
    const OpPhyStreamUpsertIntoRefT *insertOp = (const OpPhyStreamUpsertIntoRefT *)(const void *)op;
    if (insertOp->label == NULL) {
        // Illegal IR physical operator upsert into ref
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "logical label should be no-null.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    // 应当至少插入一批次的数据
    if (insertOp->insertDataList == NULL || DbListGetItemCnt(insertOp->insertDataList) == 0) {
        // Illegal IR physical operator upsert into ref
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "insert data should be no-null.");
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

Status CopyPhyOpAAWindowTable(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
    DB_ASSERT(srcOp->type == IR_PHYOP_STREAM_WINDOW_TABLE);
    const OpPhyStreamWindowTableT *src = (const OpPhyStreamWindowTableT *)(const void *)srcOp;
    OpPhyStreamWindowTableT *dst = (OpPhyStreamWindowTableT *)(void *)dstOp;
    dst->timeColId = src->timeColId;
    dst->slide = src->slide;
    dst->size = src->size;
    dst->offset = src->offset;
    return GMERR_OK;
}

bool IsPhyOpAAWindowTableEqual(const OpBaseT *op1, const OpBaseT *op2)
{
    DB_ASSERT(op1->type == IR_PHYOP_STREAM_WINDOW_TABLE && op2->type == IR_PHYOP_STREAM_WINDOW_TABLE);
    const OpPhyStreamWindowTableT *window1 = (const OpPhyStreamWindowTableT *)(const void *)op1;
    const OpPhyStreamWindowTableT *window2 = (const OpPhyStreamWindowTableT *)(const void *)op2;
    if (window1->timeColId != window2->timeColId || window1->slide != window2->slide ||
        window1->size != window2->size || window1->offset != window2->offset) {
        return false;
    }
    return true;
}

Status CheckPhyOpAAWindowTable(const OpBaseT *op, const OpBaseT **children)
{
    DB_ASSERT(op->type == IR_PHYOP_STREAM_WINDOW_TABLE);
    return GMERR_OK;
}
Status GenAASchema4PhyWindowTable(DbMemCtxT *memCtx, const AASchemaT **children, OpBaseT *op)
{
    DB_ASSERT(op->type == IR_PHYOP_STREAM_WINDOW_TABLE);

    OpPhyStreamWindowTableT *window = (OpPhyStreamWindowTableT *)(void *)op;
    uint32_t childPropNum = children[0]->propAA.propNum;
    // window产生两个新的属性
    uint32_t propNum = childPropNum + STREAM_WINDOW_PROP_NUM;
    Status ret = IRInitAASchema(memCtx, propNum, &window->schema);
    if (ret != GMERR_OK) {
        // Unable to generate schema for window
        DB_LOG_ERROR(ret, "init AASchema.");
        return ret;
    }
    // 拷贝子节点 prop
    for (uint32_t i = 0; i < childPropNum; i++) {
        window->schema.propAA.properties[i] = (*children)->propAA.properties[i];
    }
    // 生成窗口属性
    return DmGenWindowProperty(memCtx, window->schema.propAA.properties + childPropNum, childPropNum);
}

Status CopyPhyOpAAWatermarkAssigner(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
    DB_ASSERT(srcOp->type == IR_PHYOP_STREAM_WATERMARK_ASSIGNER);
    const OpPhyStreamWatermarkAssignerT *src = (const OpPhyStreamWatermarkAssignerT *)(const void *)srcOp;
    OpPhyStreamWatermarkAssignerT *dst = (OpPhyStreamWatermarkAssignerT *)(void *)dstOp;
    dst->eventTimeColId = src->eventTimeColId;
    dst->delayTime = src->delayTime;
    dst->isTolerant = src->isTolerant;
    return GMERR_OK;
}

bool IsPhyOpAAWatermarkAssignerEqual(const OpBaseT *op1, const OpBaseT *op2)
{
    DB_ASSERT(op1->type == IR_PHYOP_STREAM_WATERMARK_ASSIGNER && op2->type == IR_PHYOP_STREAM_WATERMARK_ASSIGNER);
    const OpPhyStreamWatermarkAssignerT *watermark1 = (const OpPhyStreamWatermarkAssignerT *)(const void *)op1;
    const OpPhyStreamWatermarkAssignerT *watermark2 = (const OpPhyStreamWatermarkAssignerT *)(const void *)op2;
    if (watermark1->delayTime != watermark2->delayTime || watermark1->eventTimeColId != watermark2->eventTimeColId ||
        watermark1->isTolerant != watermark2->isTolerant) {
        return false;
    }
    return true;
}

Status CheckPhyOpAAWatermarkAssigner(const OpBaseT *op, const OpBaseT **children)
{
    DB_ASSERT(op->type == IR_PHYOP_STREAM_WATERMARK_ASSIGNER);
    return GMERR_OK;
}

Status CopyPhyOpAAStreamOverAgg(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
    DB_ASSERT(srcOp->type == IR_PHYOP_STREAM_OVER_AGG);
    DB_ASSERT(dstOp->type == IR_PHYOP_STREAM_OVER_AGG);
    const OpPhyStreamOverAggT *src = (const OpPhyStreamOverAggT *)(const void *)srcOp;
    OpPhyStreamOverAggT *dst = (OpPhyStreamOverAggT *)(void *)dstOp;

    dst->aggKeyNum = src->aggKeyNum;
    dst->aggKeyIds = NULL;
    dst->funcNum = src->funcNum;
    dst->expr = src->expr;
    dst->isOnlyInstant = src->isOnlyInstant;

    Status ret = DmCopyBufWithMemCtx(memCtx, (const uint8_t *)src->aggKeyIds,
        (uint32_t)(src->aggKeyNum * sizeof(uint32_t)), (uint8_t **)&dst->aggKeyIds);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "copy aggKeyId array.");
        return ret;
    }
    dst->funcProps = src->funcProps;
    return ret;
}
void DestroyPhyOpAAStreamOverAgg(DbMemCtxT *memCtx, OpBaseT *op)
{
    DB_ASSERT(op->type == IR_PHYOP_STREAM_OVER_AGG);
    // 都转换为 logical operator 进行操作
    OpPhyStreamOverAggT *streamOverAgg = (OpPhyStreamOverAggT *)(void *)op;
    if (streamOverAgg->aggKeyIds != NULL) {
        DbDynMemCtxFree(memCtx, streamOverAgg->aggKeyIds);
        streamOverAgg->aggKeyIds = NULL;
    }
    if (streamOverAgg->funcProps != NULL) {
        DbDynMemCtxFree(memCtx, streamOverAgg->funcProps);
        streamOverAgg->funcProps = NULL;
    }
    if (streamOverAgg->expr != NULL) {
        ExprDestroy(memCtx, streamOverAgg->expr);
        streamOverAgg->expr = NULL;
    }
}
bool IsPhyOpAAStreamOverAggEqual(const OpBaseT *op1, const OpBaseT *op2)
{
    // 都转换为 logical operator 进行操作
    DB_ASSERT(op1->type == IR_PHYOP_STREAM_OVER_AGG);
    DB_ASSERT(op2->type == IR_PHYOP_STREAM_OVER_AGG);
    const OpPhyStreamOverAggT *streamOverAgg1 = (const OpPhyStreamOverAggT *)(const void *)op1;
    const OpPhyStreamOverAggT *streamOverAgg2 = (const OpPhyStreamOverAggT *)(const void *)op2;

    if (streamOverAgg1->isOnlyInstant != streamOverAgg2->isOnlyInstant) {
        return false;
    }
    if (streamOverAgg1->aggKeyNum != streamOverAgg2->aggKeyNum) {
        return false;
    }
    uint32_t size = streamOverAgg1->aggKeyNum * (uint32_t)sizeof(uint32_t);
    if (size != 0 && memcmp(streamOverAgg1->aggKeyIds, streamOverAgg2->aggKeyIds, size) != 0) {
        return false;
    }
    if (streamOverAgg1->funcNum != streamOverAgg2->funcNum) {
        return false;
    }
    if (streamOverAgg1->expr == NULL || streamOverAgg2->expr == NULL ||
        !ExprEqual(streamOverAgg1->expr, streamOverAgg2->expr)) {
        return false;
    }
    size = streamOverAgg1->funcNum * (uint32_t)sizeof(DmPropertySchemaT);
    if (memcmp(streamOverAgg1->funcProps, streamOverAgg2->funcProps, size) != 0) {
        return false;
    }
    return true;
}
Status CheckPhyOpAAStreamOverAgg(const OpBaseT *op, const OpBaseT **children)
{
    DB_ASSERT(op->type == IR_PHYOP_STREAM_OVER_AGG);
    const OpPhyStreamOverAggT *streamOverAgg1 = (const OpPhyStreamOverAggT *)(const void *)op;
    // groupBy 的 group by key num 不能小于 2
    if (streamOverAgg1->aggKeyNum < 2) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_PARAMETER_VALUE, "Group by key number = 0.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (*children == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Children of groupBy is NULL.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    // schema 的数量需要为原始 prop 加 聚合列 prop
    if (streamOverAgg1->schema.propAA.propNum !=
        IRGetOpAASchemaConst(children[0])->propAA.propNum + streamOverAgg1->funcNum) {
        // Property number of groupBy operator should equal to the sum of child's schema property number and agg func
        // number
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_VALUE, "Property number of groupBy operator.");
        return GMERR_INVALID_VALUE;
    }
    if (streamOverAgg1->funcNum != 0 && streamOverAgg1->expr == NULL) {
        // should not be NULL while funNum is not zero
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Expression of groupBy operator.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    return GMERR_OK;
}
Status GenAASchema4PhyStreamOverAgg(DbMemCtxT *memCtx, const AASchemaT **children, OpBaseT *op)
{
    DB_ASSERT(op->type == IR_PHYOP_STREAM_OVER_AGG);
    OpPhyStreamOverAggT *streamOverAgg1 = (OpPhyStreamOverAggT *)(void *)op;
    uint32_t childPropNum = children[0]->propAA.propNum;
    // 聚合方法会产生新 prop
    uint32_t propNum = childPropNum + streamOverAgg1->funcNum;
    Status ret = IRInitAASchema(memCtx, propNum, &streamOverAgg1->schema);
    if (ret != GMERR_OK) {
        // Unable to generate schema for GroupBy operator
        DB_LOG_ERROR(ret, "init AASchema.");
        return ret;
    }
    // 拷贝子节点 prop
    for (uint32_t i = 0; i < childPropNum; i++) {
        streamOverAgg1->schema.propAA.properties[i] = (*children)->propAA.properties[i];
    }
    // 将聚合算子生成的属性拷贝到中间结果集
    for (uint32_t i = childPropNum; i < propNum; i++) {
        streamOverAgg1->schema.propAA.properties[i] = &streamOverAgg1->funcProps[i - childPropNum];
    }

    return GMERR_OK;
}
#endif
#ifdef FEATURE_TS
Status CopyPhyOpBulkInsert(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
    DB_ASSERT(srcOp->type == IR_PHYOP_BULK_INSERT || srcOp->type == dstOp->type);
    const OpPhyBulkInsertT *src = (const OpPhyBulkInsertT *)(const void *)srcOp;
    OpPhyBulkInsertT *dst = (OpPhyBulkInsertT *)(void *)dstOp;
    dst->columnNum = src->columnNum;
    dst->logicLabel = src->logicLabel;
    dst->physicLabel = src->physicLabel;
    return DbCopyListWithMemCtx(memCtx, src->insertDataList, &dst->insertDataList);
}

void DestroyPhyOpBulkInsert(DbMemCtxT *memCtx, OpBaseT *op)
{
    DB_UNUSED(memCtx);
    DB_ASSERT(op->type == IR_PHYOP_BULK_INSERT);
    OpPhyBulkInsertT *bulkInsert = (OpPhyBulkInsertT *)(void *)op;
    if (bulkInsert->insertDataList != NULL) {
        // List 中存放是待插入数据 <BulkInsertArrT>, 直接释放 List 即可
        DbDestroyList(bulkInsert->insertDataList);
        bulkInsert->insertDataList = NULL;
    }
}

bool IsPhyOpBuilkInsertEqual(const OpBaseT *op1, const OpBaseT *op2)
{
    DB_ASSERT(op1->type == IR_PHYOP_BULK_INSERT && op2->type == IR_PHYOP_BULK_INSERT);
    const OpPhyBulkInsertT *bulkInsert1 = (const OpPhyBulkInsertT *)(const void *)op1;
    const OpPhyBulkInsertT *bulkInsert2 = (const OpPhyBulkInsertT *)(const void *)op2;
    if (bulkInsert1->logicLabel != bulkInsert2->logicLabel || bulkInsert1->physicLabel != bulkInsert2->physicLabel) {
        return false;
    }
    return DbIsListEqual(
        bulkInsert1->insertDataList, bulkInsert2->insertDataList, (DbListItemCompareFunc)BulkInsertArrCompareFunc);
}

Status CheckPhyOpBulkInsert(const OpBaseT *op, const OpBaseT **children)
{
    DB_ASSERT(op->type == IR_PHYOP_BULK_INSERT);
    if (children != NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "AABulkInsert, child should be null.");
        return GMERR_INTERNAL_ERROR;
    }
    const OpPhyBulkInsertT *bulkInsertOp = (const OpPhyBulkInsertT *)(const void *)op;
    if (bulkInsertOp->logicLabel == NULL) {
        DB_LOG_ERROR(GMERR_UNEXPECTED_NULL_VALUE, "AABulkInsert, logical label is null.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DmTsInfoT *tsInfo = (DmTsInfoT *)bulkInsertOp->logicLabel->metaVertexLabel->extraInfo.data;
    if (bulkInsertOp->logicLabel->metaVertexLabel->vertexLabelType == VERTEX_TYPE_NORMAL &&
        tsInfo->labelType == MEM_LABEL) {
        return GMERR_OK;
    }
    // 应当至少插入一批次的数据
    if (bulkInsertOp->insertDataList == NULL || DbListGetItemCnt(bulkInsertOp->insertDataList) == 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "AABulkInsert, insert data is null.");
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

Status CopyPhyOpCopyTo(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
    DB_ASSERT(srcOp->type == IR_PHYOP_COPY_TO && srcOp->type == dstOp->type);

    Status ret = CopyOpCopyTo(memCtx, srcOp, dstOp);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "copy physical CopyTo operator.");
    }
    return ret;
}

void DestroyPhyOpCopyTo(DbMemCtxT *memCtx, OpBaseT *op)
{
    DB_ASSERT(op->type == IR_PHYOP_COPY_TO);
    DestroyOpCopyTo(memCtx, op);
}

bool IsPhyOpCopyToEqual(const OpBaseT *op1, const OpBaseT *op2)
{
    DB_ASSERT(op1->type == IR_PHYOP_COPY_TO && op2->type == IR_PHYOP_COPY_TO);
    return IsOpCopyToEqual(op1, op2);
}

Status CheckPhyOpCopyTo(const OpBaseT *op, const OpBaseT **children)
{
    DB_ASSERT(op->type == IR_PHYOP_COPY_TO);
    Status ret = CheckOpCopyTo(op, children);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Illegal operator %" PRIu32, (uint32_t)IR_PHYOP_COPY_TO);
    }
    return ret;
}
#endif

#ifdef FEATURE_GQL
Status CopyPhyOpPathTraversal(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
    DB_ASSERT(srcOp->type == IR_PHYOP_PATH_TRAVERSAL && srcOp->type == (uint8_t)dstOp->type);
    const OpPhyPathTraversalT *src = (const OpPhyPathTraversalT *)(const void *)srcOp;
    OpPhyPathTraversalT *dest = (OpPhyPathTraversalT *)(void *)dstOp;
    dest->pathInfo = src->pathInfo;
    dest->eventType = src->eventType;
    return GMERR_OK;
}

Status CopyPhyOpFilterPathTraversal(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
    DB_ASSERT(srcOp->type == IR_PHYOP_FILTER_PATH_TRAVERSAL && srcOp->type == (uint8_t)dstOp->type);
    const OpPhyFilterPathTraversalT *src = (const OpPhyFilterPathTraversalT *)(const void *)srcOp;
    OpPhyFilterPathTraversalT *dest = (OpPhyFilterPathTraversalT *)(void *)dstOp;
    dest->pathInfo = src->pathInfo;
    return GMERR_OK;
}

Status CopyPhyOpTopoExpand(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
    DB_ASSERT(srcOp->type == IR_PHYOP_TOPO_EXPAND && srcOp->type == (uint8_t)dstOp->type);
    const OpPhyTopoExpandT *src = (const OpPhyTopoExpandT *)(const void *)srcOp;
    OpPhyTopoExpandT *dest = (OpPhyTopoExpandT *)(void *)dstOp;
    dest->deleteMode = src->deleteMode;
    dest->eventType = src->eventType;
    return GMERR_OK;
}

Status CopyPhyOpTopoScan(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
    DB_ASSERT(srcOp->type == IR_PHYOP_TOPO_SCAN && srcOp->type == (uint8_t)dstOp->type);
    const OpPhyTopoScanT *src = (const OpPhyTopoScanT *)(const void *)srcOp;
    if (src->edgeLabelNum == 0) {
        return GMERR_OK;
    }
    OpPhyTopoScanT *dest = (OpPhyTopoScanT *)(void *)dstOp;
    dest->pathName = src->pathName;
    dest->edgeLabelNum = src->edgeLabelNum;
    uint32_t size = dest->edgeLabelNum * (uint32_t)sizeof(DmEdgeLabelT *);
    DmEdgeLabelT **tmpEdgeLabels = (DmEdgeLabelT **)DbDynMemCtxAlloc(memCtx, size);
    if (tmpEdgeLabels == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "alloc for edgeLabels, size: %" PRIu32 ".", size);
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t err = memcpy_s(tmpEdgeLabels, size, src->edgeLabels, size);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DbDynMemCtxFree(memCtx, tmpEdgeLabels);
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "copy edgeLabels.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    dest->edgeLabels = tmpEdgeLabels;
    return GMERR_OK;
}

Status CopyPhyOpPathSeqFilter(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
    return CopyPathSeqFilter(memCtx, srcOp, dstOp);
}

Status CopyPhyOpReplace(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
    DB_UNUSED(memCtx);
    const OpPhyReplaceT *src = (const OpPhyReplaceT *)(const void *)srcOp;
    OpPhyReplaceT *dst = (OpPhyReplaceT *)(void *)dstOp;
    dst->label = src->label;
    dst->tuples = src->tuples;  // 性能考虑，做浅拷贝
    dst->batchBufHead = src->batchBufHead;
    dst->isInsertOnSameTable = src->isInsertOnSameTable;
    dst->isTrigsAllTrue = src->isTrigsAllTrue;
    return GMERR_OK;
}

bool IsPhyOpReplaceEqual(const OpBaseT *op1, const OpBaseT *op2)
{
    const OpPhyReplaceT *replace1 = (const OpPhyReplaceT *)(const void *)op1;
    const OpPhyReplaceT *replace2 = (const OpPhyReplaceT *)(const void *)op2;

    return (replace1->label == replace2->label) && (replace1->tuples == replace2->tuples);
}

Status GenAASchema4PhyTopoExpand(DbMemCtxT *memCtx, const AASchemaT **children, OpBaseT *op)
{
    // 当前只处理第二个孩子节点的topoAA：透传topoAA
    const AASchemaT *childSchema = children[1];
    if (childSchema->topoAA.edgeLabelNum == 0) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "TopoAA should exist.");
        return GMERR_DATA_EXCEPTION;
    }
    AASchemaT *schema = IRGetOpAASchema(op);
    Status ret = IRInitTopoAASchema(memCtx, childSchema->topoAA.edgeLabelNum, schema);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t size = schema->topoAA.edgeLabelNum * (uint32_t)sizeof(DmEdgeLabelT *);
    errno_t memRet = memcpy_s(schema->topoAA.edgeLabels, size, childSchema->topoAA.edgeLabels, size);
    if (memRet != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Copy edgeLabels in GenAASchema4PhyTopoExpand.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

Status GenAASchema4PhyTopoScan(DbMemCtxT *memCtx, const AASchemaT **children, OpBaseT *op)
{
    OpPhyTopoScanT *topoScan = (OpPhyTopoScanT *)(void *)op;
    Status ret = IRInitTopoAASchemaWithLabel(memCtx, topoScan->edgeLabelNum, topoScan->edgeLabels, &topoScan->schema);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "generate aaschema for TopoScan(type:%" PRIu32 ")", (uint32_t)op->type);
        return ret;
    }

    return GMERR_OK;
}

Status CheckPhyOpReplace(const OpBaseT *op, const OpBaseT **children)
{
    // 1. 孩子节点应该为NULL
    if (children != NULL) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "AAReplace, child should be null.");
        return GMERR_INTERNAL_ERROR;
    }

    // Replace算子无输出schema，不进行vertexlabel与schema的校验
    return GMERR_OK;
}
#endif  // FEATURE_GQL

// 物理算子成员函数, 若函数句柄为NULL, 则表明未重写OpBase基类对应的函数实现
typedef struct IRPhyOpFunc {
    // 物理算子拷贝方法(暂不拷贝AASchema和DmVertexLabel)
    Status (*copy)(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp);
    // 物理算子销毁方法
    void (*destroy)(DbMemCtxT *memCtx, OpBaseT *op);
    // 物理算子判等方法
    bool (*equal)(const OpBaseT *op1, const OpBaseT *op2);
    // 物理算子合法性校验方法
    Status (*check)(const OpBaseT *op, const OpBaseT **children);
    // 物理算子AASchema生成方法
    Status (*genSchema)(DbMemCtxT *memCtx, const AASchemaT **children, OpBaseT *op);
} IRPhyOpFuncT;

static inline IRPhyOpFuncT *SetIrPhyOpFunc(IRPhyOpFuncT *func,
    Status (*copy)(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp),
    void (*destroy)(DbMemCtxT *memCtx, OpBaseT *op), bool (*equal)(const OpBaseT *op1, const OpBaseT *op2),
    Status (*check)(const OpBaseT *op, const OpBaseT **children),
    Status (*genSchema)(DbMemCtxT *memCtx, const AASchemaT **children, OpBaseT *op))
{
    func->copy = copy;
    func->destroy = destroy;
    func->equal = equal;
    func->check = check;
    func->genSchema = genSchema;
    return func;
}

/*
 * 用途：获取物理算子函数
 * 是否并发初始化：否
 * 是否并发读写：只读，并发不影响
 * 并发方案：无
 */
static IRPhyOpFuncT *GetIrPhyOpFunc(IROpTypeE irOpType, IRPhyOpFuncT *func)
{
    switch (irOpType) {
        case IR_PHYOP_SEQSCAN:
            return SetIrPhyOpFunc(func, CopyPhyOpScan, NULL, IsPhyOpScanEqual, CheckPhyOpScan, GenAASchema4PhyScan);
        case IR_PHYOP_WORKLABELSCAN:
            return SetIrPhyOpFunc(func, CopyPhyOpScan, NULL, IsPhyOpScanEqual, CheckPhyOpScan, GenAASchema4PhyScan);
        case IR_PHYOP_INDEXSCAN:
            return SetIrPhyOpFunc(
                func, CopyPhyOpIndexScan, NULL, IsPhyOpIndexScanEqual, CheckPhyOpIndexScan, GenAASchema4PhyScan);
        case IR_PHYOP_INDEXFILTERSCAN:
            return SetIrPhyOpFunc(
                func, CopyPhyOpIndexScan, NULL, IsPhyOpIndexScanEqual, CheckPhyOpIndexFilterScan, GenAASchema4PhyScan);
        case IR_PHYOP_NESTLOOPJOIN:
            return SetIrPhyOpFunc(
                func, CopyPhyOpJoin, DestroyPhyOpJoin, IsPhyOpJoinEqual, CheckPhyOpJoin, GenAASchema4PhyJoin);
        case IR_PHYOP_INDEXLOOPJOIN:
            return SetIrPhyOpFunc(
                func, CopyPhyOpJoin, DestroyPhyOpJoin, IsPhyOpJoinEqual, CheckPhyOpJoin, GenAASchema4PhyJoin);
        case IR_PHYOP_HASHJOIN:
            return SetIrPhyOpFunc(
                func, CopyPhyOpJoin, DestroyPhyOpJoin, IsPhyOpJoinEqual, CheckPhyOpJoin, GenAASchema4PhyJoin);
        case IR_PHYOP_PROJECT:
            return SetIrPhyOpFunc(func, CopyPhyOpProject, DestroyPhyOpProject, IsPhyOpProjectEqual, CheckPhyOpProject,
                GenAASchema4PhyProject);
        case IR_PHYOP_EXTEND_PROJECT:
            return SetIrPhyOpFunc(func, CopyPhyOpExtendProject, DestroyPhyOpExtendProject, IsPhyOpExtendProjectEqual,
                CheckPhyOpExtendProject, GenAASchema4PhyExtendProject);
        case IR_PHYOP_FILTER:
            return SetIrPhyOpFunc(func, NULL, NULL, NULL, CheckPhyOpFilter, GenAASchema4PhyFilter);
        case IR_PHYOP_MERGE:
            return SetIrPhyOpFunc(func, CopyPhyOpMerge, NULL, IsPhyOpMergeEqual, CheckPhyOpDML, GenAASchema4PhyMerge);
        case IR_PHYOP_MODIFYWORKLABEL:
            return SetIrPhyOpFunc(
                func, CopyPhyOpModify, NULL, IsPhyOpModifyEqual, CheckPhyOpDML, GenAASchema4PhyWorkLabelModify);
#ifdef FEATURE_SQL
        case IR_PHYOP_UPDATE:
            return SetIrPhyOpFunc(func, CopyOpCud, NULL, IsOpCudEqual, CheckOpCud, NULL);
        case IR_PHYOP_VECTOR_INDEXSCAN:
            return SetIrPhyOpFunc(func, CopyPhyOpVecIdxScan, DestroyPhyOpVecIdxScan, IsPhyOpVecIdxScanEqual,
                CheckPhyOpVecIdxScan, GenAASchema4PhyScan);
#ifndef IDS_HAOTIAN
        case IR_PHYOP_UNION:
            return SetIrPhyOpFunc(
                func, CopyPhyOpUnion, NULL, IsPhyOpUnionEqual, CheckPhyOpCompound, GenAASchema4PhyCompound);
        case IR_PHYOP_SUB_QUERY_SCAN:
            return SetIrPhyOpFunc(
                func, CopyPhyOpSubQueryScan, NULL, IsPhyOpSubQueryScanEqual, CheckPhyOpSubQueryScan, NULL);
        case IR_PHYOP_TRIGGER:
            return SetIrPhyOpFunc(func, CopyOpTrig, DestroyOpTrig, IsOpTrigEqual, CheckOpTrig, NULL);
#endif
#endif
#if defined(FEATURE_GQL) || defined(FEATURE_SQL)
        case IR_PHYOP_DELETE:
            return SetIrPhyOpFunc(func, CopyOpCud, NULL, IsOpCudEqual, CheckOpCud, NULL);
#endif
#if defined(FEATURE_TS) || defined(FEATURE_SQL)
        case IR_PHYOP_EXTEND_ORDER_BY:
            return SetIrPhyOpFunc(
                func, CopyOpExtendOrderBy, DestroyOpExtendOrderBy, IsOpExtendOrderByEqual, CheckOpExtendOrderBy, NULL);
        case IR_PHYOP_EXPR_PROJECT:
            return SetIrPhyOpFunc(
                func, CopyOpExprProject, DestroyOpExprProject, IsOpExprProjectEqual, CheckOpExprProject, NULL);
        case IR_PHYOP_ORDER_BY:
            return SetIrPhyOpFunc(func, CopyPhyOpOrderBy, DestroyPhyOpOrderBy, IsPhyOpOrderByEqual, CheckPhyOpOrderBy,
                GenAASchema4PhyOrderBy);
        case IR_PHYOP_DISTINCT:
            return SetIrPhyOpFunc(
                func, CopyPhyOpDistinct, NULL, IsPhyOpDistinctEqual, CheckPhyOpDistinct, GenAASchema4PhyDistinct);
        case IR_PHYOP_LIMIT:
            return SetIrPhyOpFunc(func, CopyPhyOpLimit, NULL, IsPhyOpLimitEqual, CheckPhyOpLimit, GenAASchema4PhyLimit);
        case IR_PHYOP_INSERT:
            return SetIrPhyOpFunc(func, CopyOpCud, NULL, IsOpCudEqual, CheckOpCud, NULL);
        case IR_PHYOP_BUILD:
            return SetIrPhyOpFunc(func, CopyOpBuild, DestroyOpBuild, IsOpBuildEqual, CheckOpBuild, NULL);
        case IR_PHYOP_HASH_AGG:
            return SetIrPhyOpFunc(func, CopyPhyOpHashAgg, DestroyPhyOpHashAgg, IsPhyOpHashAggEqual, CheckPhyOpHashAgg,
                GenAASchema4PhyHashAgg);
#endif
#ifdef FEATURE_TS
        case IR_PHYOP_BULK_INSERT:
            return SetIrPhyOpFunc(
                func, CopyPhyOpBulkInsert, DestroyPhyOpBulkInsert, IsPhyOpBuilkInsertEqual, CheckPhyOpBulkInsert, NULL);
        case IR_PHYOP_COPY_TO:
            return SetIrPhyOpFunc(
                func, CopyPhyOpCopyTo, DestroyPhyOpCopyTo, IsPhyOpCopyToEqual, CheckPhyOpCopyTo, NULL);
        case IR_PHYOP_TIMEPARTITION_SCAN:
            return SetIrPhyOpFunc(func, CopyPhyOpScan, NULL, IsPhyOpScanEqual, CheckPhyOpScan, GenAASchema4PhyScan);
#endif
#ifdef FEATURE_STREAM
        case IR_PHYOP_STREAMSCAN:
            return SetIrPhyOpFunc(
                func, CopyPhyOpStreamScan, NULL, IsPhyOpStreamScanEqual, CheckPhyOpScan, GenAASchema4StreamPhyScan);
        case IR_PHYOP_STREAM_UPSERT_INTO_REF:
            return SetIrPhyOpFunc(func, CopyPhyOpStreamUpsertIntoRef, DestroyPhyOpStreamUpsertIntoRef,
                IsPhyOpStreamUpsertIntoRefEqual, CheckPhyOpStreamUpsertIntoRef, NULL);
        case IR_PHYOP_STREAM_WINDOW_TABLE:
            return SetIrPhyOpFunc(func, CopyPhyOpAAWindowTable, NULL, IsPhyOpAAWindowTableEqual,
                CheckPhyOpAAWindowTable, GenAASchema4PhyWindowTable);
        case IR_PHYOP_STREAM_WATERMARK_ASSIGNER:
            return SetIrPhyOpFunc(func, CopyPhyOpAAWatermarkAssigner, NULL, IsPhyOpAAWatermarkAssignerEqual,
                CheckPhyOpAAWatermarkAssigner, NULL);
        case IR_PHYOP_STREAM_OVER_AGG:
            return SetIrPhyOpFunc(func, CopyPhyOpAAStreamOverAgg, DestroyPhyOpAAStreamOverAgg,
                IsPhyOpAAStreamOverAggEqual, CheckPhyOpAAStreamOverAgg, GenAASchema4PhyStreamOverAgg);

#endif
#ifdef FEATURE_GQL
        case IR_PHYOP_PATH_TRAVERSAL:
            return SetIrPhyOpFunc(func, CopyPhyOpPathTraversal, NULL, NULL, IROpNoCheck, NULL);
        case IR_PHYOP_FILTER_PATH_TRAVERSAL:
            return SetIrPhyOpFunc(func, CopyPhyOpFilterPathTraversal, NULL, NULL, IROpNoCheck, NULL);
        case IR_PHYOP_TOPO_EXPAND:
            return SetIrPhyOpFunc(func, CopyPhyOpTopoExpand, NULL, NULL, IROpNoCheck, GenAASchema4PhyTopoExpand);
        case IR_PHYOP_TOPO_SCAN:
            return SetIrPhyOpFunc(func, CopyPhyOpTopoScan, NULL, NULL, IROpNoCheck, GenAASchema4PhyTopoScan);
        case IR_PHYOP_PATH_SEQ_FILTER:
            return SetIrPhyOpFunc(func, CopyPhyOpPathSeqFilter, NULL, NULL, IROpNoCheck, NULL);
        case IR_PHYOP_REPLACE:
            return SetIrPhyOpFunc(func, CopyPhyOpReplace, NULL, IsPhyOpReplaceEqual, CheckPhyOpReplace, NULL);
#endif  // FEATURE_GQL
        default:
            return SetIrPhyOpFunc(func, NULL, NULL, NULL, NULL, NULL);
    }
}

size_t GetPhysicalOpSize(IROpTypeE type)
{
    switch (type) {
        case IR_PHYOP_SEQSCAN:
        case IR_PHYOP_WORKLABELSCAN:
            return sizeof(OpPhyScanT);
        case IR_PHYOP_INDEXSCAN:
        case IR_PHYOP_INDEXFILTERSCAN:
            return sizeof(OpPhyIndexScanT);
        case IR_PHYOP_NESTLOOPJOIN:
        case IR_PHYOP_INDEXLOOPJOIN:
        case IR_PHYOP_HASHJOIN:
            return sizeof(OpPhyJoinT);
        case IR_PHYOP_PROJECT:
            return sizeof(OpPhyProjectT);
        case IR_PHYOP_EXTEND_PROJECT:
            return sizeof(OpPhyExtendProjectT);
        case IR_PHYOP_FILTER:
            return sizeof(OpPhyFilterT);
        case IR_PHYOP_MERGE:
            return sizeof(OpPhyMergeT);
#ifdef FEATURE_SQL
        case IR_PHYOP_UPDATE:
            return sizeof(OpPhyCudT);
        case IR_PHYOP_VECTOR_INDEXSCAN:
            return sizeof(OpPhyVectorIndexScanT);
#ifndef IDS_HAOTIAN
        case IR_PHYOP_UNION:
            return sizeof(OpPhyUnionT);
        case IR_PHYOP_SUB_QUERY_SCAN:
            return sizeof(OpPhySubQueryScanT);
        case IR_PHYOP_TRIGGER:
            return sizeof(OpPhyTrigT);
#endif
#endif
#if defined(FEATURE_GQL) || defined(FEATURE_SQL)
        case IR_PHYOP_DELETE:
            return sizeof(OpPhyCudT);
#endif
#if defined(FEATURE_TS) || defined(FEATURE_SQL)
        case IR_PHYOP_EXTEND_ORDER_BY:
            return sizeof(OpPhyExtendOrderByT);
        case IR_PHYOP_EXPR_PROJECT:
            return sizeof(OpPhyExprProjectT);
        case IR_PHYOP_ORDER_BY:
            return sizeof(OpPhyOrderByT);
        case IR_PHYOP_DISTINCT:
            return sizeof(OpPhyDistinctT);
        case IR_PHYOP_LIMIT:
            return sizeof(OpPhyLimitT);
        case IR_PHYOP_INSERT:
            return sizeof(OpPhyCudT);
        case IR_PHYOP_BUILD:
            return sizeof(OpPhyBuildT);
        case IR_PHYOP_HASH_AGG:
            return sizeof(OpPhyHashAggT);
#endif
#ifdef FEATURE_TS
        case IR_PHYOP_BULK_INSERT:
            return sizeof(OpPhyBulkInsertT);
        case IR_PHYOP_COPY_TO:
            return sizeof(OpPhyCopyToT);
        case IR_PHYOP_TIMEPARTITION_SCAN:
            return sizeof(OpPhyTimePartitionScanT);
#endif
#ifdef FEATURE_STREAM
        case IR_PHYOP_STREAMSCAN:
            return sizeof(OpPhyStreamScanT);
        case IR_PHYOP_STREAM_UPSERT_INTO_REF:
            return sizeof(OpPhyStreamUpsertIntoRefT);
        case IR_PHYOP_STREAM_WINDOW_TABLE:
            return sizeof(OpPhyStreamWindowTableT);
        case IR_PHYOP_STREAM_WATERMARK_ASSIGNER:
            return sizeof(OpPhyStreamWatermarkAssignerT);
        case IR_PHYOP_STREAM_OVER_AGG:
            return sizeof(OpPhyStreamOverAggT);
#endif
#ifdef FEATURE_GQL
        case IR_PHYOP_PATH_TRAVERSAL:
            return sizeof(OpPhyPathTraversalT);
        case IR_PHYOP_TOPO_SCAN:
            return sizeof(OpPhyTopoScanT);
        case IR_PHYOP_TOPO_EXPAND:
            return sizeof(OpPhyTopoExpandT);
        case IR_PHYOP_FILTER_PATH_TRAVERSAL:
            return sizeof(OpPhyFilterPathTraversalT);
        case IR_PHYOP_PATH_SEQ_FILTER:
            return sizeof(OpPhyPathSeqFilterT);
        case IR_PHYOP_REPLACE:
            return sizeof(OpPhyReplaceT);
#endif            // FEATURE_GQL
        default:  // IR_PHYOP_MODIFYWORKLABEL
            DB_ASSERT(type == IR_PHYOP_MODIFYWORKLABEL);
            return sizeof(OpPhyWorkLabelModifyT);
    }
}

Status IRCreatePhysicalOp(DbMemCtxT *memCtx, IROpTypeE type, OpBaseT **op)
{
    DB_POINTER2(memCtx, op);
    DB_ASSERT(type >= IR_PHYOP_BEGIN && type < IR_PHYOP_END);

    size_t size = GetPhysicalOpSize(type);
    // memCtx：由外层传入，并由外层管理释放；并发：不支持，外部模块保证线程安全
    *op = DbDynMemCtxAlloc(memCtx, size);
    if (*op == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "malloc for operator %" PRIu32 ", size: %zu", (uint32_t)type, size);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(*op, size, 0x00, size);

    (*op)->type = type;
    (*op)->arity = IRGetPhysicalOpArity(type);
    return GMERR_OK;
}

YANG_MODEL_VALIDATE_SECTION
Status IRCreatePhysicalOpWithArena(DbArenaT *arena, IROpTypeE type, OpBaseT **op)
{
    DB_POINTER2(arena, op);
    DB_ASSERT(type >= IR_PHYOP_BEGIN && type < IR_PHYOP_END);

    size_t size = GetPhysicalOpSize(type);
    *op = DbArenaAllocZero(arena, size);
    if (*op == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "malloc for operator %" PRIu32 ", size: %zu", (uint32_t)type, size);
        return GMERR_OUT_OF_MEMORY;
    }

    (*op)->type = type;
    (*op)->arity = IRGetPhysicalOpArity(type);
    return GMERR_OK;
}

// 算子内部的DmVertexLabel不销毁, 由上层统一release所有引用的DmVertexLabel
// 对于AASchema PropAA.properties数组中的DmPropertySchemaT, 只有leaf算子才销毁, 非leaf算子不处理
void IRDestroyPhysicalOp(DbMemCtxT *memCtx, OpBaseT **op)
{
    DB_POINTER2(memCtx, op);
    if (*op == NULL) {
        return;
    }

    DB_ASSERT((*op)->type >= IR_PHYOP_BEGIN && (*op)->type < IR_PHYOP_END);

    // 不用单独destroy OpBase
    // destroy AASchema内部成员, 因为所有算子的PropAA.properties都是引用了catalog中vertexLabel的property
    // schema，因此都不释放PropAA.properties
    AASchemaT *schema = IRGetOpAASchema(*op);
    IRDestroyAASchemaInner(memCtx, false, schema);

    // destroy句柄为NULL，则表明该物理算子未重写OpBase基类的destroy函数
    IRPhyOpFuncT func;
    (void)GetIrPhyOpFunc((*op)->type, &func);
    if (func.destroy != NULL) {
        func.destroy(memCtx, *op);
    }

    DbDynMemCtxFree(memCtx, *op);
    *op = NULL;
}

// 算子内部的DmVertexLabel和AASchema中的prope直接引用，不拷贝
// Copy失败时，暂不考虑内存释放，由上层模块调memCtx接口统一释放
Status IRCopyPhysicalOp(DbMemCtxT *memCtx, const OpBaseT *srcOp, OpBaseT *dstOp)
{
    DB_POINTER3(memCtx, srcOp, dstOp);
    DB_ASSERT(srcOp->type >= IR_PHYOP_BEGIN && srcOp->type < IR_PHYOP_END);

    IRCopyOpBase(srcOp, dstOp);

    Status ret = IRCopyAASchema(memCtx, IRGetOpAASchemaConst(srcOp), IRGetOpAASchema(dstOp));
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "copy AASchema of %" PRIu32, (uint32_t)srcOp->type);
        return ret;
    }

    // copy句柄为NULL，则表明该物理算子未重写OpBase基类的copy函数
    IRPhyOpFuncT func;
    (void)GetIrPhyOpFunc(srcOp->type, &func);
    if (func.copy == NULL) {
        return GMERR_OK;
    }

    ret = func.copy(memCtx, srcOp, dstOp);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "copy IR physical operator %" PRIu32, (uint32_t)srcOp->type);
    }
    return ret;
}

uint32_t IRGetPhysicalOpArity(IROpTypeE type)
{
    DB_ASSERT(type >= IR_PHYOP_BEGIN && type < IR_PHYOP_END);

    switch (type) {
        case IR_PHYOP_NESTLOOPJOIN:
        case IR_PHYOP_INDEXLOOPJOIN:
        case IR_PHYOP_HASHJOIN:
        case IR_PHYOP_FILTER:
#ifdef FEATURE_SQL
        case IR_PHYOP_UPDATE:
        case IR_PHYOP_UNION:
#endif
#ifdef FEATURE_GQL
        case IR_PHYOP_TOPO_EXPAND:
#endif  // FEATURE_GQL
            return IR_OPARITY_TWO;

        case IR_PHYOP_INDEXFILTERSCAN:
        case IR_PHYOP_PROJECT:
        case IR_PHYOP_EXTEND_PROJECT:
        case IR_PHYOP_MODIFYWORKLABEL:
#ifdef FEATURE_SQL
        case IR_PHYOP_TRIGGER:
#endif
#if defined(FEATURE_GQL) || defined(FEATURE_SQL)
        case IR_PHYOP_DELETE:
#endif
#if defined(FEATURE_TS) || defined(FEATURE_SQL)
        case IR_PHYOP_EXTEND_ORDER_BY:
        case IR_PHYOP_EXPR_PROJECT:
        case IR_PHYOP_ORDER_BY:
        case IR_PHYOP_DISTINCT:
        case IR_PHYOP_LIMIT:
        case IR_PHYOP_HASH_AGG:
        case IR_PHYOP_INSERT:
#endif
#ifdef FEATURE_TS
        case IR_PHYOP_COPY_TO:
#endif
#ifdef FEATURE_GQL
        case IR_PHYOP_FILTER_PATH_TRAVERSAL:
        case IR_PHYOP_PATH_SEQ_FILTER:
#endif  // FEATURE_GQL
#ifdef FEATURE_STREAM
        case IR_PHYOP_STREAM_WINDOW_TABLE:
        case IR_PHYOP_STREAM_WATERMARK_ASSIGNER:
        case IR_PHYOP_STREAM_OVER_AGG:
#endif
            return IR_OPARITY_ONE;
        default:
            return IR_OPARITY_ZERO;
    }
}

bool IRIsPhysicalOpEqual(const OpBaseT *op1, const OpBaseT *op2)
{
    DB_POINTER2(op1, op2);
    DB_ASSERT(op1->type >= IR_PHYOP_BEGIN && op1->type < IR_PHYOP_END);

    if (op1 == op2) {
        return true;
    }
    if (!IRIsOpBaseEqual(op1, op2)) {
        return false;
    }
    if (!IRIsAASchemaEqual(IRGetOpAASchemaConst(op1), IRGetOpAASchemaConst(op2))) {
        return false;
    }

    // equal句柄为NULL，则表明该物理算子未重写OpBase基类的equal函数
    IRPhyOpFuncT func;
    (void)GetIrPhyOpFunc(op1->type, &func);
    if (func.equal == NULL) {
        return true;
    }

    return func.equal(op1, op2);
}

Status IRCheckPhysicalOp(const OpBaseT *op, const OpBaseT **children)
{
    if (op == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Illegal operator, NULL op.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    if (op->type < IR_PHYOP_BEGIN || op->type >= IR_PHYOP_END) {
        DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "illegal op type %" PRIu32, (uint32_t)op->type);
        return GMERR_DATATYPE_MISMATCH;
    }

    Status ret = IRCheckOpBase(op);
    if (ret != GMERR_OK) {
        // Illegal IR physical operator
        DB_LOG_ERROR(ret, "check opBase.");
        return ret;
    }

    ret = IRCheckAASchema(IRGetOpAASchemaConst(op));
    if (ret != GMERR_OK) {
        // Illegal IR physical operator
        IR_ERROR_LOG_PRINT(ret, "check AASchema.");
        return ret;
    }

    // 若arity > 0, 则孩子节点不应该为NULL
    if (op->arity > 0 && children == NULL) {
        DB_LOG_AND_SET_LASERR(
            GMERR_UNEXPECTED_NULL_VALUE, "operator(%" PRIu32 "), children is NULL.", (uint32_t)op->type);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    // 若arity == 0, 则孩子节点应该为NULL
    if (op->arity == 0 && children != NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "operator(%" PRIu32 "), children is not NULL.", (uint32_t)op->type);
        return GMERR_INTERNAL_ERROR;
    }
    IRPhyOpFuncT func;
    (void)GetIrPhyOpFunc(op->type, &func);
    if (func.check == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "operator %" PRIu32, (uint32_t)op->type);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    return func.check(op, children);
}

Status IRGenAASchema4PhyOp(DbMemCtxT *memCtx, const AASchemaT **children, OpBaseT *op)
{
    DB_POINTER2(memCtx, op);  // children可能为NULL
    DB_ASSERT(op->type >= IR_PHYOP_BEGIN && op->type < IR_PHYOP_END);
    IRPhyOpFuncT func;
    (void)GetIrPhyOpFunc(op->type, &func);
    if (func.genSchema == NULL) {
        DB_LOG_AND_SET_LASERR(
            GMERR_FEATURE_NOT_SUPPORTED, "generate AASchema for operator %" PRIu32 "", (uint32_t)op->type);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return func.genSchema(memCtx, children, op);
}

#ifdef __cplusplus
}
#endif
