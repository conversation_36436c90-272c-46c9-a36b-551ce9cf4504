/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: initial function for fastpath compiler
 * Author:
 * Create: 2023-06-10
 */

#include "cpl_base_def.h"
#include "cpl_yang_verifier.h"
#include "cpl_kv_verifier.h"
#include "cpl_kv_parser.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

void FastPathCompilerFuncRegister(void)
{
    CplExportFuncsT cplExportFuncs = {0};
    // 注册实际调用的函数XXXImpl
#ifdef FEATURE_YANG
    cplExportFuncs.verifyYangPkIndex = QryVerifyYangPkIndexImpl;
    cplExportFuncs.verifyYangLabelNode = QryVerifyYangLabelNodeImpl;
    cplExportFuncs.verifyYangNode = QryVerifyYangNodeImpl;
    cplExportFuncs.verifyYangAutoInc = QryVerifyYangAutoIncImpl;
    cplExportFuncs.verifyYangRelation = QryVerifyYangRelationImpl;
    cplExportFuncs.verifyYangEdgeConstraint = QryVerifyYangEdgeConstraintImpl;
#endif
    cplExportFuncs.parseKvSubsInfoJson = QryParseKvSubsInfoJsonImpl;
    cplExportFuncs.verifyCreateKvSubs = QryVerifyCreateKvSubsImpl;
    cplExportFuncs.parseCreateKvTablesForDbStart = QryParseCreateKvTablesForDbStartImpl;
    cplExportFuncs.parseCreateKvTablesFromShmem = QryParseCreateKvTablesFromShmemImpl;
    cplExportFuncs.verifyCreateKvTableDesc = QryVerifyCreateKvTableDescImpl;
    FastPathCompilerSetExportFuncMap(&cplExportFuncs);
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
