/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: Analyzer of the 'alter' command for the Streaming engine
 * Author: Stream Team
 * Create: 2025-01-17
 */

#include "cpl_stream_analyzer_alter.h"
#include "cpl_stream_build_op.h"
#include "cpl_stream_build_ir_tree.h"
#include "cpl_stream_verify_expr.h"
#include "cpl_stream_analyzer_select.h"
#include "ee_stmt_fusion.h"

// The ALTER sql command modifies the WHERE condition of a VIEW or SOURCE node after their creation.
// During the analysis phase we execute the following steps
// |-- StreamSqlAnalyzeAlter()
//     |-- StreamSqlAnalyzeAlterVerify()            - Grab vertexlabel and do preliminary checks
//     |-- StreamSqlAlterCreateIrStmt()             - Build new where sqlStmt
//     |-- StreamSqlAlterCreateIRExpr()
//         |-- StreamSqlAlterBuildDummyFromClause() - Creates dummy entities for building the logical plan
//         |-- StreamSqlBuildItemOpTree()           - Convert sqlStmt into logical plan (tree)
// Then, in the execution phase we do the following
// |-- CreateFilterExpressionAsTree()               - Converts the logical plan into a physical plan
// |-- StreamSqlAlterReplaceWhereCondition()        - Replace the where condition with the new phy. plan
//     |-- TraverseExprAndRemoveRefs()              - Remove the old references from global ref list
//     |-- StreamRegistVertexLabelId()              - Add the new references to the global ref list

Status StreamSqlAnalyzeAlterVerify(SessionT *session, SqlAlterStreamStmtT *parsedStmt, DmVertexLabelT **vertexLabel)
{
    DB_POINTER2(session, parsedStmt);
    SqlTableRefT *refName = parsedStmt->name;

    // Get the vertexLabel from the node name specified in the query. At least, try to.
    // Then, perform some basic checks.
    Status ret = StreamSqlVerifyDBAndGetLabel(session, refName->database, refName->tableName, vertexLabel);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "get label");
        return ret;
    }
    if (vertexLabel == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNDEFINED_TABLE, "");
        return GMERR_UNDEFINED_TABLE;
    }
    if ((*vertexLabel)->metaVertexLabel->vertexLabelType != VERTEX_TYPE_STREAM) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "wrong label type");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    DmStreamVertexLabelT *streamVertexLabel = (DmStreamVertexLabelT *)vertexLabel;
    if (streamVertexLabel->streamType == STREAM_LABEL_TYPE_SOURCE) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "wrong label type");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    if (parsedStmt->newWhereCond == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_SEMANTIC_ERROR, "");
        return GMERR_SEMANTIC_ERROR;
    }

    return GMERR_OK;
}

Status StreamSqlAlterCreateIrStmt(DbMemCtxT *memCtx, SqlIrStmtT *irStmt, DmVertexLabelT *vertexLabel)
{
    DB_POINTER2(memCtx, irStmt);

    StreamAlterStmtT *node = (StreamAlterStmtT *)DbDynMemCtxAlloc(memCtx, sizeof(StreamAlterStmtT));
    if (node == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, " ");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(node, sizeof(StreamAlterStmtT), 0, sizeof(StreamAlterStmtT));

    node->node.tag = T_STREAM_ALTER_STMT;
    node->vertexLabel = vertexLabel;
    node->tempMemCtx = memCtx;
    node->newWhereIRExpr = NULL;
    irStmt->utilityStmt = (NodeT *)node;
    irStmt->irPlan = NULL;

    return GMERR_OK;
}

// In order to recycle as much as possible the functions that have been written for the streaming engine,
// we create a couple of dummy structures serving the sole purpose of not failing some of the checks occurring
// in StreamSqlBuildItemOpTree(..)
Status StreamSqlAlterBuildDummyFromClause(DmVertexLabelT *vertexLabel, SqlIrStmtT *irStmt, DbListT **dummyFromClause)
{
    DmStreamVertexLabelT *streamVertexLabel = (DmStreamVertexLabelT *)vertexLabel;
    DB_ASSERT(streamVertexLabel->streamType != STREAM_LABEL_TYPE_SOURCE);
    DmStreamLinkNodeT *node =
        streamVertexLabel->streamType == STREAM_LABEL_TYPE_SINK ?
            LIST_HEAD_ENTRY(&streamVertexLabel->streamMeta.sinkMeta.prev, DmStreamLinkNodeT, node) :
            LIST_HEAD_ENTRY(&streamVertexLabel->streamMeta.viewMeta.prev, DmStreamLinkNodeT, node);
    if (node == NULL) {
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    DmStreamVertexLabelT *prevVertexLabel = node->vertexLabel;

    // Allocate a new list; we already know the size (only one table!)
    *dummyFromClause = (DbListT *)DbDynMemCtxAlloc(irStmt->memCtx, sizeof(DbListT));
    DbCreateListWithExtendSize(*dummyFromClause, sizeof(SqlSrcItemT), SQL_LIST_EXTEND_SIZE, irStmt->memCtx);

    // Create the source item
    SqlSrcItemT *srcItem = (SqlSrcItemT *)DbDynMemCtxAlloc(irStmt->memCtx, sizeof(SqlSrcItemT));
    if (srcItem == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(srcItem, sizeof(SqlSrcItemT), 0, sizeof(SqlSrcItemT));
    // ...and the table item
    srcItem->srcTable = (SqlSrcTableT *)DbDynMemCtxAlloc(irStmt->memCtx, sizeof(SqlSrcTableT));
    if (srcItem->srcTable == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }

    // Now fill the table item field that are relevant for the checks to be performed
    srcItem->type = SRC_TABLE_REF;
    uint32_t size = (uint32_t)strlen(prevVertexLabel->base.metaVertexLabel->topRecordName) + 1;
    srcItem->srcTable->tableName = DbDynMemCtxAlloc(irStmt->memCtx, size);
    if (srcItem->srcTable->tableName == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, " ");
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t err = strcpy_s(srcItem->srcTable->tableName, size, prevVertexLabel->base.metaVertexLabel->topRecordName);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, " ");
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    srcItem->label = &prevVertexLabel->base;

    // This should be the right time to feel the property infos, but we don't have to
    // (at least for now)

    // Push the table src item on the list and we are good to go
    Status ret = DbAppendListItem(*dummyFromClause, &srcItem);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GMERR_OK;
}

Status StreamSqlAlterGetFilterSchemaFromScan(StreamScanStateT *scanState, const PlanPropDescT **propertySchema)
{
    DmVertexLabelT *vertexLabel = scanState->scan.label;
    if (vertexLabel == NULL || vertexLabel->metaVertexLabel->vertexLabelType != VERTEX_TYPE_STREAM) {
        return GMERR_INTERNAL_ERROR;
    }
    DmStreamVertexLabelT *streamVertexLabel = (DmStreamVertexLabelT *)vertexLabel;
    if (streamVertexLabel->streamType != STREAM_LABEL_TYPE_SOURCE &&
        streamVertexLabel->streamType != STREAM_LABEL_TYPE_VIEW) {
        return GMERR_INTERNAL_ERROR;
    }
    *propertySchema = &streamVertexLabel->propDesc;
    return GMERR_OK;
}

Status StreamSqlAlterGetFilterSchema(
    const DmStreamVertexLabelT *streamVertexLabel, const PlanPropDescT **propertySchema)
{
    StreamQryStmtT *fmqs = NULL;
    if (streamVertexLabel->streamType == STREAM_LABEL_TYPE_SINK) {
        fmqs = (StreamQryStmtT *)streamVertexLabel->streamMeta.sinkMeta.streamCachedPlan.fusionModelQryStmt;
    } else if (streamVertexLabel->streamType == STREAM_LABEL_TYPE_VIEW) {
        fmqs = (StreamQryStmtT *)streamVertexLabel->streamMeta.viewMeta.streamCachedPlan.fusionModelQryStmt;
    } else {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "unsupported type");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    Status ret = GMERR_OK;
    switch (fmqs->base.planState->tagType) {
        case T_STREAMSCAN: {
            StreamScanStateT *scanState = (StreamScanStateT *)fmqs->base.planState;
            ret = StreamSqlAlterGetFilterSchemaFromScan(scanState, propertySchema);
            break;
        }
        case T_STREAM_WINDOW_TABLE: {
            StreamWindowTableStateT *tableState = (StreamWindowTableStateT *)fmqs->base.planState;
            *propertySchema = &tableState->propDescWithWindowProp;
            break;
        }
        case T_STREAM_AGG_OVER_WINDOW: {
            StreamAggOverWindowStateT *tableState = (StreamAggOverWindowStateT *)fmqs->base.planState;
            *propertySchema = &tableState->aggCommon.preSlotDescWithWindow;
            break;
        }
        case T_STREAM_WINDOW_AGG: {
            StreamWindowAggStateT *tableState = (StreamWindowAggStateT *)fmqs->base.planState;
            *propertySchema = &tableState->aggCommon.preSlotDescWithWindow;
            break;
        }
        case T_SEQ_DISTINCT_COUNT: {
            SeqDistctCntStateT *tableState = (SeqDistctCntStateT *)fmqs->base.planState;
            *propertySchema = StreamGetPlanPropDescT(tableState->base.planState.leftTree);
            if (*propertySchema == NULL) {
                DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "get property schema");
                return GMERR_FEATURE_NOT_SUPPORTED;
            }
            break;
        }
        default:
            DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "unsupported type: %u", fmqs->base.planState->tagType);
            return GMERR_FEATURE_NOT_SUPPORTED;
    }

    return ret;
}
// Create a logical plan from the sql of the new where condition
Status StreamSqlAlterCreateIRExpr(SessionT *session, SqlAlterStreamStmtT *alterStmt, SqlIrStmtT *irStmt,
    DmVertexLabelT *vertexLabel, IRExprT **newWhereIRExpr)
{
    DmStreamVertexLabelT *streamVertexLabel = (DmStreamVertexLabelT *)vertexLabel;
    IRPlanT *selectIRPlan = IRCreatePlan(irStmt->memCtx);
    if (selectIRPlan == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, " ");
        return GMERR_OUT_OF_MEMORY;
    }

    IRExprT *tmpExpr = NULL;

    const PlanPropDescT *planPropDesc = NULL;
    Status ret = StreamSqlAlterGetFilterSchema(streamVertexLabel, &planPropDesc);
    if (ret != GMERR_OK) {
        return ret;
    }
    AASchemaT schema = {
        .propAA = {.properties = planPropDesc->propSchema, .propNum = planPropDesc->propNum}, .topoAA = {0}};

    // Another thing that we need is the fromClause, that unfortunately was thrown away after the
    // creation of the view/sink. The fromClause is a list of SqlSrcItemT* that can be built with the
    // property info contained into the vertexLabel. The check itself is performed in StreamSqlGetColumnCnt()
    // and StreamVerifyPropInfo().
    // The following function mimics what is done in SqlCreateSrcItemByTable() [I cannot directly use it,
    // because it requires a SqlTableRefT as input...].
    // fix: we need create from clause by the label which is the previous label of curr vertexlabel
    DbListT *dummyFromClause = NULL;
    ret = StreamSqlAlterBuildDummyFromClause(vertexLabel, irStmt, &dummyFromClause);
    if (ret != GMERR_OK) {
        return ret;
    }

    // We have all the entities to build the OpTree
    StreamItemOpTreeCtxT itemCtx = {
        .memCtx = irStmt->memCtx, .schema = &schema, .fromClause = dummyFromClause, .whereExprDepth = 0};
    ret = StreamSqlBuildItemOpTree(&itemCtx, alterStmt->newWhereCond, &tmpExpr, streamVertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    *newWhereIRExpr = tmpExpr;
    return GMERR_OK;
}

Status StreamSqlAnalyzeAlter(SessionT *session, NodeT *parsedStmt, SqlIrStmtT *irStmt)
{
    DB_POINTER3(session, parsedStmt, irStmt);
    Status ret = GMERR_OK;
    SqlAlterStreamStmtT *alterStmt = (SqlAlterStreamStmtT *)parsedStmt;
    DmVertexLabelT *vertexLabel;

    // Grab the vertex label from the catalog and perform some basic tests on the request
    ret = StreamSqlAnalyzeAlterVerify(session, alterStmt, &vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    // Create a new node alter statement and save that as 'utilityStmt' into the SqlIrStmt.
    // This temporary node will be used to convert the sql statement into a logical plan.
    ret = StreamSqlAlterCreateIrStmt(irStmt->memCtx, irStmt, vertexLabel);
    if (ret != GMERR_OK) {
        return ret;
    }

    // Convert the IRStmt (the sql one) into a tree-like logical plan
    IRExprT *newWhereIRExpr = NULL;
    ret = StreamSqlAlterCreateIRExpr(session, alterStmt, irStmt, vertexLabel, &newWhereIRExpr);
    if (ret != GMERR_OK) {
        return ret;
    }

    // Put the logical plan in the output struct. The rest has already been filled!
    ((StreamAlterStmtT *)irStmt->utilityStmt)->newWhereIRExpr = (void *)newWhereIRExpr;

    return GMERR_OK;
}

Status StreamVertifyNodeTypeAndUnionable(
    SqlAlterUnionStmtT *alterUnionStmt, DmVertexLabelT *unionVertex, DmVertexLabelT *vertex)
{
    Status ret = GMERR_OK;
    DmStreamVertexLabelT *streamVertex = (DmStreamVertexLabelT *)vertex;

    // Evaluate the alterUnionStmt nodeType corresponds to the vertex
    if (alterUnionStmt->nodeType == STREAM_SINK) {
        if (streamVertex->streamType != STREAM_LABEL_TYPE_SINK) {
            ret = GMERR_DATATYPE_MISMATCH;
        }
    } else if (alterUnionStmt->nodeType == STREAM_VIEW) {
        if (streamVertex->streamType != STREAM_LABEL_TYPE_VIEW) {
            ret = GMERR_DATATYPE_MISMATCH;
        }
    } else {
        ret = GMERR_DATATYPE_MISMATCH;
    }
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "node Type is wrong");
        return ret;
    }

    // Evaluate if the unionVertex is Unionable
    if (!streamVertex->isUnionable) {
        DB_LOG_ERROR(GMERR_FEATURE_NOT_SUPPORTED, "The node cannot union");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return ret;
}

// Get the vertex from alter union parameter in alterUnionStmt
Status StreamSqlVerifyDBAndGetUnionLabel(SessionT *session, SqlAlterUnionStmtT *alterUnionStmt,
    DmVertexLabelT **unionVertex, DmVertexLabelT **vertex, SqlSrcItemT *src)
{
    DB_POINTER3(session, alterUnionStmt, src);
    Status ret = GMERR_OK;

    char *database = src->type == SRC_TABLE_REF ? src->srcTable->database :
                                                  (src->type == SRC_STREAM_DISPATCH ? src->dispatch->database : NULL);
    char *tableName = src->type == SRC_TABLE_REF ? src->srcTable->tableName :
                                                   (src->type == SRC_STREAM_DISPATCH ? src->dispatch->tableName : NULL);

    DbStrToLower(tableName);

    // Verify vertex and union vertex
    ret = StreamSqlVerifyDBAndGetLabel(session, database, tableName, unionVertex);
    if (ret != GMERR_OK || unionVertex == NULL || *unionVertex == NULL) {
        DB_LOG_ERROR(GMERR_UNDEFINED_TABLE, "Unable to get from name.");
        return GMERR_UNDEFINED_TABLE;
    }

    SqlTableRefT *refName = alterUnionStmt->name;
    DbStrToLower(refName->tableName);
    ret = StreamSqlVerifyDBAndGetLabel(session, refName->database, refName->tableName, vertex);
    if (ret != GMERR_OK || vertex == NULL || *vertex == NULL) {
        DB_LOG_ERROR(GMERR_UNDEFINED_TABLE, "Unable to get node name.");
        return GMERR_UNDEFINED_TABLE;
    }

    // Verify node type and node is unionable
    ret = StreamVertifyNodeTypeAndUnionable(alterUnionStmt, *unionVertex, *vertex);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "NodeType cannot union.");
        return ret;
    }
    return ret;
}

// if has dispatch key, evalute key-hash

// Evaluate if unionVertex is already in the vertex preList.
Status StreamSqlCheckUnionAddState(DmVertexLabelT *unionVertex, DmVertexLabelT *vertex)
{
    Status ret = GMERR_OK;
    DmStreamVertexLabelT *streamVertex = (DmStreamVertexLabelT *)(vertex);
    TagLinkedListT *list = StreamGetPreList(streamVertex);
    DmStreamLinkNodeT *preTempNode = NULL;
    DmStreamLinkNodeT *tmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(preTempNode, tmp, list, node)
    {
        DmStreamVertexLabelT *preVertexLabel = preTempNode->vertexLabel;
        if (DbStrCmp(unionVertex->metaCommon.metaName, preVertexLabel->base.metaCommon.metaName, true) == 0) {
            ret = GMERR_INVALID_PARAMETER_VALUE;
            break;
        }
    }
    return ret;
}

// Evaluate if unionVertex is not in the preList of vertex
Status StreamSqlCheckUnionDropState(DmVertexLabelT *unionVertex, DmVertexLabelT *vertex)
{
    Status ret = GMERR_INVALID_PARAMETER_VALUE;
    DmStreamVertexLabelT *streamVertex = (DmStreamVertexLabelT *)(vertex);
    TagLinkedListT *list = StreamGetPreList(streamVertex);
    DmStreamLinkNodeT *preTempNode = NULL;
    DmStreamLinkNodeT *tmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(preTempNode, tmp, list, node)
    {
        DmStreamVertexLabelT *preVertexLabel = preTempNode->vertexLabel;
        if (DbStrCmp(unionVertex->metaCommon.metaName, preVertexLabel->base.metaCommon.metaName, true) == 0) {
            ret = GMERR_OK;
            break;
        }
    }
    return ret;
}

// The function to check if the union vertex could be added or dropped
Status StreamSqlCheckUnionState(SqlAlterUnionStmtT *alterUnionStmt, DmVertexLabelT *unionVertex, DmVertexLabelT *vertex)
{
    DB_POINTER3(alterUnionStmt, unionVertex, vertex);
    Status ret = GMERR_OK;
    switch (alterUnionStmt->alterType) {
        case STREAM_ALTER_ADD:
            ret = StreamSqlCheckUnionAddState(unionVertex, vertex);
            break;
        case STREAM_ALTER_DROP:
            ret = StreamSqlCheckUnionDropState(unionVertex, vertex);
            break;
        default:
            ret = GMERR_UNDEFINED_OBJECT;
            DB_LOG_AND_SET_LASERR(ret, "Unable to get vertex label.");
    }
    return ret;
}

// The function to compare if the schema is the same
Status StreamCmpSchema(DmSchemaT *schema1, DmSchemaT *schema2)
{
    if (schema1->propeNum != schema2->propeNum) {
        DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "The propertyNum is different.");
        return GMERR_DATATYPE_MISMATCH;
    }
    if (schema1->sysPropeNum != schema2->sysPropeNum) {
        DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "The sysPropeNum is different.");
        return GMERR_DATATYPE_MISMATCH;
    }
    DmPropertySchemaT *prop1 = schema1->properties;
    DmPropertySchemaT *prop2 = schema2->properties;
    Status ret = GMERR_OK;
    uint32_t i = 0;
    for (; i < schema1->propeNum; i++) {
        if (!DmComparePropertySchema(&prop1[i], &prop2[i])) {
            ret = GMERR_DATATYPE_MISMATCH;
            break;
        }
    }
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "The property %" PRIu32 " are different.", i);
    }
    return ret;
}

static inline Status StreamCmpUnionSchemaValid(DmVertexLabelT *unionVertex, DmVertexLabelT *vertex)
{
    DmStreamVertexLabelT *streamVertex = (DmStreamVertexLabelT *)(vertex);
    TagLinkedListT *list = StreamGetPreList(streamVertex);
    DmStreamLinkNodeT *node = LIST_HEAD_ENTRY(list, DmStreamLinkNodeT, node);
    DmStreamVertexLabelT *preVertexLabel = node->vertexLabel;
    return StreamCmpSchema(preVertexLabel->base.metaVertexLabel->schema, unionVertex->metaVertexLabel->schema);
}

static inline uint32_t TagLinkedListCnt(TagLinkedListT *list)
{
    uint32_t count = 0;
    DmStreamLinkNodeT *preTempNode = NULL;
    DmStreamLinkNodeT *tmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(preTempNode, tmp, list, node)
    {
        count++;
    }
    return count;
}

static inline bool CheckUnionLabelNum(DmVertexLabelT *vertex)
{
    DmStreamVertexLabelT *streamVertex = (DmStreamVertexLabelT *)(vertex);
    TagLinkedListT *list = StreamGetPreList(streamVertex);
    return TagLinkedListCnt(list) < STREAM_MAX_UNION_LABEL_NUM;
}

static inline bool CheckUnionVertexNextNum(DmVertexLabelT *vertex)
{
    DmStreamVertexLabelT *streamVertex = (DmStreamVertexLabelT *)(vertex);
    TagLinkedListT *list = StreamGetNextList(streamVertex);
    return TagLinkedListCnt(list) < STREAM_NEXT_MAX;
}

// It must has at least one pre node to avoid become new source node.
static inline bool vertexCannotDrop(DmVertexLabelT *vertex)
{
    DmStreamVertexLabelT *streamVertex = (DmStreamVertexLabelT *)(vertex);
    TagLinkedListT *list = StreamGetPreList(streamVertex);
    return TagLinkedListCnt(list) <= 1;
}

void updatePrePathToSink(TagLinkedListT *list, int32_t n)
{
    if (list == NULL) {
        return;
    }
    DmStreamLinkNodeT *preTempNode = NULL;
    DmStreamLinkNodeT *tmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(preTempNode, tmp, list, node)
    {
        preTempNode->pathToSink += n;
        DmStreamVertexLabelT *preVertexLabel = preTempNode->vertexLabel;
        updatePrePathToSink(StreamGetPreList(preVertexLabel), n);
    }
}

bool updateVertexDepth(DmStreamVertexLabelT *streamVertex)
{
    if (streamVertex == NULL) {
        return true;
    }
    TagLinkedListT *list = StreamGetPreList(streamVertex);
    DmStreamLinkNodeT *preTempNode = NULL;
    DmStreamLinkNodeT *tmp = NULL;
    uint8_t originDepth = streamVertex->depth;
    uint8_t depth = 0;
    LIST_FOR_EACH_ENTRY_SAFE(preTempNode, tmp, list, node)
    {
        DmStreamVertexLabelT *preVertexLabel = preTempNode->vertexLabel;
        if (preVertexLabel != NULL) {
            depth = DB_MAX(depth, preVertexLabel->depth);
        }
    }
    if (depth >= STREAM_DAG_VIEW_DEPTH_MAX) {
        return false;
    }
    streamVertex->depth = depth + 1;
    list = StreamGetNextList(streamVertex);
    if (list == NULL) {
        return true;
    }
    DmStreamLinkNodeT *nxtTempNode = NULL;
    tmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(nxtTempNode, tmp, list, node)
    {
        DmStreamVertexLabelT *nxtVertexLabel = nxtTempNode->vertexLabel;
        if (!updateVertexDepth(nxtVertexLabel)) {
            streamVertex->depth = originDepth;
            return false;
        }
    }
    return true;
}

bool CheckStreamDAG(DmStreamVertexLabelT *originVetex, DmStreamVertexLabelT *vertex)
{
    if (DbStrCmp(vertex->base.metaCommon.metaName, originVetex->base.metaCommon.metaName, true) == 0) {
        return true;
    }
    TagLinkedListT *nxtListNode = StreamGetNextList(vertex);
    if (nxtListNode == NULL) {
        return false;
    }
    DmStreamLinkNodeT *nxtTempNode = NULL;
    DmStreamLinkNodeT *tmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(nxtTempNode, tmp, nxtListNode, node)
    {
        DmStreamVertexLabelT *nxtVertexLabel = nxtTempNode->vertexLabel;
        if (CheckStreamDAG(originVetex, nxtVertexLabel)) {
            return true;
        }
    }
    return false;
}

Status CheckAddNormalDepth(DmStreamVertexLabelT *unionStream, DmStreamVertexLabelT *streamVertex)
{
    TagLinkedListT *list = StreamGetPreList(streamVertex);
    DmStreamLinkNodeT *preTempNode = NULL;
    DmStreamLinkNodeT *tmp = NULL;
    uint8_t originDepth = streamVertex->depth;
    streamVertex->depth = DB_MAX(unionStream->depth + 1, originDepth);
    LIST_FOR_EACH_ENTRY_SAFE(preTempNode, tmp, list, node)
    {
        DmStreamVertexLabelT *preVertex = preTempNode->vertexLabel;
        if (preVertex != NULL) {
            streamVertex->depth = DB_MAX(streamVertex->depth, preVertex->depth + 1);
        }
    }
    if (streamVertex->depth > STREAM_DAG_VIEW_DEPTH_MAX) {
        streamVertex->depth = originDepth;
        DB_LOG_ERROR(GMERR_SEMANTIC_ERROR, "depth exceeds");
        return GMERR_SEMANTIC_ERROR;
    }
    list = StreamGetNextList(streamVertex);
    if (list == NULL) {
        return GMERR_OK;
    }
    DmStreamLinkNodeT *nxtTempNode = NULL;
    tmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(nxtTempNode, tmp, list, node)
    {
        DmStreamVertexLabelT *nxtVertex = nxtTempNode->vertexLabel;
        if (!updateVertexDepth(nxtVertex)) {
            DB_LOG_ERROR(GMERR_SEMANTIC_ERROR, "depth exceeds");
            streamVertex->depth = originDepth;
            return GMERR_SEMANTIC_ERROR;
        }
    }
    return GMERR_OK;
}

Status StreamAddUnion(SqlAlterUnionStmtT *alterUnionStmt, DmVertexLabelT *unionVertex, DmVertexLabelT *vertex)
{
    Status ret = StreamCmpUnionSchemaValid(unionVertex, vertex);
    DmStreamVertexLabelT *unionStream = (DmStreamVertexLabelT *)unionVertex;
    DmStreamVertexLabelT *streamVertex = (DmStreamVertexLabelT *)vertex;
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Different schema to add.");
        return ret;
    }
    if (!CheckUnionLabelNum(vertex)) {
        DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED, "Union vertex exceeds.");
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    if (!CheckUnionVertexNextNum(unionVertex)) {
        DB_LOG_AND_SET_LASERR(GMERR_PROGRAM_LIMIT_EXCEEDED, "Prenode exceeds.");
        return GMERR_PROGRAM_LIMIT_EXCEEDED;
    }
    if (CheckStreamDAG(unionStream, streamVertex)) {
        DB_LOG_AND_SET_LASERR(ret, "Stream will become DAG.");
        return GMERR_SEMANTIC_ERROR;
    }
    ret = CheckAddNormalDepth(unionStream, streamVertex);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "The depth would be exceeds maximum.");
        return GMERR_SEMANTIC_ERROR;
    }
    return ret;
}

Status CheckDropNormalDepth(DmStreamVertexLabelT *unionStream, DmStreamVertexLabelT *streamVertex)
{
    TagLinkedListT *list = StreamGetPreList(streamVertex);
    DmStreamLinkNodeT *preTempNode = NULL;
    DmStreamLinkNodeT *tmp = NULL;
    uint8_t originDepth = streamVertex->depth;
    streamVertex->depth = 0;
    LIST_FOR_EACH_ENTRY_SAFE(preTempNode, tmp, list, node)
    {
        DmStreamVertexLabelT *preVertex = preTempNode->vertexLabel;
        if (preVertex != NULL) {
            if (DbStrCmp(unionStream->base.metaCommon.metaName, preVertex->base.metaCommon.metaName, true) == 0) {
                continue;
            }
            streamVertex->depth = DB_MAX(streamVertex->depth, preVertex->depth + 1);
        }
    }
    if (streamVertex->depth > STREAM_DAG_VIEW_DEPTH_MAX) {
        streamVertex->depth = originDepth;
        DB_LOG_ERROR(GMERR_SEMANTIC_ERROR, "depth exceeds");
        return GMERR_SEMANTIC_ERROR;
    }
    list = StreamGetNextList(streamVertex);
    if (list == NULL) {
        return GMERR_OK;
    }
    DmStreamLinkNodeT *nxtTempNode = NULL;
    tmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(nxtTempNode, tmp, list, node)
    {
        DmStreamVertexLabelT *nxtVertex = nxtTempNode->vertexLabel;
        if (!updateVertexDepth(nxtVertex)) {
            DB_LOG_ERROR(GMERR_SEMANTIC_ERROR, "depth exceeds");
            streamVertex->depth = originDepth;
            return GMERR_SEMANTIC_ERROR;
        }
    }
    return GMERR_OK;
}

Status StreamDropUnion(DmVertexLabelT *unionVertex, DmVertexLabelT *vertex)
{
    if (vertexCannotDrop(vertex)) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "Choose to drop sink.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    DmStreamVertexLabelT *unionStream = (DmStreamVertexLabelT *)unionVertex;
    DmStreamVertexLabelT *streamVertex = (DmStreamVertexLabelT *)vertex;
    Status ret = CheckDropNormalDepth(unionStream, streamVertex);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "The depth has problem");
        return ret;
    }
    return GMERR_OK;
}

Status StreamSqlAnalyzeAlterUnionInner(
    SessionT *session, SqlAlterUnionStmtT *alterUnionStmt, DmVertexLabelT *unionVertex, DmVertexLabelT *vertex)
{
    Status ret = StreamSqlCheckUnionState(alterUnionStmt, unionVertex, vertex);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "Unable to change union state.");
        return ret;
    }

    switch (alterUnionStmt->alterType) {
        case STREAM_ALTER_ADD:
            ret = StreamAddUnion(alterUnionStmt, unionVertex, vertex);
            if (ret != GMERR_OK) {
                return ret;
            }
            break;
        case STREAM_ALTER_DROP:
            ret = StreamDropUnion(unionVertex, vertex);
            break;
    }
    return ret;
}

Status StreamSqlAlterUnionFillOutput(
    StreamAlterTypeE alterType, DmVertexLabelT *vertex, DmVertexLabelT *unionVertex, SqlIrStmtT *irStmt)
{
    DB_POINTER3(vertex, unionVertex, irStmt);
    StreamAlterUnionStmtT *node =
        (StreamAlterUnionStmtT *)DbDynMemCtxAlloc(irStmt->memCtx, sizeof(StreamAlterUnionStmtT));
    if (node == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "defeat to alloc memory for alter");
        return GMERR_OUT_OF_MEMORY;
    }
    node->vertexLabel = vertex;
    node->unionVertex = unionVertex;
    node->alterType = alterType;
    node->node.tag = T_STREAM_ALTER_UNION_STMT;
    irStmt->utilityStmt = (NodeT *)node;
    irStmt->irPlan = NULL;
    return GMERR_OK;
}

DbOamapT *StreamGetDispatchMap(DmStreamVertexLabelT *vertexLabel)
{
    switch (vertexLabel->streamType) {
        case STREAM_LABEL_TYPE_SOURCE:
            return &vertexLabel->streamMeta.sourceMeta.streamDispatch.dispatchMap;
        case STREAM_LABEL_TYPE_VIEW:
            return &vertexLabel->streamMeta.viewMeta.streamDispatch.dispatchMap;
        default:
            return NULL;
    }
}

Status StreamVerifyDispatchAdd(SqlAlterUnionStmtT *alterUnionStmt, DmVertexLabelT *vertex, DmVertexLabelT *unionVertex,
    DmStreamDispatchKeyT *dispatchKey)
{
    Status ret = GMERR_OK;
    DmStreamVertexLabelT *streamVertex = (DmStreamVertexLabelT *)vertex;
    TagLinkedListT *preList = StreamGetPreList(streamVertex);
    DmStreamLinkNodeT *preTempNode = NULL;
    DmStreamLinkNodeT *tmp = NULL;
    bool notRelated = true;

    LIST_FOR_EACH_ENTRY_SAFE(preTempNode, tmp, preList, node)
    {
        DmStreamVertexLabelT *preVertexLabel = preTempNode->vertexLabel;
        if (DbStrCmp(unionVertex->metaCommon.metaName, preVertexLabel->base.metaCommon.metaName, true) == 0) {
            notRelated = false;
            if (preTempNode->dispatchKey == NULL) {
                DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "Cannot match dispatch key");
                return GMERR_DATATYPE_MISMATCH;
            } else {
                if (preTempNode->dispatchKey->keyHash == dispatchKey->keyHash) {
                    DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "already has dispatch relation");
                    return GMERR_DUPLICATE_TABLE;
                }
            }
        }
    }
    if (notRelated) {
        ret = StreamAddUnion(alterUnionStmt, unionVertex, vertex);
    }
    return ret;
}

Status StreamVerifyDispatchDrop(SqlAlterUnionStmtT *alterUnionStmt, DmVertexLabelT *vertex, DmVertexLabelT *unionVertex,
    DmStreamDispatchKeyT *dispatchKey)
{
    DmStreamVertexLabelT *streamVertex = (DmStreamVertexLabelT *)vertex;
    TagLinkedListT *preList = StreamGetPreList(streamVertex);
    DmStreamLinkNodeT *preTempNode = NULL;
    DmStreamLinkNodeT *tmp = NULL;
    LIST_FOR_EACH_ENTRY_SAFE(preTempNode, tmp, preList, node)
    {
        DmStreamVertexLabelT *preVertexLabel = preTempNode->vertexLabel;
        if (DbStrCmp(unionVertex->metaCommon.metaName, preVertexLabel->base.metaCommon.metaName, true) == 0) {
            if (preTempNode->dispatchKey == NULL) {
                DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "Cannot match dispatch key");
                return GMERR_DATATYPE_MISMATCH;
            } else {
                if (preTempNode->dispatchKey->keyHash == dispatchKey->keyHash) {
                    return GMERR_OK;
                }
            }
        }
    }
    DB_LOG_AND_SET_LASERR(GMERR_DATATYPE_MISMATCH, "Cannot find dispatch key");
    return GMERR_DATATYPE_MISMATCH;
}

Status StreamSqlGetDispatchKey(SessionT *session, SqlSrcItemT *src, DmStreamDispatchKeyT **dispatchKey)
{
    Status ret = GMERR_OK;
    DB_POINTER2(session, src);
    char *tableName = NULL;
    ret = StreamGetLabelName(src, &tableName);
    if (ret != GMERR_OK) {
        return ret;
    }
    src->label = StreamSqlGetVertexLabelByName(session->dbId, session->namespaceId, tableName);
    if (src->label == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNDEFINED_TABLE, "no such table: %s", tableName);
        return GMERR_UNDEFINED_TABLE;
    }
    ret = StreamVerifyFromLabel(session->memCtx, src);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "rule cannot be supported.");
        return ret;
    }
    DmStreamVertexLabelT *streamLabel = (DmStreamVertexLabelT *)src->label;
    ret = CheckAndGetDispatchKey(g_parentStreamTopoCtx, src, streamLabel, dispatchKey);
    if (ret != GMERR_OK) {
        DB_LOG_AND_SET_LASERR(ret, "cannot get dispatch key");
        return ret;
    }
    return ret;
}

Status StreamSqlFillDispatchOutput(SqlAlterUnionStmtT *alterUnionStmt, DmVertexLabelT *vertex,
    DmVertexLabelT *unionVertex, DmStreamDispatchKeyT *dispatchKey, SqlIrStmtT *irStmt)
{
    Status ret = GMERR_OK;
    DB_POINTER3(vertex, unionVertex, irStmt);
    StreamAlterUnionDispatchStmtT *node =
        (StreamAlterUnionDispatchStmtT *)DbDynMemCtxAlloc(irStmt->memCtx, sizeof(StreamAlterUnionDispatchStmtT));
    if (node == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "suffer a defeat to alloc memory for alter union stmt node");
        return GMERR_OUT_OF_MEMORY;
    }
    node->vertexLabel = vertex;
    node->unionVertex = unionVertex;
    node->dispatchKey = dispatchKey;
    node->alterType = alterUnionStmt->alterType;
    node->node.tag = T_STREAM_ALTER_UNION_DISPATCH_STMT;
    irStmt->utilityStmt = (NodeT *)node;
    irStmt->irPlan = NULL;
    return GMERR_OK;
    return ret;
}

Status StreamSqlAnalyzeAlterDispatchInner(SqlAlterUnionStmtT *alterUnionStmt, DmVertexLabelT *unionVertex,
    DmVertexLabelT *vertex, DmStreamDispatchKeyT *dispatchKey, SqlIrStmtT *irStmt)
{
    Status ret = GMERR_OK;
    switch (alterUnionStmt->alterType) {
        case STREAM_ALTER_ADD:
            ret = StreamVerifyDispatchAdd(alterUnionStmt, vertex, unionVertex, dispatchKey);
            if (ret != GMERR_OK) {
                return ret;
            }
            break;
        case STREAM_ALTER_DROP:
            ret = StreamVerifyDispatchDrop(alterUnionStmt, vertex, unionVertex, dispatchKey);
            if (ret != GMERR_OK) {
                return ret;
            }
            break;
        default:
            ret = GMERR_SEMANTIC_ERROR;
    }
    ret = StreamSqlFillDispatchOutput(alterUnionStmt, vertex, unionVertex, dispatchKey, irStmt);
    return ret;
}

Status StreamSqlAnalyzeAlterUnion(SessionT *session, NodeT *parsedStmt, SqlIrStmtT *irStmt)
{
    DB_POINTER3(session, parsedStmt, irStmt);
    Status ret = GMERR_OK;
    SqlAlterUnionStmtT *alterUnionStmt = (SqlAlterUnionStmtT *)parsedStmt;
    DmVertexLabelT *vertex = NULL;
    DmVertexLabelT *unionVertex = NULL;

    uint32_t unionCnt = DbListGetItemCnt(alterUnionStmt->fromList);
    if (unionCnt != 1) {
        DB_LOG_AND_SET_LASERR(ret, "unsupported multiple union.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }

    for (uint32_t i = 0; i < unionCnt; i++) {
        SqlSrcItemT *src = *(SqlSrcItemT **)DbListItem(alterUnionStmt->fromList, i);
        ret = StreamSqlVerifyDBAndGetUnionLabel(session, alterUnionStmt, &unionVertex, &vertex, src);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "verify vertex label.");
            return ret;
        }
        DmStreamDispatchKeyT *dispatchKey = NULL;
        switch (src->type) {
            case SRC_TABLE_REF:
                ret = StreamSqlAnalyzeAlterUnionInner(session, alterUnionStmt, unionVertex, vertex);
                if (ret != GMERR_OK) {
                    return ret;
                }
                ret = StreamSqlAlterUnionFillOutput(alterUnionStmt->alterType, vertex, unionVertex, irStmt);
                break;
            case SRC_STREAM_DISPATCH:
                ret = StreamSqlGetDispatchKey(session, src, &dispatchKey);
                if (ret != GMERR_OK) {
                    return ret;
                }
                ret = StreamSqlAnalyzeAlterDispatchInner(alterUnionStmt, unionVertex, vertex, dispatchKey, irStmt);
                break;
            default:
                return GMERR_SEMANTIC_ERROR;
        }
    }
    return ret;
}
