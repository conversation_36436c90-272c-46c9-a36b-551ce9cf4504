/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2022. All rights reserved.
 * File Name: tool_utils.c
 * Description: public interface for tools
 * Author:chenjunyu
 * Create: 2020-9-20
 */
#include "tool_utils.h"
#include <syslog.h>
#include <limits.h>
#include <dlfcn.h>
#include <unistd.h>
#include <ctype.h>
#include "adpt_openssl.h"
#include "clt_stmt.h"
#include "db_file.h"
#include "db_option_parser.h"
#include "db_signal.h"
#include "adpt_string.h"
#include "gmc.h"
#include "tool_interactive.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define BACKUP_CONF_FILE_MAX_SIZE 900000

bool g_gmdbCltSameProcessDeploy = true;

bool TlsGetCltSameProcessDeploy(void)
{
    return g_gmdbCltSameProcessDeploy;
}

void TlsSetCltSameProcessDeploy(bool flag)
{
    g_gmdbCltSameProcessDeploy = flag;
}

Status TlsPerfEnvInit(void)
{
    Status ret = GmcInit();
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "Client init unsucc. ret = %" PRId32 "\n", (int32_t)ret);
        return ret;
    }
    // pipe信号是在pipe broken的时候产生，独立的工具进程期望忽略该信号。
    // 共进程模式下，工具不接管信号
    if (!TlsGetCltSameProcessDeploy()) {
        (void)DbSignalRegister(SIGPIPE, SIG_IGN, DB_SIG_FLAG_NODEFER);
    }
    return GMERR_OK;
}

void TlsPerfEnvUnInit(void)
{
    Status ret = GmcUnInit();
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "Client uninit unsucc. ret = %" PRId32, (int32_t)ret);
    }
}

bool DbGetUseReservedConn(DbOptionRuleT *optionRule)
{
    DB_POINTER(optionRule);
    return DbCheckIsUsedByOptionName(optionRule, DB_OPTION_ARG_USE_RESERVED_CONN);
}

Status TlsConnect(GmcConnT **conn, GmcStmtT **stmt, const TlsConnOptionT *option)
{
    DB_POINTER3(conn, stmt, option);
    GmcConnOptionsT *connOptions = NULL;
    Status ret = GmcConnOptionsCreate(&connOptions);
    if (ret == GMERR_OK) {
        ret = GmcConnOptionsSetServerLocator(connOptions, option->domainName);
    }

    if (ret == GMERR_OK && option->userName != NULL && strlen(option->userName) != 0) {
        ret = GmcConnOptionsSetUserName(connOptions, option->userName);
    }

    if (ret == GMERR_OK) {
        ret = GmcConnOptionsSetReservedFlag(connOptions, option->useReservedConn);
    }
#ifdef FEATURE_TS
    if (ret == GMERR_OK && option->msgReadTimeoutMs != 0) {
        ret = GmcConnOptionsSetMsgReadTimeout(connOptions, option->msgReadTimeoutMs);
    }
    if (ret == GMERR_OK && option->isCsMode) {
        ret = GmcConnOptionsSetCSMode(connOptions);
    }
#endif
    if (ret == GMERR_OK) {
        if (option->noMemLimit) {
            connOptions->connMemCtxLimit = 0;
        }
        ret = GmcConnect(GMC_CONN_TYPE_SYNC, connOptions, conn);
    }
    GmcConnOptionsDestroy(connOptions);
    if (ret != GMERR_OK) {
        *conn = NULL;
        DB_LOG_ERROR(ret, "Connect server. ret = %" PRId32, (int32_t)ret);
        return ret;
    }

    ret = GmcAllocStmt(*conn, stmt);
    if (ret != GMERR_OK) {
        (void)GmcDisconnect(*conn);
        *conn = NULL;
        DB_LOG_ERROR(ret, "Alloc stmt. ret = %" PRId32, (int32_t)ret);
    }
    return ret;
}

void TlsDisconnect(GmcConnT *conn, GmcStmtT *stmt)
{
    if (stmt != NULL) {
        GmcFreeStmt(stmt);
    }
    if (conn != NULL) {
        Status ret = GmcDisconnect(conn);
        if (ret != GMERR_OK) {
            TOOL_RUN_ERROR(DbPrintfDefault, ret, "Disconnect server. ret = %" PRId32, (int32_t)ret);
        }
    }
}

Status TlsUseNameSpace(GmcStmtT *stmt, const char *namespaceName)
{
    DB_POINTER2(stmt, namespaceName);
    if (strlen(namespaceName) == 0) {
        return GMERR_OK;
    }
    return GmcUseNamespace(stmt, namespaceName);
}

void TlsGetLastErrorFromServer(Status errorCode)
{
    const char *lastError = GmcGetLastError();
    if (strlen(lastError) != 0) {
        DB_LOG_ERROR((int32_t)errorCode, "last wrong: %s", lastError);
    }
}

Status TlsGetLastError(Status errorCode, const char **lastError)
{
    DB_POINTER(lastError);
    *lastError = GmcGetLastError();
    if (*lastError == NULL || strlen(*lastError) == 0) {
        return GMERR_NO_DATA;
    }
    return GMERR_OK;
}

Status TlsFormatAndCheckFileName(char *filePath, uint32_t maxFilePathSize)
{
    DB_POINTER(filePath);
    char realFilePath[PATH_MAX] = {0};
    Status ret = DbGetRealPath(filePath, realFilePath, PATH_MAX);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "get realpath from %s unsucc. ret = %" PRId32, filePath, (int32_t)ret);
        return ret;
    }
    if (!DbFileExist(realFilePath) && !DbDirExist(realFilePath)) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_FILE_OPERATE_FAILED,
            "no such file or directory: %s. ret = %" PRId32, realFilePath, (int32_t)GMERR_FILE_OPERATE_FAILED);
        return GMERR_FILE_OPERATE_FAILED;
    }
    if (DbAdptAccess(realFilePath, R_OK) != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_FILE_OPERATE_FAILED, "can not access file: %s. ret = %" PRId32,
            realFilePath, (int32_t)GMERR_FILE_OPERATE_FAILED);
        return GMERR_FILE_OPERATE_FAILED;
    }
    (void)memset_s(filePath, maxFilePathSize, 0, maxFilePathSize);
    if (strcpy_s(filePath, maxFilePathSize, realFilePath) != EOK) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_FIELD_OVERFLOW,
            "strcpy_s unsucc, the absolute path len is %zu. ret = %" PRId32, strlen(realFilePath),
            (int32_t)GMERR_FIELD_OVERFLOW);
        return GMERR_FIELD_OVERFLOW;
    }
    if (filePath[strlen(realFilePath) - 1] == '/') {
        filePath[strlen(realFilePath) - 1] = 0;
    }
    return GMERR_OK;
}

bool TlsCheckFileSuffix(const char *filePath, const char *matchStr)
{
    DB_POINTER2(filePath, matchStr);
    char tempPath[PATH_MAX] = {0};
    errno_t err = strcpy_s(tempPath, sizeof(tempPath), filePath);
    if (err != EOK) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_FIELD_OVERFLOW, "strcpy_s filePath: %s unsucc.", filePath);
        return false;
    }
    const char *suffix = DbGetFileNameInfo(tempPath, sizeof(tempPath), DB_FILENAME_SUFFIX);
    if (suffix == NULL) {
        TOOL_RUN_INFO(DbPrintfDefault, "get file suffix unsucc, filePath: %s.", filePath);
        return false;
    }
    if (strcmp(suffix, matchStr) == 0) {
        return true;
    }
    return false;
}

Status TlsReadConfFile(DbMemCtxT *memCtx, char *filePath, char **fileBuff, uint32_t *fileSize)
{
    int32_t fd = DB_INVALID_FD;
    size_t fileLen;
    char *buff = NULL;

    Status ret = DbOpenFile(filePath, READ_ONLY, PERM_USER, &fd);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_FILE_OPERATE_FAILED, "Open file unsucc when read cfg file.");
        return GMERR_FILE_OPERATE_FAILED;
    }
    do {
        ret = DbFileSize(filePath, &fileLen);
        if (ret != GMERR_OK) {
            TOOL_RUN_ERROR(DbPrintfDefault, ret, "Get file size of %s unsucc. ret = %" PRId32, filePath, ret);
            break;
        }
        if (fileLen > BACKUP_CONF_FILE_MAX_SIZE) {
            TOOL_RUN_ERROR(DbPrintfDefault, GMERR_PROGRAM_LIMIT_EXCEEDED,
                "backup file exceed file max size limit. ret = %" PRId32 ". file size: %" PRId32
                ", FILE_SIZE_LIMIT: %" PRId32,
                GMERR_PROGRAM_LIMIT_EXCEEDED, (int32_t)fileLen, BACKUP_CONF_FILE_MAX_SIZE);
            ret = GMERR_PROGRAM_LIMIT_EXCEEDED;
            break;
        }
        size_t buffSize = sizeof(char) * (fileLen + 1);
        buff = DbDynMemCtxAlloc(memCtx, (size_t)buffSize);
        if (buff == NULL) {
            ret = GMERR_OUT_OF_MEMORY;
            DB_LOG_ERROR(ret, "Db malloc mem when read cfg file.");
            break;
        }
        ret = DbReadFileExpSize(fd, (char *)buff, fileLen);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Read file's expsize unsucc when read cfg file, file_name=%s.", filePath);
            // 局部变量，外部函数无法获得，未置NULL，无UAF风险
            DbDynMemCtxFree(memCtx, buff);
            buff = NULL;
            break;
        }
        buff[fileLen] = '\0';
    } while (0);
    DbCloseFile(fd);
    *fileSize = (uint32_t)fileLen;
    *fileBuff = buff;
    return ret;
}

bool NeedContinueQueryTips(DbPrintFunc printFunc)
{
    DB_POINTER(printFunc);
    char res;
    bool isContinue = false;
    do {
        printFunc("Continue displaying the rest of the content?[y/n] ");
        res = (char)(getchar());
        // 清除输入缓冲区
        while (getchar() != '\n') {
            continue;
        }
        isContinue = (res == 'y');
    } while (res != 'y' && res != 'n');
    return isContinue;
}

Status SysviewConvertVertexToStrNormal(
    GmcStmtT *stmt, DbPrintFunc printFunc, const FormatInfoT *fmtInfo, const DmVertexT *vertex, const uint32_t i)
{
    DB_POINTER2(stmt, vertex);
    uint32_t strLength = 0;
    Status ret = DmVertexGetPrintLength(vertex, fmtInfo, &strLength);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(printFunc, ret, "sysview parser record unsucc");
        return ret;
    }
    char *replyObjectText = DbDynMemCtxAlloc(stmt->memCtx, strLength);
    if (replyObjectText == NULL) {
        TOOL_RUN_ERROR(printFunc, GMERR_OUT_OF_MEMORY, "sysview string memory unsucc. size: %" PRIu32 "", strLength);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(replyObjectText, strLength, 0, strLength);
    // DmVertexPrint 接口需要借用当前的memCtx来申请内存
    DbMemCtxT *oldMemCtx = DbMemCtxSwitchTo(stmt->memCtx);
    ret = DmVertexPrint(vertex, strLength, replyObjectText, fmtInfo);
    if (oldMemCtx == NULL) {
        DbSetCurrMemCtxToNull();
    } else {
        (void)DbMemCtxSwitchTo(oldMemCtx);
    }
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(printFunc, ret, "sysview parser record unsucc");
        // 局部变量，外部函数无法获得，未置NULL，无UAF风险
        DbDynMemCtxFree(stmt->memCtx, replyObjectText);
        return ret;
    }
    errno_t error = memset_s(replyObjectText, strLength, '-', PRINT_HEAD_LINE);
    if (error != EOK) {
        TOOL_RUN_ERROR(printFunc, ret, "memset vertex string head line unsucc");
        // 局部变量，外部函数无法获得，未置NULL，无UAF风险
        DbDynMemCtxFree(stmt->memCtx, replyObjectText);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    TOOL_RUN_INFO(printFunc, "index = %" PRIu32 "", i);
    TOOL_RUN_INFO(printFunc, "%s", replyObjectText);
    // 局部变量，外部函数无法获得，未置NULL，无UAF风险
    DbDynMemCtxFree(stmt->memCtx, replyObjectText);

    return GMERR_OK;
}

Status SysviewConvertVertexToStrJson(GmcStmtT *stmt, DbPrintFunc printFunc, const DmVertexT *vertex, const uint32_t i)
{
    DB_POINTER2(stmt, vertex);
    char *jsonObjText;
    Status ret = DmVertexCreateJsonStr(stmt->memCtx, vertex, DM_JSON_VIEW_PRINT_FLAG, NULL, &jsonObjText, false);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(printFunc, ret, "sysview parser record unsucc");
        return ret;
    }
    TOOL_RUN_INFO(printFunc, "index = %" PRIu32 "", i);
    TOOL_RUN_INFO(printFunc, "%s", jsonObjText);
    // 局部变量，外部函数无法获得，未置NULL，无UAF风险
    DbDynMemCtxFree(stmt->memCtx, jsonObjText);
    return GMERR_OK;
}

Status SysviewConvertVertexToStrFlat(
    GmcStmtT *stmt, DbPrintFunc printFunc, const DmVertexT *vertex, const uint32_t i, bool isFull)
{
    DB_POINTER2(stmt, vertex);
    uint32_t strLength = 0;
    Status ret = DmVertexGetFlatPrintLength(vertex, &strLength, isFull);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(printFunc, ret, "sysview parser record unsucc when get sysview flat");
        return ret;
    }
    char *replyObjectText = DbDynMemCtxAlloc(stmt->memCtx, strLength);
    if (replyObjectText == NULL) {
        TOOL_RUN_ERROR(printFunc, GMERR_OUT_OF_MEMORY, "sysview string memory unsucc. size: %" PRIu32, strLength);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(replyObjectText, strLength, 0, strLength);
    if (i == 0) {
        ret = DmVertexPrintSchemaFlat(vertex, strLength, isFull, replyObjectText);
        if (ret != GMERR_OK) {
            // 局部变量，外部函数无法获得，未置NULL，无UAF风险
            DbDynMemCtxFree(stmt->memCtx, replyObjectText);
            return ret;
        }
        TOOL_RUN_INFO(printFunc, "%s", replyObjectText);
        (void)memset_s(replyObjectText, strLength, 0, strLength);
    }
    ret = DmVertexPrintFlat(vertex, strLength, isFull, replyObjectText);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(printFunc, ret, "sysview parser record unsucc");
        // 局部变量，外部函数无法获得，未置NULL，无UAF风险
        DbDynMemCtxFree(stmt->memCtx, replyObjectText);
        return ret;
    }
    TOOL_RUN_INFO(printFunc, "%s", replyObjectText);
    // 局部变量，外部函数无法获得，未置NULL，无UAF风险
    DbDynMemCtxFree(stmt->memCtx, replyObjectText);
    return GMERR_OK;
}

Status ConvertVertexToStr(
    GmcStmtT *stmt, DbPrintFunc printFunc, DmPrintFmtInfoT printFmtInfo, const DmVertexT *vertex, const uint32_t i)
{
    DB_POINTER2(stmt, vertex);
    DmPrintFlatE fmtType = printFmtInfo.fmtType;

    if (fmtType == PRINT_NORMAL) {
        return SysviewConvertVertexToStrNormal(stmt, printFunc, printFmtInfo.fmtInfo, vertex, i);
    } else if (fmtType == PRINT_FLAT_TRUNCATE) {
        return SysviewConvertVertexToStrFlat(stmt, printFunc, vertex, i, false);
    } else if (fmtType == PRINT_FLAT_FULL) {
        return SysviewConvertVertexToStrFlat(stmt, printFunc, vertex, i, true);
    } else if (fmtType == PRINT_JSON) {
        return SysviewConvertVertexToStrJson(stmt, printFunc, vertex, i);
    } else {
        return GMERR_DATA_EXCEPTION;
    }
}

inline static bool GmLogTmStmpCharIsSameType(char src, char dst)
{
    return isdigit(src) ? isdigit(dst) : dst == src;
}

bool TlsCheckTimestampPattern(const char *tmStamp, const char *pattern)
{
    DB_POINTER2(tmStamp, pattern);
    for (uint32_t i = 0; i < DB_LOG_TIMESTAMP_LEN; i++) {
        if (!GmLogTmStmpCharIsSameType(pattern[i], tmStamp[i])) {
            return false;
        }
    }
    return true;
}

Status TlsCheckKeyComplexity(const char *key, uint32_t keyLen)
{
    DB_POINTER(key);
    if (keyLen < HMAC_KEY_LEN_MIN || keyLen > HMAC_KEY_LEN_MAX) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    uint32_t keyComplexity = 0;
    for (uint32_t i = 0; i < keyLen; i++) {
        char c = key[i];
        if (IsLower(c)) {  // 包含a-z
            keyComplexity |= 0b0001;
        } else if (IsUpper(c)) {  // 包含A-Z
            keyComplexity |= 0b0010;
        } else if (IsNumChar(c)) {  // 包含0-9
            keyComplexity |= 0b0100;
        } else if (ispunct(c) || (c == ' ')) {  // 包含特殊字符
            keyComplexity |= 0b1000;
        } else {
            return GMERR_INVALID_PARAMETER_VALUE;
        }
    }
    uint16_t n = 0;
    while (keyComplexity != 0) {
        keyComplexity &= (keyComplexity - 1);
        n++;
    }
    // 至少包含2种字符
    return n >= 2 ? GMERR_OK : GMERR_INVALID_PARAMETER_VALUE;
}

Status TlsSecureRandBytes(uint8_t *buff, uint32_t buffLen)
{
    DB_POINTER(buff);
    int32_t fd = -1;
    Status ret = DbOpenFile(PBKDF2_RANDOM_DEV_PATH, O_RDONLY, O_RDONLY, &fd);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_FILE_OPERATE_FAILED, "[tools] transform to open file /dev/random.");
        return GMERR_FILE_OPERATE_FAILED;
    }
    size_t nread = 0;
    do {
        size_t tmpCnt = 0;
        ret = DbReadFile(fd, buff + nread, buffLen - nread, &tmpCnt);
        nread += tmpCnt;
        if (ret != GMERR_OK) {
            DbCloseFile(fd);
            TOOL_RUN_ERROR(DbPrintfDefault, GMERR_FILE_OPERATE_FAILED,
                "try read %" PRIu32 " bytes, while actually read %" PRIu32 " bytes from /dev/random.", buffLen,
                (uint32_t)nread);
            return GMERR_FILE_OPERATE_FAILED;
        }
    } while (nread != buffLen);
    DbCloseFile(fd);
    return GMERR_OK;
}

Status TlsGetFileHmacBySalt(
    const char *fileBuff, size_t fileBuffLen, TextT userKey, HmacDataT *hmacData, const uint8_t *salt)
{
    DB_POINTER3(fileBuff, hmacData, salt);
    uint8_t pkOut[PBKDF2_SHA256_KEY_LEN] = {0};
    int pbkdfRet = DbPKCS5PBKDF2HMac(userKey.str, (int)userKey.len, salt, (int)hmacData->saltLen,
        PBKDF2_SHA256_ITERATION_MIN, DbEVPSha256(), PBKDF2_SHA256_KEY_LEN, pkOut);
    if (pbkdfRet == 0) {
        return GMERR_INTERNAL_ERROR;
    }
    uint8_t *hmac = (uint8_t *)DbHMAC(DbEVPSha256(), pkOut, PBKDF2_SHA256_KEY_LEN, (const uint8_t *)fileBuff,
        fileBuffLen, hmacData->data, &hmacData->dataLen);
    if (hmac == NULL) {
        return GMERR_INTERNAL_ERROR;
    }
    return GMERR_OK;
}

Status TlsCalculateFileHmac(const char *fileBuff, size_t fileBuffLen, char *key, uint32_t keyLen, HmacDataT *hmacData)
{
    DB_POINTER3(fileBuff, key, hmacData);

    hmacData->saltLen = SALT_VALUE_LEN;
    Status ret = TlsSecureRandBytes(hmacData->salt, hmacData->saltLen);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "get built in salt value unsuccessful.");
        return ret;
    }
    TextT userKey = {keyLen, key};
    return TlsGetFileHmacBySalt(fileBuff, fileBuffLen, userKey, hmacData, hmacData->salt);
}

Status TlsLoadFile(const char *filePath, char **fileBuff, size_t *buffLen)
{
    DB_POINTER3(filePath, fileBuff, buffLen);
    size_t fileSize = 0;
    Status ret = DbFileSize(filePath, &fileSize);
    if (ret != GMERR_OK || fileSize >= MAX_CONFIG_FILE_SIZE) {
        return GMERR_FILE_OPERATE_FAILED;
    }
    char *buff = (char *)DB_MALLOC((uint32_t)fileSize);
    if (buff == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Db malloc mem.");
        return GMERR_OUT_OF_MEMORY;
    }
    int32_t fd = DB_INVALID_FD;
    ret = DbOpenFile(filePath, READ_ONLY, PERM_USER, &fd);
    if (ret != GMERR_OK) {
        DB_FREE(buff);
        DB_LOG_ERROR(ret, "Db open file. file_name=%s, os: %" PRId32 ".", filePath, DbAptGetErrno());
        return ret;
    }
    size_t readLen = 0;
    ret = DbReadFile(fd, buff, fileSize, &readLen);
    if (ret != GMERR_OK) {
        DB_FREE(buff);
        DbCloseFile(fd);
        DB_LOG_ERROR(ret, "Db read file, name=%s.", filePath);
        return ret;
    }
    *fileBuff = buff;
    *buffLen = readLen;
    DbCloseFile(fd);
    return GMERR_OK;
}

void TlsUnloadFile(char **fileBuff)
{
    if (*fileBuff != NULL) {
        DB_FREE(*fileBuff);
    }
}

Status TlsCalculateFileHmacByName(const char *filePath, char *key, uint32_t keyLen, HmacDataT *hmacData)
{
    char *fileBuff = NULL;
    size_t fileBuffLen = 0;
    Status ret = TlsLoadFile(filePath, &fileBuff, &fileBuffLen);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "Load file unsucc.");
        return ret;
    }

    ret = TlsCalculateFileHmac(fileBuff, fileBuffLen, key, keyLen, hmacData);
    TlsUnloadFile(&fileBuff);
    return ret;
}

Status TlsVerifyFileHmac(const char *filePath, char *key, uint32_t keyLen, const HmacDataT *hmacData)
{
    char *fileBuff = NULL;
    size_t fileBuffLen = 0;
    Status ret = TlsLoadFile(filePath, &fileBuff, &fileBuffLen);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "Generate file with HMAC unsucc.");
        return ret;
    }
    ret = TlsVerifyFileHmacByBuff(fileBuff, fileBuffLen, key, keyLen, hmacData);
    TlsUnloadFile(&fileBuff);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "Verify file's HMAC unsucc.");
        return ret;
    }

    return GMERR_OK;
}

Status TlsVerifyFileHmacByBuff(
    const char *fileBuff, size_t fileBuffLen, char *key, uint32_t keyLen, const HmacDataT *hmacData)
{
    HmacDataT fileHmac = {0};
    // 传入保存的salt
    fileHmac.saltLen = hmacData->saltLen;
    TextT userKey = {keyLen, key};
    Status ret = TlsGetFileHmacBySalt(fileBuff, fileBuffLen, userKey, &fileHmac, hmacData->salt);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (fileHmac.dataLen != hmacData->dataLen || memcmp(fileHmac.data, hmacData->data, fileHmac.dataLen) != 0) {
        return GMERR_DATA_EXCEPTION;
    }

    return GMERR_OK;
}

static Status GenerateHmacBuff(char *fileBuff, uint32_t fileLen, HmacDataT hmacData, char *bkupBuff)
{
    uint32_t bkfBuffHeadLen = BACKUP_HEAD_SIZE_HMAC + HMAC_LEN_BYTE + HMAC_LEN_BYTE;
    int32_t sfRet = snprintf_s(bkupBuff, bkfBuffHeadLen + 1, bkfBuffHeadLen, HMAC_FILE_HEADER_FORMAT, (char)HMAC_TYPE,
        hmacData.saltLen + hmacData.dataLen, fileLen, hmacData.saltLen, hmacData.dataLen);
    if (sfRet < 0) {
        return GMERR_FIELD_OVERFLOW;
    }
    errno_t err = memcpy_s(bkupBuff + bkfBuffHeadLen, hmacData.saltLen, hmacData.salt, hmacData.saltLen);
    if (err != 0) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    bkfBuffHeadLen += hmacData.saltLen;

    err = memcpy_s(bkupBuff + bkfBuffHeadLen, hmacData.dataLen, hmacData.data, hmacData.dataLen);
    if (err != 0) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    bkfBuffHeadLen += hmacData.dataLen + BUFF_END_LEN;

    err = memcpy_s(bkupBuff + bkfBuffHeadLen, sizeof(char), "\n", 1);
    if (err != 0) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    if (fileLen > 0) {
        err = memcpy_s(bkupBuff + bkfBuffHeadLen + 1, fileLen, fileBuff, fileLen);
        if (err != 0) {
            return GMERR_MEMORY_OPERATE_FAILED;
        }
    }
    return GMERR_OK;
}

/*
 * Backup File Struct: Header | saltLen | dataLen | Check Code1 (Hmac Salt) | Check Code2 (Hmac Data) | \n |Config File
 * Header(9 bytes): checkType 1byte[0,1], HmacCodeLen 2btye, FileLen 6byte(uint32)
 */
Status PutHmac2File(char *fileBuff, uint32_t fileLen, HmacDataT hmacData, char **backupFileBuff, size_t *backupBuffSize)
{
    DB_POINTER3(fileBuff, backupFileBuff, backupBuffSize);
    size_t bkupBuffLen = sizeof(char) * (BACKUP_HEAD_SIZE_HMAC + HMAC_LEN_BYTE + HMAC_LEN_BYTE + hmacData.saltLen +
                                            hmacData.dataLen + BUFF_END_LEN + 1 + fileLen);
    char *bkupBuff = DB_MALLOC((uint32_t)bkupBuffLen + HMAC_CODE_LEN_RESERVE);
    if (bkupBuff == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }

    Status ret = GenerateHmacBuff(fileBuff, fileLen, hmacData, bkupBuff);
    if (ret != GMERR_OK) {
        DB_FREE(bkupBuff);
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "Generate file with HMAC unsucc.");
        return ret;
    }

    *backupBuffSize = bkupBuffLen;
    *backupFileBuff = bkupBuff;
    return GMERR_OK;
}

static Status GetValueFromHmacHeader(char *buff, size_t valueSize, const uint32_t *value)
{
    char strBuff[CONF_HEADER_SIZE + HMAC_CODE_MAX_SIZE] = {0};
    errno_t secRet = strncpy_s(strBuff, CONF_HEADER_SIZE + HMAC_CODE_MAX_SIZE, buff, valueSize);
    if (secRet != EOK) {
        return GMERR_FIELD_OVERFLOW;
    }
    secRet = sscanf_s(strBuff, "%" PRIu32 "", value);
    if (secRet == -1) {
        return GMERR_FIELD_OVERFLOW;
    }
    return GMERR_OK;
}

Status GetHmacInFileBuff(char *fileBuff, uint32_t fileLen, HmacDataT *hmacData, uint32_t *hmacLen)
{
    DB_POINTER(fileBuff);
    if (fileLen < CONF_HEADER_SIZE || fileBuff[0] != HMAC_TYPE) {
        return GMERR_DATA_CORRUPTED;
    }
    HmacDataT hmacDataInFile = {0};
    uint32_t buffCursor = CONF_HEADER_SIZE;
    // get hmacData len(saltLen & dataLen)
    Status ret = GetValueFromHmacHeader(fileBuff + buffCursor, CODE_LEN_SIZE, &hmacDataInFile.saltLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    buffCursor += CODE_LEN_SIZE;
    ret = GetValueFromHmacHeader(fileBuff + buffCursor, CODE_LEN_SIZE, &hmacDataInFile.dataLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    buffCursor += CODE_LEN_SIZE;
    if (hmacDataInFile.saltLen > SALT_VALUE_LEN || hmacDataInFile.dataLen > HMAC_DATA_LEN_MAX ||
        fileLen < CONF_HEADER_SIZE + hmacDataInFile.saltLen + hmacDataInFile.dataLen + 1) {
        return GMERR_DATA_CORRUPTED;
    }
    // get Hmac code(salt & data)
    errno_t err = memcpy_s(hmacDataInFile.salt, SALT_VALUE_LEN, fileBuff + buffCursor, hmacDataInFile.saltLen);
    if (err != 0) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    buffCursor += hmacDataInFile.saltLen;
    err = memcpy_s(hmacDataInFile.data, HMAC_DATA_LEN_MAX, fileBuff + buffCursor, hmacDataInFile.dataLen);
    if (err != 0) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    buffCursor += hmacDataInFile.dataLen + CODE_LEN_SIZE + 1;
    *hmacData = hmacDataInFile;
    *hmacLen = buffCursor;
    return GMERR_OK;
}

Status GetHmacInFile(const char *backupFilePath, HmacDataT *hmac, uint32_t *hmacLen)
{
    DB_POINTER2(backupFilePath, hmac);
    char *backupFileBuff = NULL;
    size_t backupFileBuffLen = 0;
    Status ret = TlsLoadFile(backupFilePath, &backupFileBuff, &backupFileBuffLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GetHmacInFileBuff(backupFileBuff, (uint32_t)backupFileBuffLen, hmac, hmacLen);
    TlsUnloadFile(&backupFileBuff);
    return ret;
}

static bool StartsWithFrom(const TextT *source, const TextT *prefix, uint32_t offset)
{
    DB_POINTER2(source, prefix);
    if (prefix->len > source->len || prefix->len + offset > source->len) {
        return false;
    }
    for (uint32_t i = 0; i < source->len - offset; i++) {
        if (source->str[i + offset] != prefix->str[i]) {
            return false;
        }
    }
    return true;
}

static bool EndsWith(const TextT *source, const TextT *suffix)
{
    return StartsWithFrom(source, suffix, source->len - suffix->len);
}

static Status BuildFullPath(
    char *fullPath, size_t fullPathLen, const TextT *filePath, const TextT *fileName, const TextT *suffix)
{
    DB_POINTER4(fullPath, filePath, fileName, suffix);
    // remainLength 标记除后缀名外可用长度, 多预留一位填充'\0'
    uint32_t remainLength = fullPathLen - (suffix->len + 1);
    if (remainLength < filePath->len + (uint32_t)strlen("/")) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    uint32_t offset = 0;
    errno_t err;
    if (filePath->len > 0) {
        // 预留至少一位长度给文件名
        err = memcpy_s(&fullPath[offset], remainLength - 1, filePath->str, filePath->len);
        if (err != 0) {
            DB_LOG_ERROR(GMERR_FIELD_OVERFLOW, "copy filepath(no:%" PRId32 ")!", err);
            return GMERR_FIELD_OVERFLOW;
        }
        offset += filePath->len;
        remainLength -= filePath->len;

        TextT filePathEnding = {.str = (char *)"/", .len = (uint32_t)strlen("/")};
        if (!EndsWith(filePath, &filePathEnding)) {
            err = memcpy_s(&fullPath[offset], remainLength - 1, filePathEnding.str, filePathEnding.len);
            if (err != 0) {
                DB_LOG_ERROR(GMERR_FIELD_OVERFLOW, "Copy filepath-ending(no:%" PRId32 ")!", err);
                return GMERR_FIELD_OVERFLOW;
            }
            offset += filePathEnding.len;
            remainLength -= filePathEnding.len;
        }
    }

#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
    // 导出二进制能力开启的时候，字符串长度不足保存完整文件名时，需要报错
    uint32_t fileNameLen = fileName->len;
#else
    uint32_t fileNameLen = remainLength > fileName->len ? fileName->len : remainLength;
#endif
    err = memcpy_s(&fullPath[offset], remainLength, fileName->str, fileNameLen);
    if (err != 0) {
        DB_LOG_ERROR(GMERR_FIELD_OVERFLOW, "Copy filepath-filename(no:%" PRId32 ")!", err);
        return GMERR_FIELD_OVERFLOW;
    }
    offset += fileNameLen;
    int32_t count = snprintf_s(&fullPath[offset], suffix->len + 1, suffix->len, ".%s", suffix->str);
    if (count < 0) {
        DB_LOG_ERROR(GMERR_FIELD_OVERFLOW, "Copy filepath-suffix!");
        return GMERR_FIELD_OVERFLOW;
    }
    return GMERR_OK;
}

Status TlsRenameFile(const char *alterFilePath, char *inputFilePath, char *fileName, char *suffix)
{
    char finalFilePath[DB_EXPORT_FULL_FILE_PATH] = {0};
    TextT filePathText = {.str = inputFilePath, .len = (uint32_t)strlen(inputFilePath)};
    TextT fileNameText = {.str = fileName, .len = (uint32_t)strlen(fileName)};
    // 文件后缀预留一位填充'.'
    TextT suffixText = {.str = suffix, .len = (uint32_t)strlen(suffix) + 1};

    Status ret = BuildFullPath(finalFilePath, DB_EXPORT_FULL_FILE_PATH, &filePathText, &fileNameText, &suffixText);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (DbAdptRename(alterFilePath, finalFilePath) != GMERR_OK) {
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    return GMERR_OK;
}

Status TlsSaveFile(char *outputPath, char *fileName, char *suffix, char *backupFileBuff, size_t backupFileSize)
{
    char tmpFilePath[DB_MAX_PATH] = {0};
    int32_t ret = snprintf_s(tmpFilePath, DB_MAX_PATH, DB_MAX_PATH - 1, "temp-%" PRIu32 ".%s", DbAdptGetpid(), suffix);
    if (ret < 0) {
        DB_LOG_ERROR(GMERR_FIELD_OVERFLOW, "gen temp file name. ret = %" PRId32, GMERR_FIELD_OVERFLOW);
        return GMERR_FIELD_OVERFLOW;
    }
    int32_t fd = DB_INVALID_FD;
    Status status = DbOpenFile(tmpFilePath, CREATE_FILE | WRITE_ONLY, PERM_USRRW, &fd);
    if (status != GMERR_OK) {
        DB_LOG_ERROR(GMERR_GET_PATH_FAILED, "open temp file : %s, os: %" PRId32, tmpFilePath, DbAptGetErrno());
        return status;
    }
    do {
        status = DbWriteFile(fd, backupFileBuff, backupFileSize);
        if (status != GMERR_OK) {
            break;
        }
        bool isNull = strlen(outputPath) == 0;
        char tempFile[DB_MAX_PATH] = {0};
        ret = snprintf_s(tempFile, DB_MAX_PATH, DB_MAX_PATH - 1, "%s%s%s.%s", isNull ? "" : outputPath,
            isNull ? "" : "/", fileName, suffix);
        if (ret < 0) {
            status = GMERR_FIELD_OVERFLOW;
            break;
        }
        if (DbFileExist(tempFile)) {
            TOOL_RUN_ERROR(
                DbPrintfDefault, GMERR_GET_PATH_FAILED, "backup file already exist. outputPath: %s", tempFile);
            status = GMERR_GET_PATH_FAILED;
            break;
        }
        status = TlsRenameFile(tmpFilePath, outputPath, fileName, suffix);
        if (status != GMERR_OK) {
            TOOL_RUN_ERROR(DbPrintfDefault, status, "export file unsucc. ret = %" PRIu32 ", outputPath: %s",
                (uint32_t)status, outputPath);
            break;
        }
    } while (0);
    DbCloseFile(fd);
    if (DbFileExist(tmpFilePath)) {
        Status rmRet = DbRemoveFile(tmpFilePath);
        if (rmRet != GMERR_OK) {
            TOOL_RUN_ERROR(DbPrintfDefault, rmRet, "remove temp file unsucc.");
        }
    }
    return status;
}

Status TlsOptionParseDigestInfo(const char *toolsName, DbOptionRuleT *optionRule, TlsDigestInfoT *digest)
{
    DB_POINTER3(toolsName, optionRule, digest);
    digest->algorithm = DB_DIGEST_ALGORITHM_BUTT;
    digest->clearPassword = true;
    DbOptionItemT *optionItem = DbGetOptionItemByName(optionRule, DB_OPTION_ARG_RELIABILITY_CHECK);
    if (optionItem == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_OPTION, "-rc in %s.", toolsName);
        return GMERR_INVALID_OPTION;
    }
    char *algorithm = optionItem->params[0].paramVal[0].strVal;
    if (algorithm == NULL || strlen(algorithm) == 0) {
        return GMERR_OK;
    }
    if (DbStrCmp("crc", algorithm, true) == 0) {
        digest->algorithm = DB_DIGEST_ALGORITHM_CRC;
    } else if (DbStrCmp("hmac", algorithm, true) == 0) {
        digest->algorithm = DB_DIGEST_ALGORITHM_HMAC;
        Status ret = TlsRandSeed();
        if (ret != GMERR_OK) {
            PRINT_ERROR(DbPrintfDefault, "Rand seed unsucc for hmac. ret = %" PRId32 "\n", (int32_t)ret);
            return ret;
        }
    } else {
        (void)DbPrintfDefault("unexpected option(\"-rc\") param for %s.\n", toolsName);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

#define MIN_SEED_LENGTH (384 / 8)

Status TlsRandSeed(void)
{
    unsigned char buf[MIN_SEED_LENGTH];
    int32_t fd = -1;
    Status ret = DbOpenFile(PBKDF2_RANDOM_DEV_PATH, O_RDONLY | O_NONBLOCK, O_RDONLY, &fd);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_FILE_OPERATE_FAILED, "[tools] transform to open file /dev/random.");
        return GMERR_FILE_OPERATE_FAILED;
    }
    size_t readCount = 0;
    do {
        size_t tmpCnt;
        ret = DbReadFile(fd, buf + readCount, (MIN_SEED_LENGTH - readCount), &tmpCnt);
        if (ret != GMERR_OK) {
            DbCloseFile(fd);
            TOOL_RUN_ERROR(DbPrintfDefault, GMERR_FILE_OPERATE_FAILED,
                "try read %" PRIu32 " bytes, while actually read %" PRIu32 " bytes from /dev/random.",
                (uint32_t)MIN_SEED_LENGTH, (uint32_t)readCount);
            return GMERR_FILE_OPERATE_FAILED;
        }
        readCount += tmpCnt;
    } while (readCount != MIN_SEED_LENGTH);
    DbCloseFile(fd);
    DbRANDSeed(buf, MIN_SEED_LENGTH);
    return GMERR_OK;
}

#ifdef FEATURE_TOOLS_SYSLOG
void TlsPrintLog(void *handle, uint32_t level, const char *errCode, const char *logBuf)
{
    syslog(LOG_ERR, "%s", logBuf);
}
#endif

char *TlsGetProcessName(char *processPath)
{
    char *processNameStart = strrchr(processPath, '/');
    if (processNameStart == NULL) {
        return processPath;
    }
    return processNameStart + 1;
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
