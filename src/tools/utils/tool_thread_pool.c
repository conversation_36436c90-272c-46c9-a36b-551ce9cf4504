/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: tool_thread_pool.c
 * Description: Implements for tools thread pool.
 * Author: xizeming
 * Create: 2023-9-12
 */
#include <sys/epoll.h>
#include "adpt_semaphore.h"
#include "adpt_sleep.h"
#include "tool_utils.h"
#include "gmc_connection.h"
#include "clt_stmt.h"
#include "db_signal.h"
#include "tool_thread_pool.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
#define MAX_THREADS 10
#define THREADS_DESTROY (MAX_THREADS + 1)
#define TLS_DEFAULT_STACKSIZE (512 * DB_KIBI)
#define TRY_COUNT 7200000
#define TIME_OUT 1000
#define EVENTS_NUM 2048
#define EPOLL_FD (-2)

static int32_t g_gmdbResponseEpollFd = EPOLL_FD;
static DbThreadHandle g_gmdbResponseEpollThreadId;

typedef struct TlsTask {
    struct TlsTask *next;
    void *args;
    void (*function)(void *args);
} TlsTaskT;

typedef struct TlsTaskQueue {
    TlsTaskT *front;
    TlsTaskT *rear;
} TlsTaskQueueT;

typedef struct TlsThreadPool {
    DbMemCtxT *memCtx;
    pthread_mutex_t lock;
    TlsTaskQueueT queue;
    pthread_cond_t cond;
    volatile uint32_t threadNums;
    volatile uint32_t waitingNums;
    DbThreadHandle *threadIds;
    char *domainName;
    GmcConnTypeE type;
} TlsThreadPoolT;

typedef struct TaskArgHead {
    GmcStmtT *stmt;
} TaskArgHeadT;

Status ThreadPoolInitInner(
    DbMemCtxT *memCtx, uint32_t threadNums, GmcConnTypeE type, char *domainName, TlsThreadPoolT **tp)
{
    TlsThreadPoolT *tmpTp = (TlsThreadPoolT *)DbDynMemCtxAlloc(memCtx, sizeof(TlsThreadPoolT));
    if (tmpTp == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "init thread pool when alloc thread pool.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(tmpTp, sizeof(TlsThreadPoolT), 0, sizeof(TlsThreadPoolT));

    (void)DbThreadCondInit(&(tmpTp->cond), NULL);
    (void)DbThreadMutexInit(&(tmpTp->lock), NULL);
    tmpTp->memCtx = memCtx;
    tmpTp->type = type;
    tmpTp->threadNums = threadNums;
    tmpTp->domainName = domainName;

    tmpTp->threadIds = (DbThreadHandle *)DbDynMemCtxAlloc(memCtx, sizeof(DbThreadHandle) * threadNums);
    if (tmpTp->threadIds == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "init thread pool when alloc thread.");
        DbDynMemCtxFree(memCtx, tmpTp);
        return GMERR_OUT_OF_MEMORY;
    }

    (*tp) = tmpTp;
    return GMERR_OK;
}

static void *EpollThreadFunc(void *args)
{
    const int epollTimeoutMs = TIME_OUT;
    static struct epoll_event events[EVENTS_NUM];
    int *epollFd = (int *)args;
    if (*epollFd < 0) {
        *epollFd = EPOLL_FD;
        return NULL;
    }

    while (*epollFd >= 0) {
        int32_t fdCount = epoll_wait(*epollFd, events, sizeof(events), epollTimeoutMs);

        while (fdCount > 0 && fdCount < EVENTS_NUM) {
            --fdCount;
            GmcHandleRWEvent(events[fdCount].data.fd, events[fdCount].events);
        }
    }
    *epollFd = EPOLL_FD;
    return NULL;
}

static int EpollRegWithUserData(int fd, GmcEpollCtlTypeE type, uint32_t events, void *userData)
{
    struct epoll_event event;
    event.data.fd = fd;
    event.events = events;
    int *epollFd = (int *)(userData);
    int ret;
    switch (type) {
        case GMC_EPOLL_ADD:
            ret = epoll_ctl(*epollFd, EPOLL_CTL_ADD, fd, &event);
            break;
        case GMC_EPOLL_MOD:
            ret = epoll_ctl(*epollFd, EPOLL_CTL_MOD, fd, &event);
            break;
        case GMC_EPOLL_DEL:
            ret = epoll_ctl(*epollFd, EPOLL_CTL_DEL, fd, NULL);
            break;
        default:
            ret = -1;
    }
    if (ret != 0) {
        DB_LOG_ERROR(ret,
            "epoll reg with userdata. epollfd(%" PRId32 "), fd(%" PRId32 "), type(%" PRId32 "), errono(%" PRId32
            "), ret = %" PRId32,
            *epollFd, fd, (int)type, errno, ret);
    }
    return ret;
}

static int EpollReg(int fd, GmcEpollCtlTypeE type)
{
    int ret;
    switch (type) {
        case GMC_EPOLL_ADD: {
            struct epoll_event event;
            event.data.fd = fd;
            event.events = EPOLLIN;
            ret = epoll_ctl(g_gmdbResponseEpollFd, EPOLL_CTL_ADD, fd, &event);
            break;
        }
        case GMC_EPOLL_DEL:
            ret = epoll_ctl(g_gmdbResponseEpollFd, EPOLL_CTL_DEL, fd, NULL);
            break;
        case GMC_EPOLL_MOD:
            ret = -1;
            break;
        default:
            ret = -1;
    }
    if (ret != 0) {
        DB_LOG_ERROR(ret, "epoll reg. fd(%" PRId32 "), type(%" PRId32 ") errono(%" PRId32 ") ret = %" PRId32, fd,
            (int)type, errno, ret);
    }
    return ret;
}

static Status ThreadPoolConnect(GmcConnT **conn, GmcStmtT **stmt, uint32_t *epollId, TlsThreadPoolT *tp)
{
    GmcConnOptionsT *connOptions = NULL;
    Status ret = GmcConnOptionsCreate(&connOptions);
    if (ret == GMERR_OK) {
        ret = GmcConnOptionsSetServerLocator(connOptions, tp->domainName);
    }
    if (ret == GMERR_OK) {
        ret = GmcConnOptionsSetReservedFlag(connOptions, false);
    }
    if (ret == GMERR_OK && tp->type == GMC_CONN_TYPE_ASYNC) {
        if (DbIsEulerEnv()) {
            ret = GmcConnOptionsSetEpollRegFuncWithUserData(connOptions, EpollRegWithUserData, &g_gmdbResponseEpollFd);
        } else {
            ret = GmcConnOptionsSetEpollRegFunc(connOptions, (GmcEpollRegT)EpollReg);
        }
    }
    if (ret == GMERR_OK) {
        ret = GmcConnect(tp->type, connOptions, conn);
    }
    GmcConnOptionsDestroy(connOptions);
    if (ret != GMERR_OK) {
        *conn = NULL;
        DB_LOG_ERROR(ret, "Connect server. ret = %" PRId32, (int32_t)ret);
        return ret;
    }

    ret = GmcAllocStmt(*conn, stmt);
    if (ret != GMERR_OK) {
        (void)GmcDisconnect(*conn);
        *conn = NULL;
        DB_LOG_ERROR(ret, "Alloc stmt. ret = %" PRId32, (int32_t)ret);
    }
    return ret;
}

static void *TlsThreadDo(TlsThreadPoolT *tp)
{
    DB_POINTER(tp);
    GmcStmtT *stmt = NULL;
    GmcConnT *conn = NULL;
    uint32_t epollId = 0;
    Status ret = ThreadPoolConnect(&conn, &stmt, &epollId, tp);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Thread create connection.");
        (void)DbAtomicDec(&tp->threadNums);
        return NULL;
    }
    TlsTaskT *task = NULL;

    for (;;) {
        // tp->waitingNums > MAX_THREADS是结束标志
        // 如果线程跑到此处前，主线程已经将tp->waitingNums置为THREADS_DESTROY，并且发起broadcast
        // 此时该线程不在wait状态，不会处理信号。那么跑到下面的wait时，永远等不到下一个唤醒信号，造成死循环
        // 所以拿到锁后，应该先做判断，线程是否应该退出
        (void)DbThreadMutexLock(&tp->lock);
        if (tp->waitingNums > MAX_THREADS) {
            (void)DbThreadMutexUnlock(&tp->lock);
            TlsDisconnect(conn, stmt);
            (void)DbAtomicDec(&tp->threadNums);
            return NULL;
        }
        while (tp->queue.front == NULL) {
            tp->waitingNums++;
            (void)DbThreadCondWait(&tp->cond, &tp->lock);
            if (tp->waitingNums > MAX_THREADS) {
                (void)DbThreadMutexUnlock(&tp->lock);
                TlsDisconnect(conn, stmt);
                (void)DbAtomicDec(&tp->threadNums);
                return NULL;
            }
            tp->waitingNums--;
        };

        task = tp->queue.front;
        tp->queue.front = task->next;

        if (tp->queue.front == NULL) {
            tp->queue.rear = NULL;
        }

        (void)DbThreadMutexUnlock(&tp->lock);

        TaskArgHeadT *argStmt = (TaskArgHeadT *)task->args;
        argStmt->stmt = stmt;
        task->function(task->args);

        DbDynMemCtxFree(tp->memCtx, task);
        GmcResetStmt(stmt);
    }

    return NULL;
}

static Status TlsThreadPoolInitAsyncInner(ThreadAttrsT *threadAttrs)
{
    struct epoll_event events[EVENTS_NUM];
    g_gmdbResponseEpollFd = epoll_create(sizeof(events));
    if (g_gmdbResponseEpollFd < 0) {
        DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Epoll create epollFd, epollFd(%" PRId32 "), errno(%" PRId32 ").",
            g_gmdbResponseEpollFd, errno);
        return GMERR_INTERNAL_ERROR;
    }
    threadAttrs->entryFunc = (DbThreadEntryProc)EpollThreadFunc;
    threadAttrs->entryArgs = &g_gmdbResponseEpollFd;
    Status ret = DbThreadCreate(threadAttrs, &g_gmdbResponseEpollThreadId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "create thread.");
        (void)close(g_gmdbResponseEpollFd);
        g_gmdbResponseEpollFd = EPOLL_FD;
        return ret;
    }
    return GMERR_OK;
}

static void TlsClearThread(TlsThreadPoolT *tmpTp, uint32_t threadNum)
{
    for (uint32_t i = 0; i < threadNum; i++) {
        (void)DbThreadKill(tmpTp->threadIds[i], DB_SIGABRT);
    }
}

Status TlsThreadPoolInit(
    DbMemCtxT *memCtx, uint32_t threadNums, char *domainName, GmcConnTypeE type, TlsThreadPoolT **tp)
{
    DB_POINTER3(tp, domainName, memCtx);
    if (threadNums == 0 || threadNums > MAX_THREADS) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Parameters of thread pool are inv. Too many threads(%" PRIu32 ").",
            threadNums);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    DbMemCtxArgsT args = {0};
    args.liteModOn = true;
    DbMemCtxT *tpMemCtx = DbCreateDynMemCtx(memCtx, true, "THREAD_POOL_STMT", &args);
    if (tpMemCtx == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Alloc for the thread pool");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    TlsThreadPoolT *tmpTp = NULL;
    Status ret = ThreadPoolInitInner(tpMemCtx, threadNums, type, domainName, &tmpTp);
    if (ret != GMERR_OK) {
        DbDeleteDynMemCtx(tpMemCtx);
        return ret;
    }

    ThreadAttrsT threadAttrs = {};
    DbThreadHandle handle = 0;
    threadAttrs.stackSize = TLS_DEFAULT_STACKSIZE;
    threadAttrs.type = DB_THREAD_DETACHABLE;

    if (type == GMC_CONN_TYPE_ASYNC) {
        ret = TlsThreadPoolInitAsyncInner(&threadAttrs);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "create async epoll thread.");
            DbDeleteDynMemCtx(tpMemCtx);
            return ret;
        }
    }

    threadAttrs.entryArgs = tmpTp;
    threadAttrs.entryFunc = (DbThreadEntryProc)TlsThreadDo;
    for (uint32_t i = 0; i < tmpTp->threadNums; i++) {
        ret = DbThreadCreate(&threadAttrs, &handle);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "create thread.");
            TlsClearThread(tmpTp, i);
            DbDeleteDynMemCtx(tpMemCtx);
            return ret;
        }
        tmpTp->threadIds[i] = handle;
    }

    (*tp) = tmpTp;
    return GMERR_OK;
}

Status TlsThreadPoolTaskPost(TlsThreadPoolT *tp, void (*function)(void *), void *args)
{
    DB_POINTER2(tp, function);

    TlsTaskT *task = (TlsTaskT *)DbDynMemCtxAlloc(tp->memCtx, sizeof(TlsTaskT));
    if (task == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "create task.");
        return GMERR_OUT_OF_MEMORY;
    }

    task->next = NULL;
    task->function = function;
    task->args = args;
    (void)DbThreadMutexLock(&tp->lock);
    if (tp->queue.rear == NULL) {
        tp->queue.front = task;
        tp->queue.rear = task;
    } else {
        tp->queue.rear->next = task;
        tp->queue.rear = task;
    }
    (void)DbThreadCondSignal(&tp->cond);
    (void)DbThreadMutexUnlock(&tp->lock);

    return GMERR_OK;
}

Status TlsThreadPoolWait(TlsThreadPoolT *tp)
{
    DB_POINTER(tp);
    uint32_t count = 0;
    while (tp->waitingNums != tp->threadNums || tp->queue.front != NULL) {
        count++;
        if (count > TRY_COUNT || tp->threadNums == 0) {
            DB_LOG_ERROR(GMERR_INTERNAL_ERROR,
                "Thread exec (time out or exception). try count:%" PRIu32 ", thread nums:%" PRIu32 ".", count,
                tp->threadNums);
            return GMERR_INTERNAL_ERROR;
        }
        DbUsleep(USECONDS_IN_MSECOND);
    }
    return GMERR_OK;
}

Status TlsThreadPoolWaitWithTimeOut(TlsThreadPoolT *tp, uint64_t startTime, uint32_t timeout)
{
    if (timeout == 0) {
        return TlsThreadPoolWait(tp);
    }

    DB_POINTER(tp);
    uint64_t nowTime;
    while (tp->waitingNums != tp->threadNums || tp->queue.front != NULL) {
        nowTime = DbGetMsec();
        uint32_t spendTime = (uint32_t)(nowTime - startTime);
        if (spendTime > timeout) {
            DB_LOG_ERROR(
                GMERR_PROGRAM_LIMIT_EXCEEDED, "Export thread exec time out. Thread nums:%" PRIu32 ".", tp->threadNums);
            return GMERR_PROGRAM_LIMIT_EXCEEDED;
        }
        if (tp->threadNums == 0) {
            DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Thread exec");
            return GMERR_INTERNAL_ERROR;
        }
        DbUsleep(USECONDS_IN_MSECOND);
    }
    return GMERR_OK;
}

static inline void TlsThreadPoolDestroyAsyncInner(void)
{
    for (uint32_t i = 0; g_gmdbResponseEpollFd != EPOLL_FD; i++) {
        if (i > TRY_COUNT) {
            DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Waiting thread time out. try count:%" PRIu32 ".", i);
            break;
        }
        DbUsleep(USECONDS_IN_MSECOND);
    }
}

void TlsThreadPoolDestroy(TlsThreadPoolT *tp)
{
    DB_POINTER(tp);
    (void)DbThreadMutexLock(&tp->lock);
    tp->waitingNums = THREADS_DESTROY;
    (void)DbThreadCondBroadcast(&tp->cond);
    (void)DbThreadMutexUnlock(&tp->lock);

    uint32_t count = 0;
    while (tp->threadNums != 0) {
        count++;
        if (count > TRY_COUNT) {
            DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Waiting thread time out. try count:%" PRIu32 ".", count);
            break;
        }
        DbUsleep(USECONDS_IN_MSECOND);
    }

    if (tp->type == GMC_CONN_TYPE_ASYNC) {
        (void)close(g_gmdbResponseEpollFd);
        g_gmdbResponseEpollFd = DB_INVALID_FD;
        TlsThreadPoolDestroyAsyncInner();
    }

    (void)DbThreadCondDestroy(&tp->cond);
    (void)DbThreadMutexDestroy(&tp->lock);
    DbDeleteDynMemCtx(tp->memCtx);
}

DbMemCtxT *TlsGetThreadPoolSharedDynMem(TlsThreadPoolT *tp)
{
    DB_POINTER(tp);
    return tp->memCtx;
}
#endif

#ifdef __cplusplus
}
#endif /* __cplusplus */
