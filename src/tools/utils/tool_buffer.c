/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * File Name: tool_buffer.c
 * Description: public interface for tools
 * Author: yuanxin
 * Create: 2022-11-10
 */
#include "tool_buffer.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

static Status OutPutBufferGrow(OutputBufferT *buffer, uint32_t available, uint32_t required)
{
    uint64_t newCapacity = available + buffer->capacity > required ? buffer->capacity * 2 : buffer->capacity + required;
    if (newCapacity > DB_MAX_UINT32) {
        DB_LOG_ERROR(GMERR_FIELD_OVERFLOW,
            "Tool buffer overflow, available:%" PRIu32 ", required:%" PRIu32 ", buf cap:%" PRIu32, available, required,
            buffer->capacity);
        return GMERR_FIELD_OVERFLOW;
    }
    void *newBuf = DbDynMemCtxAlloc(buffer->memCtx, (uint32_t)newCapacity);
    if (SECUREC_UNLIKELY(newBuf == NULL)) {
        return GMERR_OUT_OF_MEMORY;
    }
    void *oldBuf = buffer->buf;
    errno_t err = memcpy_s(newBuf, (uint32_t)newCapacity, oldBuf, buffer->writeCursor);
    if (SECUREC_UNLIKELY(err != EOK)) {
        DbDynMemCtxFree(buffer->memCtx, newBuf);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    buffer->buf = newBuf;
    buffer->capacity = (uint32_t)newCapacity;
    // 释放后，buffer->buf 已被 newBuf 代替，未置NULL，无UAF风险
    DbDynMemCtxFree(buffer->memCtx, oldBuf);
    return GMERR_OK;
}

Status OutPutBufferWrite(OutputBufferT *buffer, const void *data, uint32_t count)
{
    DB_POINTER(buffer);
    if (buffer->writeCursor + count > buffer->capacity) {
        Status ret = OutPutBufferGrow(buffer, buffer->capacity - buffer->writeCursor, count);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    void *buf = buffer->buf + buffer->writeCursor;
    errno_t err = memcpy_s(buf, buffer->capacity - buffer->writeCursor, data, count);
    if (err != EOK) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    buffer->writeCursor += count;
    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
