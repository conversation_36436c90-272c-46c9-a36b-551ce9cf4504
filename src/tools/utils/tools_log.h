/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * Description: header file for tools log
 * Author: panpeixian
 * Create: 2021-09-07
 */

#ifndef TOOLS_LOG_H
#define TOOLS_LOG_H

#include "db_log.h"
#include "db_last_error.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#ifdef LOG_MODULE
#undef LOG_MODULE
#endif
#define LOG_MODULE "TOOLS"

/* log */
#define NONE ""
#define RED ""
#define GREEN ""
#define YELLOW ""
#define PRINT_INFO(printFunc, format, ...)                                                         \
    do {                                                                                           \
        if (!g_gmdbIsQuickLog) {                                                                   \
            printFunc == DbPrintfDefault ? (void)printFunc("[INFO] " NONE format, ##__VA_ARGS__) : \
                                           (void)printFunc("[INFO] " format, ##__VA_ARGS__);       \
            (void)printFunc("\n");                                                                 \
        }                                                                                          \
    } while (0)

#define PRINT_SUCCESS(printFunc, format, ...)                                                      \
    do {                                                                                           \
        if (!g_gmdbIsQuickLog) {                                                                   \
            printFunc == DbPrintfDefault ? (void)printFunc("[DONE] " NONE format, ##__VA_ARGS__) : \
                                           (void)printFunc("[DONE] " format, ##__VA_ARGS__);       \
            (void)printFunc("\n");                                                                 \
        }                                                                                          \
    } while (0)

#define PRINT_ERROR(printFunc, format, ...)                                                     \
    do {                                                                                        \
        printFunc == DbPrintfDefault ? (void)printFunc("[ERROR] " NONE format, ##__VA_ARGS__) : \
                                       (void)printFunc("[ERROR] " format, ##__VA_ARGS__);       \
        (void)printFunc("\n");                                                                  \
    } while (0)

#define TOOL_DEBUG_ERROR(printFunc, error_code, format, ...) \
    do {                                                     \
        DB_LOG_DEBUG(error_code, format, ##__VA_ARGS__);     \
        PRINT_ERROR(printFunc, format, ##__VA_ARGS__);       \
    } while (0)

#define TOOL_RUN_ERROR(printFunc, error_code, format, ...) \
    do {                                                   \
        DB_LOG_ERROR(error_code, format, ##__VA_ARGS__);   \
        PRINT_ERROR(printFunc, format, ##__VA_ARGS__);     \
    } while (0)

#define TOOL_RUN_INFO(printFunc, format, ...)       \
    do {                                            \
        if (!g_gmdbIsQuickLog) {                    \
            DB_LOG_INFO(format, ##__VA_ARGS__);     \
            (void)printFunc(format, ##__VA_ARGS__); \
            (void)printFunc("\n");                  \
        }                                           \
    } while (0)

#define TOOL_RUN_INFO_UNFOLD(printFunc, format, ...)   \
    do {                                               \
        if (!g_gmdbIsQuickLog) {                       \
            DB_LOG_INFO_UNFOLD(format, ##__VA_ARGS__); \
            (void)printFunc(format, ##__VA_ARGS__);    \
            (void)printFunc("\n");                     \
        }                                              \
    } while (0)

#define TOOL_WARN(printFunc, error_code, format, ...)                                              \
    do {                                                                                           \
        if (!g_gmdbIsQuickLog) {                                                                   \
            DB_LOG_WARN(error_code, format, ##__VA_ARGS__);                                        \
            printFunc == DbPrintfDefault ? (void)printFunc("[WARN] " NONE format, ##__VA_ARGS__) : \
                                           (void)printFunc("[WARN] " format, ##__VA_ARGS__);       \
            (void)printFunc("\n");                                                                 \
        }                                                                                          \
    } while (0)

#define TOOL_ERROR_INFO(printFunc, error_code, format, ...)                                    \
    do {                                                                                       \
        DB_LOG_ERROR(error_code, format, ##__VA_ARGS__);                                       \
        printFunc == DbPrintfDefault ? (void)printFunc("[WARN] " NONE format, ##__VA_ARGS__) : \
                                       (void)printFunc("[WARN] " format, ##__VA_ARGS__);       \
        (void)printFunc("\n");                                                                 \
    } while (0)

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif  // TOOLS_LOG_H
