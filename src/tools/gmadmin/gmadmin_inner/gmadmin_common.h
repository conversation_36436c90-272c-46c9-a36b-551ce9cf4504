/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: gmadmin_common.h
 * Description: Header file for gmadmin common functions
 * Create: 2023/10/28
 */
#ifndef GMADMIN_COMMON_H
#define GMADMIN_COMMON_H
#include "db_option_parser.h"
#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

typedef Status (*AdminCmdFuncT)(DbOptionRuleT *);

Status GmAdminPrepareResource(DbOptionRuleT *optionRule, GmcConnT **conn, GmcStmtT **stmt);

void GmAdminFreeResource(GmcConnT *conn, GmcStmtT *stmt);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
