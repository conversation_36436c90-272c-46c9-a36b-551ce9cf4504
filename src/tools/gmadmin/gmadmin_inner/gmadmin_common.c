/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: gmadmin_common.c
 * Description:
 * Create: 2023/10/27
 */

#include "gmadmin_common.h"
#include "tool_utils.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

Status GmAdminPrepareResource(DbOptionRuleT *optionRule, GmcConnT **conn, GmcStmtT **stmt)
{
    DB_POINTER(optionRule);
    char serverLocator[DOMAIN_NAME_MAX_LEN] = {0};
    Status ret = DbSetStrOptionIfUsed(
        optionRule, serverLocator, sizeof(serverLocator), DB_OPTION_ARG_DOMAIN_NAME, DB_DEFAULT_DOMAIN_NAME);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (strlen(serverLocator) == 0) {
        (void)DbPrintfDefault(
            "[ADMIN] gmAdmin param : null value, ret = %" PRIu32 ".\n", (uint32_t)GMERR_NULL_VALUE_NOT_ALLOWED);
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    ret = TlsPerfEnvInit();
    if (ret != GMERR_OK) {
        (void)DbPrintfDefault("[ADMIN] init env, ret = %" PRId32 ".\n", ret);
        return ret;
    }
    char userName[MAX_USER_NAME_LEN] = {0};
    ret = DbSetStrOptionIfUsed(optionRule, userName, MAX_USER_NAME_LEN, DB_OPTION_ARG_USER_NAME, NULL);
    if (ret != GMERR_OK) {
        return ret;
    }

    TlsConnOptionT option = {
        .domainName = serverLocator,
        .userName = userName,
        .namespaceName = "",
        .useReservedConn = false,
        .noMemLimit = false,
    };

    // 创建同步连接
    ret = TlsConnect(conn, stmt, &option);
    if (ret != GMERR_OK) {
        (void)DbPrintfDefault("[ADMIN] connect, ret = %" PRId32 "\n", ret);
        TlsPerfEnvUnInit();
        return ret;
    }
    return GMERR_OK;
}

void GmAdminFreeResource(GmcConnT *conn, GmcStmtT *stmt)
{
    TlsDisconnect(conn, stmt);
    TlsPerfEnvUnInit();
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
