/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: gmadmin_cfg.c
 * Description:
 *      gmadmin -setcfg
 *      gmadmin -getcfg
 * Create: 2021/07/21
 */

#include "gmadmin_cfg.h"
#include "adpt_types.h"
#include "db_option_parser.h"
#include "tool_utils.h"
#include "gmc.h"
#include "adpt_string.h"
#include "adpt_locator.h"
#include "gmadmin_typedef.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

static Status GmAdminPrintCfg(GmcStmtT *stmt);

/*
 * 用途：定义配置项是否可修改的枚举到其对应字符的映射
 * 是否并发初始化：否
 * 是否并发读写：只读，并发不影响
 * 并发方案：无
 */
static const char *g_gmadminChangeModeDesc[(size_t)DB_CFG_CHANGE_BUTT] = {"not allowed", "more", "less", "no limit"};

static Status GmAdminPrintCfgValue(const void *value, const char *text, GmcDataTypeE type)
{
    if (type == GMC_DATATYPE_INT32) {
        (void)DbPrintfDefault("%s: %" PRId32 "\n", text, *(const int32_t *)value);
    } else if (type == GMC_DATATYPE_STRING) {
        (void)DbPrintfDefault("%s: %s\n", text, (const char *)value);
    } else {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

Status GmAdminGetCfg(DbOptionRuleT *optionRule)
{
    DB_POINTER(optionRule);
    char *configName = DbGetStrParamByOptionName(optionRule, ADM_OPTION_ARG_CFG_NAME);
    if (configName == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_NULL_VALUE_NOT_ALLOWED, "[ADMIN] GmAdmin param configName:null.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }

    GmcStmtT *stmt = NULL;
    GmcConnT *conn = NULL;
    Status ret = GmAdminPrepareResource(optionRule, &conn, &stmt);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = GmcGetCfg(stmt, configName);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "[ADMIN] Unable to get config.");
        GmAdminFreeResource(conn, stmt);
        return ret;
    }

    ret = GmAdminPrintCfg(stmt);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "[ADMIN] Unable to print config.");
    }

    GmAdminFreeResource(conn, stmt);
    return ret;
}

static Status GetCfgNameAndValue(DbOptionRuleT *optionRule, char **configName, char **setValue)
{
    // 设置配置
    // 设置前先get，后打印
    *configName = DbGetStrParamByOptionName(optionRule, ADM_OPTION_ARG_CFG_NAME);
    *setValue = DbGetStrParamByOptionName(optionRule, ADM_OPTION_ARG_CFG_VALUE);
    if (*configName == NULL || *setValue == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_NULL_VALUE_NOT_ALLOWED, "[ADMIN] config name or value is null.");
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }
    return GMERR_OK;
}

Status GmAdminSetCfg(DbOptionRuleT *optionRule)
{
    DB_POINTER(optionRule);
    char *configName = NULL, *setValue = NULL;
    Status ret = GetCfgNameAndValue(optionRule, &configName, &setValue);
    if (ret != GMERR_OK) {
        return ret;
    }

    GmcStmtT *stmt = NULL;
    GmcConnT *conn = NULL;
    ret = GmAdminPrepareResource(optionRule, &conn, &stmt);
    if (ret != GMERR_OK) {
        return ret;
    }

    (void)DbPrintfDefault("before setting config:\n");

    ret = GmcGetCfg(stmt, configName);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "[ADMIN] get config before set.");
        goto EXIT;
    }
    ret = GmAdminPrintCfg(stmt);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    do {
        if (DbCheckDigit(setValue)) {
            int32_t setValueNum = DB_MAX_INT32;
            ret = DbStrToInt32(setValue, &setValueNum);
            if (ret != GMERR_OK) {
                break;
            }
            ret = GmcSetCfg(stmt, configName, GMC_DATATYPE_INT32, &setValueNum, sizeof(int32_t));
        } else {
            ret = GmcSetCfg(stmt, configName, GMC_DATATYPE_STRING, setValue, (uint32_t)strlen(setValue));
        }
    } while (0);

    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "[ADMIN] set config.");
        goto EXIT;
    }
    // 设置成功后打印
    (void)DbPrintfDefault("after setting config:\n");

    ret = GmcGetCfg(stmt, configName);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "[ADMIN] get config after set.");
        goto EXIT;
    }
    ret = GmAdminPrintCfg(stmt);
EXIT:
    GmAdminFreeResource(conn, stmt);
    return ret;
}

Status GmAdminHelp(DbOptionRuleT *optionRule)
{
    DB_POINTER(optionRule);
    const char *gmAdminOption[][MANUAL_COL_NUM] = {
        {"-h", "", "print online help manual"},
        {"-s", "<server_locator>", "serverLocator for connection"},
        {"-cfgName", "<config_name>", "config name for setting or getting config."},
        {"-cfgVal", "<config_value>", "config value for setting config."},
        {"-u", "<user_name>", "user name for connection."},
#ifdef SHUTDOWN
        {"-shutdown", "", "shutdown db server"},
#endif
    };
    DbPrintManual("[OPTION]", gmAdminOption, ELEMENT_COUNT(gmAdminOption), NULL);
    return GMERR_OK;
}

static Status GmAdminPrintDataType(GmcStmtT *stmt)
{
    void *value = NULL;
    uint32_t valueSize = 0;
    GmcDataTypeE type = GMC_DATATYPE_NULL;
    Status ret = GmcGetCfgInfoByType(stmt, GMC_CFG_INFO_TYPE_DATA_TYPE, &type, (void **)&value, &valueSize);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (*(int32_t *)value < 0 || *(int32_t *)value >= (int32_t)GMC_DATATYPE_BUTT) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INTERNAL_ERROR, "[ADMIN] Unexpected config type is %" PRId32 "\n",
            *(int32_t *)value);
        return GMERR_INTERNAL_ERROR;
    }

    (void)DbPrintfDefault("config type: %s\n", DbGetDataTypeName(*(DbDataTypeE *)value));

    return GMERR_OK;
}

static Status GmAdminPrintChangeMode(GmcStmtT *stmt)
{
    void *value = NULL;
    uint32_t valueSize = 0;
    GmcDataTypeE type = GMC_DATATYPE_NULL;
    Status ret = GmcGetCfgInfoByType(stmt, GMC_CFG_INFO_TYPE_CHANGE_MODE, &type, (void **)&value, &valueSize);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (*(int32_t *)value < 0 || *(int32_t *)value >= (int32_t)DB_CFG_CHANGE_BUTT) {
        TOOL_RUN_ERROR(
            DbPrintfDefault, GMERR_INTERNAL_ERROR, "[ADMIN] Unexpected mode is %" PRId32 ".", *(int32_t *)value);
        return GMERR_INTERNAL_ERROR;
    }

    (void)DbPrintfDefault("config change mode: %s\n", g_gmadminChangeModeDesc[*(int32_t *)value]);

    return GMERR_OK;
}

static Status GmAdminPrintCfg(GmcStmtT *stmt)
{
    void *value = NULL;
    uint32_t valueSize = 0;
    GmcDataTypeE type = GMC_DATATYPE_NULL;

    Status ret = GmcGetCfgInfoByType(stmt, GMC_CFG_INFO_TYPE_NAME, &type, (void **)&value, &valueSize);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "[ADMIN] get config name.");
        return ret;
    }
    (void)DbPrintfDefault("config name: %s\n", (char *)value);

    ret = GmcGetCfgInfoByType(stmt, GMC_CFG_INFO_TYPE_DESC, &type, (void **)&value, &valueSize);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "[ADMIN] get config desc.");
        return ret;
    }
    (void)DbPrintfDefault("config description: %s\n", (char *)value);

    ret = GmAdminPrintDataType(stmt);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = GmcGetCfgInfoByType(stmt, GMC_CFG_INFO_TYPE_CUR_VALUE, &type, (void **)&value, &valueSize);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "[ADMIN] unexpected config current value.");
        return ret;
    }
    (void)GmAdminPrintCfgValue(value, "config current value", type);

    ret = GmcGetCfgInfoByType(stmt, GMC_CFG_INFO_TYPE_MIN_VALUE, &type, (void **)&value, &valueSize);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "[ADMIN] unexpected config min value.");
        return ret;
    }
    (void)GmAdminPrintCfgValue(value, "config min value", type);

    ret = GmcGetCfgInfoByType(stmt, GMC_CFG_INFO_TYPE_MAX_VAULE, &type, (void **)&value, &valueSize);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "[ADMIN] unexpected config max value.");
        return ret;
    }
    (void)GmAdminPrintCfgValue(value, "config max value", type);

    ret = GmcGetCfgInfoByType(stmt, GMC_CFG_INFO_TYPE_DEFAULT_VALUE, &type, (void **)&value, &valueSize);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "[ADMIN] unexpected config default value.");
        return ret;
    }
    (void)GmAdminPrintCfgValue(value, "config default value", type);

    return GmAdminPrintChangeMode(stmt);
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
