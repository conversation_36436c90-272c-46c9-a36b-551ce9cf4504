/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Implementation of set
 * Author: hebaisheng
 * Create: 2023-10-11
 */

#include "gmcmd_set.h"
#include "gmcmd_log.h"
#include "gmc_graph.h"
#include "gmc_tree.h"
#include "gmc.h"
#include "gmcmd_utils.h"
#include "adpt_string.h"
#include "gmc_types.h"
#include "gmcmd_exec_base.h"
#include "string.h"

#define SET_PARAM_NUM (3)
#define SET_PARAM_OBJECT_INDEX (0)
#define SET_PARAM_LABEL_NAME_INDEX (1)
#define SET_PARAM_FILTER_INDEX (2)
#define SET_OBJECT_STR "object"
#define SET_KEY_VALUE_NUM (2)
#define SET_KEY_INDEX (0)
#define SET_VALUE_INDEX (1)
#define SET_KV_TABLE_STR "kvtable"

void GmcSetKvtable(GmCmdContextT *cmdContext, char **result)
{
    DB_POINTER(cmdContext->stmt);
    Status ret = GmcKvPrepareStmtByLabelName(cmdContext->stmt, result[SET_PARAM_LABEL_NAME_INDEX]);
    if (ret != GMERR_OK) {
        CMD_ERROR("Prepare stmt unsuccessfully when set kvtable, ret = %" PRId32 ".", ret);  // LCOV_EXCL_LINE
        return;
    }
    char *keyValue[SET_KEY_VALUE_NUM] = {0};
    uint32_t count = GmCmdStrSplit((char *)result[SET_PARAM_FILTER_INDEX], " ", SET_KEY_VALUE_NUM, keyValue);
    if (count < SET_KEY_VALUE_NUM) {
        CMD_ERROR("You need both input like this: set kvtable table key value.");  // LCOV_EXCL_LINE
        return;
    }
    char *key = keyValue[SET_KEY_INDEX];
    char *value = keyValue[SET_VALUE_INDEX];
    ret = GmcKvSet(cmdContext->stmt, key, (uint32_t)(strlen(key) + 1), value, (uint32_t)(strlen(value) + 1));
    if (ret == GMERR_OK) {
        CMD_SUCCESS("SET success");
    } else {
        CMD_ERROR("SET unsuccessfully, ret = %" PRId32 ".", ret);  // LCOV_EXCL_LINE
    }
    return;
}

void GmcSetVertexlabel(GmCmdContextT *cmdContext, char **result)
{
    Status ret = GmcPrepareStmtByLabelName(
        cmdContext->stmt, DbLeftTrim(result[SET_PARAM_LABEL_NAME_INDEX]), GMC_OPERATION_REPLACE);
    if (ret != GMERR_OK) {
        CMD_ERROR("Replace vertex label prepare stmt unsuccessfully, last  err=%s, ret = %" PRId32 ".",
            GmcGetLastError(), ret);  // LCOV_EXCL_LINE
        return;
    }
    const char *jsonStr = DbLeftTrim(result[SET_PARAM_FILTER_INDEX]);
    ret = GmcSetVertexByJson(cmdContext->stmt, GMC_JSON_REJECT_DUPLICATES, jsonStr);
    if (ret != GMERR_OK) {
        CMD_ERROR("Set vertex by json unsuccessfully, jsonStr = %s, last err=%s, ret = %" PRId32 ".", jsonStr,
            GmcGetLastError(), ret);  // LCOV_EXCL_LINE
        return;
    }
    ret = GmcExecute(cmdContext->stmt);
    if (ret != GMERR_OK) {
        CMD_ERROR("Set vertex by json execute unsuccessfully, ret = %" PRId32 ".", ret);  // LCOV_EXCL_LINE
        return;
    }
    CMD_SUCCESS("SET success!");
}

void GmCmdSetInnerExec(GmCmdContextT *cmdContext, char *cmdParam)
{
    char *result[SET_PARAM_NUM] = {0};
    uint32_t count = GmCmdStrSplit((char *)cmdParam, " ", SET_PARAM_NUM, result);
    if (count < SET_PARAM_NUM) {
        CMD_ERROR("You may miss some parameters.");  // LCOV_EXCL_LINE
        return;
    }
    if (DbStrCmp(SET_OBJECT_STR, DbLeftTrim(result[SET_PARAM_OBJECT_INDEX]), false) == 0) {
        GmcSetVertexlabel(cmdContext, result);
        return;
    }
    if (DbStrCmp(SET_KV_TABLE_STR, DbLeftTrim(result[SET_PARAM_OBJECT_INDEX]), false) == 0) {
        GmcSetKvtable(cmdContext, result);
        return;
    }
    CMD_ERROR("You may miss some keywords like object or kvtable.");  // LCOV_EXCL_LINE
    return;
}

void GmCmdSetExec(GmCmdContextT *cmdContext, char *cmdParam)
{
    DB_POINTER(cmdContext->stmt);
    if (cmdParam == NULL || strlen(cmdParam) == 0) {
        CMD_ERROR("Set param is null!");  // LCOV_EXCL_LINE
        return;
    }
    if (cmdContext->cmdType != GM_CMD_SET) {
        CMD_ERROR("Command type Error.");  // LCOV_EXCL_LINE
        return;
    }
    GmCmdSetInnerExec(cmdContext, cmdParam);
}
