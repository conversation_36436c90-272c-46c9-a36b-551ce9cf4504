/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: parse gmcmd argv
 * Author: hebaisheng
 * Create: 2022-01-18
 */

#ifndef GMDBV5_CMD_PARSE_ARGV_H
#define GMDBV5_CMD_PARSE_ARGV_H

#include "gmcmd_exec_base.h"
#include "db_option_parser.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

// clang-format off
typedef enum ArgvOptionType {
    CMD_ARGV_NULL = 0,  // 不带参数时，会默认进入交互式。
    CMD_ARGV_HELP,  // 带-h的时候，对应获取帮助信息
    CMD_ARGV_CON,  // 带-s的时候，对应连接流程
    CMD_ARGV_CMD,  // 带-c的时候，对应是代码集成命令形式
    CMD_ARGV_BUTT
} ArgvOptionTypeE;
// clang-format on

Status GmCmdPrepare(DbOptionRuleT *optionRule);

Status GetArgvType(DbOptionRuleT *optionRule, int32_t argc, char **argv, ArgvOptionTypeE *type);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif  // GMDBV5_CMD_PARSE_ARGV_H
