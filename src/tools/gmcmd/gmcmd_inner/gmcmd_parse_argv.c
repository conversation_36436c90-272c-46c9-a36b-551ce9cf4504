/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: parse gmcmd argv
 * Author: hebaisheng
 * Create: 2022-01-18
 */

#include "gmcmd_parse_argv.h"
#include "db_option_parser.h"
#include "adpt_locator.h"
#include "tool_utils.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define OPTION_PARAM_MAX_NUM_CMD 127
#define DB_OPTION_ARG_USER "-u"

// clang-format off
DbOptionItemT g_optionItems[] = {
    {DB_OPTION_ARG_HELP, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}},
    {DB_OPTION_ARG_DOMAIN_NAME, 0, PARAM_TYPE_STR, 1, DOMAIN_NAME_MAX_LEN, 1, 1, 0, 0, 0, {}},
    {DB_OPTION_ARG_USER, 0, PARAM_TYPE_STR, 1, <PERSON><PERSON><PERSON><PERSON>_NAME_MAX_LEN, 1, 1, 0, 0, 0, {}},
    {DB_OPTION_ARG_CMD, 0, PARAM_TYPE_STR, 1, MAX_LEN_GM_CMD_BUF, 1, OPTION_PARAM_MAX_NUM_CMD, 0, 0, 0, {}}
};

DbRuleItemT g_ruleItems[] = {
    {0, (int32_t)CMD_ARGV_NULL, {""}, {false}},
    {0, (int32_t)CMD_ARGV_HELP, {DB_OPTION_ARG_HELP}, {false}},
    {0, (int32_t)CMD_ARGV_CON, {DB_OPTION_ARG_DOMAIN_NAME, DB_OPTION_ARG_USER}, {false, false}},
    {0, (int32_t)CMD_ARGV_CMD, {DB_OPTION_ARG_DOMAIN_NAME, DB_OPTION_ARG_USER, DB_OPTION_ARG_CMD}, {false, false, true}}
};

Status GmCmdPrepare(DbOptionRuleT *optionRule)
{
    DB_POINTER(optionRule);
    return DbAllocOptionRuleItems(optionRule, ELEMENT_COUNT(g_optionItems), ELEMENT_COUNT(g_ruleItems));
}

// clang-format on
Status GetArgvType(DbOptionRuleT *optionRule, int32_t argc, char *argv[], ArgvOptionTypeE *type)
{
    DB_POINTER2(optionRule, type);
    Status ret = DbInitOptionItems(optionRule, g_optionItems, (int32_t)ELEMENT_COUNT(g_optionItems));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DbInitRuleItems(optionRule, g_ruleItems, (int32_t)ELEMENT_COUNT(g_ruleItems));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DbInitOptionParam(optionRule, argc, argv);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t ruleId = DbGetStartupRule(optionRule);
    if (ruleId < 0 || ruleId >= (int32_t)CMD_ARGV_BUTT) {
        return GMERR_DATA_EXCEPTION;
    }
    *type = (ArgvOptionTypeE)ruleId;
    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
