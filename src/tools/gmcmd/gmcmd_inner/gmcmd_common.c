/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Implementation of gmcmd Main
 * Author: hebaisheng
 * Create: 2022-01-18
 */

#include "gmcmd_common.h"
#include "gmcmd_conn.h"
#include "gmcmd_help.h"
#include "gmcmd_exec_base.h"
#include "gmcmd_log.h"
#include "gmcmd_parse_argv.h"
#include "db_option_parser.h"
#include "db_signal.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define GMCMD_PROMPT "gmcmd>"
#define MIN_SIZE_OF_CMD (2)

GmCmdContextT cmdContext;

void PrintPrompt(void)
{
    (void)DbPrintfDefault(GMCMD_PROMPT);
    (void)fflush(stdout);
};

void PrintLineNum(bool isBlank, uint32_t *lineNum)
{
    if (isBlank) {
        PrintPrompt();
    } else {
        (void)DbPrintfDefault("%5" PRIu32 ">", ++(*lineNum));
        (void)fflush(stdout);
    }
}

Status FetchOneLine(char *cmdBuf, uint32_t len, bool *isBlank)
{
    for (uint32_t i = 0; i < len - 1; ++i) {
        cmdBuf[i] = (char)getchar();
        if (*isBlank == true && (cmdBuf[i] != '\n' && cmdBuf[i] != ' ')) {
            *isBlank = false;
        }
        if (cmdBuf[i] == '\n') {
            cmdBuf[i] = ' ';
            cmdBuf[i + 1] = '\0';
            return GMERR_OK;
        }
    }
    int exChar;
    do {
        exChar = getchar();
    } while (exChar != EOF && exChar != '\n');
    (void)fflush(stdin);
    return GMERR_DATA_EXCEPTION;
}

bool CmdFinished(char *cmdBuf)
{
    if (strlen(cmdBuf) < MIN_SIZE_OF_CMD) {
        return false;
    }
    if (cmdBuf[strlen(cmdBuf) - MIN_SIZE_OF_CMD] == ';') {
        cmdBuf[strlen(cmdBuf) - MIN_SIZE_OF_CMD] = ' ';
        return true;
    }
    return false;
}

Status ReceiveCommand(char *cmdBuf, uint32_t cmdBufSize)
{
    uint32_t pos = 0;
    uint32_t lineNum = 1;
    bool isBlank = true;
    for (;;) {
        Status ret = FetchOneLine(cmdBuf + pos, cmdBufSize - pos, &isBlank);
        if (ret != GMERR_OK) {
            CMD_ERROR("Unsuccessful to Fetch command!");
            return ret;
        }
        pos = (uint32_t)strlen(cmdBuf);
        if (pos > cmdBufSize - 1) {
            CMD_ERROR("Command is too long.");  // LCOV_EXCL_LINE
            return GMERR_DATA_EXCEPTION;
        }
        if (CmdFinished(cmdBuf)) {
            break;
        }
        PrintLineNum(isBlank, &lineNum);
    }
    return GMERR_OK;
}

void InteractiveMain(void)
{
    char *cmdBuf = (char *)DB_MALLOC(sizeof(char) * (MAX_LEN_GM_CMD_BUF + 1));
    if (cmdBuf == NULL) {
        CMD_ERROR("Unsuccessful to malloc memory for command buffer!");  // LCOV_EXCL_LINE
        return;
    }
    (void)memset_s(cmdBuf, MAX_LEN_GM_CMD_BUF + 1, 0x00, MAX_LEN_GM_CMD_BUF + 1);
    for (;;) {
        PrintPrompt();
        if (ReceiveCommand(cmdBuf, MAX_LEN_GM_CMD_BUF + 1) == GMERR_OK) {
            ExecuteCommand(&cmdContext, cmdBuf);
        }
        (void)memset_s(cmdBuf, MAX_LEN_GM_CMD_BUF + 1, 0x00, MAX_LEN_GM_CMD_BUF + 1);
        if (cmdContext.eof) {
            break;
        }
    }
    DB_FREE(cmdBuf);
}

void SignalHandle(int32_t signal)
{
    (void)signal;
    (void)DbPrintfDefault("\n");
    GmCmdDisConnServer(&cmdContext);
    (void)fflush(stdout);
    exit(0);
}

void PrepareInteractive(void)
{
    cmdContext.eof = false;

    if (!TlsGetCltSameProcessDeploy()) {
        (void)DbSignalRegister(SIGINT, SignalHandle, DB_SIG_FLAG_NODEFER);
    }
}

void ProcessOptionsWithType(DbOptionRuleT *optionRule, ArgvOptionTypeE argvType, bool *stopCmd)
{
    *stopCmd = false;
    switch (argvType) {
        case CMD_ARGV_HELP:
            PrintArgvHelpInfo();
            *stopCmd = true;
            break;
            /* fall through */
        case CMD_ARGV_NULL:  // 不带参数的也需要进行登录连接，进入交互命令模式
        case CMD_ARGV_CON:   // 进行登录连接，进入交互命令模式
            if (GmCmdConnServer(optionRule, &cmdContext) != GMERR_OK) {
                *stopCmd = true;
            }
            break;
        case CMD_ARGV_CMD:
            ProcessCmdOption(optionRule, &cmdContext);
            *stopCmd = true;
            break;
        case CMD_ARGV_BUTT:
            *stopCmd = true;
            break;
        default:
            break;
    }
}

Status GmCmdMain(int32_t argc, char **argv)
{
    ArgvOptionTypeE argvType = CMD_ARGV_BUTT;
    DbOptionRuleT optionRule = {0};
    Status ret = GmCmdPrepare(&optionRule);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GetArgvType(&optionRule, argc, argv, &argvType);
    if (ret != GMERR_OK) {
        DbReleaseOptionRuleItems(&optionRule);
        CMD_ERROR("Command input is not correct!");  // LCOV_EXCL_LINE
        return GMERR_OK;
    }
    bool stopCmd = false;
    ProcessOptionsWithType(&optionRule, argvType, &stopCmd);
    DbReleaseOptionRuleItems(&optionRule);
    if (stopCmd) {
        return GMERR_OK;
    }
    PrepareInteractive();
    InteractiveMain();
    GmCmdDisConnServer(&cmdContext);
    (void)fflush(stdout);
    return GMERR_OK;
}

int32_t GmsCmdMain(int32_t argc, char **argv)
{
    return GmCmdMain(argc, argv);
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
