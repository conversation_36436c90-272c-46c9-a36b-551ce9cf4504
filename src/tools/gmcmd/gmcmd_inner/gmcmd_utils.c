/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Implementation gmcmd common utils
 * Author: hebaisheng
 * Create: 2022-01-27
 */

#include "gmcmd_utils.h"

uint32_t GmCmdStrSplit(char *input, const char *delim, uint32_t subStrNum, char *result[])
{
    uint32_t count = 0;
    char *context = NULL;
    for (char *token = strtok_r(input, delim, &context); token != NULL; token = strtok_r(NULL, delim, &context)) {
        result[count++] = token;
        if (context == NULL || strlen(context) == 0) {
            break;
        }
        if (count == subStrNum - 1) {
            result[count++] = context;
            break;
        }
    }
    return count;
}

void GmCmdStrStartWith(const char *input, const char *startStr, bool *isStartWith)
{
    *isStartWith = false;
    if (input == NULL || startStr == NULL) {
        return;
    }
    uint32_t len1 = (uint32_t)strlen(input);
    uint32_t len2 = (uint32_t)strlen(startStr);
    if ((len1 < len2) || (len1 == 0 || len2 == 0)) {
        return;
    }
    const char *tmp = startStr;
    uint32_t i = 0;
    while (*tmp != '\0') {
        if (*tmp != input[i]) {
            return;
        }
        tmp++;
        i++;
    }
    *isStartWith = true;
}
