/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Implementation of generate test data
 * Author: maokuancheng
 * Create: 2022/3/10
 */

#ifndef GMDBV5_GMCMD_GEN_H
#define GMDBV5_GMCMD_GEN_H

#include "gmcmd_exec_base.h"
#include "gmcmd_gen_utils.h"
#include "db_json_common.h"
#include "dm_data_prop.h"
#include "dm_data_define.h"  // 用来获取最大的schema size(for string and bite)

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

void GmCmdGenExec(GmCmdContextT *cmdContext, char *cmdParam);

Status GenParseGenCmd(GmCmdParamT *cmdParamStruct, char *cmd, GenDataCfgT *cfg);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif  // GMDBV5_GMCMD_GEN_H
