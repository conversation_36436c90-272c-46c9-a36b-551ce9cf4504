/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Implementation of connect and disconnect for gmcmd
 * Author: hebaisheng
 * Create: 2022-01-18
 */

#include "gmcmd_conn.h"
#include "gmcmd_log.h"
#include "db_option_parser.h"
#include "adpt_string.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

Status GmCmdConnServer(DbOptionRuleT *optionRule, GmCmdContextT *cmdContext)
{
    DB_POINTER(optionRule);
    char *optionServerName = DbGetStrParamByOptionName(optionRule, DB_OPTION_ARG_DOMAIN_NAME);
    if (optionServerName == NULL || DbStrCmp(optionServerName, SHM_UNIX_EMSERVER, 0) == 0) {
        optionServerName = DB_DEFAULT_DOMAIN_NAME;
    }
    char *userName = DbGetStrParamByOptionName(optionRule, DB_OPTION_ARG_USER_NAME);
    Status ret = TlsPerfEnvInit();
    if (ret != GMERR_OK) {
        return ret;
    }
    TlsConnOptionT option = {
        .domainName = optionServerName,
        .userName = userName,
        .namespaceName = NULL,
        .useReservedConn = false,
        .noMemLimit = true,
    };
    ret = TlsConnect(&cmdContext->conn, &cmdContext->stmt, &option);
    if (ret != GMERR_OK) {
        TlsPerfEnvUnInit();
        CMD_ERROR("Connect with(\"%s\") unsuccessfully!", optionServerName);  // LCOV_EXCL_LINE
        return ret;
    }
    cmdContext->connFlag = true;
    CMD_SUCCESS("Connect with(\"%s\") success!", optionServerName);
    return GMERR_OK;
}

void GmCmdDisConnServer(GmCmdContextT *cmdContext)
{
    if (cmdContext->connFlag) {
        TlsDisconnect(cmdContext->conn, cmdContext->stmt);
        TlsPerfEnvUnInit();
    }
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
