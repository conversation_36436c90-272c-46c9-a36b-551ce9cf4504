/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Implementation of generate test data
 * Author: maokuancheng
 * Create: 2022/03/10
 */

#ifndef GMDBV5_GMCMD_GEN_UTILS_H
#define GMDBV5_GMCMD_GEN_UTILS_H

#include "db_json_common.h"
#include "gmcmd_exec_base.h"
#include "adpt_types.h"
#include "dm_data_prop.h"
#include "dm_data_define.h"  // 用来获取最大的schema size(for string and bite)

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

// gen工具内部使用的一些数值最大最小值
// float
#define GEN_MAX_FLOAT_VALUE 10000.0
// time
#define GEN_TIME_FORMAT_MAX_LEN 256
#define GEN_MAX_TIME_VALUE 253402271999  // 9999-12-31 23:59:59
// bitmap
#define GEN_BITMAP_MAX_GEN_SIZE 64
#define GEN_BITMAP_MIN_GEN_SIZE 8
#define GM_BITS_NUM_OF_BYTE 8
// byte/str/fixed
#define GEN_STR_DEFAULT_MAX_LEN 32
#define GEN_STR_DEFAULT_MIN_LEN 1

// other宏定义
#define GEN_STR_NOT_SUPPORTED ";"
#define EXPORT_DATA_FILE_SUFFIX ".gmdata"
#define GEN_NEWEST_SCHEMA_DATA_STR "newest"
#define NEWEST_SCHEMA_VERSION 0xFFFFFFFF

#define GEN_RANDOM_FILE_PATH "/dev/urandom"

// 自定义错误码，仅供gmcmd使用
// 读命令行错误、读取配置字段错误、配置文件设置值不支持等错误
#define STATUS_INVALID_ARGUMENT 63001
// 拷贝相关
#define STATUS_OUT_OF_MEMORY 63002
#define STATUS_SECUREC_MEMORY_COPY_FAIL 63003
#define STATUS_SECUREC_STR_COPY_FAIL 63004
// 文件IO相关
#define STATUS_FILE_OPEN 63005
#define STATUS_FILE_IO_ERR 18001
#define STATUS_INVALID_FIELD 63006
// 获取vertex错误
#define STATUS_GET_VERTEX_ERROR 63007

// 用于处理解析或生成类string的一些辅助宏
#define DB_UPPER(c) (((c) >= 'a' && (c) <= 'z') ? ((c)-32) : (c))
#define IS_UPPER(c) ((c) >= 'A' && (c) <= 'Z')
#define IS_LOWER(c) ((c) >= 'a' && (c) <= 'z')
#define IS_NUMCHAR(c) ((c) >= '0' && (c) <= '9')
#define IS_SYMBOL(c)                                                                           \
    (((c) >= ' ' && (c) <= '/') || ((c) >= ':' && (c) <= '@') || ((c) >= '[' && (c) <= '`') || \
        ((c) >= '{' && (c) <= '~'))
#define GEN_ABS_INT64(val) ((val) >= 0 ? (val) : ((-1) * (val)))
#define SET_BIT_MAP_BIT_ONE(bitmap, site, unit) ((bitmap)[(site) / (unit)] |= ((uint32_t)1 << ((site) % (unit))))
#define SET_BIT_MAP_BIT_ZERO(bitmap, site, unit) ((bitmap)[(site) / (unit)] &= ~((uint32_t)1 << ((site) % (unit))))

typedef enum GenRuleT {
    GEN_RULE_AUTOMATIC = 0,
    GEN_RULE_LIST,
} GenRuleT;

typedef enum GenType {
    GEN_TYPE_RANDOM = 0,
    GEN_TYPE_SEQUENTIAL,
    GEN_TYPE_FIX_VALUE,
} GenTypeT;

typedef struct GenStrProperties {
    GenRuleT rule;
    GenTypeT type;
    uint32_t maxStrlen;
    uint32_t minStrlen;
    bool hasLowerCase;
    bool hasUpperCase;
    bool hasNumber;
    bool hasSpecialChar;
    char customChar[DB_MAX_INT8];
    char *fixValue;
    char dictionary[DB_MAX_INT8];
    uint32_t dictionaryLen;
} GenStrPropertiesT;

typedef struct GenInt8Properties {
    GenRuleT rule;
    GenTypeT type;
    int8_t maxValue;
    int8_t minValue;
    uint8_t step;
    int8_t fixValue;
    uint8_t currentValue;
} GenInt8PropertiesT;

typedef struct GenUint8Properties {
    GenRuleT rule;
    GenTypeT type;
    uint8_t maxValue;
    uint8_t minValue;
    uint8_t step;
    uint8_t fixValue;
    uint8_t currentValue;
} GenUint8PropertiesT;

typedef struct GenInt16Properties {
    GenRuleT rule;
    GenTypeT type;
    int16_t maxValue;
    int16_t minValue;
    uint16_t step;
    int16_t fixValue;
    uint16_t currentValue;
} GenInt16PropertiesT;

typedef struct GenUint16Properties {
    GenRuleT rule;
    GenTypeT type;
    uint16_t maxValue;
    uint16_t minValue;
    uint16_t step;
    uint16_t fixValue;
    uint16_t currentValue;
} GenUint16PropertiesT;

typedef struct GenInt32Properties {
    GenRuleT rule;
    GenTypeT type;
    int32_t maxValue;
    int32_t minValue;
    uint32_t step;
    int32_t fixValue;
    uint32_t currentValue;
} GenInt32PropertiesT;

typedef struct GenUint32PropertiesT {
    GenRuleT rule;
    GenTypeT type;
    uint32_t maxValue;
    uint32_t minValue;
    uint32_t step;
    uint32_t fixValue;
    uint32_t currentValue;
} GenUint32PropertiesT;

typedef struct GenInt64Properties {
    GenRuleT rule;
    GenTypeT type;
    int64_t maxValue;
    int64_t minValue;
    uint64_t step;
    int64_t fixValue;
    uint64_t currentValue;
} GenInt64PropertiesT;

typedef struct GenUint64Properties {
    GenRuleT rule;
    GenTypeT type;
    uint64_t maxValue;
    uint64_t minValue;
    uint64_t step;
    uint64_t fixValue;
    uint64_t currentValue;
} GenUint64PropertiesT;

typedef struct GenFloatProperties {
    GenRuleT rule;
    GenTypeT type;
    float maxValue;
    float minValue;
    float step;
    float fixValue;
    float currentValue;
} GenFloatPropertiesT;

typedef struct GenTimeProperties {
    GenRuleT rule;
    GenTypeT type;
    int64_t maxValue;
    int64_t minValue;
    int64_t step;
    int64_t fixValue;
    char format[GEN_TIME_FORMAT_MAX_LEN];
    int64_t currentValue;
} GenTimePropertiesT;

typedef struct GenByteProperties {
    GenRuleT rule;
    GenTypeT type;
    uint32_t maxStrlen;
    uint32_t minStrlen;
    uint8_t fixByteValue;
    uint32_t fixByteStrLen;
} GenBytePropertiesT;

typedef struct GenBooleanProperties {
    GenRuleT rule;
    GenTypeT type;
    bool fixValue;
} GenBooleanPropertiesT;

typedef struct GenBitmapProperties {
    GenRuleT rule;
    GenTypeT type;
    uint32_t maxBitlen;
    uint32_t minBitlen;
    char fixBitBuffer[GEN_BITMAP_MAX_GEN_SIZE];
    uint32_t fixBitSize;
} GenBitmapPropertiesT;

// bitfield
typedef struct GenBitfield8Properties {
    GenRuleT rule;
    GenTypeT type;
    uint8_t maxValue;
    uint8_t minValue;
    uint8_t step;
    uint8_t fixValue;
    uint8_t currentValue;
} GenBitfield8PropertiesT;

typedef struct GenBitfield16Properties {
    GenRuleT rule;
    GenTypeT type;
    uint16_t maxValue;
    uint16_t minValue;
    uint16_t step;
    uint16_t fixValue;
    uint16_t currentValue;
} GenBitfield16PropertiesT;

typedef struct GenBitfield32Properties {
    GenRuleT rule;
    GenTypeT type;
    uint32_t maxValue;
    uint32_t minValue;
    uint32_t step;
    uint32_t fixValue;
    uint32_t currentValue;
} GenBitfield32PropertiesT;

typedef struct GenBitfield64Properties {
    GenRuleT rule;
    GenTypeT type;
    uint64_t maxValue;
    uint64_t minValue;
    uint64_t step;
    uint64_t fixValue;
    uint64_t currentValue;
} GenBitfield64PropertiesT;

typedef struct GenVerifyProperties {
    uint32_t minMaxBytesSize;
    uint32_t minMaxStringSize;
} GenVerifyPropertiesT;

typedef struct GenDataCfg {
    GenInt8PropertiesT int8Properties;
    GenUint8PropertiesT uint8Properties;
    GenInt8PropertiesT charProperties;
    GenUint8PropertiesT ucharProperties;

    GenInt16PropertiesT int16Properties;
    GenUint16PropertiesT uint16Properties;
    GenInt32PropertiesT int32Properties;
    GenUint32PropertiesT uint32Properties;
    GenInt64PropertiesT int64Properties;
    GenUint64PropertiesT uint64Properties;

    GenUint64PropertiesT resourceProperties;
    GenFloatPropertiesT floatProperties;
    GenFloatPropertiesT doubleProperties;
    GenBooleanPropertiesT booleanProperties;

    GenTimePropertiesT timeProperties;
    GenStrPropertiesT strProperties;
    GenBytePropertiesT byteProperties;
    GenBytePropertiesT fixedProperties;
    GenBitmapPropertiesT bitmapProperties;

    GenUint8PropertiesT bitfield8Properties;
    GenUint16PropertiesT bitfield16Properties;
    GenUint32PropertiesT bitfield32Properties;
    GenUint64PropertiesT bitfield64Properties;
    GenVerifyPropertiesT *schemaverify;
} GenDataCfgT;

// 1
Status GenParseInt8Cfg(DbJsonT *cfgJson, GenInt8PropertiesT *properties);
Status GenParseUint8Cfg(DbJsonT *cfgJson, GenUint8PropertiesT *properties);
Status GenParseInt16Cfg(DbJsonT *cfgJson, GenInt16PropertiesT *properties);
Status GenParseUint16Cfg(DbJsonT *cfgJson, GenUint16PropertiesT *properties);
// 2
Status GenParseInt32Cfg(DbJsonT *cfgJson, GenInt32PropertiesT *properties);
Status GenParseUint32Cfg(DbJsonT *cfgJson, GenUint32PropertiesT *properties);
Status GenParseInt64Cfg(DbJsonT *cfgJson, GenInt64PropertiesT *properties);
Status GenParseUint64Cfg(DbJsonT *cfgJson, GenUint64PropertiesT *properties);
// 3
Status GenParseStrCfg(DbJsonT *cfgJson, GenStrPropertiesT *strProperties);
Status GenParseFloatCfg(DbJsonT *cfgJson, GenFloatPropertiesT *floatProperties);
// double和float使用同一个配置
Status GenParseDoubleCfg(GenFloatPropertiesT *floatProperties, GenFloatPropertiesT *doubleProperties);
Status GenParseTimeCfg(DbJsonT *cfgJson, GenTimePropertiesT *timeProperties);
// 4
Status GenParseByteCfg(DbJsonT *cfgJson, GenBytePropertiesT *properties);
Status GenParseBooleanCfg(DbJsonT *cfgJson, GenBooleanPropertiesT *boolProperties);
Status GenParseBitmapCfg(DbJsonT *cfgJson, GenBitmapPropertiesT *properties);
// 5
void GenInitBitfield8Cfg(GenUint8PropertiesT *properties);
void GenInitBitfield16Cfg(GenUint16PropertiesT *properties);
void GenInitBitfield32Cfg(GenUint32PropertiesT *properties);
void GenInitBitfield64Cfg(GenUint64PropertiesT *properties);
void GenInitUcharCfg(GenUint8PropertiesT *properties);
void GenInitCharCfg(GenInt8PropertiesT *properties);

// ===================== generate data begin ============================
// 1 integer
Status GenGenerateInt8(GenInt8PropertiesT *properties, int8_t *value);
Status GenGenerateUint8(GenUint8PropertiesT *properties, uint8_t *value);
Status GenGenerateInt16(GenInt16PropertiesT *properties, int16_t *value);
Status GenGenerateUint16(GenUint16PropertiesT *properties, uint16_t *value);
Status GenGenerateInt32(GenInt32PropertiesT *properties, int32_t *value);
Status GenGenerateUint32(GenUint32PropertiesT *properties, uint32_t *value);
Status GenGenerateInt64(GenInt64PropertiesT *properties, int64_t *value);
Status GenGenerateUint64(GenUint64PropertiesT *properties, uint64_t *value);

// 2 others
Status GenGenerateBoolean(GenBooleanPropertiesT *properties, bool *value);
Status GenGenerateFloat(GenFloatPropertiesT *properties, float *value);
Status GenGenerateDouble(GenFloatPropertiesT *properties, double *value);
Status GenGenerateTime(GenTimePropertiesT *properties, int64_t *value);
Status GenGenerateBytes(GenBytePropertiesT *properties, DmValueT *value, uint32_t size);
Status GenGenerateString(GenStrPropertiesT *properties, DmValueT *value, uint32_t size);
Status GenGenerateResource(GenInt64PropertiesT *properties, int64_t *value);
Status GenGenerateBitmap(GenBitmapPropertiesT *properties, DmValueT *value, uint32_t size);

// ==================== other function ===================================
Status GenInitRandomFd(void);
uint64_t GenGetRandom(void);
void GenDestroyRandomFd(void);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
