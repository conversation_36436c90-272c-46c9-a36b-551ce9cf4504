/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Implementation of delete vertexLabel Range
 * Author: wuchenyu
 * Create: 2022-01-18
 */

#include "gmcmd_delete.h"
#include "gmcmd_log.h"
#include "gmc_graph.h"
#include "gmcmd_utils.h"
#include "adpt_string.h"
#include "gmc_types.h"
#include "gmcmd_exec_base.h"
#include "string.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define DEL_PARAM_NUM (4)
#define FOURTH_CHAR_SEPARATED_BY_SPACE (3)
#define THIRD_CHAR_SEPARATED_BY_SPACE (2)
#define DELETE_VERTEX_TABLE_PARAM_FROM "from"
#define DELETE_VERTEX_TABLE_PARAM_WHERE "where"

// 从delete后开始解析.格式:(delete)from <table_name> where <node_name.field_name>  <operator>  <field_value> [and…]
void DeleteVertexLabelRange(GmCmdContextT *cmdContext, const char *name, const char *filter)
{
    DB_POINTER(cmdContext->stmt);
    Status ret = GmcPrepareStmtByLabelName(cmdContext->stmt, name, GMC_OPERATION_DELETE);
    if (ret != GMERR_OK) {
        CMD_ERROR("Range delete vertex label prepare stmt unsuccessfully, ret = %" PRId32 ".", ret);  // LCOV_EXCL_LINE
        return;
    }
    ret = GmcSetFilter(cmdContext->stmt, filter);
    if (ret != GMERR_OK) {
        CMD_ERROR("Range delete vertex label set filter unsuccessfully, ret = %" PRId32 ".", ret);  // LCOV_EXCL_LINE
        return;
    }
    ret = GmcExecute(cmdContext->stmt);
    if (ret == GMERR_OK) {
        CMD_SUCCESS("DEL success.");
    } else {
        CMD_ERROR("Delete vertex label unsuccessfully, ret = %" PRId32 ".", ret);  // LCOV_EXCL_LINE
    }
    return;
}

void GmCmdDeleteInnerExec(GmCmdContextT *cmdContext, char *cmdParam)
{
    if (cmdContext->cmdType != GM_CMD_DELETE) {
        CMD_ERROR("Command type is not correct.");  // LCOV_EXCL_LINE
        return;
    }
    // 按4个空格分
    char *result[DEL_PARAM_NUM] = {0};
    uint32_t count = GmCmdStrSplit((char *)cmdParam, " ", DEL_PARAM_NUM, result);
    // vertex类型最少四个
    if (count < (DEL_PARAM_NUM)) {
        CMD_ERROR("Number of parameters is less than four");  // LCOV_EXCL_LINE
        return;
    }
    // 加校验防止第四个解析参数是非法的字符
    if ((DbStrCmp(result[0], DELETE_VERTEX_TABLE_PARAM_FROM, true) == 0) &&
        (DbStrCmp(result[THIRD_CHAR_SEPARATED_BY_SPACE], DELETE_VERTEX_TABLE_PARAM_WHERE, true) == 0)) {
        if (result[FOURTH_CHAR_SEPARATED_BY_SPACE] == NULL || strlen(result[FOURTH_CHAR_SEPARATED_BY_SPACE]) == 0) {
            CMD_ERROR("Parameters to Delete vertexLabel are not correct.");  // LCOV_EXCL_LINE
            return;
        }
        DeleteVertexLabelRange(cmdContext, DbLeftTrim(result[1]), DbLeftTrim(result[FOURTH_CHAR_SEPARATED_BY_SPACE]));
        return;
    }
    CMD_ERROR("Parameters to Delete are not correct.");
    return;
}

void GmCmdDeleteExec(GmCmdContextT *cmdContext, char *cmdParam)
{
    DB_POINTER(cmdContext->stmt);
    if (cmdParam == NULL || strlen(cmdParam) == 0) {
        CMD_ERROR("Parameter to Delete is NULL!");  // LCOV_EXCL_LINE
        return;
    }
    GmCmdDeleteInnerExec(cmdContext, cmdParam);
}
#ifdef __cplusplus
}
#endif /* __cplusplus */
