/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: Header file of connect and disconnect for gmcmd
 * Author: hebaisheng
 * Create: 2022-01-18
 */

#ifndef GMDBV5_CMD_CONN_H
#define GMDBV5_CMD_CONN_H

#include "gmcmd_exec_base.h"
#include "db_option_parser.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

Status GmCmdConnServer(DbOptionRuleT *optionRule, GmCmdContextT *cmdContext);

void GmCmdDisConnServer(GmCmdContextT *cmdContext);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif  // GMDBV5_CMD_CONN_H
