/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Implementation of del kvtable
 * Author: hebaisheng
 * Create: 2023-10-27
 */

#include "gmcmd_del.h"
#include "gmcmd_log.h"
#include "gmcmd_utils.h"
#include "adpt_string.h"
#include "gmc_kv.h"
#include "gmc_types.h"
#include "gmcmd_exec_base.h"
#include "string.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define DEL_PARAM_NUM (3)
#define DEL_KV_NAME_INDEX (1)
#define DEL_KV_KEY_INDEX (2)
#define DELETE_TABLE_TYPE_KV "kvtable"

// 从delete后开始解析.格式:(delete)kvtable <table_name> <key>;
void DeleteKvTable(GmCmdContextT *cmdContext, const char *name, const char *key)
{
    DB_POINTER(cmdContext->stmt);
    Status ret = GmcKvPrepareStmtByLabelName(cmdContext->stmt, name);
    if (ret != GMERR_OK) {
        CMD_ERROR("Prepare stmt unsuccessfully when del kvtable, ret = %" PRId32 ".", ret);  // LCOV_EXCL_LINE
        return;
    }
    ret = GmcKvRemove(cmdContext->stmt, key, ((uint32_t)strlen(key) + 1));
    if (ret == GMERR_OK) {
        CMD_SUCCESS("DEL success");
    } else {
        CMD_ERROR("Del kvtable unsuccessfully, ret = %" PRId32 ".", ret);  // LCOV_EXCL_LINE
    }
    return;
}

void GmCmdDelInnerExec(GmCmdContextT *cmdContext, char *cmdParam)
{
    if (cmdContext->cmdType != GM_CMD_DEL) {
        CMD_ERROR("Command type is not correct.");  // LCOV_EXCL_LINE
        return;
    }
    // 按3个空格分
    char *result[DEL_PARAM_NUM] = {0};
    uint32_t count = GmCmdStrSplit((char *)cmdParam, " ", DEL_PARAM_NUM, result);
    // kv类型最少三个参数
    if (count < (DEL_PARAM_NUM)) {
        CMD_ERROR("Number of parameters is less than three");  // LCOV_EXCL_LINE
        return;
    }
    // 如果第一个字符是kvtable,就传给KV接口.加校验防止第四个解析参数是非法的字符
    if (DbStrCmp(result[0], DELETE_TABLE_TYPE_KV, true) != 0) {
        CMD_ERROR("Parameters to Del are not correct, you may miss the keyword:kvtable.");  // LCOV_EXCL_LINE
        return;
    }
    DeleteKvTable(cmdContext, DbLeftTrim(result[DEL_KV_NAME_INDEX]), DbLeftTrim(result[DEL_KV_KEY_INDEX]));
    return;
}

void GmCmdDelExec(GmCmdContextT *cmdContext, char *cmdParam)
{
    DB_POINTER(cmdContext->stmt);
    if (cmdParam == NULL || strlen(cmdParam) == 0) {
        CMD_ERROR("Parameter to Delete is NULL!");  // LCOV_EXCL_LINE
        return;
    }
    GmCmdDelInnerExec(cmdContext, cmdParam);
}
#ifdef __cplusplus
}
#endif /* __cplusplus */
