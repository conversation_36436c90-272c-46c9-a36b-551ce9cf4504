/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: Implementation of select
 * Author: hebaisheng
 * Create: 2023-10-11
 */

#include "gmcmd_select.h"
#include "gmcmd_log.h"
#include "gmc_graph.h"
#include "gmc.h"
#include "gmcmd_utils.h"
#include "adpt_string.h"
#include "gmc_types.h"
#include "gmcmd_exec_base.h"
#include "string.h"

#define SELECT_PARAM_NUM (5)
#define SELECT_PARAM_NUM_MIN (3)
#define SELECT_PARAM_FIRST_INDEX (0)
#define SELECT_PARAM_FROM_INDEX (1)
#define SELECT_PARAM_LABEL_NAME_INDEX (2)
#define SELECT_PARAM_WHERE_INDEX (3)
#define SELECT_PARAM_FILTER_INDEX (4)
#define SELECT_DATA "*"
#define SELECT_COUNT "count(*)"
#define SELECT_PARAM_FROM "from"
#define SELECT_PARAM_WHERE "where"
#define JSON_VIEW_PRINT_FLAG (GMC_JSON_EXPORT_NULL_INFO | GMC_JSON_INDENT(4))

inline static void PrintErrMsg(const char *step, char **result, Status ret)
{
    CMD_ERROR("%s process fail ret = %d, table name = %s, filter = %s.", step, ret,
        DbLeftTrim(result[SELECT_PARAM_LABEL_NAME_INDEX]),
        DbLeftTrim(result[SELECT_PARAM_FILTER_INDEX]));  // LCOV_EXCL_LINE
}

Status ProcessPrepareLabel(GmCmdContextT *cmdContext, char **result)
{
    Status ret = GmcPrepareStmtByLabelName(
        cmdContext->stmt, DbLeftTrim(result[SELECT_PARAM_LABEL_NAME_INDEX]), GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        PrintErrMsg("Select step1", result, ret);
        return ret;
    }
    char *filterStr = DbLeftTrim(result[SELECT_PARAM_FILTER_INDEX]);
    bool withFilter =
        filterStr != NULL && DbStrCmp(SELECT_PARAM_WHERE, DbLeftTrim(result[SELECT_PARAM_WHERE_INDEX]), false) == 0;
    if (withFilter) {
        ret = GmcSetFilter(cmdContext->stmt, filterStr);
        if (ret != GMERR_OK) {
            PrintErrMsg("Select step2", result, ret);
            return ret;
        }
    }
    return ret;
}

void PrintSelectResultMsg(char **result, uint64_t count)
{
    char *filterStr = DbLeftTrim(result[SELECT_PARAM_FILTER_INDEX]);
    bool withFilter =
        filterStr != NULL && DbStrCmp(SELECT_PARAM_WHERE, DbLeftTrim(result[SELECT_PARAM_WHERE_INDEX]), false) == 0;
    if (withFilter) {
        CMD_SUCCESS("There are %" PRIu64 " obj in table %s where %s", count,
            DbLeftTrim(result[SELECT_PARAM_LABEL_NAME_INDEX]), filterStr);
    } else {
        CMD_SUCCESS("%" PRIu64 " records", count);
    }
}

void ProcessSelectCountOption(GmCmdContextT *cmdContext, char **result)
{
    Status ret = ProcessPrepareLabel(cmdContext, result);
    if (ret != GMERR_OK) {
        return;
    }
    ret = GmcSetOutputFormat(cmdContext->stmt, "COUNT(1)");
    if (ret != GMERR_OK) {
        PrintErrMsg("Select count step3", result, ret);
        return;
    }
    ret = GmcExecute(cmdContext->stmt);
    if (ret != GMERR_OK) {
        PrintErrMsg("Select count step4", result, ret);
        return;
    }
    uint64_t count = 0;
    bool isEof = false;
    while (!isEof) {
        ret = GmcFetch(cmdContext->stmt, &isEof);
        if (ret != GMERR_OK) {
            PrintErrMsg("Select count step5", result, ret);
            return;
        }
        if (isEof) {
            break;
        }
        bool isNull = true;
        uint32_t aggrSize = (uint32_t)sizeof(uint64_t);
        ret = GmcGetPropertyById(cmdContext->stmt, 0, &count, &aggrSize, &isNull);
        if (ret != GMERR_OK) {
            PrintErrMsg("Select count step6", result, ret);
            return;
        }
    }
    PrintSelectResultMsg(result, count);
}

void ProcessSelectDataOption(GmCmdContextT *cmdContext, char **result)
{
    Status ret = ProcessPrepareLabel(cmdContext, result);
    if (ret != GMERR_OK) {
        return;
    }
    ret = GmcExecute(cmdContext->stmt);
    if (ret != GMERR_OK) {
        PrintErrMsg("Select data step3", result, ret);
        return;
    }
    uint64_t count = 0;
    bool isEof = false;
    while (!isEof) {
        ret = GmcFetch(cmdContext->stmt, &isEof);
        if (ret != GMERR_OK) {
            PrintErrMsg("Select data step4", result, ret);
            return;
        }
        if (isEof) {
            break;
        }
        char *jsonStr = NULL;
        ret = GmcDumpVertexToJson(cmdContext->stmt, JSON_VIEW_PRINT_FLAG, &jsonStr);
        if (ret != GMERR_OK) {
            PrintErrMsg("Select data step5", result, ret);
            return;
        }
        (void)DbPrintfDefault("-----------------object(%" PRIu64 ") details-----------------", count);
        (void)DbPrintfDefault("\n");
        (void)DbPrintfDefault("%s", jsonStr);
        (void)DbPrintfDefault("\n");
        count++;
        GmcFreeJsonStr(cmdContext->stmt, jsonStr);
    }
    PrintSelectResultMsg(result, count);
}

void GmCmdSelectInnerExec(GmCmdContextT *cmdContext, char *cmdParam)
{
    char *result[SELECT_PARAM_NUM] = {0};
    uint32_t count = GmCmdStrSplit((char *)cmdParam, " ", SELECT_PARAM_NUM, result);
    if (count < SELECT_PARAM_NUM_MIN) {
        CMD_ERROR("You select option param is incorrect.");  // LCOV_EXCL_LINE
        return;
    }
    if (DbStrCmp(SELECT_PARAM_FROM, DbLeftTrim(result[SELECT_PARAM_FROM_INDEX]), false) != 0) {
        CMD_ERROR("You select option param is incorrect, miss from or where.");  // LCOV_EXCL_LINE
        return;
    }
    if (DbStrCmp(SELECT_COUNT, DbLeftTrim(result[SELECT_PARAM_FIRST_INDEX]), false) == 0) {
        ProcessSelectCountOption(cmdContext, result);
    } else if (DbStrCmp(SELECT_DATA, DbLeftTrim(result[SELECT_PARAM_FIRST_INDEX]), false) == 0) {
        ProcessSelectDataOption(cmdContext, result);
    } else {
        CMD_ERROR("You 'select *' or 'select count(*)' option param is incorrect.");  // LCOV_EXCL_LINE
        return;
    }
}

void GmCmdSelectExec(GmCmdContextT *cmdContext, char *cmdParam)
{
    DB_POINTER(cmdContext->stmt);
    if (cmdParam == NULL || strlen(cmdParam) == 0) {
        CMD_ERROR("Select param is null!");  // LCOV_EXCL_LINE
        return;
    }
    if (cmdContext->cmdType != GM_CMD_SELECT) {
        CMD_ERROR("Command type Error.");  // LCOV_EXCL_LINE
        return;
    }
    GmCmdSelectInnerExec(cmdContext, cmdParam);
}
