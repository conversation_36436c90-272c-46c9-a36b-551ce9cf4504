/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: gmcmd log
 * Author: hebaisheng
 * Create: 2022-01-18
 */

#ifndef GMDBV5_GMCMD_LOG_H
#define GMDBV5_GMCMD_LOG_H

#include "db_utils.h"
#include "tool_utils.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define CMD_SUCCESS(format, ...)                           \
    do {                                                   \
        (void)DbPrintfDefault(NONE format, ##__VA_ARGS__); \
        (void)DbPrintfDefault("\n");                       \
    } while (0)  // NOLINT

#define CMD_ERROR(format, ...)                                        \
    do {                                                              \
        (void)DbPrintfDefault("(ERROR) " NONE format, ##__VA_ARGS__); \
        (void)DbPrintfDefault("\n");                                  \
    } while (0)  // NOLINT

#define CMD_INFO(format, ...)                                        \
    do {                                                             \
        (void)DbPrintfDefault("(INFO) " NONE format, ##__VA_ARGS__); \
        (void)DbPrintfDefault("\n");                                 \
    } while (0)  // NOLINT

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif  // GMDBV5_GMCMD_LOG_H
