project(gmcmd)

if (FUZZ OR COVERAGE)
    ADD_SUBDIRECTORY(gmcmd_inner)
endif()

ADD_COMPILE_OPTIONS(-Wl,--copy-dt-needed-entries -fPIE)

include_sub_directories_recursively(${CMAKE_CURRENT_SOURCE_DIR})
include_sub_directories_recursively(${GMDB_CLIENT_PATH})

aux_source_directory(. SRC_MAIN_LIST)
aux_source_directory(${gmcmd_SOURCE_DIR}/gmcmd_inner SRC_GMCMD_LIST)

if (TOOLS_EMBED_SO)
  generate_tools_embed_so(gmcmd_embed "${SRC_GMCMD_LIST}" "${TOOL_NAME}")
endif()

# 编译patch_obj不编译可执行程序
if(COMPILE_PATCH_OBJ)
  return()
endif()

add_executable (gmcmd ${SRC_MAIN_LIST} ${SRC_GMCMD_LIST})

if(STRIP)
  separate_debug_info(gmcmd)
endif()

target_link_libraries(gmcmd ${TOOL_NAME})

install(TARGETS gmcmd
  RUNTIME DESTINATION bin
)
