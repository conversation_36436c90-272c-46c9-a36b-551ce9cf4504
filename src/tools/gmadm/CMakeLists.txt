project(gmadm)

if (FUZZ OR COVERAGE)
    ADD_SUBDIRECTORY(gmadm_inner)
endif()

ADD_COMPILE_OPTIONS(-Wl,--copy-dt-needed-entries -fPIE)

include_sub_directories_recursively(${CMAKE_CURRENT_SOURCE_DIR})
include_sub_directories_recursively(${GMDB_CLIENT_PATH})

aux_source_directory(. SRC_MAIN_LIST)
aux_source_directory(${gmadm_SOURCE_DIR}/gmadm_inner SRC_GMADM_LIST)

set(SRC_GMADM_LIST ${SRC_MAIN_LIST} ${SRC_GMADM_LIST})

# 编译patch_obj不编译可执行程序
if(COMPILE_PATCH_OBJ)
  return()
endif()

if (NOT TOOLS_EMBED_SO)
ADD_EXECUTABLE (gmadm ${SRC_GMADM_LIST})
if(STRIP)
  separate_debug_info(gmadm)
endif()
target_link_libraries(gmadm ${TOOL_NAME})
install(TARGETS gmadm
  RUNTIME DESTINATION bin
)
endif()

