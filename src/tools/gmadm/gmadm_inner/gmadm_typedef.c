/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: gmadm_typedef.c
 * Description:
 * Create: 2023-07-20
 */
#include <stdio.h>
#include "db_option_parser.h"
#include "db_config.h"
#include "tool_utils.h"
#include "gmadm_typedef.h"

#ifdef __cplusplus
extern "C" {
#endif

#define PARAMETER_COUNT_ONE 1
#define ADM_USER_MAX_LEN DB_CFG_PARAM_MAX_STRING
#define ADM_SERVER_MAX_LEN DB_CFG_PARAM_MAX_STRING
#define ADM_PARAM_NAME_MAX_LEN DB_CFG_PARAM_MAX_STRING

/*
 * 用途：定义命令选项以及每个选项的配置属性射
 * 是否并发初始化：否
 * 是否并发读写：只读，并发不影响
 * 并发方案：无
 */

static const DbOptionItemT g_gmdbAdmOptionItems[] = {
    {DB_OPTION_ARG_HELP, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, false, false, 0, {}},
    {ADM_OPTION_ARG_USER_NAME, 0, PARAM_TYPE_STR, 0, ADM_USER_MAX_LEN, PARAMETER_COUNT_ONE, PARAMETER_COUNT_ONE, false,
        false, 0, {}},
    {DB_OPTION_ARG_DOMAIN_NAME, 0, PARAM_TYPE_STR, 0, ADM_SERVER_MAX_LEN, PARAMETER_COUNT_ONE, PARAMETER_COUNT_ONE,
        false, false, 0, {}},
    {ADM_OPTION_ARG_ADM_CMD, 0, PARAM_TYPE_STR, 0, ADM_PARAM_NAME_MAX_LEN, PARAMETER_COUNT_ONE, PARAMETER_COUNT_ONE,
        false, false, 0, {}}};

/*
 * 用途：定义每条规则具体的内容
 * 是否并发初始化：否
 * 是否并发读写：只读，并发不影响
 * 并发方案：无
 */
// 这里需要和GmAdmStartupRuleE枚举体对应上
static const DbRuleItemT g_gmdbAdmRuleItems[] = {
    {0, (int32_t)GMADM_RULE_CMD,
        {ADM_OPTION_ARG_USER_NAME, DB_OPTION_ARG_DOMAIN_NAME, ADM_OPTION_ARG_ADM_CMD, DB_OPTION_ARG_USER_NAME},
        {true, true, true, false}},
    {0, (int32_t)GMADM_RULE_HELP, {DB_OPTION_ARG_HELP}, {true}}};

Status GmAdmAllocOptionRuleItems(DbOptionRuleT *optionRule)
{
    DB_POINTER(optionRule);
    return DbAllocOptionRuleItems(optionRule, ELEMENT_COUNT(g_gmdbAdmOptionItems), ELEMENT_COUNT(g_gmdbAdmRuleItems));
}

// 初始化参数解析模块
Status GmAdmOptionInit(DbOptionRuleT *optionRule, int32_t argc, char *argv[])
{
    DB_POINTER(optionRule);
    // 注册选项
    Status ret = DbInitOptionItems(optionRule, g_gmdbAdmOptionItems, ELEMENT_COUNT(g_gmdbAdmOptionItems));
    if (ret != GMERR_OK) {
        return ret;
    }
    // 注册规则
    ret = DbInitRuleItems(optionRule, g_gmdbAdmRuleItems, ELEMENT_COUNT(g_gmdbAdmRuleItems));
    if (ret != GMERR_OK) {
        return ret;
    }
    // 解析命令行传参
    return DbInitOptionParam(optionRule, argc, argv);
}

#ifdef __cplusplus
}
#endif
