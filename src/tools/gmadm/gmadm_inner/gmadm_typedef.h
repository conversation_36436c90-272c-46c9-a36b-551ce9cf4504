/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: gmadm_typedef.h
 * Description: Header file for gmadm_typedef.
 * Create: 2023-07-19
 */

#ifndef GMADM_TYPEDEF_H
#define GMADM_TYPEDEF_H

#include "adpt_types.h"
#include "db_option_parser.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define ADM_OPTION_ARG_USER_NAME "-u"
#define ADM_OPTION_ARG_ADM_CMD "-c"

// gmadm规则编号
typedef enum GmAdmStartRule { GMADM_RULE_CMD, GMADM_RULE_HELP } GmAdmStartupRuleE;

Status GmAdmAllocOptionRuleItems(DbOptionRuleT *optionRule);
Status GmAdmOptionInit(DbOptionRuleT *optionRule, int32_t argc, char *argv[]);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
