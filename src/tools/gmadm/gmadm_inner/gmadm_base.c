/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: gmadm source
 * Create: 2023-07-20
 * Notes:
 */

#include "gmadm_base.h"
#include "db_option_parser.h"
#include "tool_utils.h"
#include "gmadm_typedef.h"
#include "adpt_string.h"
#include "gmc.h"
#include "tool_main.h"

#ifdef __cplusplus
extern "C" {
#endif

void GmAdmTruncateExec(GmAdmContextT *admContext, char *cmdParam);
void GmAdmDropExec(GmAdmContextT *admContext, char *cmdParam);
void GmAdmHelpExec(GmAdmContextT *admContext, char *cmdParam);

// 新增adm处理类型可以在这里添加实现函数
GmAdmExecInfoT g_admExecInfo[] = {
    {GM_ADM_HELP, "HELP", GmAdmHelpExec},
    {GM_ADM_TRUNCATE, "TRUNCATE", GmAdmTruncateExec},
    {GM_ADM_DROP, "DROP", GmAdmDropExec},
};

GmAdmTypeE ParseCmdType(const char *token)
{
    GmAdmTypeE cmdType = GM_ADM_BUTT;
    int32_t cmdNum = ELEMENT_COUNT(g_admExecInfo);
    for (int32_t i = 0; i < cmdNum; ++i) {
        if (DbStrCmp(g_admExecInfo[i].describe, token, true) == 0) {
            cmdType = g_admExecInfo[i].cmdType;
            break;
        }
    }
    return cmdType;
}

void DropVertexLabel(GmAdmContextT *admContext, const char *name)
{
    DB_POINTER2(admContext->stmt, name);
    Status ret = GmcDropVertexLabel(admContext->stmt, name);
    if (ret == GMERR_OK) {
        ADM_SUCCESS("Drop vertex label successfully.");
    } else {
        ADM_ERROR("Drop vertex label unsuccessfully, ret = %" PRId32 ".", ret);
    }
}

void TruncateVertexLabel(GmAdmContextT *admContext, const char *name)
{
    Status ret = GmcTruncateVertexLabel(admContext->stmt, name);
    if (ret == GMERR_OK) {
        ADM_SUCCESS("Truncate vertex label successfully.");
    } else {
        ADM_ERROR("Truncate vertex label unsuccessfully, ret = %" PRId32 ".", ret);
    }
}

uint32_t GmAdmStrSplit(char *input, char *delim, uint32_t subStrNum, char *result[])
{
    uint32_t count = 0;
    char *context = NULL;
    for (char *token = strtok_r(input, delim, &context); token != NULL; token = strtok_r(NULL, delim, &context)) {
        result[count++] = token;
        if (context == NULL || strlen(context) == 0) {
            break;
        }
        if (count == subStrNum - 1) {
            result[count++] = context;
            break;
        }
    }
    return count;
}

Status CopyCmdToBuffer(const DbOptionParamT *param, uint32_t paramNum, char *cmdBuf, uint32_t bufLen)
{
    size_t cmdPos = 0;
    size_t delimLen = strlen(DELIM_STR);
    for (uint32_t i = 0; i < paramNum; ++i) {
        size_t strLen = strlen(param->paramVal[i].strVal);
        if ((cmdPos + strLen) < cmdPos || (cmdPos + strLen) < strLen || (cmdPos + strLen) >= bufLen) {
            ADM_ERROR("Option Command is too long.");
            return GMERR_DATA_EXCEPTION;
        }
        errno_t ret = memcpy_s(cmdBuf + cmdPos, strLen, param->paramVal[i].strVal, strLen);
        if (SECUREC_UNLIKELY(ret != EOK)) {
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        cmdPos += strLen;
        if (i < paramNum - 1) {
            if ((cmdPos + delimLen) >= bufLen) {
                ADM_ERROR("Option Command is too long.");
                return GMERR_DATA_EXCEPTION;
            }
            ret = memcpy_s(cmdBuf + cmdPos, delimLen, DELIM_STR, delimLen);
            if (SECUREC_UNLIKELY(ret != EOK)) {
                return GMERR_MEMORY_OPERATE_FAILED;
            }
            cmdPos += delimLen;
        }
    }
    return GMERR_OK;
}

void DropAndTruncateInnerExec(GmAdmContextT *admContext, char *cmdParam)
{
    char *result[DROP_PARAM_NUM] = {0};
    uint32_t count = GmAdmStrSplit((char *)cmdParam, " ", DROP_PARAM_NUM, result);
    if (count < DROP_PARAM_NUM) {
        ADM_ERROR("You need both input table and table name.");
        return;
    }
    if (DbStrCmp(result[0], DROP_TABLE_TYPE_VERTEX, true) == 0) {
        admContext->cmdType == GM_ADM_DROP ? DropVertexLabel(admContext, DbLeftTrim(result[1])) :
                                             TruncateVertexLabel(admContext, DbLeftTrim(result[1]));
    } else {
        ADM_ERROR("Table type \"%s\" is not support", result[0]);
    }
}

void GmAdmHelpExec(GmAdmContextT *admContext, char *cmdParam)
{
    const char *title = "help cmd for gmadm:";
    const char *helpOption[][CMD_SUMMARY_COL_NUM] = {{"cmd", "table", "table_name"}};
    DbPrintManual(title, helpOption, ELEMENT_COUNT(helpOption), NULL);
}

void GmAdmTruncateExec(GmAdmContextT *admContext, char *cmdParam)
{
    DB_POINTER(admContext->stmt);
    if (cmdParam == NULL || strlen(cmdParam) == 0) {
        ADM_ERROR("Truncate param is null!");
        return;
    }
    DropAndTruncateInnerExec(admContext, cmdParam);
}

void GmAdmDropExec(GmAdmContextT *admContext, char *cmdParam)
{
    DB_POINTER(admContext->stmt);
    if (cmdParam == NULL || strlen(cmdParam) == 0) {
        ADM_ERROR("drop param is null!");
        return;
    }
    DropAndTruncateInnerExec(admContext, cmdParam);
}

void ExecuteCommand(GmAdmContextT *admContext, char *cmd)
{
    char *cmdStr = TrimInvisibleChar((char *)cmd);
    if (cmdStr == NULL || strlen(cmdStr) == 0) {
        ADM_ERROR("Command is empty!");
        return;
    }
    char *leftStr = NULL;
    char *token = strtok_r(cmdStr, DELIM_STR, &leftStr);
    GmAdmTypeE cmdType = ParseCmdType(token);
    if (cmdType >= GM_ADM_BUTT) {
        ADM_ERROR("Unknown command:%s", token);
        return;
    }
    admContext->cmdType = cmdType;
    g_admExecInfo[cmdType].executor(admContext, DbLeftTrim(leftStr));
}

Status GmAdmConnServer(DbOptionRuleT *optionRule, GmAdmContextT *admContext)
{
    DB_POINTER(optionRule);
    // 用户传入的server Name直接丢弃掉,使用默认的server name
    char *optionServerName = DbGetStrParamByOptionName(optionRule, DB_OPTION_ARG_DOMAIN_NAME);
    DB_UNUSED(optionServerName);
    char *serverLocator = GMADM_DEFAULT_DOMAIN_NAME;
    Status ret = TlsPerfEnvInit();
    if (ret != GMERR_OK) {
        return ret;
    }
    char *userName = DbGetStrParamByOptionName(optionRule, DB_OPTION_ARG_USER_NAME);
    TlsConnOptionT option = {
        .domainName = serverLocator,
        .userName = userName,
        .namespaceName = NULL,
        .useReservedConn = false,
        .noMemLimit = false,
    };
    ret = TlsConnect(&admContext->conn, &admContext->stmt, &option);
    if (ret != GMERR_OK) {
        TlsPerfEnvUnInit();
        ADM_ERROR("Connect with(\"%s\") unsuccessfully!", serverLocator);
        return ret;
    }
    ADM_SUCCESS("Connect with(\"%s\") success!", serverLocator);
    return GMERR_OK;
}

void GmAdmDisConnServer(GmAdmContextT *admContext)
{
    TlsDisconnect(admContext->conn, admContext->stmt);
    TlsPerfEnvUnInit();
}

static void ProcessCmdOption(DbOptionRuleT *optionRule)
{
    uint32_t paramNum = DbGetParamNumByOptionName(optionRule, ADM_OPTION_ARG_ADM_CMD);
    DbOptionParamT *param = DbGetParamsByOptionName(optionRule, ADM_OPTION_ARG_ADM_CMD);
    char *cmdBuf = (char *)malloc(sizeof(char) * (MAX_LEN_GM_ADM_BUF + 1));
    if (cmdBuf == NULL) {
        ADM_ERROR("Unsuccessful to malloc memory for command buffer!");
        return;
    }
    (void)memset_s(cmdBuf, MAX_LEN_GM_ADM_BUF + 1, 0x00, MAX_LEN_GM_ADM_BUF + 1);
    Status ret = CopyCmdToBuffer(param, paramNum, cmdBuf, MAX_LEN_GM_ADM_BUF);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

    // 建连
    GmAdmContextT admContext = {};
    ret = GmAdmConnServer(optionRule, &admContext);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    // 执行命令
    ExecuteCommand(&admContext, cmdBuf);
    // 断连
    GmAdmDisConnServer(&admContext);
EXIT:
    free(cmdBuf);
    (void)fflush(stdout);
}

void PrintArgvHelpInfo(void)
{
    const char *title = "help information:";
    const char *helpOption[][CMD_SUMMARY_COL_NUM] = {
        {"[-h]", "", ""},
        {"[-u]", "", "<user name>"},
        {"[-s]", "", "<server_locator>"},
        {"[-c]", "", "<command>"},
    };
    DbPrintManual(title, helpOption, ELEMENT_COUNT(helpOption), NULL);
}

void ProcessOptionsWithType(DbOptionRuleT *optionRule, GmAdmStartupRuleE ruleType)
{
    switch (ruleType) {
        case GMADM_RULE_HELP:
            PrintArgvHelpInfo();
            break;
        case GMADM_RULE_CMD:
            ProcessCmdOption(optionRule);
            break;
        default:
            break;
    }
}

Status GmAdmEntry(int32_t argc, char **argv, DbOptionRuleT *optionRule)
{
    DB_POINTER(optionRule);
    Status ret = GmAdmOptionInit(optionRule, argc, argv);
    if (ret != GMERR_OK) {
        (void)DbPrintfDefault("[ADM] Unsuccessful to init gmadm option!\n");
        return ret;
    }
    // 获取启动规则
    int32_t startupRule = DbGetStartupRule(optionRule);
    if (startupRule == OPTION_NOT_FOUND) {
        (void)DbPrintfDefault("[ADM] Invalid option.\n");
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    ProcessOptionsWithType(optionRule, (GmAdmStartupRuleE)startupRule);
    // 结束进程
    return ret;
}

Status GmAdmMain(int32_t argc, char **argv)
{
#ifdef FEATURE_TOOLS_SYSLOG
    GmcLogAdptFuncsT tlsLogFuncs = {.userWriteFunc = TlsPrintLog, .handle = NULL};
    DbLogRegAdptFuncs(&tlsLogFuncs);
#endif
    DbOptionRuleT optionRule = {0};
    Status ret = GmAdmAllocOptionRuleItems(&optionRule);
    if (ret != GMERR_OK) {
        DbReleaseOptionRuleItems(&optionRule);
        return ret;
    }
    ret = GmAdmEntry(argc, argv, &optionRule);
    DbReleaseOptionRuleItems(&optionRule);
    return ret;
}

#ifdef __cplusplus
}
#endif
