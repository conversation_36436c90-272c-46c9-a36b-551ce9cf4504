/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: gmadm interface
 * Create: 2023-07-20
 * Notes:
 */
#ifndef GMADM_TOOL_H
#define GMADM_TOOL_H

#include "adpt_types.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define DROP_PARAM_NUM (2)
#define MAX_LEN_GM_ADM_BUF (2048)
#define CMD_USAGE_COL_NUM (1)
#define CMD_OPTION_COL_NUM (2)
#define CMD_SUMMARY_COL_NUM (3)
#define PRINT_ALIGN_STR_NUM (4)
#define ALIGN_STR " "
#define DELIM_STR " "

// 这里为了和V3一致，用table
#define DROP_TABLE_TYPE_VERTEX "table"

#define ADM_SUCCESS(format, ...)                                     \
    do {                                                             \
        (void)DbPrintfDefault("[DONE] " NONE format, ##__VA_ARGS__); \
        (void)DbPrintfDefault("\n");                                 \
    } while (0)  // NOLINT

#define ADM_ERROR(format, ...)                                        \
    do {                                                              \
        (void)DbPrintfDefault("[ERROR] " NONE format, ##__VA_ARGS__); \
        (void)DbPrintfDefault("\n");                                  \
    } while (0)  // NOLINT

#define ADM_INFO(format, ...)                                        \
    do {                                                             \
        (void)DbPrintfDefault("[INFO] " NONE format, ##__VA_ARGS__); \
        (void)DbPrintfDefault("\n");                                 \
    } while (0)  // NOLINT

#if defined(HPE) || defined(RTOSV2X) || defined(RTOSV2)
#define GMADM_DEFAULT_DOMAIN_NAME "channel:"
#else
#define GMADM_DEFAULT_DOMAIN_NAME "usocket:/run/verona/unix_emserver"
#endif

// 后续要添加类型枚举的话在这里添加，需要和g_admExecInfo一致
typedef enum GmAdmType { GM_ADM_HELP = 0, GM_ADM_TRUNCATE, GM_ADM_DROP, GM_ADM_BUTT } GmAdmTypeE;
typedef struct {
    GmcStmtT *stmt;
    GmcConnT *conn;
    GmAdmTypeE cmdType;
} GmAdmContextT;

typedef void (*GmAdmExecute)(GmAdmContextT *, char *);

typedef struct GmAdmExecInfo {
    GmAdmTypeE cmdType;     // 命令类型
    const char *describe;   // 命令信息
    GmAdmExecute executor;  // 具体的执行方法
} GmAdmExecInfoT;

#ifdef __cplusplus
}
#endif

#endif
