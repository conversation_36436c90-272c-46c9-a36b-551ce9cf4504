/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * File Name: tool_thread_pool.h
 * Description: Header file for tools thread pool.
 * Author: xizeming
 * Create: 2023-9-12
 */
#ifndef TOOL_THREAD_POOL_H
#define TOOL_THREAD_POOL_H

#include "adpt_types.h"
#include "db_mem_context.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
typedef struct TlsThreadPool TlsThreadPoolT;

/*
 * @brief Initialize thread pool
 * @param memCtx[in]: Memory context
 * @param threadNums[in]: Number of threads in thread pool
 * @param type[in]: thread connection type
 * @param domainName[in]: thread connection domainName
 * @param tp[out]: Initialized thread pool
 * @return: GMERR_OK if init success, else init fail
 */
GMDB_EXPORT Status TlsThreadPoolInit(
    DbMemCtxT *memCtx, uint32_t threadNums, char *domainName, GmcConnTypeE type, TlsThreadPoolT **tp);

/*
 * @brief Add single task to task queue
 * @param function[in]: Task function with ctx as parameter
 * @param args[in]: Parameter of task function
 * @return: GMERR_OK if post success, else fail
 */
GMDB_EXPORT Status TlsThreadPoolTaskPost(TlsThreadPoolT *tp, void (*function)(void *), void *args);

/*
 * @brief Wait for all tasks to finish
 * @param tp[in]: Thread pool need to finish
 * @return: GMERR_OK if post success, else fail
 */
GMDB_EXPORT Status TlsThreadPoolWait(TlsThreadPoolT *tp);

GMDB_EXPORT Status TlsThreadPoolWaitWithTimeOut(TlsThreadPoolT *tp, uint64_t startTime, uint32_t timeout);

/*
 * @brief Destroy all threads
 * @param tp[in]: Thread pool to be destroyed
 * @return: GMERR_OK if destroy success, else fail
 */
GMDB_EXPORT void TlsThreadPoolDestroy(TlsThreadPoolT *tp);

/*
 * @brief Get ThreadPool DynMem which can be shared in multi-thread
 * @param tp[in]: inited Thread pool
 * @return: DynMemCtx
 */
GMDB_EXPORT DbMemCtxT *TlsGetThreadPoolSharedDynMem(TlsThreadPoolT *tp);
#endif

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
