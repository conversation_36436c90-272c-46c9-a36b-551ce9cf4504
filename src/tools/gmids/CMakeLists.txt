project(gmids)

if (FUZZ OR COVERAGE)
    ADD_SUBDIRECTORY(gmids_inner)
endif()

ADD_COMPILE_OPTIONS(-Wl,--copy-dt-needed-entries -fPIE)

include_sub_directories_recursively(${CMAKE_CURRENT_SOURCE_DIR})
include_sub_directories_recursively(${GMDB_CLIENT_PATH})

aux_source_directory(. SRC_MAIN_LIST)
aux_source_directory(${gmids_SOURCE_DIR}/gmids_inner SRC_GMIDS_LIST)

if (TOOLS_EMBED_SO)
  generate_tools_embed_so(gmids_embed "${SRC_GMIDS_LIST}" "${TOOL_NAME}")
endif()

# 编译patch_obj不编译可执行程序
if(COMPILE_PATCH_OBJ)
  return()
endif()

add_executable (gmids ${SRC_MAIN_LIST} ${SRC_GMIDS_LIST})
if(STRIP)
  separate_debug_info(gmids)
endif()

target_link_libraries(gmids ${TOOL_NAME})

install(TARGETS gmids
  RUNTIME DESTINATION bin
)
