project(gminspect)

if (FUZZ OR COVERAGE)
    ADD_SUBDIRECTORY(gminspect_inner)
endif()

ADD_COMPILE_OPTIONS(-Wl,--copy-dt-needed-entries -fPIE)

include_sub_directories_recursively(${CMAKE_CURRENT_SOURCE_DIR})
include_sub_directories_recursively(${GMDB_CLIENT_PATH})

aux_source_directory(. SRC_MAIN_LIST)
aux_source_directory(${gminspect_SOURCE_DIR}/gminspect_inner SRC_GMRULE_LIST)

if (TOOLS_EMBED_SO)
  generate_tools_embed_so(gminspect_embed "${SRC_GMRULE_LIST}" "${TOOL_NAME}")
endif()

# 编译patch_obj不编译可执行程序
if(COMPILE_PATCH_OBJ)
  return()
endif()

add_executable (gminspect ${SRC_MAIN_LIST} ${SRC_GMRULE_LIST})
if(STRIP)
  separate_debug_info(gminspect)
endif()

target_link_libraries(gminspect ${TOOL_NAME})

install(TARGETS gminspect
  RUNTIME DESTINATION bin
)
