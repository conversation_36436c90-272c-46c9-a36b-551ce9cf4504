/*
 * Copyright (c) 2025 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef ARKDATA_UTILS_H
#define ARKDATA_UTILS_H

#include <string>

typedef enum { ARKDATA_STRING = 0, ARKDATA_BOOL, ARKDATA_INT } ARKDATA_TYPE;

bool isStringData(std::string &s);
bool isBoolData(std::string &s);
bool isIntData(std::string &s);
bool getBoolValue(std::string &s);
int char_to_int(const char *str, bool *success);

char *AllocateCharSpace(uint32_t spaceSize);

std::string &Trim(std::string &str);
bool createPathIfNotExist(const std::string &path);
#endif /* ARKDATA_UTILS_H */
