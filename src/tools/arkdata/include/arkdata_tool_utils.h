/*
 * Copyright (c) 2025 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef ARKDATA_UTILS_H
#define ARKDATA_UTILS_H

#include <string>

void TrimChar(char *str);
void ArkDataStrTrim(const char *input, char *output, uint32_t outputSize);
bool ArkDataFileIsExist(const char *filePath);
bool ArkDataDirIsExist(const char *dirPath);
char *ArkDataTrans2Lowercase(char *x);
char *ArkDataTrans2Uppercase(char *x);
uint8_t Ctoi(char d);
char *SkipSpace(char *src);

// 公共工具
enum ErrorType {
    TOOLS_OK,
    TOOLS_CMD_NOT_FOUND,
    TOOLS_CONN_EXIST,
    TOOLS_CONN_NOT_EXIST,
    TOOLS_CMD_EXECUTE_FAILED,
    TOOLS_FILE_NOT_EXIST,
    TOOLS_PARAM_INVALID,
    TOOLS_QUIT
};
enum ErrorType ToolsFetchStrFromCmdPara(char *src, char *dst, uint32_t *dstLen);
typedef enum { ARKDATA_STRING = 0, ARKDATA_BOOL, ARKDATA_INT } ARKDATA_TYPE;
bool isBoolData(std::string &s);
bool isIntData(std::string &s);
bool getBoolValue(std::string &s);

char *AllocateCharSpace(uint32_t spaceSize);
std::string &Ltrim(std::string &str);
std::string &Rtrim(std::string &str);
std::string &Trim(std::string &str);
bool StartWith(const std::string &str, const std::string &prefix);
bool IsContainsQuotes(const std::string &str);
std::string RemoveDoubleQuotes(std::string str);
#endif /* ARKDATA_UTILS_H */
