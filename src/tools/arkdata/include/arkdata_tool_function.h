/*
 * Copyright (c) 2025 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef ARKDATA_TOOL_FUNCTION_H
#define ARKDATA_TOOL_FUNCTION_H

#include <cstdio>
#include <cstdint>
#include <cstring>
#include <cstdlib>
#include <string>
#include <assert.h>
#include "securec.h"

#ifndef ARCH
#define ARCH "aarch64"
#endif

#define ARKDATA_CONTINUE_PROMPT " ..."
#define ARKDATA_ASSERT(exp) assert(exp)

#define ARKDATA_DEFAULT_DB_NAME "arkdata"
#define ARKDATA_WELCOME_TEXT "Enter \".help\" for usage hints."
#define ARKDATA_SPLIT_80 "-------------------------------------------------------------------------------"
#define ARKDATA_SPLIT ARKDATA_SPLIT_80 ARKDATA_SPLIT_80 ARKDATA_SPLIT_80

#define ARKDATA_KEY_WORD "key:"
#define ARKDATA_VALUE_WORD "value:"
#define ARKDATA_KV_TYPE "kv"
#define ARKDATA_PREFERENCE_TYPE "preference"
#define ARKDATA_DEFAULT_DB_KV_FILE "./data/kv/arkdata"
#define ARKDATA_DEFAULT_DB_PREFERENCE_FILE "./data/preference/arkdata"

#define ARKDATA_MAX_STR_LEN 512

#define ARKDATA_MAX_BUFF_SIZE (1024 * 1024)
#define ONE_NUM_BIT 4
#define HEX_A_DEC 10

#define ARKDATA_ELEMENT_NUM(x) (sizeof(x) / sizeof((x)[0]))

#define ARKDATA_OK 0
#define ARKDATA_ERR 1

#define ARKDATA_ENABLE_LINENO 0

#if ARKDATA_ENABLE_LINENO == 1
#define ARKDATA_LOG_ERROR(format, ...) printf("[ERROR] [%s:%d] " format, __FUNCTION__, __LINE__, ##__VA_ARGS__)
#else
#define ARKDATA_LOG_ERROR(format, ...) printf("[ERROR] " format, ##__VA_ARGS__)
#endif

#define ARKDATA_LOG_DEBUG(format, ...) printf(" " format, ##__VA_ARGS__)

#define ENTRY_COUNT_PER_PAGE 5
#define CMD_BASE_ARGS_LIST \
    char *input;           \
    uint32_t inputLen
typedef struct StCmdBaseArgs {
    CMD_BASE_ARGS_LIST;
} CmdBaseArgs;

// 和数据库功能相关的函数
typedef struct {
    const char *dbtype;
    int32_t (*arkDataConnect)(void);
    int32_t (*arkDataDisconn)(void);
    int32_t (*arkDataRegisterCmd)(void);
} ArkDataDbFuncT;

ArkDataDbFuncT ArkDataDbHandlerGet(void);
bool ArkDataDbTypeIsRegistered(char *model);
void ArkDataOptionSetPrompt(char *dbtype);
// KV模型
int32_t ArkDataKVConnect(void);
int32_t ArkDataKVDisconn(void);
int32_t ArkDataKVRegisterCmd(void);
// Preference模型
int32_t ArkDataPreConnect(void);
int32_t ArkDataPreDisconn(void);
int32_t ArkDataPreRegisterCmd(void);

// 读取用户输入
void ArkDataHistoryInit(void);
void ArkDataHistoryAdd(const char *history);
std::string ArkDataReadLine(const char *prompt, bool *breakRead);

#endif /* ARKDATA_TOOL_FUNCTION_H */
