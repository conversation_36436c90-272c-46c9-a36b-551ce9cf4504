/*
 * Copyright (c) 2025 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef PREFERENCES_VALUE_PARCEL_H
#define PREFERENCES_VALUE_PARCEL_H

#ifndef PREF_API_EXPORT
#define PREF_API_EXPORT __attribute__((visibility("default")))
#endif

#include <vector>
#include <cstdint>
#include <string>
#include <type_traits>
#include <variant>

struct Object {
    std::string valueStr;
    Object() = default;
    Object(const std::string &str) : valueStr(str){};
    bool operator==(const Object &other) const
    {
        return valueStr == other.valueStr;
    }
};

struct BigInt {
public:
    BigInt() = default;
    BigInt(const std::vector<uint64_t> &words, int sign) : words_(std::move(words)), sign(sign)
    {}
    ~BigInt() = default;
    bool operator==(const BigInt &value) const
    {
        return sign == value.sign && words_ == value.words_;
    }
    std::vector<uint64_t> words_;
    int sign;
};

/**
 * The PreferencesValue class of the preference. Various operations on PreferencesValue are provided in this class.
 */
class PREF_API_EXPORT PreferencesValue {
public:
    PREF_API_EXPORT PreferencesValue()
    {
        value_ = std::monostate();
    }

    PREF_API_EXPORT ~PreferencesValue()
    {}

    /* *
     * @brief Move constructor.
     */
    PREF_API_EXPORT PreferencesValue(PreferencesValue &&preferencesValue) noexcept;

    /* *
     * @brief Copy constructor.
     */
    PREF_API_EXPORT PreferencesValue(const PreferencesValue &preferencesValue);

    /* *
     * @brief Constructor.
     *
     * This constructor is used to convert the int input parameter to a value of type PreferencesValue.
     *
     * @param value Indicates an int input parameter.
     */
    PREF_API_EXPORT PreferencesValue(int value);

    /* *
     * @brief Constructor.
     *
     * This constructor is used to convert the int64_t input parameter to a value of type PreferencesValue.
     *
     * @param value Indicates a int64_t input parameter.
     */
    PREF_API_EXPORT PreferencesValue(int64_t value);

    /* *
     * @brief Constructor.
     *
     * This constructor is used to convert the int64_t input parameter to a value of type PreferencesValue.
     *
     * @param value Indicates a int64_t input parameter.
     */
    PREF_API_EXPORT PreferencesValue(float value);

    /* *
     * @brief Constructor.
     *
     * This constructor is used to convert the double input parameter to a value of type PreferencesValue.
     *
     * @param value Indicates a double input parameter.
     */
    PREF_API_EXPORT PreferencesValue(double value);

    /* *
     * @brief Constructor.
     *
     * This constructor is used to convert the bool input parameter to a value of type PreferencesValue.
     *
     * @param value Indicates a bool input parameter.
     */
    PREF_API_EXPORT PreferencesValue(bool value);

    /* *
     * @brief Constructor.
     *
     * This constructor is used to convert the string input parameter to a value of type PreferencesValue.
     *
     * @param value Indicates string input parameter.
     */
    PREF_API_EXPORT PreferencesValue(std::string value);

    /* *
     * @brief Constructor.
     *
     * This constructor is used to convert the char input parameter to a value of type PreferencesValue.
     *
     * @param value Indicates a char input parameter.
     */
    PREF_API_EXPORT PreferencesValue(const char *value);

    /* *
     * @brief Constructor.
     *
     * This constructor is used to convert the vector<double> input parameter to a value of type PreferencesValue.
     *
     * @param value Indicates a vector<double> input parameter.
     */
    PREF_API_EXPORT PreferencesValue(std::vector<double> value);

    /* *
     * @brief Constructor.
     *
     * This constructor is used to convert the vector<std::string> input parameter to a value of type PreferencesValue.
     *
     * @param value Indicates a vector<std::string> input parameter.
     */
    PREF_API_EXPORT PreferencesValue(std::vector<std::string> value);

    /* *
     * @brief Constructor.
     *
     * This constructor is used to convert the vector<bool> input parameter to a value of type PreferencesValue.
     *
     * @param value Indicates a vector<bool> input parameter.
     */
    PREF_API_EXPORT PreferencesValue(std::vector<bool> value);

    /* *
     * @brief Constructor.
     *
     * This constructor is used to convert the vector<uint8_t> input parameter to a value of type PreferencesValue.
     *
     * @param value Indicates a vector<uint8_t> input parameter.
     */
    PREF_API_EXPORT PreferencesValue(std::vector<uint8_t> value);

    PREF_API_EXPORT PreferencesValue(Object value);

    /* *
     * @brief Constructor.
     *
     * This constructor is used to convert the BigInt input parameter to a value of type PreferencesValue.
     *
     * @param value Indicates a vector<uint8_t> input parameter.
     */
    PREF_API_EXPORT PreferencesValue(BigInt value);

    /* *
     * @brief Move assignment operator overloaded function.
     */
    PREF_API_EXPORT PreferencesValue &operator=(PreferencesValue &&preferencesValue) noexcept;

    /* *
     * @brief Copy assignment operator overloaded function.
     */
    PreferencesValue &operator=(const PreferencesValue &preferencesValue);

    /* *
     * @brief Determines whether the int type PreferencesValue is currently used.
     *
     * @return Returning true means it is, false means it isn't.
     */
    PREF_API_EXPORT bool IsInt() const;

    /* *
     * @brief Determines whether the long type PreferencesValue is currently used.
     *
     * @return Returning true means it is, false means it isn't.
     */
    PREF_API_EXPORT bool IsLong() const;

    /* *
     * @brief Determines whether the float type PreferencesValue is currently used.
     *
     * @return Returning true means it is, false means it isn't.
     */
    PREF_API_EXPORT bool IsFloat() const;

    /* *
     * @brief Determines whether the double type PreferencesValue is currently used.
     *
     * @return Returning true means it is, false means it isn't.
     */
    PREF_API_EXPORT bool IsDouble() const;

    /* *
     * @brief Determines whether the bool type PreferencesValue is currently used.
     *
     * @return Returning true means it is, false means it isn't.
     */
    PREF_API_EXPORT bool IsBool() const;

    /* *
     * @brief Determines whether the string type PreferencesValue is currently used.
     *
     * @return Returning true means it is, false means it isn't.
     */
    PREF_API_EXPORT bool IsString() const;

    /* *
     * @brief Determines whether the string array type PreferencesValue is currently used.
     *
     * @return Returning true means it is, false means it isn't.
     */
    PREF_API_EXPORT bool IsStringArray() const;

    /* *
     * @brief Determines whether the bool array type PreferencesValue is currently used.
     *
     * @return Returning true means it is, false means it isn't.
     */
    PREF_API_EXPORT bool IsBoolArray() const;

    /* *
     * @brief Determines whether the double array type PreferencesValue is currently used.
     *
     * @return Returning true means it is, false means it isn't.
     */
    PREF_API_EXPORT bool IsDoubleArray() const;

    /* *
     * @brief Determines whether the uint8 array type PreferencesValue is currently used.
     *
     * @return Returning true means it is, false means it isn't.
     */
    PREF_API_EXPORT bool IsUint8Array() const;

    PREF_API_EXPORT bool IsObject() const;

    /* *
     * @brief Determines whether the BigInt type PreferencesValue is currently used.
     *
     * @return Returning true means it is, false means it isn't.
     */
    PREF_API_EXPORT bool IsBigInt() const;

    /* *
     * @brief Type conversion function.
     *
     * @return The int type PreferencesValue.
     */
    PREF_API_EXPORT operator int() const;

    /* *
     * @brief Type conversion function.
     *
     * @return Returns float type PreferencesValue.
     */
    PREF_API_EXPORT operator float() const;

    /* *
     * @brief Type conversion function.
     *
     * @return Returns double type PreferencesValue.
     */
    PREF_API_EXPORT operator double() const;

    /* *
     * @brief Type conversion function.
     *
     * @return Returns bool type PreferencesValue.
     */
    PREF_API_EXPORT operator bool() const;

    /* *
     * @brief Type conversion function.
     *
     * @return Returns int64_t type PreferencesValue.
     */
    PREF_API_EXPORT operator int64_t() const;

    /* *
     * @brief Type conversion function.
     *
     * @return Returns string type PreferencesValue.
     */
    PREF_API_EXPORT operator std::string() const;

    /* *
     * @brief Type conversion function.
     *
     * @return Returns vector<double> type PreferencesValue.
     */
    PREF_API_EXPORT operator std::vector<double>() const;

    /* *
     * @brief Type conversion function.
     *
     * @return Returns vector<bool> type PreferencesValue.
     */
    PREF_API_EXPORT operator std::vector<bool>() const;

    /* *
     * @brief Type conversion function.
     *
     * @return Returns vector<string> type PreferencesValue.
     */
    PREF_API_EXPORT operator std::vector<std::string>() const;

    /* *
     * @brief Type conversion function.
     *
     * @return Returns vector<uint8_t> type PreferencesValue.
     */
    PREF_API_EXPORT operator std::vector<uint8_t>() const;

    PREF_API_EXPORT operator Object() const;

    /* *
     * @brief Type conversion function.
     *
     * @return Returns BigInt type PreferencesValue.
     */
    PREF_API_EXPORT operator BigInt() const;

    /* *
     * @brief Overloaded operator "==".
     *
     * This function is used to determine whether the input value is equal to the current PreferencesValue.
     *
     * @param value Indicates a PreferencesValue.
     *
     * @return Returning true means the input value is equal to the current PreferencesValue, false means it isn't.
     */
    PREF_API_EXPORT bool operator==(const PreferencesValue &value);

    std::variant<std::monostate, int, int64_t, float, double, bool, std::string, std::vector<std::string>,
        std::vector<bool>, std::vector<double>, std::vector<uint8_t>, Object, BigInt>
        value_;
};

class PreferencesValueParcel {
public:
    static uint8_t GetTypeIndex(const PreferencesValue &value);
    static uint32_t CalSize(PreferencesValue value);
    static std::pair<int, PreferencesValue> UnmarshallingPreferenceValue(const std::vector<uint8_t> &data);
    static int MarshallingPreferenceValue(const PreferencesValue &value, std::vector<uint8_t> &data);

private:
    enum ParcelTypeIndex {
        MONO_TYPE = 0,
        INT_TYPE = 1,
        LONG_TYPE = 2,
        FLOAT_TYPE = 3,
        DOUBLE_TYPE = 4,
        BOOL_TYPE = 5,
        STRING_TYPE = 6,
        STRING_ARRAY_TYPE = 7,
        BOOL_ARRAY_TYPE = 8,
        DOUBLE_ARRAY_TYPE = 9,
        UINT8_ARRAY_TYPE = 10,
        OBJECT_TYPE = 11,
        BIG_INT_TYPE = 12
    };
    // move this marshalling api from public to private
    static int MarshallingBasicValue(const PreferencesValue &value, const uint8_t type, std::vector<uint8_t> &data);
    static int MarshallingStringValue(const PreferencesValue &value, const uint8_t type, std::vector<uint8_t> &data);
    static int MarshallingStringArrayValue(
        const PreferencesValue &value, const uint8_t type, std::vector<uint8_t> &data);
    static int MarshallingVecUInt8AfterType(const PreferencesValue &value, uint8_t *startAddr);
    static int MarshallingVecBigIntAfterType(const PreferencesValue &value, uint8_t *startAddr);
    static int MarshallingVecDoubleAfterType(const PreferencesValue &value, uint8_t *startAddr);
    static int MarshallingVecBoolAfterType(const PreferencesValue &value, uint8_t *startAddr);
    static int MarshallingBasicArrayValue(
        const PreferencesValue &value, const uint8_t type, std::vector<uint8_t> &data);
    static std::pair<int, PreferencesValue> UnmarshallingBasicValue(
        const uint8_t type, const std::vector<uint8_t> &data);
    static std::pair<int, PreferencesValue> UnmarshallingStringValue(
        const uint8_t type, const std::vector<uint8_t> &data);
    static std::pair<int, PreferencesValue> UnmarshallingStringArrayValue(
        const uint8_t type, const std::vector<uint8_t> &data);
    static std::pair<int, PreferencesValue> UnmarshallingVecUInt8(const std::vector<uint8_t> &data);
    static std::pair<int, PreferencesValue> UnmarshallingVecDouble(const std::vector<uint8_t> &data);
    static std::pair<int, PreferencesValue> UnmarshallingVecBool(const std::vector<uint8_t> &data);
    static std::pair<int, PreferencesValue> UnmarshallingVecBigInt(const std::vector<uint8_t> &data);
    static std::pair<int, PreferencesValue> UnmarshallingBasicArrayValue(
        const uint8_t type, const std::vector<uint8_t> &data);
    static int MarshallingBasicValueInner(
        const PreferencesValue &value, const uint8_t type, std::vector<uint8_t> &data);
};

constexpr int SUBSYS_DISTRIBUTEDDATAMNG = 13;
constexpr int SUBSYSTEM_BIT_NUM = 21;
constexpr int MODULE_BIT_NUM = 16;
constexpr int MODULE_PREFERENCES = 6;
constexpr int DISTRIBUTEDDATAMGR_PREFERENCES_ERR_OFFSET =
    (SUBSYS_DISTRIBUTEDDATAMNG << SUBSYSTEM_BIT_NUM) | (MODULE_PREFERENCES << MODULE_BIT_NUM);

/**
 * @brief The error code in the correct case.
 */
constexpr int E_OK = 0;

/**
 * @brief The base code of the exception error code.
 */
constexpr int E_BASE = DISTRIBUTEDDATAMGR_PREFERENCES_ERR_OFFSET;

/**
 * @brief The error when the capability not supported.
 */
constexpr int E_NOT_SUPPORTED = (E_BASE + 801);

/**
 * @brief The error code for common exceptions.
 */
constexpr int E_ERROR = E_BASE;

/**
 * @brief The error code for resource has been stopped, killed or destroyed.
 */
constexpr int E_STALE = (E_BASE + 1);  // Resource has been stopped, killed or destroyed.

/**
 * @brief The error code for the input args is invalid.
 */
constexpr int E_INVALID_ARGS = (E_BASE + 2);  // the input args is invalid.

/**
 * @brief The error code for out of memory.
 */
constexpr int E_OUT_OF_MEMORY = (E_BASE + 3);  // out of memory

/**
 * @brief The error code for operation is not permitted.
 */
constexpr int E_NOT_PERMIT = (E_BASE + 4);  // operation is not permitted

/**
 * @brief The error code for the key is empty.
 */
constexpr int E_KEY_EMPTY = (E_BASE + 5);

/**
 * @brief The error code for the key string length exceed the max length (1024).
 */
constexpr int E_KEY_EXCEED_MAX_LENGTH = (E_BASE + 6);

/**
 * @brief The error code for the former Preferences object pointer is held by another thread and may
 * not be able to be deleted.
 */
constexpr int E_PTR_EXIST_ANOTHER_HOLDER = (E_BASE + 7);

/**
 * @brief The error code for the file path is relative path.
 */
constexpr int E_RELATIVE_PATH = (E_BASE + 8);

/**
 * @brief The error code for the file path is empty.
 */
constexpr int E_EMPTY_FILE_PATH = (E_BASE + 9);

/**
 * @brief The error code when deleting a file fails.
 */
constexpr int E_DELETE_FILE_FAIL = (E_BASE + 10);

/**
 * @brief The error code for the file name is empty.
 */
constexpr int E_EMPTY_FILE_NAME = (E_BASE + 11);

/**
 * @brief The error code for the file path is invalid.
 */
constexpr int E_INVALID_FILE_PATH = (E_BASE + 12);

/**
 * @brief The error code for the file path exceeds the max length.
 */
constexpr int E_PATH_EXCEED_MAX_LENGTH = (E_BASE + 13);

/**
 * @brief The error code for the value exceeds the max length (16 * 1024 * 1024 ).
 */
constexpr int E_VALUE_EXCEED_MAX_LENGTH = (E_BASE + 14);

/**
 * @brief The error code for the key exceeds the length limit (32).
 */
constexpr int E_KEY_EXCEED_LENGTH_LIMIT = (E_BASE + 15);

/**
 * @brief The error code for the value exceeds the length limit (128).
 */
constexpr int E_VALUE_EXCEED_LENGTH_LIMIT = (E_BASE + 16);

/**
 * @brief The error code for the default exceeds the max length (128).
 */
constexpr int E_DEFAULT_EXCEED_LENGTH_LIMIT = (E_BASE + 17);

/**
 * @brief The error code for permission denied.
 */
constexpr int PERMISSION_DENIED = (E_BASE + 18);

/**
 * @brief Failed to get DataObsMgrClient.
 */
static constexpr int E_GET_DATAOBSMGRCLIENT_FAIL = (E_BASE + 19);

/**
 * @brief The error code is used to retain the observer.
 */
constexpr int E_OBSERVER_RESERVE = (E_BASE + 20);

/**
 * @brief The error code is used to indicate that database has been closed.
 */
constexpr int E_ALREADY_CLOSED = (E_BASE + 21);

/**
 * @brief The error code is used to indicate that database has been closed.
 */
constexpr int E_NO_DATA = (E_BASE + 22);
#endif
