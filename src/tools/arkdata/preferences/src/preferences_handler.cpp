/*
 * Copyright (c) 2025 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <cstdio>
#include <cstdint>
#include <cstring>
#include <cstdlib>
#include "securec.h"
#include <iostream>
#include <string>
#include <algorithm>
#include <vector>
#include <iomanip>
#include <fstream>
#include <cstring>
#include <variant>
#include <functional>
#include "preferences_value_parcel.h"
#include "arkdata_tool_utils.h"
#include "arkdata_tool_function.h"
#include "arkdata_print_data.h"
#include "grd_db_api.h"
#include "grd_kv_api.h"
#include "grd_error.h"
#include "grd_resultset.h"
#include "arkdata_print_data.h"
#include "arkdata_command.h"
#include "arkdata_input_option.h"

#define PREFERENCES_COLLECTION_NAME "preferences_data"

GRD_DB *g_predb = nullptr;

const char *const PREFERENCE_TABLE_MODE = "{\"mode\" : \"kv\", \"indextype\" : \"hash\"}";
const char *const PREFERENCE_CONFIG_STR =
    "{\"pageSize\": 4, \"redoFlushByTrx\": 2, \"redoPubBufSize\": 256, \"maxConnNum\": 100, "
    "\"bufferPoolSize\": 1024, \"crcCheckEnable\": 0, \"bufferPoolPolicy\" : \"BUF_PRIORITY_INDEX\", "
    "\"sharedModeEnable\" : 1, \"MetaInfoBak\": 1}";

PreferencesValue SetPreferenceValue(char *input)
{
    if (input == nullptr) {
        return nullptr;
    }
    std::string strInput(input);
    if (IsContainsQuotes(input)) {
        // remove quotes
        std::string strRet = RemoveDoubleQuotes(strInput);
        return PreferencesValue(strRet);
    } else if (isBoolData(strInput)) {
        bool value = getBoolValue(strInput);
        return PreferencesValue(value);
    } else if (isIntData(strInput)) {
        int num = std::stoi(strInput);
        return PreferencesValue(num);
    } else {
        return PreferencesValue(strInput);
    }
}
// open database
int32_t ArkDataPreConnect(void)
{
    int32_t flags = GRD_DB_OPEN_CREATE | GRD_DB_OPEN_CHECK_FOR_ABNORMAL;
    // get database name
    char *dbFile = ArkDataOptionGetDbName();
    // check if db file exist
    bool isDBFileExist = ArkDataFileIsExist(dbFile);
    int32_t ret = GRD_DBOpen(dbFile, PREFERENCE_CONFIG_STR, flags, &g_predb);
    if (ret != GRD_OK) {
        ARKDATA_LOG_ERROR("open db fail, ret = %d\n", ret);
        return ARKDATA_ERR;
    }
    // if db file not exist, need to create collection
    if (!isDBFileExist) {
        ret = GRD_CreateCollection(g_predb, PREFERENCES_COLLECTION_NAME, PREFERENCE_TABLE_MODE, 0);
        if (ret != GRD_OK) {
            // Handle Exception
            ARKDATA_LOG_ERROR("create db collection fail, ret = %d\n", ret);
            return ARKDATA_ERR;
        }
    }
    return ARKDATA_OK;
}
// release connection
int32_t ArkDataPreDisconn(void)
{
    int32_t ret = GRD_DBClose(g_predb, GRD_DB_CLOSE);
    if (ret != GRD_OK) {
        ARKDATA_LOG_ERROR("close db fail, ret = %d\n", ret);
        return ARKDATA_ERR;
    }
    g_predb = nullptr;

    return ARKDATA_OK;
}
// execute query operation to preference database
void ArkDataPreQuery(UserInputArgs *inputArgs)
{
    if (inputArgs->keyItem == nullptr) {
        ARKDATA_LOG_ERROR("Unable to query null key.\n");
        return;
    }
    GRD_KVItemT keyItem = {inputArgs->keyItem, inputArgs->keyItemLen};
    GRD_KVItemT valueItem = {nullptr, 0};
    uint32_t ret = GRD_KVGet(g_predb, PREFERENCES_COLLECTION_NAME, &keyItem, &valueItem);
    if (ret != GRD_OK) {
        if (ret == GRD_NO_DATA) {
            ARKDATA_LOG_DEBUG("No data for key = %s\n", inputArgs->keyItem);
        } else {
            ARKDATA_LOG_ERROR("query db fail, ret = %d\n", ret);
        }
        return;
    }
    // start to print valueItem
    PrintPreferencesData((uint8_t *)valueItem.data, valueItem.dataLen);
    ret = GRD_KVFreeItem(&valueItem);
    if (ret != GRD_OK) {
        // Handle Exception
        ARKDATA_LOG_ERROR("free KVItem fail, ret = %d\n", ret);
        return;
    }
}
// execute scan operation to preference
void ArkDataPreScan(UserInputArgs *inputArgs)
{
    // no need other parameter
    GRD_ResultSet *resultSet = nullptr;
    int32_t ret = GRD_KVScan(g_predb, PREFERENCES_COLLECTION_NAME, nullptr, KV_SCAN_EQUAL_OR_GREATER_KEY, &resultSet);
    if (ret != GRD_OK) {
        ARKDATA_LOG_ERROR("Scan full table fail, ret = %d\n", ret);
        return;
    }
    PrintTabledData(resultSet, true);
    ret = GRD_FreeResultSet(resultSet);
    if (ret != GRD_OK) {
        // Handle Exception
        ARKDATA_LOG_ERROR("Free result set fail, ret = %d\n", ret);
        return;
    }
}
GRD_KVItemT BlobToPreferenceItem(const std::vector<uint8_t> &blob)
{
    return {.data = (void *)&blob[0], .dataLen = (uint32_t)blob.size()};
}
// execute put operation to preference
// current key and value contain 'key:' and 'value:' words
void ArkDataPrePut(UserInputArgs *inputArgs)
{
    // need to marshall key and value
    if (inputArgs->keyItem == nullptr) {
        ARKDATA_LOG_ERROR("Put command input data error.\n");
        return;
    }
    GRD_KVItemT keyItem = {inputArgs->keyItem, inputArgs->keyItemLen};
    // convert data to execute marshall operation
    GRD_KVItemT valueItem = {nullptr, 0};
    std::vector<uint8_t> marshallValue;
    if (inputArgs->valueItemLen > 0) {
        PreferencesValue outPreference = SetPreferenceValue(inputArgs->valueItem);
        uint32_t marshallValueLen = PreferencesValueParcel::CalSize(outPreference);
        marshallValue.resize(marshallValueLen);
        int status = PreferencesValueParcel::MarshallingPreferenceValue(outPreference, marshallValue);
        if (status != E_OK) {
            ARKDATA_LOG_ERROR("Unable to marshall data.\n");
            return;
        }
        valueItem = BlobToPreferenceItem(marshallValue);
        if (valueItem.data == nullptr || valueItem.dataLen == 0) {
            return;
        }
    }
    // start to put data
    uint32_t ret = GRD_KVPut(g_predb, PREFERENCES_COLLECTION_NAME, &keyItem, &valueItem);
    if (ret != GRD_OK) {
        ARKDATA_LOG_ERROR("Put command faile, ret = %d\n", ret);
        return;
    }
}
// execute delete operation to preference
// when value is null, need to delete all data
// when value is not null, need to delete specific key
void ArkDataPreDelete(UserInputArgs *inputArgs)
{
    // 不支持delete全表，所以必须要有key参数，参数以‘key:’开始
    if (inputArgs->keyItem == nullptr) {
        // start to drop collection
        uint32_t ret = GRD_DropCollection(g_predb, PREFERENCES_COLLECTION_NAME, 0);
        if (ret != GRD_OK) {
            // Handle Exception
            ARKDATA_LOG_ERROR("Delete all data collection fail, ret = %d\n", ret);
            return;
        }
        // start to create collection
        ret = GRD_CreateCollection(g_predb, PREFERENCES_COLLECTION_NAME, PREFERENCE_TABLE_MODE, 0);
        if (ret != GRD_OK) {
            // Handle Exception
            ARKDATA_LOG_ERROR("Create db collection fail, ret = %d\n", ret);
            return;
        }
    } else {
        // start to delete
        GRD_KVItemT keyItem = {inputArgs->keyItem, inputArgs->keyItemLen};
        int32_t ret = GRD_KVDel(g_predb, PREFERENCES_COLLECTION_NAME, &keyItem);
        if (ret != GRD_OK) {
            ARKDATA_LOG_ERROR("Delete key fail, ret = %d\n", ret);
            return;
        }
    }
}

ArkDataCommandT g_preferenceCmds[] = {
    // 配置函数, 命令, 参数, 帮助信息, 类型, 值
    {ArkDataPreQuery, "query", nullptr, "query key", ARKDATA_CMD_TYPE_ACTION, 0},
    {ArkDataPrePut, "put", nullptr, "add or update key/value", ARKDATA_CMD_TYPE_ACTION, 0},
    {ArkDataPreDelete, "delete", nullptr, "delete one key/value or delete all tables", ARKDATA_CMD_TYPE_ACTION, 0},
};

// register new preference command to command list
int32_t ArkDataPreRegisterCmd(void)
{
    int32_t ret = ArkDataCmdRegister(g_preferenceCmds, sizeof(g_preferenceCmds));
    if (ret != ARKDATA_OK) {
        ARKDATA_LOG_ERROR("Unable to register sql cmd.\n");
        return ret;
    }
    return ARKDATA_OK;
}
