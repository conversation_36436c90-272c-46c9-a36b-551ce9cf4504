/*
 * Copyright (c) 2025 Huawei Device Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <cstdio>
#include <cstdint>
#include <cctype>
#include <cstring>
#include <cstdlib>
#include <sys/stat.h>
#include <cerrno>
#include <climits>
#include <iostream>
#include <filesystem>
#include <string>
#include <sstream>
#include <algorithm>
#include <cctype>
#include "securec.h"
#include "db_define.h"
#include "arkdata_tool_utils.h"
#include "arkdata_tool_function.h"

using namespace std;

namespace fs = std::filesystem;

// 去除字符串头尾的空白字符
void ArkDataStrTrim(const char *input, char *output, uint32_t outputSize)
{
    if (input == nullptr || strlen(input) <= 0) {
        return;
    }

    int32_t len = strlen(input);
    int32_t startIdx = 0;
    int32_t endIdx = len - 1;
    while (input[startIdx] == ' ' || input[startIdx] == '\t') {
        if (startIdx >= len - 1) {
            break;
        }
        startIdx++;
    }
    while (input[endIdx] == ' ' || input[endIdx] == '\t') {
        if (endIdx <= 0) {
            break;
        }
        endIdx--;
    }
    if (startIdx > endIdx) {
        return;
    }

    errno_t err = strncpy_s(output, outputSize, input + startIdx, endIdx - startIdx + 1);
    if (err != EOK) {
        ARKDATA_LOG_ERROR("Unable to copy string.\n");
        return;
    }
    output[endIdx - startIdx + 1] = '\0';
}

// 检查文件存在
bool ArkDataFileIsExist(const char *filePath)
{
    struct stat statBuf;
    if (stat(filePath, &statBuf) != 0) {
        return false;
    }
    if (S_ISREG(statBuf.st_mode)) {
        return true;
    }
    return false;
}

// 检查目录存在
bool ArkDataDirIsExist(const char *dirPath)
{
    struct stat statBuf;
    if (stat(dirPath, &statBuf) != 0) {
        return false;
    }
    if (S_ISDIR(statBuf.st_mode)) {
        return true;
    }
    return false;
}

// 字符串转大写
char *ArkDataTrans2Uppercase(char *x)
{
    int32_t length = strlen(x);
    for (int32_t i = 0; i < length; i++) {
        if (x[i] >= 'a' && x[i] <= 'z') {
            x[i] = x[i] - ('a' - 'A');
        }
    }
    return x;
}

// 字符串转小写
char *ArkDataTrans2Lowercase(char *x)
{
    int32_t length = strlen(x);
    for (int32_t i = 0; i < length; i++) {
        if (x[i] >= 'A' && x[i] <= 'Z') {
            x[i] = x[i] + ('a' - 'A');
        }
    }
    return x;
}

uint8_t Ctoi(char d)
{
    if ('0' <= d && d <= '9') {
        return d - '0';
    }
    if ('a' <= d && d <= 'f') {
        return d - 'a' + HEX_A_DEC;
    }
    if ('A' <= d && d <= 'F') {
        return d - 'A' + HEX_A_DEC;
    }
    return DB_INVALID_UINT8;
}

char *SkipSpace(char *src)
{
    while (*src != '\0' && *src == ' ') {
        src++;
    }
    return src;
}

bool CalculateXValue(char *src, char *dst)
{
    src++;
    uint8_t m = Ctoi(*src);
    if (m == DB_INVALID_UINT8) {
        return false;
    }
    src++;
    uint8_t n = Ctoi(*src);
    if (n == DB_INVALID_UINT8) {
        return false;
    }
    *dst = (m << ONE_NUM_BIT) | n;
    return true;
}

int FetchStrByDelim(char *src, char delim, char *dst, bool escape = true)
{
    int dstCount = 0;
    while (*src != '\0' && *src != delim) {
        if (escape && *src == '\\') {  // escape character
            src++;
            bool bCalculate = true;
            switch (*src) {
                case 't':
                    *dst = '\t';
                    break;
                case '0':
                    *dst = 0;
                    break;
                case '\'':
                    *dst = '\'';
                    break;
                case '"':
                    *dst = '"';
                    break;
                case '\\':
                    *dst = '\\';
                    break;
                case 'x':
                    bCalculate = CalculateXValue(src, dst);
                    break;
                default:
                    cout << "don't support \\" << *src << endl;
                    return -1;
            }
            if (!bCalculate) {
                return -1;
            }
        } else {
            *dst = *src;
        }
        dst++;
        src++;
        dstCount++;
    }
    if (escape) {
        return *src == '\0' ? -1 : dstCount;
    }
    return dstCount;
}

enum ErrorType ToolsFetchStrFromCmdPara(char *src, char *dst, uint32_t *dstLen)
{
    int ret;
    if (*src == '"') {
        ret = FetchStrByDelim(src + 1, '"', dst);
    } else {
        ret = FetchStrByDelim(src, ' ', dst, false);
        ret += 1;
    }
    if (ret <= 0) {
        return ErrorType::TOOLS_PARAM_INVALID;
    }
    if (dstLen != nullptr) {
        *dstLen = ret;
    }
    return ErrorType::TOOLS_OK;
}

std::string &Ltrim(std::string &str)
{
    str.erase(str.begin(), std::find_if(str.begin(), str.end(), [](unsigned char ch) { return !std::isspace(ch); }));
    return str;
}

std::string &Rtrim(std::string &str)
{
    str.erase(
        std::find_if(str.rbegin(), str.rend(), [](unsigned char ch) { return !std::isspace(ch); }).base(), str.end());
    return str;
}

std::string &Trim(std::string &str)
{
    return Ltrim(Rtrim(str));
}

bool isIntData(std::string &s)
{
    std::istringstream iss(s);
    int value;
    return (iss >> value) && (iss.eof());
}

bool isBoolData(std::string &s)
{
    std::string str = s;
    std::transform(str.begin(), str.end(), str.begin(), ::tolower);
    return (str == "true") || (str == "false");
}

bool getBoolValue(std::string &s)
{
    std::string str = s;
    std::transform(str.begin(), str.end(), str.begin(), ::tolower);
    return (str == "true");
}

char *AllocateCharSpace(uint32_t spaceSize)
{
    char *buffer = (char *)malloc(spaceSize);
    if (buffer == nullptr) {
        ARKDATA_LOG_ERROR("Unable to malloc buffer.\n");
        return nullptr;
    }
    (void)memset_s(buffer, spaceSize, '\0', spaceSize);
    return buffer;
}

bool StartWith(const std::string &str, const std::string &prefix)
{
    if (str.length() < prefix.length()) {
        return false;
    }
    return str.substr(0, prefix.length()) == prefix;
}

bool IsContainsQuotes(const std::string &str)
{
    if (str.find('\"') != std::string::npos) {
        return true;
    }
    if (str.find('\'') != std::string::npos) {
        return true;
    }
    return false;
}

std::string RemoveDoubleQuotes(std::string str)
{
    str.erase(std::remove(str.begin(), str.end(), '\''), str.end());
    str.erase(std::remove(str.begin(), str.end(), '"'), str.end());
    return str;
}
