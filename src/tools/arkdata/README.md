1. 当前迭代支持 kvStore, preference 数据库
2. 待支持: 图数据库, 向量数据库

preference操作命令 (默认hash表, 不支持全表扫描)
      1
      .help
      显示帮助信息
      2
      .q | .quit
      退出
      3
      query key: {key name}
      根据指定的key查询数据库
      4
      put key: {key name}
       value:{value}
      插入指定的键值对到数据库或者更新键值对。
      输入需要两行，第一行输入key，使用”key:”作为关键字，第二行输入value，使用“value:”作为关键字；
      5
      delete key：{key name}
      删除指定键值对。
      6
      delete
      删除表内所有内容

kvStore操作命令 (不支持全表删除)
      1
      .help
      显示帮助信息
      2
      .q | .quit
      退出
      3
      query key: {key name}
      根据指定的key查询数据库
      4
      scan
      全表查询
      5
      put key: {key name}
      value:{value}
      插入指定的键值对到数据库或者更新键值对。
      输入需要两行，第一行输入key，使用”key:”作为关键字，第二行输入value，使用“value:”作为关键字；
      6
      delete key：{key name}
      删除指定键值对。
