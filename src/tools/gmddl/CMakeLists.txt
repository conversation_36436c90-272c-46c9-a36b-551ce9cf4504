project(gmddl)

if (FUZZ OR COVERAGE)
    ADD_SUBDIRECTORY(gmddl_inner)
endif()

ADD_COMPILE_OPTIONS(-Wl,--copy-dt-needed-entries -fPIE)

include_sub_directories_recursively(${CMAKE_CURRENT_SOURCE_DIR})
include_sub_directories_recursively(${GMDB_CLIENT_PATH})

aux_source_directory(. SRC_MAIN_LIST)
aux_source_directory(${gmddl_SOURCE_DIR}/gmddl_inner SRC_GMDDL_LIST)

if (TOOLS_EMBED_SO)
  generate_tools_embed_so(gmddl_embed "${SRC_GMDDL_LIST}" "${TOOL_NAME}")
endif()

# 编译patch_obj不编译可执行程序
if(COMPILE_PATCH_OBJ)
  return()
endif()

add_executable (gmddl ${SRC_MAIN_LIST} ${SRC_GMDDL_LIST})
if(STRIP)
  separate_debug_info(gmddl)
endif()

target_link_libraries(gmddl ${TOOL_NAME})

install(TARGETS gmddl
  RUNTIME DESTINATION bin
)
