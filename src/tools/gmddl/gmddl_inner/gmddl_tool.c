/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: the tool for change or delete vertexLabel meta data
 * Author: cuijinxin
 * Create: 2023-2-21
 */
#include "gmddl_tool.h"
#include "gmddl_typedef.h"
#include "gmc_internal.h"
#include "gmimport_file.h"
#include "clt_stmt.h"
#include "adpt_sleep.h"
#include "adpt_define.h"
#include "gmc_graph.h"
#include "gmc_namespace.h"
#include "tool_main.h"
#include "gmc_kv.h"
#include "gmc.h"

#ifdef __cplusplus
extern "C" {
#endif

#define MGDDL_SCHEMA_FILE_SUFFIX "gmjson"

// 从文件路径中获取表名
static Status GetVertexLabelNameFromFilePath(const char *filePath, const char *vertexLabelName)
{
    // 截取配置文件名
    char splitStr[MAX_TABLE_NAME_LEN] = {};
    char path[DB_MAX_PATH] = {};
    errno_t err = strcpy_s(path, DB_MAX_PATH, filePath);
    if (err != EOK) {
        return GMERR_DATA_EXCEPTION;
    }
    char *nextStr = NULL;
    char *delStr = NULL;
    char *targetStr = path;
    while (strstr(targetStr, GM_DDL_FILEPATH_DELIM) != NULL) {
        delStr = strtok_s(targetStr, GM_DDL_FILEPATH_DELIM, &nextStr);
        if (nextStr != NULL && strlen(nextStr) > 0) {
            targetStr = nextStr;
        } else {
            targetStr = delStr;
        }
    }
    err = strcpy_s(splitStr, MAX_TABLE_NAME_LEN, targetStr);
    if (err != EOK) {
        return GMERR_DATA_EXCEPTION;
    }
    // 匹配.*json前的表名
    int32_t shouldGetCount = 1;
    err = sscanf_s(splitStr, "%[^.]s", vertexLabelName, MAX_TABLE_NAME_LEN);
    if (err != shouldGetCount) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

static Status GmDDLTrimSpaceAndReturn(char *originStr, uint32_t originStrLen, char *destStr, uint32_t destStrLen)
{
    DB_POINTER2(originStr, destStr);
    if (destStrLen < originStrLen) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    uint32_t i = 0;
    char *str = originStr;
    // 通过GmDDLReadConfFile读出cfg,结尾有结束符,后续由strtok_s分割得出字符串originStr,此处必定有结束符
    for (; *str != '\0'; str++) {
        if (*str != ' ' && (char)*str != GM_DDL_UPGRADE_CONFIG_RETURN_ASCII) {
            destStr[i] = *str;
            i++;
        }
    }
    destStr[i] = '\0';
    return GMERR_OK;
}

static Status GmDDLReadConfFile(DbMemCtxT *memCtx, char *filePath, char **fileBuff)
{
    DB_POINTER3(memCtx, filePath, fileBuff);
    int32_t fd = DB_INVALID_FD;
    size_t fileLen = 0;
    char *buff = NULL;
    Status ret = DbFileSize(filePath, &fileLen);
    if (ret != GMERR_OK) {
        return GMERR_FILE_OPERATE_FAILED;
    }
    if (fileLen > MAX_VSCHEMA_FILE_SIZE) {
        ret = GMERR_PROGRAM_LIMIT_EXCEEDED;
        return ret;
    }

    size_t buffSize = sizeof(char) * (fileLen + 1);
    // buff用于存放读取的文件buf,本函数执行成功后由调用者GmDDLBatchUpgrade/GmDDLUpgradeSingleLabel释放
    buff = DbDynMemCtxAlloc(memCtx, (size_t)buffSize);
    if (buff == NULL) {
        ret = GMERR_OUT_OF_MEMORY;
        return ret;
    }
    ret = DbOpenFile(filePath, READ_ONLY, PERM_USER, &fd);
    if (ret != GMERR_OK || fd == DB_INVALID_FD) {
        DbDynMemCtxFree(memCtx, buff);
        return GMERR_FILE_OPERATE_FAILED;
    }
    ret = DbReadFileExpSize(fd, buff, fileLen);
    if (ret != GMERR_OK) {
        // 局部变量，外部函数无法获得，未置NULL，无UAF风险
        DbDynMemCtxFree(memCtx, buff);
        DbCloseFile(fd);
        return ret;
    }
    DbCloseFile(fd);
    buff[fileLen] = '\0';
    *fileBuff = buff;
    return ret;
}

void GmDDLPrintHelp(void)
{
    const char *gmDDLOption[][MANUAL_COL_NUM] = {
        {"-h", "", "print the online help manual"},
        {"-c", "<cmd_type>", "drop | alter"},
        {"-t", "<label_name>", "the specified label to drop or alter"},
        {"-f", "<file_path>", "upgrade or drop by specified file path"},
        {"-u", "<upgrade_mode>", "online | batch"},
        {"-d", "<degrade_mode>", "sync | async"},
        {"-v", "<version>", "degrade target version"},
        {"-s", "<server_locator>", "serverLocator for connection"},
        {"-ns", "<namespace_name>", "namespace of the correspond label or resource"},
        {"-user", "<user_name>", "user name for connection"},
    };
    DbPrintManual("gmddl help information:", gmDDLOption, ELEMENT_COUNT(gmDDLOption), NULL);
    return;
}

inline static void GmDDLInitCtx(GmDDLContextT *ctx)
{
    DB_POINTER(ctx);
    ctx->conn = NULL;
    ctx->stmt = NULL;
    (void)memset_s(&ctx->paramInfo, sizeof(ParamInfoT), 0, sizeof(ParamInfoT));
}

// 断开连接
inline static void GmDDLDisconnect(GmDDLContextT *ctx)
{
    TlsDisconnect(ctx->conn, ctx->stmt);
    TlsPerfEnvUnInit();
}

// 建立连接
static Status GmDDLConnect(GmDDLContextT *ctx)
{
    Status ret = TlsPerfEnvInit();
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "gmddl env initial failed.");
        return ret;
    }
    TlsConnOptionT option = {
        .domainName = ctx->paramInfo.serverLocator,
        .userName = ctx->paramInfo.userName,
        .useReservedConn = false,
        .namespaceName = ctx->paramInfo.namespace,
        .noMemLimit = false,
    };
    ret = TlsConnect(&ctx->conn, &ctx->stmt, &option);
    if (ret != GMERR_OK) {
        TlsPerfEnvUnInit();
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "Can not connect server.");
        return ret;
    }
    ret = GmcUseNamespace(ctx->stmt, ctx->paramInfo.namespace);
    if (ret != GMERR_OK) {
        GmDDLDisconnect(ctx);
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "Can not use namespace.");
        return ret;
    }
    return ret;
}

static Status GmDDLParseParam(DbOptionRuleT *rule, char *dst, uint32_t dstLen, const char *optionName)
{
    DB_POINTER3(rule, dst, optionName);
    if (!DbCheckIsUsedByOptionName(rule, optionName)) {
        return GMERR_OK;
    }
    DbOptionParamT *params = DbGetParamsByOptionName(rule, optionName);
    /* 内部接口，该值不会为NULL，参数解析时已校验 */
    if (params == NULL) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    errno_t ret = strncpy_s(dst, (size_t)dstLen, params->paramVal[FIRST_PARAM].strVal,
        (size_t)strlen(params->paramVal[FIRST_PARAM].strVal));
    return (ret == EOK) ? GMERR_OK : GMERR_INVALID_PARAMETER_VALUE;
}

static Status GmDDLSetDefaultParam(GmDDLContextT *ctx)
{
    // -s 为空设置默认值设置
    Status ret = GmDDLParseParam(ctx->optionRule, ctx->paramInfo.serverLocator, sizeof(ctx->paramInfo.serverLocator),
        GM_DDL_OPTION_SERVER_ADDRESS);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 当serverLocator为空或者为V3的默认serverLocator，使用V5默认serverLocator
    if (strlen(ctx->paramInfo.serverLocator) == 0 ||
        DbStrCmp(ctx->paramInfo.serverLocator, SHM_UNIX_EMSERVER, 0) == 0) {
        errno_t r =
            strcpy_s(ctx->paramInfo.serverLocator, sizeof(ctx->paramInfo.serverLocator), DB_DEFAULT_DOMAIN_NAME);
        if (r != EOK) {
            return GMERR_INVALID_PARAMETER_VALUE;
        }
        (void)DbPrintfDefault("[GMDDL] Server locator use default value: %s.\n", DB_DEFAULT_DOMAIN_NAME);
    }
    // -ns 为空设置默认值设置
    ret = GmDDLParseParam(
        ctx->optionRule, ctx->paramInfo.namespace, sizeof(ctx->paramInfo.namespace), GM_DDL_OPTION_NAMESPACE);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (strlen(ctx->paramInfo.namespace) == 0) {
        errno_t r = strcpy_s(ctx->paramInfo.namespace, sizeof(ctx->paramInfo.namespace), DB_DEFAULT_NAMESPACE);
        if (r != EOK) {
            return GMERR_INVALID_PARAMETER_VALUE;
        }
        (void)DbPrintfDefault("[GMDDL] NameSpace use default value: %s.\n", DB_DEFAULT_NAMESPACE);
    }
    // userName在不同场景下有可能会为空，不用校验
    (void)GmDDLParseParam(
        ctx->optionRule, ctx->paramInfo.userName, sizeof(ctx->paramInfo.userName), GM_DDL_OPTION_USER_NAME);

    return ret;
}

static Status GmDDLParseParams(GmDDLContextT *ctx)
{
    DB_POINTER(ctx);
    Status ret;
    ret = GmDDLParseParam(ctx->optionRule, ctx->paramInfo.alterType, GM_DDL_MAX_MODE_TYPE, GM_DDL_OPTION_TYPE);
    if (ret != GMERR_OK) {
        return ret;
    }
    // -f
    ret = GmDDLParseParam(ctx->optionRule, ctx->paramInfo.schemaFilePath, DB_MAX_PATH, GM_DDL_OPTION_FILE_PATH);
    if (ret != GMERR_OK) {
        return ret;
    }
    // -t
    ret = GmDDLParseParam(ctx->optionRule, ctx->paramInfo.labelName, MAX_TABLE_NAME_LEN, GM_DDL_OPTION_TABLE_NAME);
    if (ret != GMERR_OK) {
        return ret;
    }
    // -u
    ret = GmDDLParseParam(ctx->optionRule, ctx->paramInfo.updateMode, GM_DDL_MAX_MODE_TYPE, GM_DDL_OPTION_UPGRADE_MODE);
    if (ret != GMERR_OK) {
        return ret;
    }
    // -d
    ret = GmDDLParseParam(
        ctx->optionRule, ctx->paramInfo.downgradeMode, GM_DDL_MAX_MODE_TYPE, GM_DDL_OPTION_DOWNGRADE_MODE);
    if (ret != GMERR_OK) {
        return ret;
    }
    // -v
    char versionStr[GM_DDL_MAX_VERSION_LEN] = {};
    ret = GmDDLParseParam(ctx->optionRule, versionStr, GM_DDL_MAX_VERSION_LEN, GM_DDL_OPTION_DROP_VERSION);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (strlen(versionStr) > 0) {
        ret = DbStrToUint32(versionStr, &ctx->paramInfo.version);
        if (ret != GMERR_OK) {
            return ret;
        }
    } else {
        ctx->paramInfo.version = DB_MAX_UINT32;
    }

    // -s 和 -ns 设置默认值
    ret = GmDDLSetDefaultParam(ctx);
    if (ret != GMERR_OK) {
        return ret;
    }
    return ret;
}

static Status GmDDLWaitDowngradeFinish(GmDDLContextT *ctx, const char *vertexLabelName)
{
    DB_POINTER2(ctx, vertexLabelName);
    Status ret = GMERR_OK;
    uint64_t startTime = DbClockGetTsc();
    uint32_t degradeProcess = 0;
    while (!DbExceedTime(startTime, GM_DDL_DOWNGRADE_MAX_WAIT_MSECONDS)) {
        // 查询表降级进度
        ret = GmcGetVertexLabelDegradeProgress(ctx->stmt, vertexLabelName, &degradeProcess);
        if (ret != GMERR_OK) {
            break;
        }
        // 轮询等待
        DbSleep(GM_DDL_WAIT_DOWNGRADE_MSECONDS);
    }
    // 当查询不到降级信息时表明降级任务已结束
    if (ret == GMERR_NO_DATA) {
        return GMERR_OK;
    }
    if (DbExceedTime(startTime, GM_DDL_DOWNGRADE_MAX_WAIT_MSECONDS)) {
        return GMERR_REQUEST_TIME_OUT;
    }
    return ret;
}

static Status GmDDLDowngradeSingleLabel(GmDDLContextT *ctx, const char *vertexLabelName, uint32_t version, bool isSync)
{
    DB_POINTER2(ctx, vertexLabelName);
    Status ret = GMERR_OK;
    // 发起降级请求
    ret = GmcDegradeVertexLabel(ctx->stmt, vertexLabelName, version);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 同步模式需等待表降级结束
    if (isSync) {
        ret = GmDDLWaitDowngradeFinish(ctx, vertexLabelName);
    }
    return ret;
}

static Status GmDDLDowngradeVertexLabel(GmDDLContextT *ctx)
{
    DB_POINTER(ctx);
    Status ret = GMERR_OK;
    if (strcmp(ctx->paramInfo.downgradeMode, GM_DDL_OPTION_DOWNGRADE_MODE_SYNC) != 0 &&
        strcmp(ctx->paramInfo.downgradeMode, GM_DDL_OPTION_DOWNGRADE_MODE_ASYNC) != 0) {
        TOOL_WARN(DbPrintfDefault, GMERR_INVALID_PARAMETER_VALUE, "Degrade scene only support sync or async.");
        GmDDLPrintHelp();
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (strlen(ctx->paramInfo.labelName) == 0) {
        TOOL_WARN(DbPrintfDefault, GMERR_INVALID_PARAMETER_VALUE, "Degrade scene need appoint vertexLabel name.");
        GmDDLPrintHelp();
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    // V5此处updateMode必为空,但为与V3保持一致,日志仍有-u输出
    if (strlen(ctx->paramInfo.schemaFilePath) != 0) {
        TOOL_WARN(DbPrintfDefault, GMERR_INVALID_PARAMETER_VALUE, "Degrade scene do not need -f -u.");
        GmDDLPrintHelp();
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    ret = GmDDLConnect(ctx);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmDDLDowngradeSingleLabel(ctx, ctx->paramInfo.labelName, ctx->paramInfo.version,
        strcmp(ctx->paramInfo.downgradeMode, GM_DDL_OPTION_DOWNGRADE_MODE_SYNC) == 0);
    if (ret == GMERR_OK) {
        (void)DbPrintfDefault("Alter schema degrade successfully\n");
    } else if (ret == GMERR_REQUEST_TIME_OUT) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "Alter schema degrade timeout");
    } else {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "Alter schema degrade unsuccessfully");
    }
    GmDDLDisconnect(ctx);
    return ret;
}

static Status GmDDLUpgradeSingleLabel(GmDDLContextT *ctx, const char *vertexLabelName, char *schemaFilePath)
{
    DB_POINTER2(ctx, schemaFilePath);
    Status ret = GMERR_OK;
    // 读取json文件
    char *cfgJson = NULL;
    ret = GmDDLReadConfFile(ctx->stmt->memCtx, schemaFilePath, &cfgJson);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret,
            "Unsuccess read config json when upgrade vertexLabel, ret= %" PRIu32 ", os ret no: %" PRId32 ".", ret,
            DbAptGetErrno());
        return ret;
    }
    // 发起升级请求(当表名为空则使用schema json中的表名)
    if (vertexLabelName == NULL || strlen(vertexLabelName) == 0) {
        ret = GmcAlterVertexLabelWithName(ctx->stmt, cfgJson, true, NULL);
    } else {
        ret = GmcAlterVertexLabelWithName(ctx->stmt, cfgJson, true, vertexLabelName);
    }

    // 使用结束释放 GmDDLReadConfFile 内申请的内存，无风险
    DbDynMemCtxFree(ctx->stmt->memCtx, cfgJson);
    return ret;
}

static bool GmDDLCheckUpdateTask(DbListT *upgradeTaskList, char *vertexLabelName, char *filePath)
{
    DB_POINTER2(upgradeTaskList, vertexLabelName);
    if (strlen(vertexLabelName) == 0) {
        TOOL_RUN_ERROR(
            DbPrintfDefault, GMERR_INVALID_PARAMETER_VALUE, "Unsuccess read config,the vertexLabel name len is 0.");
        return false;
    }
    uint32_t count = upgradeTaskList->count;
    GmDDLUpgradeTaskT *taskList = upgradeTaskList->items;
    for (uint32_t i = 0; i < count; i++) {
        if (strcmp(taskList[i].vertexLabelName, vertexLabelName) == 0) {
            TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_PARAMETER_VALUE,
                "Unsuccess read config,the repeated vertexLabel name is: %s.", vertexLabelName);
            return false;
        }
    }
    if (!DbFileExist(filePath)) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_PARAMETER_VALUE,
            "Unsuccess read config,the schema file can not found: %s.", filePath);
        return false;
    }
    return true;
}

static Status GmDDLCreateUpgradeTaskList(GmDDLContextT *ctx, char *cfg, DbListT *upgradeTaskList)
{
    DB_POINTER2(cfg, upgradeTaskList);
    Status ret = GMERR_OK;
    // 以换行符作为分割
    char *nextStr = NULL;
    char *delStr = strtok_s(cfg, GM_DDL_UPGRADE_CONFIG_DELIM, &nextStr);
    if (delStr == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "Unsuccess read config when create upgrade task list.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    while (delStr != NULL) {
        // 字符串空格清理
        char configStr[GM_DDL_MAX_CONFIG_LINE_LEN] = {};
        ret = GmDDLTrimSpaceAndReturn(delStr, (uint32_t)strlen(delStr) + 1, configStr, GM_DDL_MAX_CONFIG_LINE_LEN);
        if (ret != GMERR_OK) {
            return ret;
        }
        // 全换行符和空格则忽略
        if (strlen(configStr) == 0) {
            delStr = strtok_s(nextStr, GM_DDL_UPGRADE_CONFIG_DELIM, &nextStr);
            continue;
        }
        // DbAppendListItem 内实现会拷贝
        GmDDLUpgradeTaskT upgradeTask = {0};
        // 需包含","分隔符
        if (!strstr(configStr, GM_DDL_UPGRADE_CONFIG_ITEM_DELIM)) {
            TOOL_RUN_ERROR(
                DbPrintfDefault, GMERR_INVALID_PARAMETER_VALUE, "Unsuccess read config,the delimiter must be ','.");
            return GMERR_INVALID_PARAMETER_VALUE;
        }
        errno_t err = sscanf_s(configStr, "%[^,]s", upgradeTask.vertexLabelName, sizeof(upgradeTask.vertexLabelName));
        if (err != 1) {
            TOOL_RUN_ERROR(
                DbPrintfDefault, GMERR_DATA_EXCEPTION, "Unsuccess read config,can not read vertexLabel name.");
            return GMERR_DATA_EXCEPTION;
        }
        // schemaFilePath begin = configStr + vertexLabelName + ','
        // schemaFilePath len = configStrLen - vertexLabelNameLen - ','
        size_t nameLen = strlen(upgradeTask.vertexLabelName);
        size_t filePathLen = (strlen(configStr) - nameLen) - strlen(GM_DDL_UPGRADE_CONFIG_ITEM_DELIM);
        err = strncpy_s(
            upgradeTask.schemaFilePath, sizeof(upgradeTask.schemaFilePath), configStr + nameLen + 1, filePathLen);
        if (err != EOK) {
            TOOL_RUN_ERROR(DbPrintfDefault, GMERR_DATA_EXCEPTION, "Unsuccess read config,can not copy file path.");
            return GMERR_DATA_EXCEPTION;
        }
        // 表名不可为空,不允许重复,配置文件必须存在
        if (!GmDDLCheckUpdateTask(upgradeTaskList, upgradeTask.vertexLabelName, upgradeTask.schemaFilePath)) {
            return GMERR_INVALID_PARAMETER_VALUE;
        }
        ret = DbAppendListItem(upgradeTaskList, &upgradeTask);
        if (ret != GMERR_OK) {
            return ret;
        }
        delStr = strtok_s(nextStr, GM_DDL_UPGRADE_CONFIG_DELIM, &nextStr);
    }
    return ret;
}

static void GmDDLRetryDowngrade(GmDDLContextT *ctx, GmDDLUpgradeTaskT *task)
{
    DB_POINTER2(ctx, task);
    while (task->retryTime < GM_DDL_DOWNGRADE_RETRY_TIME) {
        DbSleep(GM_DDL_WAIT_DOWNGRADE_MSECONDS);
        Status ret = GmDDLDowngradeSingleLabel(ctx, task->vertexLabelName, DB_MAX_UINT32, false);
        if (ret == GMERR_OK) {
            break;
        }
        task->retryTime++;
    }
}

static Status GmDDLDowngradeAfterBatchUpgrade(
    GmDDLContextT *ctx, DbListT *downgradeTaskList, const uint32_t downgradeCount)
{
    DB_POINTER2(ctx, downgradeTaskList);
    Status ret = GMERR_OK;
    // 先将升级成功的全部发起降级
    GmDDLUpgradeTaskT *taskList = downgradeTaskList->items;
    uint32_t needWaitCount = 0;
    for (uint32_t i = 0; i < downgradeCount; i++) {
        ret = GmDDLDowngradeSingleLabel(ctx, taskList[i].vertexLabelName, DB_MAX_UINT32, false);
        if (ret == GMERR_OK) {
            needWaitCount++;
        } else {
            TOOL_RUN_ERROR(DbPrintfDefault, ret, "Unsuccess downgrade vertexLabel: %s,ret = %" PRIu32 ",begin retry.",
                taskList[i].vertexLabelName, ret);
            // 降级失败重试
            GmDDLRetryDowngrade(ctx, &taskList[i]);
        }
    }
    // 轮询等待降级结果
    for (uint32_t i = 0; i < needWaitCount; i++) {
        if (taskList[i].retryTime < GM_DDL_DOWNGRADE_RETRY_TIME) {
            ret = GmDDLWaitDowngradeFinish(ctx, taskList[i].vertexLabelName);
            if (ret != GMERR_OK) {
                TOOL_RUN_ERROR(DbPrintfDefault, ret,
                    "Unsuccess query vertexLabel downgrade info: %s,ret = %" PRIu32 ".", taskList[i].vertexLabelName,
                    ret);
            }
        }
    }
    return ret;
}

static Status GmDDLBatchUpgrade(GmDDLContextT *ctx)
{
    DB_POINTER(ctx);
    Status ret = GMERR_OK;
    if (strlen(ctx->paramInfo.labelName) != 0) {
        TOOL_WARN(DbPrintfDefault, GMERR_INVALID_PARAMETER_VALUE, "Batch upgrade scene do not need table name.");
        GmDDLPrintHelp();
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    // 读取批量升级配置文件生成升级列表
    char *cfg = NULL;
    ret = GmDDLReadConfFile(ctx->stmt->memCtx, ctx->paramInfo.schemaFilePath, &cfg);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret,
            "Unsuccess read config when batch upgrade, ret= %" PRIu32 ", os ret no: %" PRId32 ".", ret,
            DbAptGetErrno());
        return ret;
    }
    DbListT upgradeTaskList;
    DbCreateList(&upgradeTaskList, sizeof(GmDDLUpgradeTaskT), ctx->stmt->memCtx);
    ret = GmDDLCreateUpgradeTaskList(ctx, cfg, &upgradeTaskList);
    // 使用结束释放 GmDDLReadConfFile 内申请的内存，无风险
    DbDynMemCtxFree(ctx->stmt->memCtx, cfg);
    if (ret != GMERR_OK) {
        DbDestroyList(&upgradeTaskList);
        return ret;
    }
    // 批量升级
    uint32_t upCount = DbListGetItemCnt(&upgradeTaskList);
    uint32_t successfulCount = 0;
    GmDDLUpgradeTaskT *taskList = DbListGetItems(&upgradeTaskList);
    for (uint32_t i = 0; i < upCount; i++) {
        (void)DbPrintfDefault("Execute upgradeTask,vertexLabel name: %s, schemaFilePath: %s.\n",
            taskList[i].vertexLabelName, taskList[i].schemaFilePath);
        ret = GmDDLUpgradeSingleLabel(ctx, taskList[i].vertexLabelName, taskList[i].schemaFilePath);
        if (ret != GMERR_OK) {
            TOOL_RUN_ERROR(DbPrintfDefault, ret,
                "Unsuccess upgrade vertexLabel: %s,begin downgrade vertexLabel in upgradeTaskList.",
                taskList[i].vertexLabelName);
            break;
        }
        successfulCount++;
    }
    // 升级失败需要降级回原版本
    if (successfulCount > 0 && successfulCount < upCount) {
        Status result = GmDDLDowngradeAfterBatchUpgrade(ctx, &upgradeTaskList, successfulCount);
        if (result != GMERR_OK) {
            TOOL_RUN_ERROR(DbPrintfDefault, result, "Unsuccess downgrade after updrage: ret = %" PRIu32 ".", result);
        }
    }
    // 使用结束释放
    DbDestroyList(&upgradeTaskList);
    return ret;
}

static Status GmDDLUpgradeVertexLabel(GmDDLContextT *ctx)
{
    DB_POINTER(ctx);
    Status ret = GMERR_OK;
    if (strlen(ctx->paramInfo.schemaFilePath) == 0) {
        TOOL_WARN(DbPrintfDefault, GMERR_INVALID_PARAMETER_VALUE, "File path should be specified.");
        GmDDLPrintHelp();
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (strcmp(ctx->paramInfo.updateMode, GM_DDL_OPTION_UPGRADE_MODE_BATCH) != 0 &&
        strcmp(ctx->paramInfo.updateMode, GM_DDL_OPTION_UPGRADE_MODE_ONLINE) != 0) {
        TOOL_WARN(DbPrintfDefault, GMERR_INVALID_PARAMETER_VALUE, "Upgrade scene only support offline or online.");
        GmDDLPrintHelp();
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (strlen(ctx->paramInfo.downgradeMode) != 0 || ctx->paramInfo.version != DB_MAX_UINT32) {
        TOOL_WARN(DbPrintfDefault, GMERR_INVALID_PARAMETER_VALUE, "Upgrade scene do not need degrade mode or version.");
        GmDDLPrintHelp();
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    // 当非批量升级模式且表名为空时,以文件名作为表名
    if (strcmp(GM_DDL_OPTION_UPGRADE_MODE_BATCH, ctx->paramInfo.updateMode) != 0 &&
        strlen(ctx->paramInfo.labelName) == 0) {
        ret = GetVertexLabelNameFromFilePath(ctx->paramInfo.schemaFilePath, ctx->paramInfo.labelName);
        if (ret != GMERR_OK) {
            TOOL_WARN(DbPrintfDefault, ret, "Get table name from filepath unsuccess:%" PRIu32 ".", ret);
            return ret;
        }
    }
    ret = GmDDLConnect(ctx);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (strcmp(GM_DDL_OPTION_UPGRADE_MODE_BATCH, ctx->paramInfo.updateMode) == 0) {
        // 批量升级
        ret = GmDDLBatchUpgrade(ctx);
    } else {
        ret = GmDDLUpgradeSingleLabel(ctx, ctx->paramInfo.labelName, ctx->paramInfo.schemaFilePath);
    }
    if (ret == GMERR_OK) {
        (void)DbPrintfDefault("Alter schema upgrade successfully\n");
    } else {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "Alter schema upgrade unsuccessfully");
    }
    GmDDLDisconnect(ctx);
    return ret;
}

static uint32_t g_gmdbDroppedTableNum = 0;
static uint32_t g_gmdbUnDroppedTableNum = 0;

Status GmDDLDropLabelByName(GmcStmtT *stmt, const char *labelName)
{
    uint32_t labelType;
    Status ret = GmcGetLabelTypeByName(stmt, labelName, &labelType);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "unable to get label type. label name:%s.", labelName);
        return ret;
    }

#ifdef FEATURE_KV
    if (labelType == GMC_LABEL_TYPE_KV) {
        return GmcKvDropTable(stmt, labelName);
    }
#endif
    return GmcDropVertexLabel(stmt, labelName);
}

static Status GmGetTableNameAndDrop(const char *fileName, GmDDLContextT *ctx)
{
    DB_POINTER2(fileName, ctx);
    char labelName[MAX_TABLE_NAME_LEN] = {0};
    // 将文件名前缀作为表的实际名字
    errno_t err = strcpy_s(labelName, sizeof(labelName), fileName);
    if (err != EOK) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_FILE_OPERATE_FAILED, "string copy failed, string is %s.", fileName);
        return GMERR_FILE_OPERATE_FAILED;
    }
    char *prefix = DbGetFileNameInfo(labelName, MAX_TABLE_NAME_LEN, DB_FILENAME_PREFIX);
    if (prefix == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_FILE_OPERATE_FAILED, "Get file prefix form %s failed.", fileName);
        return GMERR_FILE_OPERATE_FAILED;
    }
    Status ret = GmDDLDropLabelByName(ctx->stmt, prefix);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "drop label %s unsuccessfully.", prefix);
        g_gmdbUnDroppedTableNum++;
        return ret;
    }
    g_gmdbDroppedTableNum++;
    TOOL_RUN_INFO(DbPrintfDefault, "[INFO] drop label %s successfully.", prefix);
    return GMERR_OK;
}

static Status CheckFilePathCorrect(const char *filePath, uint32_t recurDepth, struct stat *pathInfo)
{
    DB_POINTER2(filePath, pathInfo);
    if (recurDepth > MAX_RECURSION_DEPTH) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_FILE_OPERATE_FAILED,
            "recursion exceeded the maximum depth, ret = %d", (int32_t)GMERR_FILE_OPERATE_FAILED);
        return GMERR_FILE_OPERATE_FAILED;
    }

    if (stat(filePath, pathInfo) != 0) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_FILE_OPERATE_FAILED, "Stat file_path %s unsuccessfully.", filePath);
        return GMERR_FILE_OPERATE_FAILED;
    }
    return GMERR_OK;
}

static Status GmDropTableByFilePath(const char *filePath, GmDDLContextT *ctx, uint32_t recurDepth)
{
    struct stat pathInfo;
    Status ret = CheckFilePathCorrect(filePath, recurDepth, &pathInfo);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (S_ISREG(pathInfo.st_mode)) {
        if (!TlsCheckFileSuffix(filePath, MGDDL_SCHEMA_FILE_SUFFIX)) {
            TOOL_RUN_ERROR(DbPrintfDefault, GMERR_FILE_OPERATE_FAILED, "Invalid suffix for file %s.", filePath);
            return GMERR_FILE_OPERATE_FAILED;
        }
        return GmGetTableNameAndDrop(filePath, ctx);
    } else if (S_ISDIR(pathInfo.st_mode)) {
        DIR *dir = opendir(filePath);
        if (dir == NULL) {
            TOOL_RUN_ERROR(DbPrintfDefault, GMERR_FILE_OPERATE_FAILED, "Open directory %s unsuccessfully.", filePath);
            return GMERR_FILE_OPERATE_FAILED;
        }
        char dirName[DB_MAX_PATH] = {0};
        struct dirent *fileName = readdir(dir);
        while (fileName != NULL) {
            if (((strlen(fileName->d_name) == strlen(".")) && (strncmp(fileName->d_name, ".", strlen(".")) == 0)) ||
                ((strlen(fileName->d_name) == strlen("..")) && (strncmp(fileName->d_name, "..", strlen("..")) == 0))) {
                fileName = readdir(dir);
                continue;
            }
            (void)memset_s(dirName, DB_MAX_PATH, 0, sizeof(dirName));
            int32_t length = sprintf_s(dirName, DB_MAX_PATH, "%s/%s", filePath, fileName->d_name);
            if (length < 0) {
                TOOL_RUN_ERROR(
                    DbPrintfDefault, GMERR_FILE_OPERATE_FAILED, "sprintf_s unsuccessfully, ret = %d.", length);
                fileName = readdir(dir);
                continue;
            }
            ret = GmDropTableByFilePath(dirName, ctx, recurDepth + 1);
            if (ret != GMERR_OK) {
                TOOL_RUN_ERROR(DbPrintfDefault, ret, "drop table by file path unsuccessfully, ret = %d.", ret);
                fileName = readdir(dir);
                continue;
            }
            fileName = readdir(dir);
        }
        (void)closedir(dir);
        return GMERR_OK;
    }
    TOOL_RUN_ERROR(DbPrintfDefault, GMERR_FILE_OPERATE_FAILED, "File %s is invalid.", filePath);
    return GMERR_FILE_OPERATE_FAILED;
}

static Status GmDDLDropLabel(GmDDLContextT *ctx)
{
    DB_POINTER(ctx);
    Status ret = GMERR_OK;
    ret = GmDDLConnect(ctx);
    if (ret != GMERR_OK) {
        return ret;
    }
    const char *labelName = ctx->paramInfo.labelName;
    const char *fileName = ctx->paramInfo.schemaFilePath;
    if (strlen(labelName) != 0) {
        ret = GmDDLDropLabelByName(ctx->stmt, labelName);
        if (ret != GMERR_OK) {
            g_gmdbUnDroppedTableNum++;
            TOOL_RUN_ERROR(DbPrintfDefault, ret, "drop label %s unsuccessfully.", labelName);
            goto CLEAR;
        }
        TOOL_RUN_INFO(DbPrintfDefault, "[INFO] drop label %s successfully.", labelName);
        g_gmdbDroppedTableNum++;
    } else if (strlen(fileName) != 0) {
        ret = GmDropTableByFilePath(ctx->paramInfo.schemaFilePath, ctx, 1);
        if (ret != GMERR_OK) {
            TOOL_RUN_ERROR(DbPrintfDefault, ret, "drop label unsuccessfully, please check file name.");
            goto CLEAR;
        }
    }
CLEAR:
    TOOL_RUN_INFO(DbPrintfDefault,
        "[INFO] total %d legal tables name are found, successfully dropped table num is %d, unsuccessfully dropped "
        "table num is %d.",
        g_gmdbDroppedTableNum + g_gmdbUnDroppedTableNum, g_gmdbDroppedTableNum, g_gmdbUnDroppedTableNum);
    GmDDLDisconnect(ctx);
    return ret;
}

static Status GmDDLExec(GmDDLContextT *ctx)
{
    // 信息构造
    Status ret = GmDDLParseParams(ctx);
    if (ret != GMERR_OK) {
        TOOL_WARN(DbPrintfDefault, ret, "Invalid option, please use 'gmddl -h' for more information.");
        GmDDLPrintHelp();
        return ret;
    }
    // 升降级
    if (strcmp(GM_DDL_OPTION_TYPE_ALTER, ctx->paramInfo.alterType) == 0) {
        if (strlen(ctx->paramInfo.updateMode) > 0) {
            return GmDDLUpgradeVertexLabel(ctx);
        } else if (strlen(ctx->paramInfo.downgradeMode) > 0) {
            return GmDDLDowngradeVertexLabel(ctx);
        }
        TOOL_WARN(DbPrintfDefault, GMERR_INVALID_PARAMETER_VALUE, "Alter command only support upgrade or degrade.");
    } else if (strcmp(GM_DDL_OPTION_TYPE_DROP, ctx->paramInfo.alterType) == 0) {
        if (strlen(ctx->paramInfo.labelName) != 0 || strlen(ctx->paramInfo.schemaFilePath) != 0) {
            return GmDDLDropLabel(ctx);
        }
        TOOL_WARN(DbPrintfDefault, GMERR_INVALID_PARAMETER_VALUE,
            "drop command should provide table name, or file name, or dir name.");
    }
    GmDDLPrintHelp();
    return GMERR_DATA_EXCEPTION;
}

static Status GmDDLEntry(int32_t argc, char **argv, DbOptionRuleT *optionRule)
{
    DB_POINTER(optionRule);
    Status ret = GMERR_OK;
    // 如果没有输入参数，直接打印帮助信息。
    if (argc == 1) {
        GmDDLPrintHelp();
        return GMERR_OK;
    }
    GmDDLContextT ctx;
    ctx.optionRule = optionRule;
    GmDDLInitCtx(&ctx);
    // 注册命令参数
    ret = GmDDLOptionInit(argc, argv, optionRule);
    if (ret != GMERR_OK) {
        return ret;
    }
    int32_t ruleIndex = DbGetStartupRule(ctx.optionRule);
    if (ruleIndex == OPTION_NOT_FOUND) {
        TOOL_WARN(DbPrintfDefault, GMERR_SYNTAX_ERROR, "Invalid option, please use 'gmddl -h' for more information.");
        return GMERR_SYNTAX_ERROR;
    }
    if (ruleIndex == (int32_t)GM_DDL_RULE_HELP) {
        GmDDLPrintHelp();
        return GMERR_OK;
    }
    // 表结构变更
    ret = GmDDLExec(&ctx);
    return ret;
}
Status GmDDLMain(int32_t argc, char **argv)
{
#ifdef FEATURE_TOOLS_SYSLOG
    GmcLogAdptFuncsT tlsLogFuncs = {.userWriteFunc = TlsPrintLog, .handle = NULL};
    DbLogRegAdptFuncs(&tlsLogFuncs);
#endif

    Status ret = GMERR_OK;
    DbOptionRuleT optionRule = {0};
    ret = GmDDLAllocOptionRuleItems(&optionRule);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmDDLEntry(argc, argv, &optionRule);
    DbReleaseOptionRuleItems(&optionRule);
    return ret;
}

#ifdef FEATURE_CLT_SERVER_SAME_PROCESS
inline int32_t GmsDDLMain(int32_t argc, char *argv[])
{
    if (DbGetServerSameProcessStartFlag() > 0) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "execute gms-tools in same process as gmserver.");
        return GMERR_DATA_EXCEPTION;
    }

    return GmDDLMain(argc, argv);
}
#endif

#ifdef __cplusplus
}
#endif
