/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: gmddl interface
 * Author: cuijinxin
 * Create: 2023-2-21
 */

#ifndef GM_DDL_TOOLS
#define GM_DDL_TOOLS

#include "adpt_types.h"
#include "db_option_parser.h"
#include "adpt_locator.h"
#include "db_utils.h"
#include "tool_utils.h"
#include "adpt_string.h"

#ifdef __cplusplus
extern "C" {
#endif

#define GM_DDL_FILEPATH_DELIM "/"
#define GM_DDL_UPGRADE_CONFIG_ITEM_DELIM ","
#define GM_DDL_UPGRADE_CONFIG_DELIM "\n"
// 回车符 "/r" ASCII码
#define GM_DDL_UPGRADE_CONFIG_RETURN_ASCII 13

#define GM_DDL_MAX_MODE_TYPE 16
#define GM_DDL_MAX_VERSION_LEN 12
#define GM_DDL_MAX_CONFIG_LINE_LEN (MAX_TABLE_NAME_LEN + DB_MAX_PATH)

#define GM_DDL_WAIT_DOWNGRADE_MSECONDS 500
#define GM_DDL_DOWNGRADE_MAX_WAIT_MSECONDS (60 * 1000)
#define GM_DDL_DOWNGRADE_RETRY_TIME 20

typedef struct {
    uint32_t version;
    char alterType[GM_DDL_MAX_MODE_TYPE];
    char labelName[MAX_TABLE_NAME_LEN];
    char schemaFilePath[DB_MAX_PATH];
    char updateMode[GM_DDL_MAX_MODE_TYPE];
    char downgradeMode[GM_DDL_MAX_MODE_TYPE];
    char namespace[MAX_NAMESPACE_LENGTH];
    char serverLocator[DOMAIN_NAME_MAX_LEN];
    char userName[MAX_USER_NAME_LEN];
} ParamInfoT;

typedef struct {
    GmcConnT *conn;
    GmcStmtT *stmt;
    ParamInfoT paramInfo;
    DbOptionRuleT *optionRule;
} GmDDLContextT;

typedef struct {
    uint8_t retryTime;
    bool downgradeFinish;
    char vertexLabelName[MAX_TABLE_NAME_LEN];
    char schemaFilePath[DB_MAX_PATH];
} GmDDLUpgradeTaskT;

void GmDDLPrintHelp(void);

#ifdef __cplusplus
}
#endif

#endif  // GM_DDL_TOOLS
