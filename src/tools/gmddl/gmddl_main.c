/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: main function file of gmddl
 * Author: cuijinxin
 * Create: 2023-2-21
 */

#include "tool_main.h"
#include "tool_utils.h"

#define GMDDL_ERROR 1
#define GMDDL_DUPLICATE_TABLE 2  // 如果升级前后的表完全一致，工具返回2

int32_t main(int32_t argc, char *argv[])
{
    (void)DbToolPatchInit("gmddl", syslog, LOG_ERR);
    TlsSetCltSameProcessDeploy(false);
    int32_t ret = (int32_t)GmDDLMain(argc, argv);
    DbToolPatchUnInit("gmddl", syslog, LOG_ERR);
    if (ret == GMERR_OK) {
        return GMERR_OK;
    }
    if (ret == GMERR_DUPLICATE_TABLE) {
        return GMDDL_DUPLICATE_TABLE;
    }
    return GMDDL_ERROR;
}
