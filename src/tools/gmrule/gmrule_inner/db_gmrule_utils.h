/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: db_gmrule_utils.h
 * Description: public interface for gmrule
 * Author: zhangyoujian
 * Create: 2021-10-20
 */
#ifndef DB_GMRULE_UTILS_H
#define DB_GMRULE_UTILS_H
#include "db_json_common.h"
#include "db_gmrule_tool.h"
#include "gmc_internal_types.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define FIELD_GROUP_NAME "group"
#define FIELD_USER_NAME "user"
#define FIELD_PROCESS_NAME "process"
#define FILED_USERS "users"
#define FIELD_GROUPS "groups"
#define FIELD_RESERVED_CONN_NUM "reserved_conn_num"
#define ALLOWLIST_SUFFIX "gmuser"
#define POLICY_SUFFIX "gmpolicy"

#define GMRULE_USER_METADATA_MAX_VALUE DB_MAX_UINT16

typedef struct TagGmRuleCtxT *GmruleCtxHd;

typedef Status (*GmruleImportFunc)(GmruleCtxHd ctx, GmRuleOptionT *option, char *filePath);

typedef struct AllowListStat {
    uint32_t userSuccessNum;
    uint32_t userWarnNum;
    uint32_t userRollbackNum;
    uint32_t groupSuccessNum;
    uint32_t groupWarnNum;
    uint32_t groupRollbackNum;
} AllowListStatT;

typedef struct PolicyStat {
    uint32_t objPrivsSuccessNum;
    uint32_t objPrivsWarnNum;
    uint32_t objPrivsRollbackNum;
    uint32_t sysPrivsSuccessNum;
    uint32_t sysPrivsWarnNum;
    uint32_t sysPrivsRollbackNum;
} PolicyStatT;

typedef struct TagGmRuleCtxT {
    GmcStmtT *stmt;
    int32_t totalFileCnt;
    int32_t importFileCnt;
    AllowListStatT allowListStat;
    PolicyStatT policyStat;
    GmruleImportFunc importFunc;  // 具体的导入函数
    const char *fileSufix;        // 导入文件的后缀
    GmRuleImpTypeE opType;        // 导入操作类型
} GmRuleCtxT;

typedef struct GmruleAddBatchRange {
    uint32_t startPos;
    uint32_t endPos;
} GmruleAddBatchRangeT;

void GmruleInitRunCtx(GmRuleCtxT *ctx, GmcStmtT *stmt, GmRuleImpTypeE opType);

const char *GmruleGetStringValueByKey(const DbJsonT *json, const char *key, bool needLog);

uint16_t GmruleGetUint16ValueByKey(const DbJsonT *json, const char *key);

const DbJsonT *GmruleGetJsonStringOrArray(const DbJsonT *json, const char *key);

Status GmruleCheckJsonUsersList(const DbJsonT *usersOrGrpList, const char *arrayName, const char *tupleName);

Status GmruleLoadJson(char *filePath, uint32_t maxFilePathSize, DbJsonT **json);

Status GmruleParseUser(const DbJsonT *json, const char **processName, const char **userName, uint16_t *connNum);

Status GmruleParseUserOnCreation(const DbJsonT *json, GmcUsersCreateInfoT *usersInfo, uint32_t index);

Status GmruleParseGroup(const DbJsonT *json, const char **processName, const char **groupName);

Status GmruleParseGroupOnCreation(const DbJsonT *json, GmcGroupCreateInfoT *groupInfo);

typedef bool (*GmruleTupleCompare)(const DbJsonT *first, const DbJsonT *second);

bool GmruleCompareUserTuple(const DbJsonT *first, const DbJsonT *second);

bool GmruleCompareUserTupleList(const DbJsonT *first, const DbJsonT *second);

bool GmruleCompareGroupTuple(const DbJsonT *first, const DbJsonT *second);

bool GmruleCompareJsonString(const DbJsonT *first, const DbJsonT *second);

Status GmruleCheckDuplicate(const DbJsonT *tupleList, const GmruleTupleCompare compFunc);

Status GmruleImportMutipleFile(GmRuleCtxT *ctx, GmRuleOptionT *option, char *filePath, uint32_t recurDepth);

#ifdef __cplusplus
}
#endif
#endif
