/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: header file for importing policy
 * Author: zhangyoujian
 * Create: 2021-10-20
 */
#ifndef DB_GMRULE_POLICY_H
#define DB_GMRULE_POLICY_H
#include "db_gmrule_tool.h"
#include "db_json_common.h"
#include "db_gmrule_utils.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

typedef struct TagGmRuleStrDataMap {
    uint32_t dataType;
    const char *strType;
} GmGmRuleStrDataMap;

typedef enum EnumPolicyOperation { GRANT_PRIVS, REVOKE_PRIVS, ROLLBACK_GRANT_PRIVS } PolicyOperationE;

typedef struct TagGmObjTypeT {
    uint32_t *type;
    uint32_t count;
} GmObjTypeT;

typedef struct TagPrivilegeOperationCtx {
    GmcStmtT *stmt;
    PolicyOperationE opType;  // 权限操作类型
    uint32_t objType;         // 对象类型
    union {
        uint32_t objPrivs;  // 对象权限值
        uint32_t sysPrivs;  // 系统权限值
    };
    const char *namespace;  // 对象所属命名空间
    const char *userOrGrp;  // OS用户名/用户组名
    const char *process;    // 进程名称
    bool toUser;
    const char *objTypeStr;  // 对象字符类型
    const DbJsonT *objName;  // 对象名称，当其为array类型时，表示将多个表的权限批量授予一些用户, 仅用于对象权限
    const DbJsonT *usersList;   // 用户列表，批量用户权限配置场景下使用, 仅用于系统权限
    const DbJsonT *groupsList;  // 用户组列表，批量用户权限配置场景下使用, 仅用于系统权限
    GmObjTypeT labelType;  // 当objName为array类型时，用于标记每一张表是vertex或者KV对象, 仅用于对象权限
    uint32_t warningCnt;               // 警告次数
    uint32_t grantOrRevokeObjCount;    // 成功授权或者撤销權限的對象个数, 仅用于對象权限
    uint32_t grantOrRevokeUsersCount;  // 成功授权或者撤销的用户个数, 仅用于系统权限
    bool isGrantOrRevokeOk;            // 是否成功授权或者撤销权限
    bool reserved[3];                  // 预留字节补齐
} PrivilegeOperationCtxT;

inline static PrivilegeOperationCtxT GmruleInitPrivilegeOpCtx(GmcStmtT *stmt, PolicyOperationE opType)
{
    DB_POINTER(stmt);
    return (PrivilegeOperationCtxT){.stmt = stmt, .opType = opType, .isGrantOrRevokeOk = false};
}

Status GmruleAllocLabelTypeBuffer(PrivilegeOperationCtxT *ctx, uint32_t count);

void GmruleFreeLableTypeBuffer(PrivilegeOperationCtxT *ctx);

Status GmruleImportPolicyFile(GmRuleCtxT *ctx, GmRuleOptionT *option, char *filePath);

Status GmruleRevokePolicy(GmcStmtT *stmt, GmRuleOptionT *option, char *filePath);

#ifdef __cplusplus
}
#endif
#endif
