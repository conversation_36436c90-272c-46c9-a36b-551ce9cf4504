/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: source file for gmrule kernel
 * Author: <PERSON><PERSON><PERSON><PERSON>jian
 * Create: 2021-06-10
 */
#include "db_gmrule_kernel.h"
#include "db_gmrule_allowlist.h"
#include "db_gmrule_policy.h"
#include "db_gmrule_utils.h"
#include "db_file.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

void GmrulePrintImportPolicySuccessLog(const GmRuleCtxT *ctx, bool isAtomic)
{
    if (isAtomic) {
        PRINT_SUCCESS(DbPrintfDefault,
            "import policy. object privilege success: %" PRIu32 ", warning: %" PRIu32 ", rollback: %" PRIu32 ". ",
            ctx->policyStat.objPrivsSuccessNum, ctx->policyStat.objPrivsWarnNum, ctx->policyStat.objPrivsRollbackNum);
        PRINT_SUCCESS(DbPrintfDefault,
            "import policy. system privilege success: %" PRIu32 ", warning: %" PRIu32 ", rollback: %" PRIu32 ". ",
            ctx->policyStat.sysPrivsSuccessNum, ctx->policyStat.sysPrivsWarnNum, ctx->policyStat.sysPrivsRollbackNum);
    } else {
        PRINT_SUCCESS(DbPrintfDefault, "import policy. object privilege success: %" PRIu32 ", warning: %" PRIu32 ". ",
            ctx->policyStat.objPrivsSuccessNum, ctx->policyStat.objPrivsWarnNum);
        PRINT_SUCCESS(DbPrintfDefault, "import policy. system privilege success: %" PRIu32 ", warning: %" PRIu32 ". ",
            ctx->policyStat.sysPrivsSuccessNum, ctx->policyStat.sysPrivsWarnNum);
    }
}

Status GmruleImportPolicy(GmcStmtT *stmt, GmRuleOptionT *option, char *filePath)
{
    DB_POINTER3(stmt, option, filePath);
    GmRuleCtxT ctx = {0};
    GmruleInitRunCtx(&ctx, stmt, GMRULE_CMD_IMPORT_POLICY);
    if (!DbFileExist(filePath) && !DbDirExist(filePath)) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_FILE_OPERATE_FAILED,
            "no such file or directory: %s, ret = %" PRIi32, filePath, (int32_t)GMERR_FILE_OPERATE_FAILED);
        return GMERR_FILE_OPERATE_FAILED;
    }
    Status ret = GMERR_OK;
    if (DbFileExist(filePath)) {
        if (!TlsCheckFileSuffix(filePath, POLICY_SUFFIX)) {
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_FILE_OPERATE_FAILED,
                "policy suffix from file %s must be ." POLICY_SUFFIX ", ret = %" PRIi32, filePath,
                (int32_t)GMERR_FILE_OPERATE_FAILED);
            return GMERR_FILE_OPERATE_FAILED;
        }
        ret = GmruleImportPolicyFile(&ctx, option, filePath);
        if (ret == GMERR_OK) {
            TOOL_RUN_INFO(DbPrintfDefault, "[INFO] Import single policy file from %s successfully.", filePath);
        }
    } else {
        ret = GmruleImportMutipleFile(&ctx, option, filePath, 1);
        TOOL_RUN_INFO(DbPrintfDefault,
            "[INFO] Total %" PRIi32 " policy files were found in %s, %" PRIi32 " files OK, %" PRIi32
            " files unsuccessful",
            ctx.totalFileCnt, filePath, ctx.importFileCnt, ctx.totalFileCnt - ctx.importFileCnt);
    }

    GmrulePrintImportPolicySuccessLog(&ctx, option->isAtomic);

    return ret;
}

void GmrulePrintImportAllowListSuccessLog(const GmRuleCtxT *ctx, bool isAtomic)
{
    if (isAtomic) {
        PRINT_SUCCESS(DbPrintfDefault,
            "import allowlist, create db user. success: %" PRIu32 ", warning: %" PRIu32 ", rollback: %" PRIu32 ".",
            ctx->allowListStat.userSuccessNum, ctx->allowListStat.userWarnNum, ctx->allowListStat.userRollbackNum);
        PRINT_SUCCESS(DbPrintfDefault,
            "import allowlist, create db group. success: %" PRIu32 ", warning: %" PRIu32 ", rollback: %" PRIu32 ".",
            ctx->allowListStat.groupSuccessNum, ctx->allowListStat.groupWarnNum, ctx->allowListStat.groupRollbackNum);
    } else {
        PRINT_SUCCESS(DbPrintfDefault, "import allowlist, create db user. success: %" PRIu32 ", warning: %" PRIu32 ".",
            ctx->allowListStat.userSuccessNum, ctx->allowListStat.userWarnNum);
        PRINT_SUCCESS(DbPrintfDefault, "import allowlist, create db group. success: %" PRIu32 ", warning: %" PRIu32 ".",
            ctx->allowListStat.groupSuccessNum, ctx->allowListStat.groupWarnNum);
    }
}

Status GmruleImportAllowList(GmcStmtT *stmt, GmRuleOptionT *option, char *filePath)
{
    DB_POINTER3(stmt, option, filePath);
    GmRuleCtxT ctx = {0};
    GmruleInitRunCtx(&ctx, stmt, GMRULE_CMD_IMPORT_ALLOWLIST);
    if (!DbFileExist(filePath) && !DbDirExist(filePath)) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_FILE_OPERATE_FAILED,
            "no such file or directory: %s, ret = %" PRIi32, filePath, (int32_t)GMERR_FILE_OPERATE_FAILED);
        return GMERR_FILE_OPERATE_FAILED;
    }

    Status ret = GMERR_OK;
    if (DbFileExist(filePath)) {
        if (!TlsCheckFileSuffix(filePath, ALLOWLIST_SUFFIX)) {
            // "import white list from file %s unsuccessful, suffix must be ." ALLOWLIST_SUFFIX ", ret = %"
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_FILE_OPERATE_FAILED,
                "white list suffix from file %s must be ." ALLOWLIST_SUFFIX ", ret = %" PRIi32, filePath,
                (int32_t)GMERR_FILE_OPERATE_FAILED);
            return GMERR_FILE_OPERATE_FAILED;
        }
        ret = GmruleImportAllowListFile(&ctx, option, filePath);
        if (ret == GMERR_OK) {
            TOOL_RUN_INFO(DbPrintfDefault, "[INFO] Import single allow list file from %s successfully.", filePath);
        }
    } else {
        ret = GmruleImportMutipleFile(&ctx, option, filePath, 1);
        TOOL_RUN_INFO(DbPrintfDefault,
            "[INFO] Total %" PRIi32 " allow list files were found in %s, %" PRIi32 " files OK, %" PRIi32
            " files unsuccessful.",
            ctx.totalFileCnt, filePath, ctx.importFileCnt, ctx.totalFileCnt - ctx.importFileCnt);
    }

    GmrulePrintImportAllowListSuccessLog(&ctx, option->isAtomic);

    return ret;
}

static Status GmruleExecute(GmcStmtT *stmt, GmRuleOptionT *option)
{
    DB_POINTER2(stmt, option);
    Status ret = GMERR_OK;
    for (uint16_t idx = 0; idx < option->importNum; idx++) {
        GmRuleImpTypeE cmdType = option->importOptionArray[idx].cmdType;
        char *filePath = option->importOptionArray[idx].filePath;
        switch (cmdType) {
            case GMRULE_CMD_IMPORT_ALLOWLIST:
                ret = GmruleImportAllowList(stmt, option, filePath);
                break;
            case GMRULE_CMD_IMPORT_POLICY:
                ret = GmruleImportPolicy(stmt, option, filePath);
                break;
            case GMRULE_CMD_REMOVE_ALLOWLIST:
                ret = GmruleRemoveAllowList(stmt, option, filePath);
                break;
            case GMRULE_CMD_REVOKE_POLICY:
                ret = GmruleRevokePolicy(stmt, option, filePath);
                break;
            case GMRULE_CMD_BOTTON:
            default:
                break;
        }
    }

    return ret;
}

Status GmruleStart(GmRuleOptionT *option)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    TlsConnOptionT connOpt = {
        .domainName = option->domainName,
        .userName = option->userName,
        .namespaceName = NULL,
        .useReservedConn = false,
        .noMemLimit = false,
    };
    Status ret = TlsConnect(&conn, (GmcStmtT **)&stmt, &connOpt);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "gmrule connect unsuccessful. ret = %" PRIi32, (int32_t)ret);
        return ret;
    }
    ret = GmruleExecute(stmt, option);
    TlsDisconnect(conn, stmt);
    return ret;
}

#ifdef __cplusplus
}
#endif
