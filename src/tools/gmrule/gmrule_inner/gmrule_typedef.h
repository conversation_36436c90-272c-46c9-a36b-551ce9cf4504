/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: gmrule 命令格式定义头文件
 * Author: <PERSON><PERSON><PERSON><PERSON><PERSON>an
 * Create: 2021-06-10
 */
#ifndef GMRULE_TYPEDEF_H
#define GMRULE_TYPEDEF_H
#include "adpt_types.h"
#include "db_option_parser.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define RULE_OPTION_ARG_OPERATION "-c"
#define RULE_OPTION_ARG_FILE_PATH "-f"
#define RULE_OPTION_ARG_PRINT_DETAIL "-d"
#define RULE_OPTION_ARG_ATOMIC "atomic"

typedef enum GmImpStartupRule {
    GMRULE_RULE_HELP,
    GMRULE_RULE_VERSION,
    GMRULE_RULE_IMP,
    GMRULE_RULE_BOTTOM
} GmRuleStartupRuleE;

#define GMRULE_OPTION_NUM 9

Status GmRuleAllocOptionRuleItems(DbOptionRuleT *optionRule);
Status GmRuleInit(DbOptionRuleT *optionRule, int32_t argc, char *argv[]);
void GmRuleGetHelp(void);
void GmRuleGetVersion(void);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
