/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * File Name: public interface for gmrule
 * Description: 提供gmrule公共使用的接口
 * Author: zhangyoujian
 * Create: 2021-10-20
 */
#include "db_gmrule_utils.h"
#include "adpt_string.h"
#include "db_file.h"
#include "dirent.h"
#include "db_gmrule_allowlist.h"
#include "db_gmrule_policy.h"

#ifdef __cplusplus
extern "C" {
#endif

static char *g_allowListMetadataFiledList[USER_METADATA_FILED_NUM] = {"normal_vertexlabel_max", "yang_vertexlabel_max",
    "kv_label_max", "subscription_max", "resource_max", "namespace_max", "tablespace_max", "edge_max", "udf_max"};

bool GmruleIsFilePrefixValid(const char *prefix)
{
    DB_POINTER(prefix);
    const char *ch = prefix;
    if (!IsAlpha(*ch)) {
        return false;
    }
    while (*(++ch) != '\0') {
        bool ret = IsAlpha(*ch) || IsNumChar(*ch) || (*ch == '_');
        if (!ret) {
            return false;
        }
    }
    return true;
}

Status GmruleCheckFileNameValidity(const char *filePath)
{
    char tempPath[DB_MAX_GMRULE_FILE_PATH_LEN] = {0};
    errno_t err = strcpy_s(tempPath, sizeof(tempPath), filePath);
    if (err != EOK) {
        // "strcpy_s unsuccessful, too long filePath length: %zu, limited length: %"
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_FIELD_OVERFLOW,
            "strcpy_s unsucc, filePath length: %zu, limited len: %" PRIu32 ", ret = %" PRIi32, strlen(filePath),
            (uint32_t)(DB_MAX_GMRULE_FILE_PATH_LEN - 1), (int32_t)GMERR_FIELD_OVERFLOW);
        return GMERR_FIELD_OVERFLOW;
    }
    const char *prefix = DbGetFileNameInfo(tempPath, DB_MAX_GMRULE_FILE_PATH_LEN, DB_FILENAME_PREFIX);
    if (prefix == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_FILE_OPERATE_FAILED,
            "get fileName prefix from %s unsucc. ret = %" PRIi32, tempPath, (int32_t)GMERR_FILE_OPERATE_FAILED);
        return GMERR_FILE_OPERATE_FAILED;
    }
    // 文件名首字母必须是字母开头，并且除首字母外的其他字符只支持大小写字母、数字、_等字符
    if (!GmruleIsFilePrefixValid(prefix)) {
        // "invalid file name: %s, "
        // "The file name must start with a letter and contain only upper or lower case letters and \"_\", ret = "
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_INVALID_NAME,
            "inv file name prefix: %s, "
            "ret = "
            "%" PRIi32,
            filePath, (int32_t)GMERR_INVALID_NAME);
        return GMERR_INVALID_NAME;
    }
    return GMERR_OK;
}

const char *GmruleGetStringValueByKey(const DbJsonT *json, const char *key, bool needLog)
{
    DB_POINTER2(json, key);
    DbJsonT *obj = DbJsonObjectGet(json, key);
    if (obj == NULL) {
        if (needLog) {
            TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_JSON_CONTENT, "field \"%s\" inv in json file.", key);
        }
        return NULL;
    }
    const char *result = DbJsonStringValue(obj);
    if (result == NULL) {
        if (needLog) {
            TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_JSON_CONTENT, "field \"%s\" non-str type.", key);
        }
    }
    return result;
}

inline static bool GmruleIsInvalidUint16Value(DbJsonT *obj)
{
    return !DbJsonIsInteger(obj) || DbJsonIntegerValue(obj) >= DB_INVALID_ID16 || DbJsonIntegerValue(obj) < 0;
}

uint16_t GmruleGetUint16ValueByKey(const DbJsonT *json, const char *key)
{
    DB_POINTER2(json, key);
    DbJsonT *obj = DbJsonObjectGet(json, key);
    if (obj == NULL) {
        return 0;
    }
    if (GmruleIsInvalidUint16Value(obj)) {
        return DB_INVALID_ID16;
    }
    return (uint16_t)DbJsonIntegerValue(obj);
}

const DbJsonT *GmruleGetJsonStringOrArray(const DbJsonT *json, const char *key)
{
    DB_POINTER2(json, key);
    DbJsonT *obj = DbJsonObjectGet(json, key);
    if (obj == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_JSON_CONTENT, "field \"%s\" is inv in json file.", key);
        return NULL;
    }
    if (!DbJsonIsString(obj) && !DbJsonIsArray(obj)) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_JSON_CONTENT, "field \"%s\" not str or arr.", key);
        return NULL;
    }
    if (DbJsonIsArray(obj)) {
        uint32_t arraySize = (uint32_t)DbJsonGetArraySize(obj);
        if (arraySize == 0) {
            TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_JSON_CONTENT, "field \"%s\" is empty arr.", key);
            return NULL;
        }
        // 检查每个元素是否均为string类型
        for (uint32_t i = 0; i < arraySize; ++i) {
            const DbJsonT *str = DbJsonArrayGet(obj, (size_t)i);
            if (str == NULL || !DbJsonIsString(str)) {
                TOOL_RUN_ERROR(DbPrintfDefault, GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED,
                    "the %s[%" PRIu32 "] non-str type.", key, i);
                return NULL;
            }
        }
    }
    return obj;
}

Status GmruleCheckJsonUsersList(const DbJsonT *usersOrGrpList, const char *arrayName, const char *tupleName)
{
    if (!DbJsonIsArray(usersOrGrpList)) {
        TOOL_RUN_ERROR(
            DbPrintfDefault, (int32_t)GMERR_INVALID_JSON_CONTENT, "field \"%s\" is non-arr type.", arrayName);
        return GMERR_INVALID_JSON_CONTENT;
    }
    uint32_t size = (uint32_t)DbJsonGetArraySize(usersOrGrpList);
    if (size == 0) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_INVALID_JSON_CONTENT, "field \"%s\" is empty.", arrayName);
        return GMERR_INVALID_JSON_CONTENT;
    }
    for (uint32_t i = 0; i < size; ++i) {
        const DbJsonT *tuple = DbJsonArrayGet(usersOrGrpList, (size_t)i);
        if (tuple == NULL) {
            TOOL_RUN_ERROR(DbPrintfDefault, GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED,
                "get obj from array at index[%" PRIu32 "] unsucc", i);
            return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
        }
        const DbJsonT *userName = DbJsonObjectGet(tuple, tupleName);
        if (userName == NULL || !DbJsonIsString(userName)) {
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_INVALID_JSON_CONTENT,
                "field \"%s\" of %s[%" PRIu32 "] non-str type.", tupleName, arrayName, i);
            return GMERR_INVALID_JSON_CONTENT;
        }
        const DbJsonT *processName = DbJsonObjectGet(tuple, FIELD_PROCESS_NAME);
#ifdef EXPERIMENTAL_NERGC
        if (processName != NULL) {
            // NERGC场景下不支持process字段
            TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_JSON_CONTENT, "field \"%s\" not surported in NERGC.",
                FIELD_PROCESS_NAME);
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
#else
        if (processName == NULL || !DbJsonIsString(processName)) {
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_INVALID_JSON_CONTENT,
                "filed \"%s\" of %s[%" PRIu32 "] non-str type.", FIELD_PROCESS_NAME, arrayName, i);
            return GMERR_INVALID_JSON_CONTENT;
        }
#endif
    }
    return GMERR_OK;
}

Status GmruleLoadJson(char *filePath, uint32_t maxFilePathSize, DbJsonT **json)
{
    DB_POINTER2(filePath, json);
    Status ret = GmruleCheckFileNameValidity(filePath);
    if (ret != GMERR_OK) {
        return ret;
    }
    // format and check permission on this file
    ret = TlsFormatAndCheckFileName(filePath, maxFilePathSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    // check file size
    size_t fileSize;
    ret = DbFileSize(filePath, &fileSize);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_FILE_OPERATE_FAILED,
            "get file size of %s unsucc. ret = %" PRIi32, filePath, (int32_t)GMERR_FILE_OPERATE_FAILED);
        return GMERR_FILE_OPERATE_FAILED;
    }
    if (fileSize > MAX_FILE_SIZE) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_INSUFFICIENT_RESOURCES, "file %s is too large. ret = %" PRIi32,
            filePath, (int32_t)GMERR_INSUFFICIENT_RESOURCES);
        return GMERR_INSUFFICIENT_RESOURCES;
    }
    // load json
    *json = DbLoadJsonFileCltWithHeader(filePath, DB_JSON_REJECT_DUPLICATES);
    if (*json == NULL) {
        // "%s is not a standard json file, os ret no: %"
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_JSON_CONTENT, "%s nonstandard json file, ret: %" PRId32, filePath,
            DbAptGetErrno());
        return GMERR_INVALID_JSON_CONTENT;
    }
    return GMERR_OK;
}

Status GmruleParseUser(const DbJsonT *json, const char **processName, const char **userName, uint16_t *connNum)
{
    DB_POINTER3(json, processName, userName);
    const DbJsonT *user = DbJsonObjectGet(json, FIELD_USER_NAME);
    if (user == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_JSON_CONTENT, "field \"%s\" is inv.", FIELD_USER_NAME);
        return GMERR_INVALID_JSON_CONTENT;
    }
    *userName = DbJsonStringValue(user);
    if (*userName == NULL) {
        return GMERR_INVALID_JSON_CONTENT;
    }
    const DbJsonT *process = DbJsonObjectGet(json, FIELD_PROCESS_NAME);
#ifdef EXPERIMENTAL_NERGC
    if (process != NULL) {
        // NERGC场景下不支持process字段
        TOOL_RUN_ERROR(
            DbPrintfDefault, GMERR_INVALID_JSON_CONTENT, "field \"%s\" not surported in NERGC.", FIELD_PROCESS_NAME);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    *processName = "dummyProcess";
#else
    if (process == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_JSON_CONTENT, "field \"%s\" is inv.", FIELD_PROCESS_NAME);
        return GMERR_INVALID_JSON_CONTENT;
    }
    *processName = DbJsonStringValue(process);
    if (*processName == NULL) {
        return GMERR_INVALID_JSON_CONTENT;
    }
#endif
    if (connNum != NULL) {
        uint16_t reservedConnNum = GmruleGetUint16ValueByKey(json, FIELD_RESERVED_CONN_NUM);
        if (reservedConnNum > MAX_RESERVED_CONN_NUM_PER_USER || reservedConnNum == DB_INVALID_ID16) {
            return GMERR_INVALID_JSON_CONTENT;
        }
        *connNum = reservedConnNum;
    }
    return GMERR_OK;
}

Status GmruleParseMetaDataMaxNum(const DbJsonT *json, uint16_t metadataMaxNumArray[USER_METADATA_FILED_NUM])
{
    // 解析元数据，每个user按照以下顺序读取
    // normal_vertexlabel_max
    // yang_vertexlabel_max
    // kv_label_max
    // subscription_max
    // resource_max
    // namespace_max
    // tablespace_max
    // edge_max
    // udf_max
    for (uint32_t i = 0; i < USER_METADATA_FILED_NUM; i++) {
        DbJsonT *metaDataVal = DbJsonObjectGet(json, g_allowListMetadataFiledList[i]);
        if (metaDataVal == NULL) {
            // 字段为空，取默认值 GMRULE_USER_METADATA_MAX_VALUE
            metadataMaxNumArray[i] = GMRULE_USER_METADATA_MAX_VALUE;
        } else {
            // 字段不为空，校验允许取值的范围 [0, 65535]
            if (!DbJsonIsInteger(metaDataVal) || DbJsonIntegerValue(metaDataVal) > GMRULE_USER_METADATA_MAX_VALUE ||
                DbJsonIntegerValue(metaDataVal) < 0) {
                return GMERR_INVALID_JSON_CONTENT;
            }
            metadataMaxNumArray[i] = (uint16_t)DbJsonIntegerValue(metaDataVal);
        }
    }
    return GMERR_OK;
}

Status GmruleParseUserOnCreation(const DbJsonT *json, GmcUsersCreateInfoT *usersInfo, uint32_t index)
{
    DB_POINTER2(json, usersInfo);
    Status ret = GmruleParseUser(
        json, &usersInfo->processNames[index], &usersInfo->userNames[index], &usersInfo->reservedConnNumArray[index]);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GmruleParseMetaDataMaxNum(json, usersInfo->userMetadataMaxNumArray[index]);
}

Status GmruleParseGroup(const DbJsonT *json, const char **processName, const char **groupName)
{
    DB_POINTER3(json, processName, groupName);
    const DbJsonT *process = DbJsonObjectGet(json, FIELD_PROCESS_NAME);
    if (process == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_JSON_CONTENT, "field \"%s\" is inv.", FIELD_PROCESS_NAME);
        return GMERR_INVALID_JSON_CONTENT;
    }
    *processName = DbJsonStringValue(process);
    if (*processName == NULL) {
        return GMERR_INVALID_JSON_CONTENT;
    }
    const DbJsonT *role = DbJsonObjectGet(json, FIELD_GROUP_NAME);
    if (role == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_JSON_CONTENT, "field \"%s\" is inv.", FIELD_GROUP_NAME);
        return GMERR_INVALID_JSON_CONTENT;
    }
    *groupName = DbJsonStringValue(role);
    if (*groupName == NULL) {
        return GMERR_INVALID_JSON_CONTENT;
    }
    return GMERR_OK;
}

Status GmruleParseGroupOnCreation(const DbJsonT *json, GmcGroupCreateInfoT *groupInfo)
{
    DB_POINTER2(json, groupInfo);
    Status ret = GmruleParseGroup(json, &groupInfo->processName, &groupInfo->groupName);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GmruleParseMetaDataMaxNum(json, groupInfo->groupMetadataMaxNumArray);
}

bool GmruleCompareUserTuple(const DbJsonT *first, const DbJsonT *second)
{
    DB_POINTER2(first, second);
#ifdef EXPERIMENTAL_NERGC
    // NERGC场景下不支持process字段, 仅需校验user字段
    const char *userNameFirst = GmruleGetStringValueByKey(first, FIELD_USER_NAME, false);
    if (userNameFirst == NULL) {
        return false;
    }

    const char *userNameSecond = GmruleGetStringValueByKey(second, FIELD_USER_NAME, false);
    if (userNameSecond == NULL) {
        return false;
    }

    if (strcmp(userNameFirst, userNameSecond) == 0) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_DUPLICATE_OBJECT, "dup user tup found, user: %s, ret = %" PRIi32,
            userNameFirst, (int32_t)GMERR_DUPLICATE_OBJECT);
        return true;
    }
#else
    const char *userNameFirst = GmruleGetStringValueByKey(first, FIELD_USER_NAME, false);
    if (userNameFirst == NULL) {
        return false;
    }

    const char *processNameFirst = GmruleGetStringValueByKey(first, FIELD_PROCESS_NAME, false);
    if (processNameFirst == NULL) {
        return false;
    }

    const char *userNameSecond = GmruleGetStringValueByKey(second, FIELD_USER_NAME, false);
    if (userNameSecond == NULL) {
        return false;
    }

    const char *processNameSecond = GmruleGetStringValueByKey(second, FIELD_PROCESS_NAME, false);
    if (processNameSecond == NULL) {
        return false;
    }

    if (strcmp(userNameFirst, userNameSecond) == 0 && strcmp(processNameFirst, processNameSecond) == 0) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_DUPLICATE_OBJECT,
            "dup user tup found, user: %s, process: %s. ret = %" PRIi32, userNameFirst, processNameFirst,
            (int32_t)GMERR_DUPLICATE_OBJECT);
        return true;
    }
#endif
    return false;
}

bool GmruleCompareGroupTuple(const DbJsonT *first, const DbJsonT *second)
{
    DB_POINTER2(first, second);
#ifdef EXPERIMENTAL_NERGC
    // NERGC场景下不支持group字段和process字段
    return false;
#endif
    const char *groupNameFirst = GmruleGetStringValueByKey(first, FIELD_GROUP_NAME, false);
    if (groupNameFirst == NULL) {
        return false;
    }

    const char *processNameFirst = GmruleGetStringValueByKey(first, FIELD_PROCESS_NAME, false);
    if (processNameFirst == NULL) {
        return false;
    }

    const char *groupNameSecond = GmruleGetStringValueByKey(second, FIELD_GROUP_NAME, false);
    if (groupNameSecond == NULL) {
        return false;
    }

    const char *processNameSecond = GmruleGetStringValueByKey(second, FIELD_PROCESS_NAME, false);
    if (processNameSecond == NULL) {
        return false;
    }

    if (strcmp(groupNameFirst, groupNameSecond) == 0 && strcmp(processNameFirst, processNameSecond) == 0) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_DUPLICATE_OBJECT,
            "dup role tup found, group: %s, process: %s. ret = %" PRIi32, groupNameFirst, processNameFirst,
            (int32_t)GMERR_DUPLICATE_OBJECT);
        return true;
    }
    return false;
}

bool GmruleCompareJsonString(const DbJsonT *first, const DbJsonT *second)
{
    DB_POINTER2(first, second);
    const char *strFirst = DbJsonStringValue(first);
    if (strFirst == NULL) {
        return false;
    }
    const char *strSecond = DbJsonStringValue(second);
    if (strSecond == NULL) {
        return false;
    }
    if (strcmp(strFirst, strSecond) == 0) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_DUPLICATE_OBJECT, "dup str found, str: %s. ret = %" PRIi32,
            strFirst, (int32_t)GMERR_DUPLICATE_OBJECT);
        return true;
    }
    return false;
}

bool GmruleCheckIsDuplicateBetweenEachOther(const DbJsonT *usersListFirst, const DbJsonT *usersListSecond)
{
    // 检查两两之间有没有重复
    for (uint32_t i = 0; i < (uint32_t)DbJsonGetArraySize(usersListFirst); ++i) {
        const DbJsonT *userFirst = DbJsonArrayGet(usersListFirst, (size_t)i);
        if (userFirst == NULL) {
            TOOL_RUN_ERROR(DbPrintfDefault, GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED,
                "get obj from arr at index[%" PRIu32 "] unsucc", i);
            return false;
        }
        for (uint32_t j = 0; j < (uint32_t)DbJsonGetArraySize(usersListSecond); ++j) {
            const DbJsonT *userSecond = DbJsonArrayGet(usersListSecond, (size_t)j);
            if (userSecond == NULL) {
                TOOL_RUN_ERROR(DbPrintfDefault, GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED,
                    "get obj from arr at index[%" PRIu32 "] unsucc", i);
                return false;
            }
            if (GmruleCompareUserTuple(userFirst, userSecond)) {
                return true;
            }
        }
    }
    return false;
}

bool GmruleCompareUserTupleList(const DbJsonT *first, const DbJsonT *second)
{
    DB_POINTER2(first, second);
    // 检查first json对象自身有没有user tuple重复
    const DbJsonT *usersListFirst = DbJsonObjectGet(first, FILED_USERS);
    if (usersListFirst == NULL || !DbJsonIsArray(usersListFirst)) {
        return false;
    }
    Status ret = GmruleCheckDuplicate(usersListFirst, GmruleCompareUserTuple);
    if (ret == GMERR_DUPLICATE_OBJECT) {
        return true;
    } else if (ret != GMERR_OK) {
        return false;
    }

    // 检查second json对象自身有没有user tuple重复
    const DbJsonT *usersListSecond = DbJsonObjectGet(second, FILED_USERS);
    if (usersListSecond == NULL || !DbJsonIsArray(usersListSecond)) {
        return false;
    }
    ret = GmruleCheckDuplicate(usersListSecond, GmruleCompareUserTuple);
    if (ret == GMERR_DUPLICATE_OBJECT) {
        return true;
    } else if (ret != GMERR_OK) {
        return false;
    }

    return GmruleCheckIsDuplicateBetweenEachOther(usersListFirst, usersListSecond);
}

Status GmruleCheckDuplicate(const DbJsonT *tupleList, const GmruleTupleCompare compFunc)
{
    DB_POINTER2(tupleList, compFunc);
    int64_t listCnt = (int64_t)DbJsonGetArraySize(tupleList);
    for (int64_t index = 0; index < listCnt - 1; ++index) {
        const DbJsonT *first = DbJsonArrayGet(tupleList, (size_t)index);
        if (first == NULL) {
            TOOL_RUN_ERROR(DbPrintfDefault, GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED,
                "get obj from arr at index[%" PRIi64 "] unsucc", index);
            return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
        }

        for (int64_t j = index + 1; j < listCnt; ++j) {
            const DbJsonT *second = DbJsonArrayGet(tupleList, (size_t)j);
            if (second == NULL) {
                TOOL_RUN_ERROR(DbPrintfDefault, GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED,
                    "get obj from arr at index[%" PRIi64 "] unsucc", j);
                return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
            }

            bool hasDuplicate = compFunc(first, second);
            if (hasDuplicate) {
                TOOL_RUN_INFO(DbPrintfDefault, "[INFO] get duplicate obj");
                return GMERR_DUPLICATE_OBJECT;
            }
        }
    }
    return GMERR_OK;
}

void GmruleInitRunCtx(GmRuleCtxT *ctx, GmcStmtT *stmt, GmRuleImpTypeE opType)
{
    DB_POINTER2(ctx, stmt);
    ctx->stmt = stmt;
    ctx->totalFileCnt = 0;
    ctx->importFileCnt = 0;
    ctx->opType = opType;
    if (opType == GMRULE_CMD_IMPORT_ALLOWLIST || opType == GMRULE_CMD_REMOVE_ALLOWLIST) {
        ctx->importFunc = GmruleImportAllowListFile;
        ctx->fileSufix = ALLOWLIST_SUFFIX;
    } else {
        ctx->importFunc = GmruleImportPolicyFile;
        ctx->fileSufix = POLICY_SUFFIX;
    }
}

Status GmruleImportMutipleFile(GmRuleCtxT *ctx, GmRuleOptionT *option, char *filePath, uint32_t recurDepth)
{
    DB_POINTER5(ctx, ctx->stmt, ctx->importFunc, ctx->fileSufix, filePath);
    if (recurDepth > MAX_RECURSION_DEPTH) {
        // "The file path is nested too deeply. ret = %"
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_OUT_OF_MEMORY, "Deep file path. ret = %" PRIi32,
            (int32_t)GMERR_OUT_OF_MEMORY);
        return GMERR_OUT_OF_MEMORY;
    }
    if (DbFileExist(filePath)) {
        if (!TlsCheckFileSuffix(filePath, ctx->fileSufix)) {
            return GMERR_OK;
        }
        return ctx->importFunc(ctx, option, filePath);
    }
    if (!DbDirExist(filePath)) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_DIRECTORY_OPERATE_FAILED,
            "no such directory: %s, ret = %" PRIi32, filePath, (int32_t)GMERR_DIRECTORY_OPERATE_FAILED);
        return GMERR_DIRECTORY_OPERATE_FAILED;
    }
    DIR *dir = opendir(filePath);
    if (dir == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_DIRECTORY_OPERATE_FAILED,
            "open directory: %s unsucc, ret = %" PRIi32 ", errno = %" PRIi32, filePath,
            (int32_t)GMERR_DIRECTORY_OPERATE_FAILED, DbAptGetErrno());
        return GMERR_DIRECTORY_OPERATE_FAILED;
    }

    Status ret = GMERR_OK;
    while (true) {
        struct dirent *curDir = readdir(dir);
        if (curDir == NULL) {
            break;
        }
        if (strcmp(curDir->d_name, ".") == 0 || strcmp(curDir->d_name, "..") == 0) {
            continue;
        }
        size_t count = strlen(filePath) + strlen(curDir->d_name) + 1;
        char tmpPath[DB_MAX_GMRULE_FILE_PATH_LEN] = {0};
        int32_t tmp = snprintf_s(tmpPath, sizeof(tmpPath), count, "%s/%s", filePath, curDir->d_name);
        if (tmp < 0) {
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_FIELD_OVERFLOW,
                "snprintf_s unsucc, long filePath:%s, len: %zu, ret = %" PRIi32, curDir->d_name, count,
                (int32_t)GMERR_FIELD_OVERFLOW);
            continue;
        }
        ret = GmruleImportMutipleFile(ctx, option, tmpPath, recurDepth + 1);
        // 如果失败了会有打屏信息，不立即中断退出，与V3保持一致
    }
    if (closedir(dir) < 0) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_FILE_OPERATE_FAILED, " close dir unsucc.");
    }
    return ret;
}

#ifdef __cplusplus
}
#endif
