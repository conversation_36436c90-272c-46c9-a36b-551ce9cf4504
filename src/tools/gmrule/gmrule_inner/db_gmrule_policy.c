/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: header file for importing policy
 * Author: <PERSON><PERSON><PERSON><PERSON><PERSON>an
 * Create: 2021-10-20
 */
#include "db_gmrule_policy.h"
#include "db_gmrule_utils.h"
#include "gmc_privilege.h"
#include "clt_stmt.h"
#include "gmc_graph.h"
#include "gmc.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

// note：以下对象类型并非是新增的对象类型，而是VERTEX_LABEL对象和KV_TABLE对象的组合
#define GMC_VERTEX_OR_KV_TABLE ((uint32_t)GMC_OBJ_TYPE_NUM + 1)
#define GMC_VERTEX_KV_TABLE_NOT_EXIST ((uint32_t)GMC_OBJ_TYPE_NUM + 2)

// clang-format off
// 对象枚举类型与字符串的映射表.
// 通过编码赋值, 无并发初始化, 初始化之后只读，不涉及并发读写
// 注意: 这里的字符串已经进行正向排序，当需要添加新的对象类型时请按照字符串正向排序方式添加.
static const GmGmRuleStrDataMap g_gmdbObjectTypeMap[] = {
    {GMC_BINARY_FILE,        "BINARY_FILE"},
    {GMC_DATALOG_UDF,        "DATALOG_UDF"},
    {GMC_EDGE_LABEL,         "EDGE_LABEL"},
    {GMC_KV_TABLE,           "KV_TABLE"},
    {GMC_VERTEX_OR_KV_TABLE, "KV_TABLE|VERTEX_LABEL"},
    {GMC_NAMESPACE,          "NAMESPACE"},
    {GMC_PATH_SUBS,          "PATH_SUBS"},
    {GMC_RESOURCE,           "RESOURCE"},
    {GMC_ROLE,               "ROLE"},
    {GMC_TABLESPACE,          "TABLESPACE"},
    {GMC_USER,               "USER"},
    {GMC_VERTEX_LABEL,       "VERTEX_LABEL"},
    {GMC_VERTEX_OR_KV_TABLE, "VERTEX_LABEL|KV_TABLE"}  // 当VERTEX_LABEL对象不存在时将其当做KV_TABLE对象
};

// 对象权限枚举类型与字符串的映射表.
// 通过编码赋值, 无并发初始化, 初始化之后只读，不涉及并发读写
// 注意: 这里的字符串已经进行正向排序，当需要添加新的对象权限类型时请按照字符串正向排序方式添加.
static const GmGmRuleStrDataMap g_gmdbObjectPrivsMap[] = {
    {GMC_OBJ_ALTER_IN_NSP_PRIV,    "ALTER_IN_NSP"},
    {GMC_OBJ_CREATE_IN_NSP_PRIV,   "CREATE_IN_NSP"},
    {GMC_OBJ_DELETE_PRIV,          "DELETE"},
    {GMC_OBJ_DROP_IN_NSP_PRIV,     "DROP_IN_NSP"},
    {GMC_OBJ_GET_IN_NSP_PRIV,      "GET_IN_NSP"},
    {GMC_OBJ_INSERT_PRIV,          "INSERT"},
    {GMC_OBJ_INVOKE_PRIV,         "INVOKE"},
    {GMC_OBJ_MERGE_PRIV,           "MERGE"},
    {GMC_OBJ_REPLACE_PRIV,         "REPLACE"},
    {GMC_OBJ_SELECT_PRIV,          "SELECT"},
    {GMC_OBJ_TRUNCATE_IN_NSP_PRIV, "TRUNCATE_IN_NSP"},
    {GMC_OBJ_UPDATE_PRIV,          "UPDATE"},
    {GMC_OBJ_USE_NSP_PRIV,         "USE_NSP"},
    {GMC_OBJ_VALIDATION,          "VALIDATION"}
};

// 系统权限枚举类型与字符串的映射表.
// 通过编码赋值, 无并发初始化, 初始化之后只读，不涉及并发读写
// 注意: 这里的字符串已经进行正向排序，当需要添加新的系统权限类型时请按照字符串正向排序方式添加.
static const GmGmRuleStrDataMap g_gmdbSystemPrivsMap[] = {
    {GMC_ALTER_PRIV,            "ALTER"},
    {GMC_BIND_PRIV,             "BIND"},
    {GMC_CLOSE_PRIV,           "CLOSE"},
    {GMC_CREATE_PRIV,           "CREATE"},
    {GMC_DELETEANY_PRIV,        "DELETE_ANY"},
    {GMC_DROP_PRIV,             "DROP"},
    {GMC_GET_PRIV,              "GET"},
    {GMC_GRANT_PRIV,            "GRANT"},
    {GMC_INSERTANY_PRIV,        "INSERT_ANY"},
    {GMC_INVOKEANY_PRIV,        "INVOKE_ANY"},
    {GMC_MERGEANY_PRIV,         "MERGE_ANY"},
    {GMC_OPEN_PRIV,             "OPEN"},
    {GMC_REPLACEANY_PRIV,       "REPLACE_ANY"},
    {GMC_REVOKE_PRIV,           "REVOKE"},
    {GMC_SELECTANY_PRIV,        "SELECT_ANY"},
    {GMC_TRUNCATE_PRIV,         "TRUNCATE"},
    {GMC_UNBIND_PRIV,           "UNBIND"},
    {GMC_UPDATEANY_PRIV,        "UPDATE_ANY"},
    {GMC_USE_PRIV,              "USE"}
};
// clang-format on

int GmruleCmpString(const void *left, const void *right)
{
    return strcmp(((const GmGmRuleStrDataMap *)left)->strType, ((const GmGmRuleStrDataMap *)right)->strType);
}

const GmGmRuleStrDataMap *GmruleParseStrType(
    const char *strType, const GmGmRuleStrDataMap *typeMap, uint32_t elementCount)
{
    DB_POINTER2(strType, typeMap);
    GmGmRuleStrDataMap ref = {.dataType = 0, .strType = strType};
    return (GmGmRuleStrDataMap *)bsearch(&ref, typeMap, elementCount, sizeof(GmGmRuleStrDataMap), GmruleCmpString);
}

Status GmruleParseSystemPrivsType(const DbJsonT *json, uint32_t *systemPrivsType)
{
    DB_POINTER2(json, systemPrivsType);
    const char *privsStrType = DbJsonStringValue(json);
    if (privsStrType == NULL) {
        // "Non-string type mistake, invalid system priv type."
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_JSON_CONTENT, "Inv sys priv type.");
        return GMERR_INVALID_JSON_CONTENT;
    }

    const GmGmRuleStrDataMap *result =
        GmruleParseStrType(privsStrType, g_gmdbSystemPrivsMap, ELEMENT_COUNT(g_gmdbSystemPrivsMap));
    if (result == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_INVALID_JSON_CONTENT,
            "No matched system priv type for \"%s\". ret = %" PRIi32 "", privsStrType,
            (int32_t)GMERR_INVALID_JSON_CONTENT);
        return GMERR_INVALID_JSON_CONTENT;
    }
    *systemPrivsType = result->dataType;
    return GMERR_OK;
}

Status GmruleParseObjectType(const DbJsonT *objCfg, uint32_t *objType)
{
    DB_POINTER2(objCfg, objType);
    const char *objTypeStr = GmruleGetStringValueByKey(objCfg, "obj_type", true);
    if (objTypeStr == NULL) {
        return GMERR_INVALID_JSON_CONTENT;
    }
    const GmGmRuleStrDataMap *result =
        GmruleParseStrType(objTypeStr, g_gmdbObjectTypeMap, ELEMENT_COUNT(g_gmdbObjectTypeMap));
    if (result == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_INVALID_JSON_CONTENT,
            "no matched object type for \"%s\". ret = %" PRIi32 "", objTypeStr, (int32_t)GMERR_INVALID_JSON_CONTENT);
        return GMERR_INVALID_JSON_CONTENT;
    }
    *objType = result->dataType;
    return GMERR_OK;
}

Status GmruleParseObjectPrivsType(const DbJsonT *objCfg, uint32_t *privsVal)
{
    DB_POINTER2(objCfg, privsVal);
    const char *objPrivsType = DbJsonStringValue(objCfg);
    if (objPrivsType == NULL) {
        TOOL_RUN_ERROR(
            // "Non-string type mistake, invalid object priv type."
            DbPrintfDefault, GMERR_INVALID_JSON_CONTENT, "inv obj priv type.");
        return GMERR_INVALID_JSON_CONTENT;
    }
    const GmGmRuleStrDataMap *match =
        GmruleParseStrType(objPrivsType, g_gmdbObjectPrivsMap, ELEMENT_COUNT(g_gmdbObjectPrivsMap));
    if (match == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_INVALID_JSON_CONTENT,
            "No matched object priv type for \"%s\". ret = %" PRIi32 "", objPrivsType,
            (int32_t)GMERR_INVALID_JSON_CONTENT);
        return GMERR_INVALID_JSON_CONTENT;
    }
    *privsVal = match->dataType;
    return GMERR_OK;
}

Status GmruleAllocLabelTypeBuffer(PrivilegeOperationCtxT *ctx, uint32_t count)
{
    DB_POINTER2(ctx, ctx->stmt);
    // 申请的内存，最终在GmruleFreeLableTypeBuffer中释放
    uint32_t *types = DbDynMemCtxAlloc(ctx->stmt->memCtx, sizeof(uint32_t) * count);
    if (types == NULL) {
        // "alloc label type buffer unsuccessful."
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_OUT_OF_MEMORY, "alloc label type buffer.");
        return GMERR_OUT_OF_MEMORY;
    }
    ctx->labelType.count = count;
    ctx->labelType.type = types;
    return GMERR_OK;
}

void GmruleFreeLableTypeBuffer(PrivilegeOperationCtxT *ctx)
{
    DB_POINTER2(ctx, ctx->stmt);
    if (ctx->labelType.type != NULL) {
        DbDynMemCtxFree(ctx->stmt->memCtx, ctx->labelType.type);
        ctx->labelType.type = NULL;
        ctx->labelType.count = 0;
    }
}

Status GmruleSetObjPrivsRunCtx(PrivilegeOperationCtxT *runCtx, const DbJsonT *objCfg)
{
    DB_POINTER2(runCtx, objCfg);
    Status ret = GmruleParseObjectType(objCfg, &runCtx->objType);
    if (ret != GMERR_OK) {
        return ret;
    }
    runCtx->namespace = GmruleGetStringValueByKey(objCfg, "namespace", runCtx->objType != GMC_NAMESPACE);
    if (((runCtx->objType == GMC_NAMESPACE) && (runCtx->namespace != NULL)) ||
        ((runCtx->objType != GMC_NAMESPACE) && (runCtx->namespace == NULL))) {
        return GMERR_INVALID_JSON_CONTENT;
    }
    runCtx->objName = GmruleGetJsonStringOrArray(objCfg, "obj_name");
    if (runCtx->objName == NULL) {
        return GMERR_INVALID_JSON_CONTENT;
    }
    // 检查对象名称是否存在重复的字符串
    if (DbJsonIsArray(runCtx->objName)) {
        ret = GmruleCheckDuplicate(runCtx->objName, GmruleCompareJsonString);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status GmruleCheckVertexLabelExist(PrivilegeOperationCtxT *runCtx, const char *objName, bool showDetail, bool *res)
{
    DB_POINTER2(runCtx, objName);
    bool isExist = true;
    Status ret;
    do {
        ret = GmcUseNamespace(runCtx->stmt, runCtx->namespace);
        if (ret != GMERR_OK) {
            isExist = false;
            break;
        }
        runCtx->stmt->isTools = true;
        ret = GmcPrepareStmtByLabelName(runCtx->stmt, objName, GMC_OPERATION_SCAN);
        runCtx->stmt->isTools = false;
        if (ret != GMERR_OK) {
            isExist = false;
        }
    } while (0);
    if (!isExist) {
        if (ret == GMERR_UNDEFINED_TABLE || ret == GMERR_UNDEFINED_OBJECT) {
            if (showDetail) {
                TOOL_ERROR_INFO(DbPrintfDefault, (int32_t)ret, "vertexlabel [%s] is not found at namespace [%s].",
                    objName, runCtx->namespace);
            }
            runCtx->warningCnt++;
        } else if (showDetail) {
            TOOL_ERROR_INFO(DbPrintfDefault, (int32_t)ret, "useNameSpace or prepare vertexlabel unsuccessfully.");
        }
    }
    ret = (ret == GMERR_UNDEFINED_TABLE || ret == GMERR_UNDEFINED_OBJECT) ? GMERR_OK : ret;  // 兼容V3，这种情况不报错
    Status tmpRet = GmcUseNamespace(runCtx->stmt, DB_DEFAULT_NAMESPACE);
    if (tmpRet != GMERR_OK) {
        if (showDetail) {
            TOOL_ERROR_INFO(DbPrintfDefault, (int32_t)tmpRet, "use default NameSpace unsuccessfully.");
        }
        isExist = false;
    }
    runCtx->objType = isExist ? GMC_VERTEX_LABEL : GMC_VERTEX_KV_TABLE_NOT_EXIST;
    *res = isExist;
    return (ret == GMERR_OK) ? tmpRet : ret;
}

#ifdef FEATURE_KV
Status GmruleCheckKvTableExist(PrivilegeOperationCtxT *runCtx, const char *objName, bool showDetail, bool *res)
{
    DB_POINTER2(runCtx, objName);
    Status ret;
    bool isExist = true;
    do {
        ret = GmcUseNamespace(runCtx->stmt, runCtx->namespace);
        if (ret != GMERR_OK) {
            isExist = false;
            break;
        }
        ret = GmcKvPrepareStmtByLabelName(runCtx->stmt, objName);
        if (ret != GMERR_OK) {
            isExist = false;
        }
    } while (0);
    if (!isExist) {
        if (ret == GMERR_UNDEFINED_TABLE || ret == GMERR_UNDEFINED_OBJECT) {
            if (showDetail) {
                TOOL_ERROR_INFO(DbPrintfDefault, (int32_t)ret, "kvtable [%s] is not found at namespace [%s].", objName,
                    runCtx->namespace);
            }
            runCtx->warningCnt++;
        } else if (showDetail) {
            TOOL_ERROR_INFO(DbPrintfDefault, (int32_t)ret, "useNameSpace or prepare kvtable unsuccessfully.");
        }
    }
    ret = (ret == GMERR_UNDEFINED_TABLE || ret == GMERR_UNDEFINED_OBJECT) ? GMERR_OK : ret;  // 兼容V3，这种情况不报错
    Status tmpRet = GmcUseNamespace(runCtx->stmt, DB_DEFAULT_NAMESPACE);
    if (tmpRet != GMERR_OK) {
        if (showDetail) {
            TOOL_ERROR_INFO(DbPrintfDefault, (int32_t)tmpRet, "use default NameSpace unsuccessfully.");
        }
        isExist = false;
    }
    runCtx->objType = isExist ? GMC_KV_TABLE : GMC_VERTEX_KV_TABLE_NOT_EXIST;
    *res = isExist;
    return (ret == GMERR_OK) ? tmpRet : ret;
}

Status GmruleCheckVertexOrKvTableExist(
    PrivilegeOperationCtxT *runCtx, const char *objName, bool showDetail, bool *isExist)
{
    DB_POINTER2(runCtx, objName);
    // 此处的第3个参数固定传false，只有vertexLabel和kvTable都不存在，才在最后打印一个日志
    Status ret = GmruleCheckVertexLabelExist(runCtx, objName, false, isExist);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (*isExist) {
        runCtx->objType = GMC_VERTEX_LABEL;
        return GMERR_OK;
    }
    // 此处的第3个参数固定传false，只有vertexLabel和kvTable都不存在，才在最后打印一个日志
    ret = GmruleCheckKvTableExist(runCtx, objName, false, isExist);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (*isExist) {
        runCtx->objType = GMC_KV_TABLE;
        return GMERR_OK;
    }
    runCtx->warningCnt++;
    if (showDetail) {
        TOOL_ERROR_INFO(DbPrintfDefault, (int32_t)GMERR_INVALID_OBJECT_DEFINITION,
            "object %s is neither vertex label nor kv table", objName);
    }
    runCtx->objType = GMC_VERTEX_KV_TABLE_NOT_EXIST;
    return GMERR_OK;  // 兼容V3，这种情况不报错
}
#endif

Status GmruleCheckObjectExist(PrivilegeOperationCtxT *runCtx, const char *objName, bool showDetail, bool *isExist)
{
    DB_POINTER2(runCtx, objName);
    switch (runCtx->objType) {
        case GMC_VERTEX_LABEL:
            return GmruleCheckVertexLabelExist(runCtx, objName, showDetail, isExist);
#ifdef FEATURE_KV
        case GMC_KV_TABLE:
            return GmruleCheckKvTableExist(runCtx, objName, showDetail, isExist);
        case GMC_VERTEX_OR_KV_TABLE:
            return GmruleCheckVertexOrKvTableExist(runCtx, objName, showDetail, isExist);
#endif
        default:
            break;
    }
    *isExist = true;
    return GMERR_OK;
}

inline static bool GmruleIsLabelObjectOrUdf(uint32_t objType)
{
    return (objType == GMC_VERTEX_LABEL || objType == GMC_KV_TABLE || objType == GMC_VERTEX_OR_KV_TABLE ||
            objType == GMC_DATALOG_UDF);
}

Status GmruleCheckAllObjectExist(PrivilegeOperationCtxT *runCtx, bool *isExist, bool showDetail)
{
    DB_POINTER2(runCtx, isExist);
    uint32_t originObjType = runCtx->objType;
    bool tmpIsExist = false;
    // 批量配置表权限时，判断每个表是否存在
    for (uint32_t i = 0; i < runCtx->labelType.count; ++i) {
        const DbJsonT *objTmp = DbJsonArrayGet(runCtx->objName, (size_t)i);
        if (objTmp == NULL) {
            // "get obj from array at index[%" PRIu32 "] unsuccessful"
            TOOL_RUN_ERROR(
                DbPrintfDefault, (int32_t)GMERR_UNEXPECTED_NULL_VALUE, "get obj at index[%" PRIu32 "] unsucc", i);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        const char *objName = DbJsonStringValue(objTmp);
        if (objName == NULL) {
            // "get objName at index[%" PRIu32 "] unsuccessful."
            TOOL_RUN_ERROR(
                DbPrintfDefault, (int32_t)GMERR_UNEXPECTED_NULL_VALUE, "get objName at index[%" PRIu32 "] unsucc.", i);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        // 如果对象类型是复合对象类型，在下面check流程会将runCtx上objType更新为VERTEX_LABEL或者KV_TABLE
        Status ret = GmruleCheckObjectExist(runCtx, objName, showDetail, &tmpIsExist);
        if (ret != GMERR_OK) {
            *isExist = false;
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "check objName at index[%" PRIu32 "] unsucc.", i);
            return ret;
        }
        runCtx->labelType.type[i] = runCtx->objType;
        if (tmpIsExist) {
            *isExist = true;  // 任意一个存在，即最终结果为存在
        }
        runCtx->objType = originObjType;
    }
    return GMERR_OK;
}

Status GmruleCheckObjectExistAndMarkObjectType(PrivilegeOperationCtxT *runCtx, bool *isExist, bool showDetail)
{
    DB_POINTER2(runCtx, isExist);
    *isExist = false;
    if (DbJsonIsString(runCtx->objName)) {
        const char *objName = DbJsonStringValue(runCtx->objName);
        if (objName == NULL) {
            // "get object name unsuccessful."
            TOOL_RUN_ERROR(DbPrintfDefault, GMERR_UNEXPECTED_NULL_VALUE, "get obj name unsucc.");
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        // 不存在也不会报错，记一个warning
        return GmruleCheckObjectExist(runCtx, objName, showDetail, isExist);
    }

    // 只有vertex label、kv table和datalog udf对象类型才允许批量配置权限
    if (!GmruleIsLabelObjectOrUdf(runCtx->objType)) {
        // "the object can not be configured in batches, except for VERTEX_LABEL or KV_TABLE or DATALOG_UDF."
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_FEATURE_NOT_SUPPORTED, "inv obj configured in batches.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    uint32_t nums = (uint32_t)DbJsonGetArraySize(runCtx->objName);
    Status ret = GmruleAllocLabelTypeBuffer(runCtx, nums);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GmruleCheckAllObjectExist(runCtx, isExist, showDetail);
}

Status GmruleGetObjectPrivilege(const DbJsonT *privsJson, uint32_t *privsVal)
{
    DB_POINTER2(privsJson, privsVal);

    DbJsonT *privsArray = DbJsonObjectGet(privsJson, "privs_type");
    if (privsArray == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_INVALID_JSON_CONTENT,
            "field \"privs_type\" is invalid in \"object_privilege_config\". ret = %" PRIi32 "",
            (int32_t)GMERR_INVALID_JSON_CONTENT);
        return GMERR_INVALID_JSON_CONTENT;
    }
    int64_t privsArraySize = (int64_t)DbJsonGetArraySize(privsArray);
    if (privsArraySize == 0) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_INVALID_JSON_CONTENT,
            "field \"privs_type\" is empty array in \"object_privilege_config\". ret = %" PRIi32 "",
            (int32_t)GMERR_INVALID_JSON_CONTENT);
        return GMERR_INVALID_JSON_CONTENT;
    }
    // 检查权限字符串是否出现重复
    Status ret = GmruleCheckDuplicate(privsArray, GmruleCompareJsonString);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t privsType = 0;
    for (int64_t i = 0; i < privsArraySize; ++i) {
        const DbJsonT *privilege = DbJsonArrayGet(privsArray, (size_t)i);
        if (privilege == NULL) {
            // "get obj from array at index[%" PRIi64 "] unsuccessful"
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED,
                "get obj at index[%" PRIi64 "] unsucc", i);
            return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
        }
        uint32_t privsTmp = 0;
        ret = GmruleParseObjectPrivsType(privilege, &privsTmp);
        if (ret != GMERR_OK) {
            return ret;
        }
        privsType |= privsTmp;
    }
    *privsVal = privsType;
    return GMERR_OK;
}

Status GmruleGetSystemPrivilege(const DbJsonT *privsJson, uint32_t *privsVal)
{
    DB_POINTER2(privsJson, privsVal);

    DbJsonT *privilegeType = DbJsonObjectGet(privsJson, "privs_type");
    if (privilegeType == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_INVALID_JSON_CONTENT,
            "field \"privs_type\" is invalid in \"system_privilege_config\". ret = %" PRIi32 "",
            (int32_t)GMERR_INVALID_JSON_CONTENT);
        return GMERR_INVALID_JSON_CONTENT;
    }
    uint32_t arraySize = (uint32_t)DbJsonGetArraySize(privilegeType);
    if (arraySize == 0) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_INVALID_JSON_CONTENT,
            "field \"privs_type\" is empty array in \"system_privilege_config\". ret = %" PRIi32 "",
            (int32_t)GMERR_INVALID_JSON_CONTENT);
        return GMERR_INVALID_JSON_CONTENT;
    }

    // 检查系统权限字符串是否出现重复
    Status ret = GmruleCheckDuplicate(privilegeType, GmruleCompareJsonString);
    if (ret != GMERR_OK) {
        return ret;
    }

    uint32_t systemPrivsType = 0;
    for (uint32_t i = 0; i < arraySize; ++i) {
        const DbJsonT *sysPrivsJson = DbJsonArrayGet(privilegeType, (size_t)i);
        if (sysPrivsJson == NULL) {
            // "get obj from array at index[%" PRIu32 "] unsuccessful"
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED,
                "get obj at index[%" PRIu32 "] unsucc", i);
            return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
        }
        uint32_t privsType = 0;
        ret = GmruleParseSystemPrivsType(sysPrivsJson, &privsType);
        if (ret != GMERR_OK) {
            return ret;
        }
        systemPrivsType |= privsType;
    }
    *privsVal = systemPrivsType;
    return GMERR_OK;
}

// clang-format off
inline static GmcObjectPrivsT GmruleInitObjectPrivilegeData(
    const char *namespace, const char *objName, uint32_t objType, uint32_t privs, bool isAtomic)
{
    return (GmcObjectPrivsT){
        .nameSpaceName = namespace, .objName = objName, .objType = objType, .privs = privs, .isAtomic = isAtomic};
}
// clang-format on

void GmruleGrantOrRevokePrintWarnLog(
    Status ret, PolicyOperationE opType, PrivilegeOperationCtxT *runCtx, bool isObjPriv)
{
    if (isObjPriv) {
        const char *objName = DbJsonStringValue(runCtx->objName);
        if (opType == GRANT_PRIVS) {
            TOOL_WARN(DbPrintfDefault, (int32_t)ret,
                "grant object priv to \"%s:%s\" unsuccessful, objName: %s. ret = %" PRIi32 ".", runCtx->userOrGrp,
                runCtx->process, objName, (int32_t)ret);
        } else {
            // "revoke object priv from \"%s:%s\" unsuccessful, objName: %s. ret = %" PRIi32 "."
            TOOL_WARN(DbPrintfDefault, (int32_t)ret,
                "revoke obj priv from \"%s:%s\" unsucc, objName: %s. ret = %" PRIi32 ".", runCtx->userOrGrp,
                runCtx->process, objName, (int32_t)ret);
        }
    } else {
        if (opType == GRANT_PRIVS) {
            // "grant system priv to \"%s:%s\" unsuccessful, objType: %s. ret = %" PRIi32 ""
            TOOL_WARN(DbPrintfDefault, (int32_t)ret,
                "grant sys priv to \"%s:%s\" unsucc, objType: %s. ret = %" PRIi32 "", runCtx->userOrGrp,
                runCtx->process, runCtx->objTypeStr, (int32_t)ret);
        } else {
            // "revoke system priv to \"%s:%s\" unsuccessful, objType: %s. ret = %" PRIi32 ""
            TOOL_WARN(DbPrintfDefault, (int32_t)ret,
                "revoke sys priv to \"%s:%s\" unsucc, objType: %s. ret = %" PRIi32 "", runCtx->userOrGrp,
                runCtx->process, runCtx->objTypeStr, (int32_t)ret);
        }
    }
}

static Status GmruleGrantOrRevokeHandleHandleError(
    Status ret, PolicyOperationE opType, const GmRuleOptionT *option, PrivilegeOperationCtxT *runCtx, bool isObjPriv)
{
    if (ret == GMERR_OK) {
        return GMERR_OK;
    }
    bool isWarnAndReturnOk = false;
    if (opType == REVOKE_PRIVS || ret == GMERR_DATATYPE_MISMATCH) {
        // 撤权server返回错误，仅打warning日志,并且warningCnt+1
        // 赋权时，若遍历privs时，若server返回GMERR_DATATYPE_MISMATCH错误码，仅打warning日志，并且warningCnt+1
        isWarnAndReturnOk = true;
    } else if (ret == GMERR_UNDEFINED_OBJECT && !option->isAtomic) {
        // 这里是接收GMERR_UNDEFINED_OBJECT的情况，非atomic下，兼容V3不报错，但是warningCnt+1
        isWarnAndReturnOk = true;
    }

    if (isWarnAndReturnOk) {
        if (option->showDetail) {
            GmruleGrantOrRevokePrintWarnLog(ret, opType, runCtx, isObjPriv);
        }
        runCtx->warningCnt++;
        return GMERR_OK;
    }

    return ret;
}

Status GmruleGrantSingleObjectPrivilege(PrivilegeOperationCtxT *runCtx, const DbJsonT *objJson, bool isAtomic)
{
    DB_POINTER2(runCtx, objJson);
    const char *objName = DbJsonStringValue(objJson);
    if (objName == NULL) {
        // "get object name unsuccessful."
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_JSON_CONTENT, "get obj name unsucc.");
        return GMERR_INVALID_JSON_CONTENT;
    }
    GmcObjectPrivsT objectPrivs =
        GmruleInitObjectPrivilegeData(runCtx->namespace, objName, runCtx->objType, runCtx->objPrivs, isAtomic);
    objectPrivs.toUser = runCtx->toUser;

    Status ret = GmcGrantObjectPrivs(runCtx->stmt, runCtx->userOrGrp, runCtx->process, &objectPrivs);
    if (ret == GMERR_OK) {
        runCtx->grantOrRevokeObjCount++;
    }
    return ret;
}

Status GmruleRevokeSingleObjectPrivilege(PrivilegeOperationCtxT *runCtx, const DbJsonT *objJson, bool showDetail)
{
    DB_POINTER2(runCtx, objJson);
    const char *objName = DbJsonStringValue(objJson);
    if (objName == NULL) {
        // "get object name unsuccessful."
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_JSON_CONTENT, "get obj name unsucc.");
        return GMERR_INVALID_JSON_CONTENT;
    }
    GmcObjectPrivsT objectPrivs =
        GmruleInitObjectPrivilegeData(runCtx->namespace, objName, runCtx->objType, runCtx->objPrivs, false);
    objectPrivs.toUser = runCtx->toUser;

    Status ret = GmcRevokeObjectPrivs(runCtx->stmt, runCtx->userOrGrp, runCtx->process, &objectPrivs);
    if (ret == GMERR_OK) {
        runCtx->grantOrRevokeObjCount++;
    }
    // 撤销权限失败仅打印warnning信息，不报错中断
    return ret;
}

Status GmruleGrantSystemPrivilege(PrivilegeOperationCtxT *runCtx, bool isAtomic, bool toUser)
{
    DB_POINTER4(runCtx, runCtx->stmt, runCtx->userOrGrp, runCtx->process);
    GmcSystemPrivsT sysPrivs = {.userOrGrpName = runCtx->userOrGrp,
        .processName = runCtx->process,
        .opType = (GmcSystemPrivsTypeE)runCtx->sysPrivs,
        .objectType = (GmcObjectTypeE)runCtx->objType,
        .isAtomic = isAtomic,
        .toUser = toUser};
    Status ret = GmcGrantSystemPrivs(runCtx->stmt, &sysPrivs);
    if (ret == GMERR_OK) {
        runCtx->grantOrRevokeUsersCount++;
    }
    return ret;
}

Status GmruleRevokeSystemPrivilege(PrivilegeOperationCtxT *runCtx, bool showDetail, bool toUser)
{
    DB_POINTER4(runCtx, runCtx->stmt, runCtx->userOrGrp, runCtx->process);
    GmcSystemPrivsT sysPrivs = {
        .userOrGrpName = runCtx->userOrGrp,
        .processName = runCtx->process,
        .opType = (GmcSystemPrivsTypeE)runCtx->sysPrivs,
        .objectType = (GmcObjectTypeE)runCtx->objType,
        .isAtomic = false,
        .toUser = toUser,
    };
    Status ret = GmcRevokeSystemPrivs(runCtx->stmt, &sysPrivs);
    if (ret == GMERR_OK) {
        runCtx->grantOrRevokeUsersCount++;
    }
    // 撤销权限失败仅打印warnning信息，不报错中断
    return ret;
}

inline static bool GmruleIsRollbackGrantOrRevokeMultipleObjectPrivilege(
    Status res, PolicyOperationE opType, bool isAtomic, uint32_t indexNum)
{
    return res != GMERR_OK && opType == GRANT_PRIVS && isAtomic && indexNum > 0;
}

Status GmruleRollbackGrantOrRevokeMultipleObjectPrivilege(
    PrivilegeOperationCtxT *runCtx, GmRuleOptionT *option, uint32_t indexNum)
{
    for (uint32_t i = 0; i < indexNum; i++) {
        runCtx->objType = runCtx->labelType.type[i];
        if (runCtx->objType == GMC_VERTEX_KV_TABLE_NOT_EXIST) {
            continue;
        }
        // 仅支持对多个vertex label或者kv table批量授权，这个应由上层校验
        DB_ASSERT(runCtx->objType == GMC_VERTEX_LABEL || runCtx->objType == GMC_KV_TABLE ||
                  runCtx->objType == GMC_DATALOG_UDF);
        const DbJsonT *objJson = DbJsonArrayGet(runCtx->objName, (size_t)i);
        if (objJson == NULL) {
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED,
                "get obj at index[%" PRIu32 "] unsucc", i);
            return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
        }
        Status tmp = GmruleRevokeSingleObjectPrivilege(runCtx, objJson, option->showDetail);
        if (tmp != GMERR_OK) {
            return tmp;
        }
    }
    return GMERR_OK;
}

Status GmruleGrantOrRevokeMultipleObjectPrivilege(PrivilegeOperationCtxT *runCtx, GmRuleOptionT *option)
{
    DB_POINTER2(runCtx, option);
    Status ret = GMERR_OK;
    uint32_t index = 0;
    for (; index < (uint32_t)DbJsonGetArraySize(runCtx->objName); ++index) {
        runCtx->objType = runCtx->labelType.type[index];
        if (runCtx->objType == GMC_VERTEX_KV_TABLE_NOT_EXIST) {
            continue;
        }
        // 仅支持对多个vertex label或者kv table批量授权，这个应由上层校验
        DB_ASSERT(runCtx->objType == GMC_VERTEX_LABEL || runCtx->objType == GMC_KV_TABLE ||
                  runCtx->objType == GMC_DATALOG_UDF);
        const DbJsonT *objJson = DbJsonArrayGet(runCtx->objName, (size_t)index);
        if (objJson == NULL) {
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED,
                "get obj at index[%" PRIu32 "] unsucc", index);
            return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
        }
        if (runCtx->opType == GRANT_PRIVS) {
            ret = GmruleGrantSingleObjectPrivilege(runCtx, objJson, option->isAtomic);
        } else {
            ret = GmruleRevokeSingleObjectPrivilege(runCtx, objJson, option->showDetail);
        }
        ret = GmruleGrantOrRevokeHandleHandleError(ret, runCtx->opType, option, runCtx, true);
        if (ret != GMERR_OK) {
            DB_ASSERT(runCtx->opType == GRANT_PRIVS);
            const char *objName = DbJsonStringValue(objJson);
            // 因为撤权永远不会报错，仅会打warning日志，因此ret在处理完错误码后必定为OK
            // 仅在赋权的时候有可能会打错误日志;
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret,
                "grant object priv to \"%s:%s\" unsuccessful, objName: %s. ret = %" PRIi32 ".", runCtx->userOrGrp,
                runCtx->process, objName, (int32_t)ret);
            break;
        }
    }
    if (GmruleIsRollbackGrantOrRevokeMultipleObjectPrivilege(ret, runCtx->opType, option->isAtomic, index)) {
        Status tmp = GmruleRollbackGrantOrRevokeMultipleObjectPrivilege(runCtx, option, index);
        if (tmp != GMERR_OK) {
            return tmp;
        }
    }
    return ret;
}

Status GmruleParseUserAndGroup(PrivilegeOperationCtxT *runCtx, const DbJsonT *privs)
{
    const DbJsonT *user = DbJsonObjectGet(privs, FIELD_USER_NAME);
    const DbJsonT *group = DbJsonObjectGet(privs, FIELD_GROUP_NAME);
    if ((user == NULL && group == NULL) || (user != NULL && group != NULL)) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_JSON_CONTENT, "field \"%s\" and \"%s\" is invalid.",
            FIELD_USER_NAME, FIELD_GROUP_NAME);
        return GMERR_INVALID_JSON_CONTENT;
    }
    Status ret = GMERR_OK;
    if (user != NULL) {
        ret = GmruleParseUser(privs, &runCtx->process, &runCtx->userOrGrp, NULL);
        if (ret != GMERR_OK) {
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "parse uname unsucc. ret = %" PRIi32 "", (int32_t)ret);
            return ret;
        }
        runCtx->toUser = true;
    }
    if (group != NULL) {
#ifdef EXPERIMENTAL_NERGC
        // NERGC场景下不支持group字段
        TOOL_RUN_ERROR(
            DbPrintfDefault, GMERR_FEATURE_NOT_SUPPORTED, "not supported field %s in NERGC.", FIELD_GROUP_NAME);
        return GMERR_FEATURE_NOT_SUPPORTED;
#else
        ret = GmruleParseGroup(privs, &runCtx->process, &runCtx->userOrGrp);
        if (ret != GMERR_OK) {
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "parse uname unsucc. ret = %" PRIi32 "", (int32_t)ret);
            return ret;
        }
        runCtx->toUser = false;
#endif
    }
    return GMERR_OK;
}

Status GmruleGrantOrRevokeSingleObjPrivsInner(
    PrivilegeOperationCtxT *runCtx, const DbJsonT *privs, GmRuleOptionT *option)
{
    DB_POINTER3(runCtx, privs, option);
    // 获取用户名/用户组名
    Status ret = GmruleParseUserAndGroup(runCtx, privs);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 获取对象权限
    ret = GmruleGetObjectPrivilege(privs, &runCtx->objPrivs);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 授予或者撤销单个对象权限
    if (DbJsonIsString(runCtx->objName)) {
        if (runCtx->opType == GRANT_PRIVS) {
            ret = GmruleGrantSingleObjectPrivilege(runCtx, runCtx->objName, option->isAtomic);
        } else {
            ret = GmruleRevokeSingleObjectPrivilege(runCtx, runCtx->objName, option->showDetail);
        }
        ret = GmruleGrantOrRevokeHandleHandleError(ret, runCtx->opType, option, runCtx, true);
        if (ret != GMERR_OK) {
            DB_ASSERT(runCtx->opType == GRANT_PRIVS);
            // 因为撤权永远不会报错，仅会打warning日志，因此ret在处理完错误码后必定为OK
            // 仅在赋权的时候有可能会打错误日志;
            const char *objName = DbJsonStringValue(runCtx->objName);
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret,
                "grant object priv to \"%s:%s\" unsuccessful, objName: %s. ret = %" PRIi32 ".", runCtx->userOrGrp,
                runCtx->process, objName, (int32_t)ret);
        }
    } else if (DbJsonIsArray(runCtx->objName)) {  // 同时将多个表对象的权限进行授权或者撤销
        ret = GmruleGrantOrRevokeMultipleObjectPrivilege(runCtx, option);
    } else {
        // "objName is neither string nor array"
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_DATA_EXCEPTION, "objName not str or arr");
        return GMERR_DATA_EXCEPTION;
    }
    return ret;
}

Status GmruleGrantOrRevokeSingleObjPrivsCheck(PrivilegeOperationCtxT *runCtx, const DbJsonT *objCfg,
    const GmRuleOptionT *option, const DbJsonT *privsDbJsonArray, bool *isObjExist)
{
    Status ret = GmruleSetObjPrivsRunCtx(runCtx, objCfg);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 检查对象是否存在，如果是表对象，并且是批量配置，会检查每个表对象是否都存在,并标记其真正的表类型
    ret = GmruleCheckObjectExistAndMarkObjectType(runCtx, isObjExist, option->showDetail);
    if (ret != GMERR_OK || !(*isObjExist)) {
        return ret;
    }
    // 检查是否存在重复的用户名
    ret = GmruleCheckDuplicate(privsDbJsonArray, GmruleCompareUserTuple);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GmruleCheckDuplicate(privsDbJsonArray, GmruleCompareGroupTuple);
}

inline static bool GmruleIsRollbackGrantOrRevokeSingleObjPrivs(Status res, PolicyOperationE opType, bool isAtomic)
{
    return res != GMERR_OK && opType == GRANT_PRIVS && isAtomic;
}

Status GmruleRollbackGrantOrRevokeSingleObjPrivs(
    PrivilegeOperationCtxT *runCtx, GmRuleOptionT *option, DbJsonT *privsDbJsonArray, int64_t indexNum)
{
    int64_t i = indexNum;
    for (i--; i >= 0; i--) {
        DbJsonT *privs = DbJsonArrayGet(privsDbJsonArray, (size_t)i);
        if (privs == NULL) {
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED,
                "get obj at index[%" PRIi64 "] unsucc", i);
            return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
        }
        runCtx->opType = ROLLBACK_GRANT_PRIVS;
        Status tmp = GmruleGrantOrRevokeSingleObjPrivsInner(runCtx, privs, option);
        if (tmp != GMERR_OK) {
            return tmp;
        }
    }
    return GMERR_OK;
}

Status GmruleGrantOrRevokeSingleObjPrivs(PrivilegeOperationCtxT *runCtx, const DbJsonT *objCfg, GmRuleOptionT *option)
{
    DB_POINTER4(runCtx, runCtx->stmt, objCfg, option);
    runCtx->isGrantOrRevokeOk = false;
    DbJsonT *privsDbJsonArray = DbJsonObjectGet(objCfg, "privs");
    if (privsDbJsonArray == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_JSON_CONTENT,
            "field \"privs\" is invalid in \"object_privilege_config\". ret = %" PRIi32 "",
            (int32_t)GMERR_INVALID_JSON_CONTENT);
        return GMERR_INVALID_JSON_CONTENT;
    }
    int64_t privsArraySize = (int64_t)DbJsonGetArraySize(privsDbJsonArray);
    if (privsArraySize == 0) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_JSON_CONTENT,
            "field \"privs\" is empty array in \"object_privilege_config\". ret = %" PRIi32 "",
            (int32_t)GMERR_INVALID_JSON_CONTENT);
        return GMERR_INVALID_JSON_CONTENT;
    }
    bool isObjExist = false;
    Status ret = GmruleGrantOrRevokeSingleObjPrivsCheck(runCtx, objCfg, option, privsDbJsonArray, &isObjExist);
    if (ret != GMERR_OK || !isObjExist) {
        return ret;
    }
    int64_t i = 0;
    for (; i < privsArraySize; i++) {
        DbJsonT *privs = DbJsonArrayGet(privsDbJsonArray, (size_t)i);
        if (privs == NULL) {
            ret = GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "get obj at index[%" PRIi64 "] unsucc", i);
        }
        ret = GmruleGrantOrRevokeSingleObjPrivsInner(runCtx, privs, option);
        if (ret != GMERR_OK) {
            break;
        }
    }
    if (GmruleIsRollbackGrantOrRevokeSingleObjPrivs(ret, runCtx->opType, option->isAtomic)) {
        Status tmp = GmruleRollbackGrantOrRevokeSingleObjPrivs(runCtx, option, privsDbJsonArray, i);
        if (tmp != GMERR_OK) {
            return tmp;
        }
    } else if (ret == GMERR_OK && runCtx->grantOrRevokeObjCount > 0) {
        runCtx->isGrantOrRevokeOk = true;
    }
    return ret;
}

inline static bool GmruleIsRollbackGrantOrRevokeSingleSysPrivsInner(
    Status res, PolicyOperationE opType, bool isAtomic, uint32_t indexNum)
{
    return res != GMERR_OK && opType == GRANT_PRIVS && isAtomic && indexNum > 0;
}

Status GmruleRollbackGrantOrRevokeSingleUserSysPrivsInner(
    PrivilegeOperationCtxT *runCtx, const GmRuleOptionT *option, uint32_t indexNum)
{
    for (uint32_t idx = 0; idx < indexNum; idx++) {
        const DbJsonT *userTuple = DbJsonArrayGet(runCtx->usersList, (size_t)idx);
        if (userTuple == NULL) {
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED,
                "get obj at index[%" PRIu32 "] unsucc", idx);
            return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
        }
        Status tmp = GmruleParseUser(userTuple, &runCtx->process, &runCtx->userOrGrp, NULL);
        if (tmp != GMERR_OK) {
            return tmp;
        }
        tmp = GmruleRevokeSystemPrivilege(runCtx, option->showDetail, true);
        if (tmp != GMERR_OK) {
            return tmp;
        }
    }
    return GMERR_OK;
}

Status GmruleRollbackGrantOrRevokeSingleGroupSysPrivsInner(
    PrivilegeOperationCtxT *runCtx, const GmRuleOptionT *option, uint32_t indexNum)
{
    for (uint32_t idx = 0; idx < indexNum; idx++) {
        const DbJsonT *groupTuple = DbJsonArrayGet(runCtx->groupsList, (size_t)idx);
        if (groupTuple == NULL) {
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED,
                "get obj at index[%" PRIu32 "] unsucc", idx);
            return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
        }
        Status tmp = GmruleParseGroup(groupTuple, &runCtx->process, &runCtx->userOrGrp);
        if (tmp != GMERR_OK) {
            return tmp;
        }
        tmp = GmruleRevokeSystemPrivilege(runCtx, option->showDetail, false);
        if (tmp != GMERR_OK) {
            return tmp;
        }
    }
    return GMERR_OK;
}

Status GmruleGrantOrRevokeSingleUserSysPrivsInner(PrivilegeOperationCtxT *runCtx, const GmRuleOptionT *option)
{
    DB_POINTER2(runCtx, option);
    Status ret = GMERR_OK;
    uint32_t i = 0;
    for (; i < (uint32_t)DbJsonGetArraySize(runCtx->usersList); i++) {
        const DbJsonT *userTuple = DbJsonArrayGet(runCtx->usersList, (size_t)i);
        if (userTuple == NULL) {
            ret = GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "get obj at index[%" PRIu32 "] unsucc", i);
            break;
        }
        ret = GmruleParseUser(userTuple, &runCtx->process, &runCtx->userOrGrp, NULL);
        if (ret != GMERR_OK) {
            break;
        }
        if (runCtx->opType == GRANT_PRIVS) {
            ret = GmruleGrantSystemPrivilege(runCtx, option->isAtomic, true);
        } else {
            ret = GmruleRevokeSystemPrivilege(runCtx, option->showDetail, true);
        }
        ret = GmruleGrantOrRevokeHandleHandleError(ret, runCtx->opType, option, runCtx, false);
        if (ret != GMERR_OK) {
            // 因为撤权永远不会报错，仅会打warning日志，因此ret在处理完错误码后必定为OK
            // 仅在赋权的时候有可能会打错误日志;
            DB_ASSERT(runCtx->opType == GRANT_PRIVS);
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret,
                "grant system priv to user %s:%s unsuccessful, objType: %s. ret = %" PRIi32 "", runCtx->userOrGrp,
                runCtx->process, runCtx->objTypeStr, (int32_t)ret);
            break;
        }
    }

    if (GmruleIsRollbackGrantOrRevokeSingleSysPrivsInner(ret, runCtx->opType, option->isAtomic, i)) {
        Status tmp = GmruleRollbackGrantOrRevokeSingleUserSysPrivsInner(runCtx, option, i);
        if (tmp != GMERR_OK) {
            return tmp;
        }
    }
    return ret;
}

Status GmruleGrantOrRevokeSingleGroupSysPrivsInner(PrivilegeOperationCtxT *runCtx, const GmRuleOptionT *option)
{
    DB_POINTER2(runCtx, option);
    Status ret = GMERR_OK;
    uint32_t i = 0;
    for (; i < (uint32_t)DbJsonGetArraySize(runCtx->groupsList); i++) {
        const DbJsonT *groupTuple = DbJsonArrayGet(runCtx->groupsList, (size_t)i);
        if (groupTuple == NULL) {
            ret = GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "get obj at index[%" PRIu32 "] unsucc", i);
            break;
        }
        ret = GmruleParseGroup(groupTuple, &runCtx->process, &runCtx->userOrGrp);
        if (ret != GMERR_OK) {
            break;
        }
        if (runCtx->opType == GRANT_PRIVS) {
            ret = GmruleGrantSystemPrivilege(runCtx, option->isAtomic, false);
        } else {
            ret = GmruleRevokeSystemPrivilege(runCtx, option->showDetail, false);
        }
        ret = GmruleGrantOrRevokeHandleHandleError(ret, runCtx->opType, option, runCtx, false);
        if (ret != GMERR_OK) {
            // 因为撤权永远不会报错，仅会打warning日志，因此ret在处理完错误码后必定为OK
            // 仅在赋权的时候有可能会打错误日志;
            DB_ASSERT(runCtx->opType == GRANT_PRIVS);
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret,
                "grant sys priv to group %s:%s unsucc, objType: %s. ret = %" PRIi32 "", runCtx->userOrGrp,
                runCtx->process, runCtx->objTypeStr, (int32_t)ret);
            break;
        }
    }

    if (GmruleIsRollbackGrantOrRevokeSingleSysPrivsInner(ret, runCtx->opType, option->isAtomic, i)) {
        Status tmp = GmruleRollbackGrantOrRevokeSingleGroupSysPrivsInner(runCtx, option, i);
        if (tmp != GMERR_OK) {
            return tmp;
        }
    }
    return ret;
}

Status GmruleGrantOrRevokeSingleSysPrivs(
    PrivilegeOperationCtxT *runCtx, const DbJsonT *privsJson, GmRuleOptionT *option)
{
    DB_POINTER4(runCtx, runCtx->stmt, privsJson, option);
    // 解析对象类型
    Status ret = GmruleParseObjectType(privsJson, &runCtx->objType);
    if (ret != GMERR_OK) {
        return ret;
    }
    // VERTEX_LABEL|KV_TABLE 和 KV_TABLE|VERTEX_LABEL不能用于系统权限授权
    if (runCtx->objType == GMC_VERTEX_OR_KV_TABLE) {
        // "VERTEX_LABEL|KV_TABLE is not supported for system priv."
        TOOL_RUN_ERROR(
            DbPrintfDefault, (int32_t)GMERR_INVALID_JSON_CONTENT, "VERTEX_LABEL|KV_TABLE unsupport for sys priv.");
        return GMERR_INVALID_JSON_CONTENT;
    }
    runCtx->objTypeStr = GmruleGetStringValueByKey(privsJson, "obj_type", true);
    if (runCtx->objTypeStr == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_INVALID_JSON_CONTENT, "objTypeStr is NULL.");
        return GMERR_INVALID_JSON_CONTENT;
    }

    // 获取系统权限类型
    ret = GmruleGetSystemPrivilege(privsJson, &runCtx->sysPrivs);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 循环给每一个用户授予或者撤销相同的系统权限
    ret = GmruleGrantOrRevokeSingleUserSysPrivsInner(runCtx, option);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmruleGrantOrRevokeSingleGroupSysPrivsInner(runCtx, option);
    if (ret != GMERR_OK) {
        if (GmruleIsRollbackGrantOrRevokeSingleSysPrivsInner(
                ret, runCtx->opType, option->isAtomic, (uint32_t)DbJsonGetArraySize(runCtx->usersList))) {
            Status tmp = GmruleRollbackGrantOrRevokeSingleUserSysPrivsInner(
                runCtx, option, (uint32_t)DbJsonGetArraySize(runCtx->usersList));
            if (tmp != GMERR_OK) {
                return tmp;
            }
        }
        return ret;
    }
    return GMERR_OK;
}

Status GmruleGrantOrRevokeUserSysPrivsCheck(PrivilegeOperationCtxT *runCtx, const DbJsonT *usrCfg)
{
    const DbJsonT *usersList = DbJsonObjectGet(usrCfg, FILED_USERS);
    const DbJsonT *groupsList = DbJsonObjectGet(usrCfg, FIELD_GROUPS);
    if (usersList == NULL && groupsList == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_JSON_CONTENT, "usersList and grouplist is null.");
        return GMERR_INVALID_JSON_CONTENT;
    }
    Status ret;
    if (usersList != NULL) {
        ret = GmruleCheckJsonUsersList(usersList, FILED_USERS, FIELD_USER_NAME);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = GmruleCheckDuplicate(usersList, GmruleCompareUserTuple);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    if (groupsList != NULL) {
#ifdef EXPERIMENTAL_NERGC
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_FEATURE_NOT_SUPPORTED, "not supported field %s in NERGC.", FIELD_GROUPS);
        return GMERR_FEATURE_NOT_SUPPORTED;
#else
        ret = GmruleCheckJsonUsersList(groupsList, FIELD_GROUPS, FIELD_GROUP_NAME);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = GmruleCheckDuplicate(groupsList, GmruleCompareGroupTuple);
        if (ret != GMERR_OK) {
            return ret;
        }
#endif
    }
    runCtx->usersList = usersList;
    runCtx->groupsList = groupsList;
    return GMERR_OK;
}

inline static bool GmruleIsRollbackGrantOrRevokeUserSysPrivs(
    Status res, PolicyOperationE opType, bool isAtomic, int64_t indexNum)
{
    return res != GMERR_OK && opType == GRANT_PRIVS && isAtomic;
}

static Status GmruleRoolbackGrantOrRevokeUserSysPrivs(
    PrivilegeOperationCtxT *runCtx, GmRuleOptionT *option, DbJsonT *privsArray, int64_t indexNum)
{
    int64_t i = indexNum;
    for (i--; i >= 0; i--) {
        DbJsonT *privs = DbJsonArrayGet(privsArray, (size_t)i);
        if (privs == NULL) {
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED,
                "get obj at index[%" PRIi64 "] unsucc", i);
            return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
        }
        runCtx->opType = ROLLBACK_GRANT_PRIVS;
        Status tmp = GmruleGrantOrRevokeSingleSysPrivs(runCtx, privs, option);
        if (tmp != GMERR_OK) {
            return tmp;
        }
    }
    return GMERR_OK;
}

static Status GmruleGrantOrRevokeUserSysPrivs(
    PrivilegeOperationCtxT *runCtx, const DbJsonT *usrCfg, GmRuleOptionT *option)
{
    DB_POINTER4(runCtx, runCtx->stmt, usrCfg, option);
    runCtx->isGrantOrRevokeOk = false;
    Status ret = GmruleGrantOrRevokeUserSysPrivsCheck(runCtx, usrCfg);
    if (ret != GMERR_OK) {
        return ret;
    }

    DbJsonT *privsArray = DbJsonObjectGet(usrCfg, "privs");
    if (privsArray == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_JSON_CONTENT, "field \"privs\" is invalid.");
        return GMERR_INVALID_JSON_CONTENT;
    }
    int64_t privsArraySize = (int64_t)DbJsonGetArraySize(privsArray);
    if (privsArraySize == 0) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_JSON_CONTENT, "field \"privs\" is empty.");
        return GMERR_INVALID_JSON_CONTENT;
    }
    int64_t i = 0;
    for (; i < privsArraySize; i++) {
        DbJsonT *privs = DbJsonArrayGet(privsArray, (size_t)i);
        if (privs == NULL) {
            ret = GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "get obj at index[%" PRIi64 "] unsucc", i);
            break;
        }
        ret = GmruleGrantOrRevokeSingleSysPrivs(runCtx, privs, option);
        if (ret != GMERR_OK) {
            break;
        }
    }
    if (GmruleIsRollbackGrantOrRevokeUserSysPrivs(ret, runCtx->opType, option->isAtomic, i)) {
        Status tmp = GmruleRoolbackGrantOrRevokeUserSysPrivs(runCtx, option, privsArray, i);
        if (tmp != GMERR_OK) {
            return tmp;
        }
    } else if (ret == GMERR_OK && runCtx->grantOrRevokeUsersCount > 0) {
        runCtx->isGrantOrRevokeOk = true;
    }
    return ret;
}

static inline Status GmruleGrantOrRevokeObjPrivsInner(
    const DbJsonT *objPrivsArray, int64_t index, PrivilegeOperationCtxT *runCtx, GmRuleOptionT *option)
{
    DbJsonT *objPrivs = DbJsonArrayGet(objPrivsArray, (size_t)index);
    if (objPrivs == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, "get obj unsucc");
        return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
    }
    Status ret = GmruleGrantOrRevokeSingleObjPrivs(runCtx, objPrivs, option);
    GmruleFreeLableTypeBuffer(runCtx);
    return ret;
}

inline static bool GmruleIsRollbackGrantOrRevokeObjPrivs(Status res, PolicyOperationE opType, bool isAtomic)
{
    return res != GMERR_OK && opType == GRANT_PRIVS && isAtomic;
}

static Status GmruleGrantOrRevokeObjPrivs(
    GmcStmtT *stmt, const DbJsonT *json, PolicyOperationE opType, PolicyStatT *policyStat, GmRuleOptionT *option)
{
    DB_POINTER3(stmt, json, option);
    DbJsonT *objPrivsArray = DbJsonObjectGet(json, "object_privilege_config");
    if (objPrivsArray == NULL) {
        return GMERR_OK;
    }

    int64_t objCnt = (int64_t)DbJsonGetArraySize(objPrivsArray);
    // grant or revoke object priv
    Status ret = GMERR_OK;
    int64_t i = 0;
    for (; i < objCnt; i++) {
        PrivilegeOperationCtxT runCtx = GmruleInitPrivilegeOpCtx(stmt, opType);
        ret = GmruleGrantOrRevokeObjPrivsInner(objPrivsArray, i, &runCtx, option);
        if (ret != GMERR_OK) {
            break;
        }
        policyStat->objPrivsWarnNum += runCtx.warningCnt;
        if (runCtx.isGrantOrRevokeOk) {
            if (opType == ROLLBACK_GRANT_PRIVS) {
                policyStat->objPrivsRollbackNum++;
                // 进入回滚流程，那之前必然成功导入，因此objPrivsSuccessNum必然大于0
                DB_ASSERT(policyStat->objPrivsSuccessNum > 0);
                policyStat->objPrivsSuccessNum--;
            } else {
                policyStat->objPrivsSuccessNum++;
            }
        }
    }
    if (GmruleIsRollbackGrantOrRevokeObjPrivs(ret, opType, option->isAtomic)) {
        for (i--; i >= 0; i--) {
            PrivilegeOperationCtxT runCtx = GmruleInitPrivilegeOpCtx(stmt, ROLLBACK_GRANT_PRIVS);
            Status tmp = GmruleGrantOrRevokeObjPrivsInner(objPrivsArray, i, &runCtx, option);
            if (tmp != GMERR_OK) {
                return tmp;
            }
            policyStat->objPrivsRollbackNum++;
            // 进入回滚流程，那之前必然成功导入，因此objPrivsSuccessNum必然大于0
            DB_ASSERT(policyStat->objPrivsSuccessNum > 0);
            policyStat->objPrivsSuccessNum--;
        }
    }
    return ret;
}

static inline Status GmruleGrantOrRevokeSysPrivsInner(
    const DbJsonT *sysPrivsArray, int64_t index, PrivilegeOperationCtxT *runCtx, GmRuleOptionT *option)
{
    DbJsonT *sysPrivs = DbJsonArrayGet(sysPrivsArray, (size_t)index);
    if (sysPrivs == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED, "sysPrivs is NULL.");
        return GMERR_THIRD_PARTY_FUNCTION_EXECUTE_FAILED;
    }
    return GmruleGrantOrRevokeUserSysPrivs(runCtx, sysPrivs, option);
}

inline static bool GmruleIsRollbackGrantOrRevokeSysPrivs(Status res, PolicyOperationE opType, bool isAtomic)
{
    return res != GMERR_OK && opType == GRANT_PRIVS && isAtomic;
}

static Status GmruleGrantOrRevokeSysPrivs(
    GmcStmtT *stmt, const DbJsonT *json, PolicyOperationE opType, PolicyStatT *policyStat, GmRuleOptionT *option)
{
    DB_POINTER3(stmt, json, option);
    DbJsonT *sysPrivsArray = DbJsonObjectGet(json, "system_privilege_config");
    if (sysPrivsArray == NULL) {
        return GMERR_OK;
    }
    // 检查是否存在对相同的用户授予系统权限
    Status ret = GmruleCheckDuplicate(sysPrivsArray, GmruleCompareUserTupleList);
    if (ret != GMERR_OK) {
        return ret;
    }
    int64_t privsCnt = (int64_t)DbJsonGetArraySize(sysPrivsArray);
    // grant or revoke system priv
    int64_t i = 0;
    for (; i < privsCnt; i++) {
        PrivilegeOperationCtxT runCtx = GmruleInitPrivilegeOpCtx(stmt, opType);
        ret = GmruleGrantOrRevokeSysPrivsInner(sysPrivsArray, i, &runCtx, option);
        if (ret != GMERR_OK) {
            break;
        }
        policyStat->sysPrivsWarnNum += runCtx.warningCnt;
        if (runCtx.isGrantOrRevokeOk) {
            if (opType == ROLLBACK_GRANT_PRIVS) {
                policyStat->sysPrivsRollbackNum++;
                // 进入回滚流程，那之前必然成功导入，因此sysPrivsSuccessNum必然大于0
                DB_ASSERT(policyStat->sysPrivsSuccessNum > 0);
                policyStat->sysPrivsSuccessNum--;
            } else {
                policyStat->sysPrivsSuccessNum++;
            }
        }
    }
    if (GmruleIsRollbackGrantOrRevokeSysPrivs(ret, opType, option->isAtomic)) {
        for (i--; i >= 0; i--) {
            PrivilegeOperationCtxT runCtx = GmruleInitPrivilegeOpCtx(stmt, ROLLBACK_GRANT_PRIVS);
            Status tmp = GmruleGrantOrRevokeSysPrivsInner(sysPrivsArray, i, &runCtx, option);
            if (tmp != GMERR_OK) {
                return tmp;
            }
            policyStat->sysPrivsRollbackNum++;
            // 进入回滚流程，那之前必然成功导入，因此sysPrivsSuccessNum必然大于0
            DB_ASSERT(policyStat->sysPrivsSuccessNum > 0);
            policyStat->sysPrivsSuccessNum--;
        }
    }
    return ret;
}

Status GmruleImportPolicyFile(GmRuleCtxT *ctx, GmRuleOptionT *option, char *filePath)
{
    DB_POINTER3(ctx, ctx->stmt, option);
    ctx->totalFileCnt++;
    DbJsonT *json = NULL;
    Status ret = GmruleLoadJson(filePath, DB_MAX_GMRULE_FILE_PATH_LEN, &json);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (DbJsonObjectGet(json, "object_privilege_config") == NULL &&
        DbJsonObjectGet(json, "system_privilege_config") == NULL) {
        TOOL_WARN(DbPrintfDefault, (int32_t)GMERR_NO_DATA, "file %s is empty or no valid field is found.", filePath);
        DbJsonDelete(json);
        return GMERR_OK;
    }
    ret = GmruleGrantOrRevokeObjPrivs(ctx->stmt, json, GRANT_PRIVS, &ctx->policyStat, option);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "import obj priv conf from %s unsucc. ret = %" PRIi32 "",
            filePath, (int32_t)ret);
        DbJsonDelete(json);
        return ret;
    }

    ret = GmruleGrantOrRevokeSysPrivs(ctx->stmt, json, GRANT_PRIVS, &ctx->policyStat, option);
    if (ret == GMERR_OK) {
        DbJsonDelete(json);
        ctx->importFileCnt++;
        return GMERR_OK;
    }
    TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "import sys priv conf from %s unsucc. ret = %" PRIi32 "", filePath,
        (int32_t)ret);
    if (option->isAtomic) {
        Status result = GmruleGrantOrRevokeObjPrivs(ctx->stmt, json, ROLLBACK_GRANT_PRIVS, &ctx->policyStat, option);
        if (result != GMERR_OK) {
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)result,
                "rollback import obj priv conf from %s unsucc. ret = %" PRIi32 "", filePath, (int32_t)result);
        }
    }
    DbJsonDelete(json);
    return ret;
}

Status GmruleRevokePolicyCheck(const char *filePath, bool isAtomic)
{
    if (!DbFileExist(filePath)) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_FILE_OPERATE_FAILED, "no such file: %s, ret = %" PRIi32 "",
            filePath, (int32_t)GMERR_FILE_OPERATE_FAILED);
        return GMERR_FILE_OPERATE_FAILED;
    }
    if (!TlsCheckFileSuffix(filePath, POLICY_SUFFIX)) {
        // revoke policy from file %s unsuccessful, suffix must be ." POLICY_SUFFIX ", ret = %" PRIi32 ""
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_FILE_OPERATE_FAILED,
            "revoke policy suffix from file %s must be ." POLICY_SUFFIX ", ret = %" PRIi32 "", filePath,
            (int32_t)GMERR_FILE_OPERATE_FAILED);
        return GMERR_FILE_OPERATE_FAILED;
    }
    // 当前remove操作不允许加atomic选项
    if (isAtomic) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_FEATURE_NOT_SUPPORTED,
            "revoke policy from file %s unsuccessful, parameter atomic is not supported. ret = %" PRIi32 "", filePath,
            (int32_t)GMERR_FEATURE_NOT_SUPPORTED);
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

Status GmruleRevokePolicy(GmcStmtT *stmt, GmRuleOptionT *option, char *filePath)
{
    DB_POINTER3(stmt, option, filePath);
    Status ret = GmruleRevokePolicyCheck(filePath, option->isAtomic);
    if (ret != GMERR_OK) {
        return ret;
    }

    DbJsonT *jsonConfig = NULL;
    ret = GmruleLoadJson(filePath, DB_MAX_GMRULE_FILE_PATH_LEN, &jsonConfig);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (DbJsonObjectGet(jsonConfig, "object_privilege_config") == NULL &&
        DbJsonObjectGet(jsonConfig, "system_privilege_config") == NULL) {
        TOOL_WARN(DbPrintfDefault, (int32_t)GMERR_NO_DATA, "file %s is empty or no valid field is found.", filePath);
        DbJsonDelete(jsonConfig);
        return GMERR_OK;
    }

    PolicyStatT policyStat = {0};
    ret = GmruleGrantOrRevokeObjPrivs(stmt, jsonConfig, REVOKE_PRIVS, &policyStat, option);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(
            DbPrintfDefault, (int32_t)ret, "revoke obj priv from %s unsucc. ret = %" PRIi32 "", filePath, (int32_t)ret);
        DbJsonDelete(jsonConfig);
        return ret;
    }
    ret = GmruleGrantOrRevokeSysPrivs(stmt, jsonConfig, REVOKE_PRIVS, &policyStat, option);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(
            DbPrintfDefault, (int32_t)ret, "revoke sys priv from %s unsucc. ret = %" PRIi32 "", filePath, (int32_t)ret);
        DbJsonDelete(jsonConfig);
        return ret;
    }
    DbJsonDelete(jsonConfig);

    TOOL_RUN_INFO(DbPrintfDefault, "[INFO] revoke policy from %s successfully.", filePath);
    PRINT_SUCCESS(DbPrintfDefault, "revoke policy. object privilege success: %" PRIu32 ", warning: %" PRIu32 ". ",
        policyStat.objPrivsSuccessNum, policyStat.objPrivsWarnNum);
    PRINT_SUCCESS(DbPrintfDefault, "revoke policy. system privilege success: %" PRIu32 ", warning: %" PRIu32 ". ",
        policyStat.sysPrivsSuccessNum, policyStat.sysPrivsWarnNum);
    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif
