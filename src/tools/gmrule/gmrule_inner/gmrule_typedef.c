/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: gmrule 命令格式定义源文件
 * Author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * Create: 2021-06-10
 */
#include "gmrule_typedef.h"
#include "adpt_locator.h"
#include "db_option_parser.h"
#include "tool_utils.h"

#ifdef __cplusplus
extern "C" {
#endif

// gmrule命令行参数校验模板, 用于校验命令行参数数据类型、参数组合约束, 在编码阶段初始化、不涉及并发读写
static const DbRuleItemT g_gmdbRuleRuleItems[] = {
    {0, (int32_t)GMRULE_RULE_HELP, {"-h"}, {true}},
    {0, (int32_t)GMRULE_RULE_VERSION, {"-v"}, {true}},
    {0, (int32_t)GMRULE_RULE_IMP, {"-c", "-f", "-s", "-d", "-q", "atomic", "-u"},
        {true, true, false, false, false, false, false}},
};

Status GmRuleAllocOptionRuleItems(DbOptionRuleT *optionRule)
{
    DB_POINTER(optionRule);
    return DbAllocOptionRuleItems(optionRule, GMRULE_OPTION_NUM, ELEMENT_COUNT(g_gmdbRuleRuleItems));
}

// 存储gmRule工具的cmd命令，新增命令要同步新增该处注释以及修改GMRULE_OPTION_NUM确保数组不会越界
// 格式：{ OptionName, Index, Param Type, Min value, Max Value, Min Param Count, Max Param Count,
// Used, Repeatable, Repeat Times, Params }
// {"-h", 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}}
// {"-v", 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}}
// {"-c", 0, PARAM_TYPE_STR, 1, MAX_COMMAND_LENGTH, 1, DB_MAX_GMRULE_OPTION_REPEAT_NUM, 0, 0, 0, {}}
// {"-f", 0, PARAM_TYPE_STR, 1, DB_MAX_GMRULE_FILE_PATH_LEN, 1, DB_MAX_GMRULE_OPTION_REPEAT_NUM, 0, 0, 0, {}}
// {"-s", 0, PARAM_TYPE_STR, 1, DOMAIN_NAME_MAX_LEN, 1, 1, 0, 0, 0, {}}
// {"-d", 0, PARAM_TYPE_STR, 0, 0, 0, 0, 0, 0, 0, {}}
// {"-q", 0, PARAM_TYPE_STR, 0, 0, 0, 0, 0, 0, 0, {}}
// {"atomic", 0, PARAM_TYPE_STR, 0, 0, 0, 0, 0, 0, 0, {}}
// {"-u", 0, PARAM_TYPE_STR, MIN_USER_NAME_LEN, MAX_USER_NAME_LEN, 0, 0, 0, 0, 0, {}}
Status DbInitRuleOptionItems(DbOptionRuleT *optionRule, uint32_t num)
{
    DB_POINTER(optionRule);
    if (num > OPTION_ITEM_MAX_NUM) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    DbOptionItemT *tmpOptionItems = optionRule->optionItems;
    *tmpOptionItems = (DbOptionItemT){"-h", 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}};
    tmpOptionItems += 1;

    *tmpOptionItems = (DbOptionItemT){"-v", 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}};
    tmpOptionItems += 1;

    *tmpOptionItems = (DbOptionItemT){
        "-c", 0, PARAM_TYPE_STR, 1, MAX_COMMAND_LENGTH, 1, DB_MAX_GMRULE_OPTION_REPEAT_NUM, 0, 0, 0, {}};
    tmpOptionItems += 1;

    *tmpOptionItems = (DbOptionItemT){
        "-f", 0, PARAM_TYPE_STR, 1, DB_MAX_GMRULE_FILE_PATH_LEN, 1, DB_MAX_GMRULE_OPTION_REPEAT_NUM, 0, 0, 0, {}};
    tmpOptionItems += 1;

    *tmpOptionItems = (DbOptionItemT){"-s", 0, PARAM_TYPE_STR, 1, DOMAIN_NAME_MAX_LEN, 1, 1, 0, 0, 0, {}};
    tmpOptionItems += 1;

    *tmpOptionItems = (DbOptionItemT){"-d", 0, PARAM_TYPE_STR, 0, 0, 0, 0, 0, 0, 0, {}};
    tmpOptionItems += 1;

    *tmpOptionItems = (DbOptionItemT){"-q", 0, PARAM_TYPE_STR, 0, 0, 0, 0, 0, 0, 0, {}};
    tmpOptionItems += 1;

    *tmpOptionItems = (DbOptionItemT){"atomic", 0, PARAM_TYPE_STR, 0, 0, 0, 0, 0, 0, 0, {}};
    tmpOptionItems += 1;

    *tmpOptionItems = (DbOptionItemT){"-u", 0, PARAM_TYPE_STR, MIN_USER_NAME_LEN, MAX_USER_NAME_LEN, 0, 0, 0, 0, 0, {}};

    return DbInitOptionItemsEntry(optionRule, num);
}

// clang-format on
Status GmRuleInit(DbOptionRuleT *optionRule, int32_t argc, char *argv[])
{
    Status ret = DbInitRuleOptionItems(optionRule, GMRULE_OPTION_NUM);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "DB init option items unsucc, ret = %" PRIi32, (int32_t)ret);
        return ret;
    }
    ret = DbInitRuleItems(optionRule, g_gmdbRuleRuleItems, (int32_t)ELEMENT_COUNT(g_gmdbRuleRuleItems));
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "DB init rule items unsucc, ret = %" PRIi32, (int32_t)ret);
        return ret;
    }
    ret = DbInitOptionParam(optionRule, argc, argv);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "DB init option param unsucc, ret = %" PRIi32, (int32_t)ret);
        return ret;
    }
    return GMERR_OK;
}

// clang-format off
void GmRuleGetHelp(void)
{
    const char *gmruleOptionManual[][MANUAL_COL_NUM] = {
        {"-h", "", "print the online help manual"},
        {"-v", "", "print the gmrule version"},
        {"-c", "<cmd_type>", "import_allowlist | remove_allowlist | import_policy | revoke_policy"},
        {"-f", "<file_path>", "a specific filePath or directory of the allowlist or policy"},
        {"-s", "<server_locator>", "serverLocator for connection"},
        {"-d", "", "print detail warn information"},
        {"-q", "", "only print unsuccessful log"},
        {"atomic", "", "rollback mode import"},
        {"-u", "<user_name>", "user name for connection"},
    };
    DbPrintManual("[OPTION]", gmruleOptionManual, (int32_t)ELEMENT_COUNT(gmruleOptionManual), NULL);
}

void GmRuleGetVersion(void)
{
    const char *gmruleVersion[][MANUAL_COL_NUM] = {
        {"gmrule GMDBV5", "", ""},
        {"Copyright (c) Huawei Technologies Co.", "", ""},
    };
    DbPrintManual("[VERSION]", gmruleVersion, (int32_t)ELEMENT_COUNT(gmruleVersion), NULL);
}
// clang-format on

#ifdef __cplusplus
}
#endif
