project(gmerror)

if (FUZZ OR COVERAGE)
    ADD_SUBDIRECTORY(gmerror_inner)
endif()

ADD_COMPILE_OPTIONS(-Wl,--copy-dt-needed-entries -fPIE)

include_sub_directories_recursively(${CMAKE_CURRENT_SOURCE_DIR})
include_sub_directories_recursively(${GMDB_CLIENT_PATH})

aux_source_directory(. SRC_MAIN_LIST)
aux_source_directory(${gmerror_SOURCE_DIR}/gmerror_inner SRC_GMERROR_LIST)

if (TOOLS_EMBED_SO)
  generate_tools_embed_so(gmerror_embed "${SRC_GMERROR_LIST}" "${TOOL_NAME}")
endif()

# 编译patch_obj不编译可执行程序
if(COMPILE_PATCH_OBJ)
  return()
endif()

add_executable (gmerror ${SRC_MAIN_LIST} ${SRC_GMERROR_LIST})
if(STRIP)
  separate_debug_info(gmerror)
endif()

target_link_libraries(gmerror ${TOOL_NAME})

install(TARGETS gmerror
  RUNTIME DESTINATION bin
)
