/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: file content
 * Author: gaohaiyang
 * Create: 2020-09-10
 */

#ifndef GMERROR_TYPEDEF_H
#define GMERROR_TYPEDEF_H
#include "adpt_types.h"
#include "db_option_parser.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

// 定义启动规则
typedef enum GmerrStartupRule { GMERR_RULE_H = 0, GMERR_RULE_ERRCODE, GMERR_RULE_BUTT } GmerrStartupRuleE;

Status GmErrAllocOptionRuleItems(DbOptionRuleT *optionRule);

/*
 * description: 初始化选项数组
 * return: 成功返回 GMERR_OK
 */
Status GmErrInitOptionItems(DbOptionRuleT *optionRule);

/*
 * description: 初始化规则项
 * return: 成功返回 GMERR_OK
 */
Status GmErrInitRuleItems(DbOptionRuleT *optionRule);

// 打印手册
void GmErrPrintHelp(void);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
