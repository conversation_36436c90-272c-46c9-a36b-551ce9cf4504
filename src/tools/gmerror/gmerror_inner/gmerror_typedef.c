/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * Description: file content
 * Author: gaohaiyang
 * Create: 2020-09-10
 */
#include "gmerror_typedef.h"
#include "adpt_define.h"
#include "db_option_parser.h"

#ifdef __cplusplus
extern "C" {
#endif

// 定义选项数组
// 并发方案：不涉及并发，资源只读
static DbOptionItemT g_gmdbErrOptionItems[] = {{"-h", 0, (int32_t)PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}},
    {"-n", 0, (int32_t)PARAM_TYPE_INT32, 0, GMERR_BASE + 999999, 1, 1, 0, 0, 0, {}}};

// 定义规则
// 并发方案：不涉及并发，资源只读
static DbRuleItemT g_gmdbErrRuleItems[] = {
    {0, (int32_t)GMERR_RULE_H, {"-h"}, {true}}, {0, (int32_t)GMERR_RULE_ERRCODE, {"-n"}, {true}}};

Status GmErrAllocOptionRuleItems(DbOptionRuleT *optionRule)
{
    DB_POINTER(optionRule);
    return DbAllocOptionRuleItems(optionRule, ELEMENT_COUNT(g_gmdbErrOptionItems), ELEMENT_COUNT(g_gmdbErrRuleItems));
}

Status GmErrInitOptionItems(DbOptionRuleT *optionRule)
{
    DB_POINTER(optionRule);
    return DbInitOptionItems(optionRule, g_gmdbErrOptionItems, ELEMENT_COUNT(g_gmdbErrOptionItems));
}

Status GmErrInitRuleItems(DbOptionRuleT *optionRule)
{
    DB_POINTER(optionRule);
    return DbInitRuleItems(optionRule, g_gmdbErrRuleItems, ELEMENT_COUNT(g_gmdbErrRuleItems));
}

void GmErrPrintHelp(void)
{
    const char *gmerrOptionManual[][MANUAL_COL_NUM] = {
        {"-h", "", "print online manual"}, {"-n <errorCode>", "", "print errorCode description"}};
    DbPrintManual("[OPTION]", gmerrOptionManual, ELEMENT_COUNT(gmerrOptionManual), NULL);
}

#ifdef __cplusplus
}
#endif
