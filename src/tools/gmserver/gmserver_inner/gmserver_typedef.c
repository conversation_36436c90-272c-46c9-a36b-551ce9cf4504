/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: gmserver_typedef.c
 * Description: Option definition for starting the gmserver
 * Author: chenjunyu
 * Create: 2020-9-20
 */
#include "gmserver_typedef.h"
#include "db_option_parser.h"
#include "db_mem_context.h"
#include "db_server.h"
#include "db_version.h"  // cmake自动生成的头文件
#include "db_utils.h"
#include "adpt_string.h"
#include "db_config_file.h"
#include "db_se_trx.h"
#ifndef HPE
#include "tool_interactive.h"
#include "tool_utils.h"
#endif
#include "tool_main.h"

#ifdef __cplusplus
extern "C" {
#endif

// gmserver命令行参数校验模板, 用于校验命令行参数数据类型、参数组合约束, 在编码阶段初始化、不涉及并发读写
static const DbRuleItemT g_gmdbServerRuleItems[] = {
    {0, (int32_t)GMSRV_RULE_NO_PARA, {}, {}},
    {0, (int32_t)GMSRV_RULE_H, {"-h"}, {true}},
    {0, (int32_t)GMSRV_RULE_V, {"-v"}, {true}},
#ifdef FEATURE_RSMEM
    {0, (int32_t)GMSRV_RULE_BP, {"-b", "-p", DB_OPTION_ARG_WARMREBOOT}, {false, false, false}},
#else
    {0, (int32_t)GMSRV_RULE_BP, {"-b", "-p"}, {false, false}},
#endif
#ifndef HPE
#ifdef FEATURE_RSMEM
    {0, (int32_t)GMSRV_RULE_BPM,
        {"-b", "-p", GMSRV_OPTION_ARG_INPUT_PATH, DB_OPTION_ARG_INTEGRITY_CHECK, DB_OPTION_ARG_WARMREBOOT},
        {false, false, true, true, false}},
#else
    {0, (int32_t)GMSRV_RULE_BPM, {"-b", "-p", GMSRV_OPTION_ARG_INPUT_PATH, DB_OPTION_ARG_INTEGRITY_CHECK},
        {false, false, true, true}},
#endif
#endif
#ifdef FEATURE_PERSISTENCE
    {0, (int32_t)GMSRV_RULE_BPR, {"-b", "-p", "-r", "-c"}, {false, false, false, false}},
#endif
};

uint32_t GmServerInitOptionGetItemNum(void)
{
    uint32_t serverOptionItemsNum = GMSERVER_OPTION_BASE_NUM;
#ifdef FEATURE_RSMEM
    serverOptionItemsNum += GMSERVER_OPTION_FEATURE_RSMEM_NUM;
#endif
#ifndef HPE
    serverOptionItemsNum += GMSERVER_OPTION_NO_HPE_NUM;
#endif
#ifdef FEATURE_PERSISTENCE
    serverOptionItemsNum += GMSERVER_OPTION_FEATURE_PERSISTENCE_NUM;
#endif
    return serverOptionItemsNum;
}

Status GmServerAllocOptionRuleItems(DbOptionRuleT *optionRule)
{
    DB_POINTER(optionRule);
    return DbAllocOptionRuleItems(optionRule, GmServerInitOptionGetItemNum(), ELEMENT_COUNT(g_gmdbServerRuleItems));
}

// 注册选项
// {"-h", 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}},
// {"-v", 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}},
// {"-b", 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}},
// {"-p", 0, PARAM_TYPE_STR, 0, PATH_MAX, 1, 1, 0, 0, 0, {}},
// #ifdef FEATURE_RSMEM
// {"-rb", 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}},
// #ifndef HPE
// {"-i", 0, PARAM_TYPE_STR, 1, DB_MAX_PATH, 1, 1, 0, 0, 0, {}},
// {"-ic", 0, PARAM_TYPE_STR, 1, MAX_COMMAND_LENGTH, 1, 1, 0, 0, 0, {}},
// #ifdef FEATURE_PERSISTENCE
// {"-r", 0, PARAM_TYPE_STR, 0, DB_MAX_PATH, 1, 1, 0, 0, 0, {}},
// {"-c", 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}}
Status DbInitServerOptionItems(DbOptionRuleT *optionRule, uint32_t num)
{
    DB_POINTER(optionRule);
    if (num > OPTION_ITEM_MAX_NUM) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    DbOptionItemT *tmpOptionItems = optionRule->optionItems;
    *tmpOptionItems = (DbOptionItemT){"-h", 0, PARAM_TYPE_NULL, 0, 0, 0, 0, false, false, 0, {}};

    tmpOptionItems += 1;
    *tmpOptionItems = (DbOptionItemT){"-v", 0, PARAM_TYPE_NULL, 0, 0, 0, 0, false, false, 0, {}};

    tmpOptionItems += 1;
    *tmpOptionItems = (DbOptionItemT){"-b", 0, PARAM_TYPE_NULL, 0, 0, 0, 0, false, false, 0, {}};

    tmpOptionItems += 1;
    *tmpOptionItems = (DbOptionItemT){"-p", 0, PARAM_TYPE_STR, 0, PATH_MAX, 1, 1, false, false, 0, {}};

#ifdef FEATURE_RSMEM
    tmpOptionItems += 1;
    *tmpOptionItems = (DbOptionItemT){DB_OPTION_ARG_WARMREBOOT, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, false, false, 0, {}};
#endif
#ifndef HPE
    tmpOptionItems += 1;
    *tmpOptionItems =
        (DbOptionItemT){GMSRV_OPTION_ARG_INPUT_PATH, 0, PARAM_TYPE_STR, 1, DB_MAX_PATH, 1, 1, false, false, 0, {}};

    tmpOptionItems += 1;
    *tmpOptionItems = (DbOptionItemT){
        DB_OPTION_ARG_INTEGRITY_CHECK, 0, PARAM_TYPE_STR, 1, MAX_COMMAND_LENGTH, 1, 1, false, false, 0, {}};
#endif
#ifdef FEATURE_PERSISTENCE
    tmpOptionItems += 1;
    *tmpOptionItems = (DbOptionItemT){"-r", 0, PARAM_TYPE_STR, 0, DB_MAX_PATH, 1, 1, false, false, 0, {}};

    tmpOptionItems += 1;
    *tmpOptionItems = (DbOptionItemT){"-c", 0, PARAM_TYPE_NULL, 0, 0, 0, 0, false, false, 0, {}};
#endif

    return DbInitOptionItemsEntry(optionRule, num);
}

Status GmServerInitOptionItem(DbOptionRuleT *optionRule)
{
    return DbInitServerOptionItems(optionRule, GmServerInitOptionGetItemNum());
}

static Status GmServerInitItems(DbOptionRuleT *optionRule)
{
    Status ret = GmServerInitOptionItem(optionRule);
    if (ret != GMERR_OK) {
        (void)DbPrintfDefault("Option initialization unsuccessful, mistaken code: %" PRIi32 ".\n", (int32_t)ret);
        return ret;
    }
    ret = DbInitRuleItems(optionRule, g_gmdbServerRuleItems, ELEMENT_COUNT(g_gmdbServerRuleItems));
    if (ret != GMERR_OK) {
        (void)DbPrintfDefault("Rule initialization unsuccessful, mistaken code: %" PRIi32 ".\n", (int32_t)ret);
    }
    return ret;
}

#ifndef HPE
static Status CheckConfFile(DbServerOptionT *dbServerOption)
{
    // get backup hmac
    HmacDataT backupHmac = {0};
    uint32_t backupHmacLen = 0;
    Status ret = GetHmacInFile(dbServerOption->backupFilePath, &backupHmac, &backupHmacLen);
    if (ret != GMERR_OK) {
        (void)DbPrintfDefault("Can't get the hmac from backup file.\n");
        return ret;
    }
    // get key
    char userKey[MAX_HMAC_KEY_LENGTH] = {0};
    uint32_t keyLen = 0;
    ret = TlsEnterKeyInteractive(userKey, &keyLen);
    if (ret != GMERR_OK) {
        (void)memset_s(userKey, MAX_HMAC_KEY_LENGTH, 0, MAX_HMAC_KEY_LENGTH);
        return ret;
    }
    // verify configFile
    ret = TlsVerifyFileHmac(dbServerOption->cfgFilePath, userKey, keyLen, &backupHmac);
    (void)memset_s(userKey, MAX_HMAC_KEY_LENGTH, 0, MAX_HMAC_KEY_LENGTH);
    return ret;
}
#endif

static Status GetPathByOptionName(DbOptionRuleT *optionRule, const char *optionName, char **outFilePath)
{
    char *cfgFilePath = DbGetStrParamByOptionName(optionRule, optionName);
    if (cfgFilePath != NULL) {
        uint32_t len = (uint32_t)strlen(cfgFilePath);
        if (len >= (uint32_t)PATH_MAX) {
            (void)DbPrintfDefault("File Path too long, mistaken code: %" PRIi32 ".\n", (int32_t)GMERR_INVALID_OPTION);
            return GMERR_INVALID_OPTION;
        }
        char *filePath = (char *)DB_MALLOC(len + 1);
        if (filePath == NULL) {
            (void)DbPrintfDefault("FilePath alloc unsuccessfully");
            return GMERR_OUT_OF_MEMORY;
        }
        errno_t ret = strncpy_s(filePath, len + 1, cfgFilePath, len);
        if (ret != EOK) {
            DB_FREE(filePath);
            (void)DbPrintfDefault("FilePath memcpy unsuccessfully, mistaken code: %" PRIi32 ".\n", (int32_t)ret);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        *outFilePath = filePath;
    }
    return GMERR_OK;
}

static Status GmSrvGetBP(DbOptionRuleT *optionRule, DbServerOptionT *dbServerOption)
{
    DB_POINTER2(optionRule, dbServerOption);
    if (DbCheckIsUsedByOptionName(optionRule, "-b")) {
        dbServerOption->isBackground = true;
    }
    if (DbCheckIsUsedByOptionName(optionRule, "-p")) {
        Status ret = GetPathByOptionName(optionRule, "-p", &dbServerOption->cfgFilePath);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    if (DbCheckIsUsedByOptionName(optionRule, DB_OPTION_ARG_WARMREBOOT)) {
        dbServerOption->isWarmReboot = true;
    }
    return GMERR_OK;
}

#ifdef FEATURE_PERSISTENCE
static Status GmSrvGetBPR(DbOptionRuleT *optionRule, DbServerOptionT *dbServerOption)
{
    DB_POINTER2(optionRule, dbServerOption);
    Status ret = GmSrvGetBP(optionRule, dbServerOption);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (DbCheckIsUsedByOptionName(optionRule, "-r")) {
        ret = GetPathByOptionName(optionRule, "-r", &dbServerOption->recoveryPath);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    if (DbCheckIsUsedByOptionName(optionRule, "-c")) {
        // 开启bep二进制一致性，设置为purge等待刷盘状态。业务线程开始刷盘之后才能允许purge回收
        SeSetBepState(SE_BEP_STATE_WAITING_FLUSH);
    }
    return GMERR_OK;
}
#endif

#ifndef HPE
static Status GmSrvGetBPM(DbOptionRuleT *optionRule, DbServerOptionT *dbServerOption)
{
    DB_POINTER2(optionRule, dbServerOption);

    Status ret = GmSrvGetBP(optionRule, dbServerOption);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (DbCheckIsUsedByOptionName(optionRule, GMSRV_OPTION_ARG_INPUT_PATH)) {
        ret = GetPathByOptionName(optionRule, GMSRV_OPTION_ARG_INPUT_PATH, &dbServerOption->backupFilePath);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    if (DbCheckIsUsedByOptionName(optionRule, DB_OPTION_ARG_INTEGRITY_CHECK)) {
        char *optParamsMode = DbGetStrParamByOptionName(optionRule, DB_OPTION_ARG_INTEGRITY_CHECK);
        if (DbStrCmp(STR_HMAC, optParamsMode, true) == 0) {
            dbServerOption->isHmacVrf = true;
        } else if (DbStrCmp(STR_CRC, optParamsMode, true) == 0) {
            (void)DbPrintfDefault("The CRC check mode is not supported.\n");
            return GMERR_INVALID_OPTION;
        } else {
            PRINT_ERROR(DbPrintfDefault, "Invalid param of option '-m': %s.", optParamsMode);
            return GMERR_INVALID_OPTION;
        }
    }
    if (dbServerOption->backupFilePath != NULL && dbServerOption->isHmacVrf) {
        dbServerOption->cfgFilePath =
            (dbServerOption->cfgFilePath == NULL) ? DB_DEFAULT_CFG_PATH : dbServerOption->cfgFilePath;
        ret = CheckConfFile(dbServerOption);
        if (ret != GMERR_OK) {
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret,
                "Check HMAC code failed, make sure the key is right, or configuration file may be modified: "
                "conf file path %s, backup file path %s. ret = %" PRIi32,
                dbServerOption->cfgFilePath, dbServerOption->backupFilePath, (int32_t)ret);
            return ret;
        }
    }
    return GMERR_OK;
}
#endif

Status GmServerInit(DbOptionRuleT *optionRule, DbServerOptionT *dbServerOption, int32_t argc, char *argv[])
{
    DB_POINTER2(optionRule, dbServerOption);
    Status ret = GmServerInitItems(optionRule);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DbInitOptionParam(optionRule, argc, argv);
    if (ret != GMERR_OK) {
        (void)DbPrintfDefault("Wrong options or parameters, mistaken code: %" PRIi32 ".\n", (int32_t)ret);
        return ret;
    }
    int32_t startupRule = DbGetStartupRule(optionRule);
    if (startupRule == OPTION_NOT_FOUND) {
        (void)DbPrintfDefault("Invalid option, please use 'gmserver -h' for more information.\n");
        return GMERR_SYNTAX_ERROR;
    }
    switch (startupRule) {
        case GMSRV_RULE_NO_PARA:
            break;
        case GMSRV_RULE_H:
            GmSrvGetHelp();
            return GMERR_OK;
        case GMSRV_RULE_V:
            GmSrvGetVersion();
            return GMERR_OK;
        case GMSRV_RULE_BP:
            ret = GmSrvGetBP(optionRule, dbServerOption);
            break;
#ifndef HPE
        case GMSRV_RULE_BPM:
            ret = GmSrvGetBPM(optionRule, dbServerOption);
            break;
#endif
#ifdef FEATURE_PERSISTENCE
        case GMSRV_RULE_BPR:
            ret = GmSrvGetBPR(optionRule, dbServerOption);
            break;
#endif
        default:
            return GMERR_DATA_EXCEPTION;
    }
    dbServerOption->needStartServer = true;
    return ret;
}

int32_t GmServerMain(int32_t argc, char *argv[], bool isLoopInside)
{
    // 用于区分共进程模式下的客户端和服务端线程
    DbSetServerThreadFlag();
    DbOptionRuleT optionRule = {0};
    Status ret = GmServerAllocOptionRuleItems(&optionRule);
    if (ret != GMERR_OK) {
        DbClearServerThreadFlag();
        return ret;
    }
    DbServerOptionT dbServerOption = {0};
    ret = GmServerInit(&optionRule, &dbServerOption, argc, argv);
    DbReleaseOptionRuleItems(&optionRule);
#if defined(FEATURE_TS) && !defined(FEATURE_STREAM)
    DbAdptMallocTrim(0);
#endif
    if (ret != GMERR_OK || !dbServerOption.needStartServer) {
        DbClearServerThreadFlag();
        DbServerFreeFilePathMem(&dbServerOption);
        return ret;
    }
    dbServerOption.isLoopInside = isLoopInside;
    ret = DbServerStart(&dbServerOption);
    DbClearServerThreadFlag();
    return ret;
}

void GmServerStop(void)
{
    DbSetServerThreadFlag();
    DbServerCleanAfterInitFail("Service manager", GMERR_OK, true, NULL);
    DbClearServerThreadFlag();
}

void GmSrvGetHelp(void)
{
    const char *gmserverOptionManual[][MANUAL_COL_NUM] = {
        {"-h", "", "print online manual"},
        {"-v", "", "print the gmserver version"},
        {"-b", "", "start gmserver in background process"},
        {"-p", "<config_file>", "configuration file path"},
#ifdef FEATURE_RSMEM
        {DB_OPTION_ARG_WARMREBOOT, "", "booting in warmreboot mode"},
#endif
#ifndef HPE
        {GMSRV_OPTION_ARG_INPUT_PATH, "<input_path>", "backup configuration file input path"},
        {DB_OPTION_ARG_INTEGRITY_CHECK, "<integrity_check>", "file integrity check mode"},
#endif
#ifdef FEATURE_PERSISTENCE
        {"-r", "<path>", "recovery dir path"},
        {"-c", "", "enable binary equivalence project while flushing"},
#endif
    };
    DbPrintManual("[OPTION]", gmserverOptionManual, ELEMENT_COUNT(gmserverOptionManual), NULL);
}

void GmSrvGetVersion(void)
{
    const char *gmserverVersion[][MANUAL_COL_NUM] = {
        {GMDB_V5_CODE_VERSION, "", ""},
        {"Copyright (c) Huawei Technologies Co.", "", ""},
    };
    DbPrintManual("[VERSION]", gmserverVersion, ELEMENT_COUNT(gmserverVersion), NULL);
}

#ifdef FEATURE_CLT_SERVER_SAME_PROCESS
inline int32_t GmsServerMain(int32_t argc, char *argv[])
{
    // 防止共进程模式下，业务侧同时创建多个服务端线程
    if (!DbSetServerSameProcessStartFlag()) {
        return GMERR_DUPLICATE_OBJECT;
    }
    Status ret = GmServerMain(argc, argv, false);
    if (ret != GMERR_OK) {
        DbClearServerSameProcessStartFlag();
        return ret;
    }
    return ret;
}

inline int32_t GmsShmemClear(void)
{
    uint16_t instanceId = DbGetProcGlobalId();
    if (instanceId == DB_MAX_UINT16) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Invalid instanceId");
        return GMERR_DATA_EXCEPTION;
    }
    Status ret = DbDestroyTopShmemCtx(instanceId);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Shmem clear unsucc");
    } else {
        DB_LOG_WARN(GMERR_OK, "Shmem clear finished");
    }
    return ret;
}
#endif

#ifdef __cplusplus
}
#endif
