/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2020. All rights reserved.
 * File Name: gmserver_typedef.h
 * Description: Header file for gmserver typedef.
 * Author: chenjunyu
 * Create: 2020-9-20
 */

#ifndef GMSERVER_TYPEDEF_H
#define GMSERVER_TYPEDEF_H

#include "adpt_types.h"
#include "db_option_parser.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define GMSERVER_OPTION_BASE_NUM 4
#define GMSERVER_OPTION_FEATURE_RSMEM_NUM 1
#define GMSERVER_OPTION_NO_HPE_NUM 2
#define GMSERVER_OPTION_FEATURE_PERSISTENCE_NUM 2

typedef enum GmSrvStartupRule {
    GMSRV_RULE_NO_PARA = 0,
    GMSRV_RULE_H,
    GMSRV_RULE_V,
    GMSRV_RULE_BP,
#ifndef HPE
    GMSRV_RULE_BPM,
#endif
#ifdef FEATURE_PERSISTENCE
    GMSRV_RULE_BPR,
#endif
    GMSRV_RULE_BUTT
} GmSrvStartupRuleE;

#define DB_OPTION_ARG_WARMREBOOT "-rb"

#ifndef HPE
#define GMSRV_OPTION_ARG_INPUT_PATH "-i"
#endif

Status GmServerAllocOptionRuleItems(DbOptionRuleT *optionRule);
void GmSrvGetHelp(void);
void GmSrvGetVersion(void);
void GmServerStop(void);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
