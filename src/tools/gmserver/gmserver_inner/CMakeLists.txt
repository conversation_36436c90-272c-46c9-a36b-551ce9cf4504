project(gmserverFuzz)

include_sub_directories_recursively(${CMAKE_CURRENT_SOURCE_DIR})
include_sub_directories_recursively(${GMDB_CLIENT_PATH})

aux_source_directory(${gmserver_SOURCE_DIR}/gmserver_inner SRC_GMSERVER_LIST)

add_library(gmserverFuzz SHARED ${SRC_GMSERVER_LIST})

if(MODULARBUILD)
    target_link_libraries(gmserverFuzz gmservice)
else()
    target_link_libraries(gmserverFuzz gmdb ${TOOL_NAME})
endif()

install(TARGETS gmserverFuzz LIBRARY DESTINATION lib)
