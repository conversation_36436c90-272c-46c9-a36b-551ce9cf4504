
project(gmserver)

if (FUZZ OR COVERAGE)
    ADD_SUBDIRECTORY(gmserver_inner)
endif()

#注意：只有在编译可执行文件时才需要打开解析递归依赖选项ADD_COMPILE_OPTIONS(-Wl,--copy-dt-needed-entries)
ADD_COMPILE_OPTIONS(-Wl,--copy-dt-needed-entries -fPIE)
# -fstack-protector-strong 只有源代码函数里有以下2种代码之一才会插入保护代码: A:  局部数组 B:  局部变量addr作为赋值语句的右值或函数参数 ，gmserver中都没有，扫描工具会误报
ADD_COMPILE_OPTIONS(-fstack-protector-all)


#当前模块的头文件目录相对路径
#其他依赖模块的头文件目录按需添加
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/gmserver_inner)

aux_source_directory(${gmserver_SOURCE_DIR} SRC_GMSERVER_LIST)
aux_source_directory(${gmserver_SOURCE_DIR}/gmserver_inner SRC_GMSERVER_INNER_LIST)

if(HPE)
    include_directories(${CMAKE_CURRENT_SOURCE_DIR}/gmserver_hpe)
    aux_source_directory(${gmserver_SOURCE_DIR}/gmserver_hpe SRC_GMSERVER_HPE_LIST)
    link_directories(${HPE_PATH}/lib)
endif()

link_directories(${LIBRARY_OUTPUT_PATH} ${HWSECUREC_LIB_PATH})

if (TOOLS_EMBED_SO)
    generate_tools_embed_so(gmserver_embed "${SRC_GMSERVER_INNER_LIST}" "${TOOL_NAME}")
endif()

# 编译patch_obj不编译可执行程序
if(COMPILE_PATCH_OBJ)
  return()
endif()

#设备上ONLY_TS场景需要与V3共存，避免冲突需要改名
#MULTI_TS场景需要与V5共存，避免冲突需要改名
set(SERVER_NAME gmserver)
if(TS_RENAME_BIN)
    set(SERVER_NAME gmserver_ts)
endif()

add_executable(${SERVER_NAME} ${SRC_GMSERVER_LIST} ${SRC_GMSERVER_INNER_LIST} ${SRC_GMSERVER_HPE_LIST})
if(STRIP)
    separate_debug_info(${SERVER_NAME})
endif()

if(MODULARBUILD)
    target_link_libraries(${SERVER_NAME} gmservice)
else()
    if(HPE)
        target_link_libraries(${SERVER_NAME} ${GMDB_NAME})
    else()
        target_link_libraries(${SERVER_NAME} ${TOOL_NAME})
    endif()
endif()
install(TARGETS ${SERVER_NAME} RUNTIME DESTINATION bin)

# 双实例场景需要拷贝一个gmserver_ts,直接多install一次并改名
if (TS_MULTI_INST)
    install(CODE "
        file(INSTALL ${CMAKE_INSTALL_PREFIX}bin/${SERVER_NAME} DESTINATION ${CMAKE_INSTALL_PREFIX}bin RENAME gmserver_ts
        USE_SOURCE_PERMISSIONS)
        "
    )
endif()
