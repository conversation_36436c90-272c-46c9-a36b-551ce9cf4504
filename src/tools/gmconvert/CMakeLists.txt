project(gmconvert)
if (FUZZ OR COVERAGE)
    ADD_SUBDIRECTORY(gmconvert_inner)
endif()
ADD_COMPILE_OPTIONS(-Wl,--copy-dt-needed-entries -fPIE)

include_directories(${GMDB_ROOT_DIR}/src/tools/include)
include_directories(${GMDB_ROOT_DIR}/src/compiler/include)
include_directories(${GMDB_ROOT_DIR}/src/adapter/include)
include_directories(${GMDB_ROOT_DIR}/src/datamodel/include)
include_directories(${GMDB_ROOT_DIR}/src/datamodel/include/reservedmem)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/gmconvert_inner)
include_sub_directories_recursively(${GMDB_ROOT_DIR}/src/common/include)

aux_source_directory(. SRC_MAIN_LIST)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/gmconvert_inner SRC_GMCONVERT_LIST)

# 光启业务暂不用datalog静态工具，不生成 gmconvert_embed.so. 故暂不处理 TOOLS_EMBED_SO 的场景

# 编译patch_obj不编译可执行程序
if(COMPILE_PATCH_OBJ)
  return()
endif()

if (NOT TOOLS_EMBED_SO)
add_executable(gmconvert ${SRC_MAIN_LIST} ${SRC_GMCONVERT_LIST})

LINK_DIRECTORIES(${GMDB_BUILD_LIB_DIR})

target_link_libraries(gmconvert gmadapterStatic gmcommonStatic gmdatamodelStatic gmdatalogStatic)

install(TARGETS gmconvert
   DESTINATION bin
)
endif()
