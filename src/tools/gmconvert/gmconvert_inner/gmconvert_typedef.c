/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: source file for convert typedef
 * Author: piyejian
 * Create: 2022-10-29
 */

#include "gmconvert_typedef.h"
#include "adpt_locator.h"
#include "db_option_parser.h"
#include "tool_utils.h"

#ifdef __cplusplus
extern "C" {
#endif

static DbOptionItemT g_gmdbConvertOptionItems[] = {{DB_OPTION_ARG_HELP, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}},
    {DB_OPTION_ARG_VERSION, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}},
    {CONVT_OPTION_ARG_INPUT_PATH, 0, PARAM_TYPE_STR, 1, DB_MAX_PATH, 1, 1, 0, 0, 0, {}},
    {CONVT_OPTION_ARG_UPGRADE_PATH, 0, PARAM_TYPE_STR, 1, DB_MAX_PATH, 2, 2, 0, 0, 0, {}},
    {CONVT_OPTION_ARG_OUTPUT_PATH, 0, PARAM_TYPE_STR, 1, DB_MAX_PATH, 1, 1, 0, 0, 0, {}}};

static DbRuleItemT g_gmdbConvertRuleItems[] = {{0, (int32_t)GMCONVT_RULE_HELP, {DB_OPTION_ARG_HELP}, {true}},
    {0, (int32_t)GMCONVT_RULE_VERSION, {DB_OPTION_ARG_VERSION}, {true}},
    {0, (int32_t)GMCONVT_RULE_CONVERT_DATALOG, {CONVT_OPTION_ARG_INPUT_PATH, CONVT_OPTION_ARG_OUTPUT_PATH},
        {true, false}},
    {0, (int32_t)GMCONVT_RULE_CONVERT_UPGRADE_DATALOG, {CONVT_OPTION_ARG_UPGRADE_PATH, CONVT_OPTION_ARG_OUTPUT_PATH},
        {true, false}}};

Status GmConvtAllocOptionRuleItems(DbOptionRuleT *optionRule)
{
    DB_POINTER(optionRule);
    return DbAllocOptionRuleItems(
        optionRule, ELEMENT_COUNT(g_gmdbConvertOptionItems), ELEMENT_COUNT(g_gmdbConvertRuleItems));
}

Status GmConvtInit(DbOptionRuleT *optionRule, int32_t argc, char *argv[])
{
    DB_POINTER(optionRule);
    Status ret =
        DbInitOptionItems(optionRule, g_gmdbConvertOptionItems, (int32_t)ELEMENT_COUNT(g_gmdbConvertOptionItems));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DbInitRuleItems(optionRule, g_gmdbConvertRuleItems, (int32_t)ELEMENT_COUNT(g_gmdbConvertRuleItems));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DbInitOptionParam(optionRule, argc, argv);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

// clang-format off
void GmConvtGetHelp(void)
{
    const char *convertOptionManual[][MANUAL_COL_NUM] = {
        {DB_OPTION_ARG_HELP, "", "print the online help manual"},
        {DB_OPTION_ARG_VERSION, "", "print the gmconvert version"},
        {CONVT_OPTION_ARG_INPUT_PATH, "<input_path>", "input datalog file path"},
        {CONVT_OPTION_ARG_UPGRADE_PATH, "<old_file_name> <patch_file_name>", 
            "Input old datalog file name and input patch datalog file name."},
        {CONVT_OPTION_ARG_OUTPUT_PATH, "<output_path>", "output directory path to store gmjson file"}};
    DbPrintManual("[OPTION]", convertOptionManual, (int32_t)ELEMENT_COUNT(convertOptionManual), NULL);
}
// clang-format on

void GmConvtGetVersion(void)
{
    const char *gmconvertVersion[][MANUAL_COL_NUM] = {
        {"gmconvert GMDBV5", "", ""},
        {"Copyright (c) Huawei Technologies Co.", "", ""},
    };
    DbPrintManual("[VERSION]", gmconvertVersion, (int32_t)ELEMENT_COUNT(gmconvertVersion), NULL);
}

#ifdef __cplusplus
}
#endif
