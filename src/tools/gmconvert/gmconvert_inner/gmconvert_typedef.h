/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: head file for convert typedef
 * Author: piyejian
 * Create: 2022-10-29
 */

#ifndef GMCONVERT_TYPEDEF_H
#define GMCONVERT_TYPEDEF_H

#include "gmc_errno.h"
#include "cpl_dtl_offline_compiler.h"
#include "adpt_types.h"
#include "db_option_parser.h"
#include "db_mem_context.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define CONVT_OPTION_ARG_INPUT_PATH "-i"
#define CONVT_OPTION_ARG_UPGRADE_PATH "-upgrade"
#define CONVT_OPTION_ARG_OUTPUT_PATH "-o"

typedef enum GmImpStartupRule {
    GMCONVT_RULE_HELP = 0,
    GMCONVT_RULE_VERSION,
    GMCONVT_RULE_CONVERT_DATALOG,
    GMCONVT_RULE_CONVERT_UPGRADE_DATALOG,
    GMCONVT_RULE_BOTTOM
} GmConvertStartupRuleE;

typedef struct ConvertArgInfo {
    char inputPath[DB_MAX_PATH];
    char deltaFilePath[DB_MAX_PATH];
    char outputPath[DB_MAX_PATH];
} ConvertArgumentT;

Status GmConvtAllocOptionRuleItems(DbOptionRuleT *optionRule);
Status GmConvtInit(DbOptionRuleT *optionRule, int32_t argc, char *argv[]);
void GmConvtGetHelp(void);
void GmConvtGetVersion(void);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
