project(gmlog)

if (FUZZ OR COVERAGE)
    ADD_SUBDIRECTORY(gmlog_inner)
endif()

ADD_COMPILE_OPTIONS(-Wl,--copy-dt-needed-entries -fPIE)

include_sub_directories_recursively(${CMAKE_CURRENT_SOURCE_DIR})
include_sub_directories_recursively(${GMDB_CLIENT_PATH})

aux_source_directory(. SRC_MAIN_LIST)
aux_source_directory(${gmlog_SOURCE_DIR}/gmlog_inner SRC_GMLOG_LIST)

if (TOOLS_EMBED_SO)
  generate_tools_embed_so(gmlog_embed "${SRC_GMLOG_LIST}" "${TOOL_NAME}")
endif()

# 编译patch_obj不编译可执行程序
if(COMPILE_PATCH_OBJ)
  return()
endif()

add_executable (gmlog ${SRC_MAIN_LIST} ${SRC_GMLOG_LIST})
if(STRIP)
  separate_debug_info(gmlog)
endif()

target_link_libraries(gmlog ${TOOL_NAME})

install(TARGETS gmlog
  RUNTIME DESTINATION bin
)
