/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: export tool source file
 * Author: chenjunyu
 * Create: 2021-3-9
 */

#include "tool_main.h"
#include "gmexport_typedef.h"
#include "db_option_parser.h"
#include "adpt_memory.h"
#include "adpt_locator.h"
#include "adpt_string.h"
#include "adpt_define.h"
#include "tool_utils.h"
#include "tool_interactive.h"
#include "clt_stmt.h"
#include "tool_thread_pool.h"
#include "gmexport_file.h"
#ifdef TS_MULTI_INST
#include "gmexport_ts.h"
#endif

#ifdef __cplusplus
extern "C" {
#endif

#define GMEXP_MIN_THREAD_NUM 1
#define GMEXP_MAX_THREAD_NUM 10
#define GMEXP_DEFAULT_THREAD_NUM 2

static Status ExpOptionProcNameSpace(DbOptionRuleT *optionRule, ExportOptionT *toolOption)
{
    if (!DbCheckIsUsedByOptionName(optionRule, DB_OPTION_ARG_NAMESPACE)) {
        // -ns 为可选参数，如果不指定namespace，默认为public,public进行TlsUseNameSpace默认成功
        toolOption->namespaceName[0] = '\0';
        return GMERR_OK;
    }
    char *optParams = DbGetStrParamByOptionName(optionRule, DB_OPTION_ARG_NAMESPACE);
    if (optParams == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_OPTION, "-ns option params is null.");  // LCOV_EXCL_LINE
        return GMERR_INVALID_OPTION;
    }
    int32_t ret = strcpy_s(toolOption->namespaceName, sizeof(toolOption->namespaceName), optParams);
    return (ret != EOK) ? GMERR_FIELD_OVERFLOW : GMERR_OK;
}

static Status ExpOptionProcDominName(DbOptionRuleT *optionRule, ExportOptionT *toolOption)
{
    int32_t ret;
    // 不指定 -s参数
    if (!DbCheckIsUsedByOptionName(optionRule, DB_OPTION_ARG_DOMAIN_NAME)) {
        ret = strcpy_s(toolOption->domainName, sizeof(toolOption->domainName), DB_DEFAULT_DOMAIN_NAME);
        return (ret != EOK) ? GMERR_FIELD_OVERFLOW : GMERR_OK;
    }
    char *optParams = DbGetStrParamByOptionName(optionRule, DB_OPTION_ARG_DOMAIN_NAME);
    if (optParams == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_OPTION, "-s option params is null.");  // LCOV_EXCL_LINE
        return GMERR_INVALID_OPTION;
    }
    // 校验-s参数的范围
    if ((size_t)(strlen(optParams)) > (size_t)DOMAIN_NAME_MAX_LEN) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "Domain: '%s' has longer len than max.", optParams);
        // LCOV_EXCL_STOP
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    // V3的serverLocator改成V5默认
    if (strlen(optParams) != 0 && DbStrCmp(optParams, SHM_UNIX_EMSERVER, 0) == 0) {
        ret = strcpy_s(toolOption->domainName, sizeof(toolOption->domainName), DB_DEFAULT_DOMAIN_NAME);
    } else {
        ret = strcpy_s(toolOption->domainName, sizeof(toolOption->domainName), optParams);
    }
    return (ret != EOK) ? GMERR_FIELD_OVERFLOW : GMERR_OK;
}

static Status ExpOptionProcUserName(DbOptionRuleT *optionRule, ExportOptionT *toolOption)
{
    char *optParams = DbGetStrParamByOptionName(optionRule, DB_OPTION_ARG_USER_NAME);
    if (optParams == NULL) {
        toolOption->userName[0] = '\0';
        return GMERR_OK;
    }
    int32_t ret = strcpy_s(toolOption->userName, sizeof(toolOption->userName), optParams);
    return (ret != EOK) ? GMERR_FIELD_OVERFLOW : GMERR_OK;
}

#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
Status TlsOptionParseStoreType(const char *toolsName, DbOptionRuleT *optionRule, ExportLabelOptionT *labelOption)
{
    DB_POINTER3(toolsName, optionRule, labelOption);
    char *storeType = DbGetStrParamByOptionName(optionRule, EXP_OPTION_ARG_STORE_TYPE);
    if (storeType == NULL) {
        // should not be here
        DB_LOG_ERROR(GMERR_INVALID_OPTION, "-st option params is null.");  // LCOV_EXCL_LINE
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (DbStrCmp("disk", storeType, false) == 0) {
        labelOption->isWarmRebootStoreDisk = true;
    } else {
        (void)DbPrintfDefault("unexpected option(\"-st\") param for %s.\n", toolsName);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

Status TlsOptionParseThreadNum(const char *toolsName, DbOptionRuleT *optionRule, ExportLabelOptionT *labelOption)
{
    DB_POINTER3(toolsName, optionRule, labelOption);
    if (!DbCheckIsUsedByOptionName(optionRule, DB_OPTION_ARG_THREAD_NUM)) {
        labelOption->threadNum = GMEXP_DEFAULT_THREAD_NUM;
        return GMERR_OK;
    }
    DbOptionItemT *optionItem = DbGetOptionItemByName(optionRule, DB_OPTION_ARG_THREAD_NUM);
    if (optionItem == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_OPTION, "-tn in %s.", toolsName);  // LCOV_EXCL_LINE
        return GMERR_INVALID_OPTION;
    }
    uint32_t threadNum = optionItem->params[0].paramVal[0].uintVal;
    if (GMEXP_MIN_THREAD_NUM <= threadNum && threadNum <= GMEXP_MAX_THREAD_NUM) {
        labelOption->threadNum = threadNum;
    } else {
        (void)DbPrintfDefault("unexpected thread num for %s.\n", toolsName);  // LCOV_EXCL_LINE
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}
#endif

static Status ExportSetCmdType(DbOptionRuleT *optionRule, ExportFileTypeE *cmdType)
{
    DB_POINTER(optionRule);
    char *optParams = DbGetStrParamByOptionName(optionRule, EXP_OPTION_ARG_OPERATION);
    if (optParams == NULL) {
        // should not be here
        DB_LOG_ERROR(GMERR_INVALID_OPTION, "-c option params is null.");  // LCOV_EXCL_LINE
        return GMERR_INVALID_PARAMETER_VALUE;
    } else if (DbStrCmp("vdata", optParams, true) == 0) {
        *cmdType = GMEXP_CMD_VDATA;
    } else if (DbStrCmp("exp_data", optParams, true) == 0) {
        *cmdType = GMEXP_CMD_VDATA;
    } else if (DbStrCmp("respool", optParams, true) == 0) {
        *cmdType = GMEXP_CMD_RESPOOL;
    } else if (DbStrCmp("kvdata", optParams, true) == 0) {
        *cmdType = GMEXP_CMD_KVDATA;
    } else if (DbStrCmp("exp_kv", optParams, true) == 0) {
        *cmdType = GMEXP_CMD_KVDATA;
    } else if (DbStrCmp("conf", optParams, true) == 0) {
        *cmdType = GMEXP_CMD_CONF;
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
    } else if (DbStrCmp("bin_data", optParams, true) == 0) {
        *cmdType = GMEXP_CMD_BINDATA;
#endif
#ifdef TS_MULTI_INST
    } else if (DbStrCmp("systbl_data", optParams, true) == 0) {
        *cmdType = GMEXP_CMD_TS_SYSTBL;
#endif
    } else {
        *cmdType = GMEXP_CMD_BOTTOM;
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(
            DbPrintfDefault, GMERR_INVALID_PARAMETER_VALUE, "unexpected option(\"-c\") param for gmexport.\n");
        // LCOV_EXCL_STOP
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
Status BinDataOptionParseDigestInfo(const char *toolsName, DbOptionRuleT *optionRule, TlsDigestInfoT *digest)
{
    DB_POINTER3(toolsName, optionRule, digest);
    digest->algorithm = DB_DIGEST_ALGORITHM_CRC;
    DbOptionItemT *option = DbGetOptionItemByName(optionRule, DB_OPTION_ARG_RELIABILITY_CHECK);
    if (option == NULL) {
        DB_LOG_ERROR(GMERR_INVALID_OPTION, "-rc in %s.", toolsName);  // LCOV_EXCL_LINE
        return GMERR_INVALID_OPTION;
    }
    char *algorithm = option->params[0].paramVal[0].strVal;
    if (algorithm == NULL || strlen(algorithm) == 0 || DbStrCmp("crc", algorithm, true) == 0) {
        return GMERR_OK;
    }
    if (DbStrCmp("none", algorithm, true) == 0) {
        digest->algorithm = DB_DIGEST_ALGORITHM_BUTT;
    } else {
        (void)DbPrintfDefault("unexpected option(\"-rc\") param for %s.\n", toolsName);  // LCOV_EXCL_LINE
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}
#endif

static Status ExportInitBasicOption(DbOptionRuleT *optionRule, ExportOptionT *expOption)
{
    Status ret = ExpOptionProcNameSpace(optionRule, expOption);
    // -s
    if (ret == GMERR_OK) {
        ret = ExpOptionProcDominName(optionRule, expOption);
    }
    if (ret == GMERR_OK) {
        ret = ExpOptionProcUserName(optionRule, expOption);
    }

    if (ret == GMERR_OK) {
        ret = DbSetStrOptionIfUsed(
            optionRule, expOption->labelOption.filePath, DB_MAX_PATH, EXP_OPTION_ARG_FILE_PATH, NULL);
    }
    if (ret == GMERR_OK) {
        ret = ExportSetCmdType(optionRule, &expOption->labelOption.cmdType);
    }
#if !defined(WARM_REBOOT) && !defined(FEATURE_RSMEM)
    if (ret == GMERR_OK) {
        ret = TlsOptionParseDigestInfo("gmexport", optionRule, &expOption->labelOption.digest);
    }
#else
    if (ret == GMERR_OK && expOption->labelOption.cmdType != GMEXP_CMD_BINDATA) {
        ret = TlsOptionParseDigestInfo("gmexport", optionRule, &expOption->labelOption.digest);
    }
    if (ret == GMERR_OK && expOption->labelOption.cmdType == GMEXP_CMD_BINDATA) {
        if (!DbDirExist(expOption->labelOption.filePath)) {
            return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
        }
        ret = BinDataOptionParseDigestInfo("gmexport", optionRule, &expOption->labelOption.digest);
        if (ret == GMERR_OK) {
            ret = TlsOptionParseStoreType("gmexport", optionRule, &expOption->labelOption);
        }
        if (ret == GMERR_OK) {
            ret = TlsOptionParseThreadNum("gmexport", optionRule, &expOption->labelOption);
        }
    }
#endif
    return ret;
}

static Status ExportGetTableName(DbOptionRuleT *optionRule, ExportOptionT *expOption)
{
    DB_POINTER2(optionRule, expOption);
    Status ret = GMERR_OK;
#ifdef TS_MULTI_INST
    if (expOption->labelOption.cmdType != GMEXP_CMD_KVDATA && expOption->labelOption.cmdType != GMEXP_CMD_TS_SYSTBL) {
#else
    if (expOption->labelOption.cmdType != GMEXP_CMD_KVDATA) {
#endif
        ret = DbCheckOptionAndSet(
            optionRule, expOption->labelOption.labelName, MAX_TABLE_NAME_LEN, EXP_OPTION_ARG_TABLE_NAME);
        if (ret != GMERR_OK) {
            TOOL_RUN_ERROR(DbPrintfDefault, ret, "unexpected gmexport parameter. ret = %d", ret);  // LCOV_EXCL_LINE
            return ret;
        }
    }
    ret = DbSetStrOptionIfUsed(
        optionRule, expOption->labelOption.labelName, MAX_TABLE_NAME_LEN, EXP_OPTION_ARG_TABLE_NAME, NULL);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (strlen(expOption->labelOption.labelName) == 0) {
        errno_t error = strncpy_s(
            expOption->labelOption.labelName, MAX_TABLE_NAME_LEN, GLOBAL_KV_TABLE_NAME, strlen(GLOBAL_KV_TABLE_NAME));
        ret = (error == EOK) ? GMERR_OK : GMERR_FIELD_OVERFLOW;
    }
    return ret;
}

static Status ExportInitSchemaOption(DbOptionRuleT *optionRule, ExportOptionT *expOption)
{
    Status ret = ExportInitBasicOption(optionRule, expOption);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = ExportGetTableName(optionRule, expOption);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (DbStrCmp(expOption->labelOption.labelName, "all", true) == 0) {
        expOption->labelOption.isAll = true;
    } else {
        expOption->labelOption.isAll = false;
    }
    return GMERR_OK;
}

#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
static Status ExportInitBinDataOption(DbOptionRuleT *optionRule, ExportOptionT *expOption)
{
    Status ret = ExportInitBasicOption(optionRule, expOption);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (expOption->labelOption.digest.algorithm == DB_DIGEST_ALGORITHM_HMAC) {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "unexpected gmexport check parameter. ret = %d", GMERR_INVALID_OPTION);
        // LCOV_EXCL_STOP
        return GMERR_INVALID_OPTION;
    }
    return GMERR_OK;
}
#endif

#ifdef TS_MULTI_INST
static Status ExportInitTsSysTblOption(DbOptionRuleT *optionRule, ExportOptionT *tOpt)
{
    Status ret = ExportInitBasicOption(optionRule, tOpt);
    // 校验命令格式
    if (ret != GMERR_OK || tOpt->labelOption.cmdType != GMEXP_CMD_TS_SYSTBL) {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_OPTION,
            "invalid option, ts only support gmexport -c systbl_data -f <dir> format, ret = %" PRId32,
            (int32_t)GMERR_INVALID_OPTION);
        // LCOV_EXCL_STOP
        return GMERR_INVALID_OPTION;
    }

    // 校验路径长度（最后生成的文件全路径为 /xxx/xxx/文件名.bindata+'\0' 不能超过 256 字节）
    if (DM_STR_LEN(tOpt->labelOption.filePath) > TS_EXPORT_DIR_PATH_LENGTH) {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_PARAMETER_VALUE,
            "Import file path exceeds limit: %" PRIu32 ", ret = %" PRId32, TS_EXPORT_DIR_PATH_LENGTH,
            (int32_t)GMERR_INVALID_PARAMETER_VALUE);
        // LCOV_EXCL_STOP
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}
#endif

static Status ExportInitRespoolOption(DbOptionRuleT *optionRule, ExportOptionT *expOption)
{
    Status ret = ExportInitBasicOption(optionRule, expOption);
    if (ret != GMERR_OK) {
        return ret;
    }
    return DbCheckOptionAndSet(
        optionRule, expOption->labelOption.resPoolName, MAX_RES_POOL_NAME_LEN, EXP_OPTION_ARG_RES_POOL_NAME);
}

static Status ExportInitBackupConfOption(DbOptionRuleT *optionRule, ExportOptionT *expOption)
{
    Status ret = ExpOptionProcNameSpace(optionRule, expOption);
    if (ret == GMERR_OK) {
        ret = DbSetStrOptionIfUsed(
            optionRule, expOption->domainName, DOMAIN_NAME_MAX_LEN, DB_OPTION_ARG_DOMAIN_NAME, DB_DEFAULT_DOMAIN_NAME);
    }
    if (ret == GMERR_OK) {
        ret = DbCheckOptionAndSet(optionRule, expOption->labelOption.filePath, DB_MAX_PATH, EXP_OPTION_ARG_INPUT_PATH);
    }
    if (ret == GMERR_OK) {
        ret = DbSetStrOptionIfUsed(
            optionRule, expOption->labelOption.outputPath, DB_MAX_PATH, EXP_OPTION_ARG_OUTPUT_PATH, NULL);
    }
    if (ret == GMERR_OK) {
        ret = ExportSetCmdType(optionRule, &expOption->labelOption.cmdType);
    }
    if (ret != GMERR_OK) {
        return ret;
    }
    return TlsOptionParseDigestInfo("gmexport", optionRule, &expOption->labelOption.digest);
}

static Status ExportStart(ExportOptionT *expOpt)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    TlsConnOptionT connOpt = {
        .domainName = expOpt->domainName,
        .userName = expOpt->userName,
        .namespaceName = expOpt->namespaceName,
        .useReservedConn = false,
        .noMemLimit = false,
    };
    Status ret = TlsConnect(&conn, &stmt, &connOpt);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "gmexport connect unsucc. ret = %d", (int32_t)ret);  // LCOV_EXCL_LINE
        return ret;
    }
    ret = TlsUseNameSpace(stmt, expOpt->namespaceName);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(
            DbPrintfDefault, ret, "gmexport use namespace:%s unsucc. ret = %d", expOpt->namespaceName, (int32_t)ret);
        // LCOV_EXCL_STOP
        TlsDisconnect(conn, stmt);
        return ret;
    }

    ret = ExportFile(stmt, (ExportLabelOptionT *)&expOpt->labelOption);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "Export file unsucc. ret = %d", (int32_t)ret);  // LCOV_EXCL_LINE
    }

    TlsDisconnect(conn, stmt);
    return ret;
}

#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
static Status ExportBinDataStart(ExportOptionT *expOpt)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    TlsConnOptionT connOpt = {
        .domainName = expOpt->domainName,
        .userName = expOpt->userName,
        .namespaceName = expOpt->namespaceName,
        .useReservedConn = false,
    };
    Status ret = TlsConnect(&conn, &stmt, &connOpt);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "gmexport bindata connect unsucc. ret = %d", (int32_t)ret);
        return ret;
    }
    ret = TlsThreadPoolInit(
        conn->memCtx, expOpt->labelOption.threadNum, expOpt->domainName, GMC_CONN_TYPE_SYNC, &expOpt->labelOption.tp);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "gmexport use thread pool unsucc. ret = %d", (int32_t)ret);
        TlsDisconnect(conn, stmt);
        return ret;
    }
    ret = TlsUseNameSpace(stmt, expOpt->namespaceName);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(
            DbPrintfDefault, ret, "gmexport use namespace:%s unsucc. ret = %d", expOpt->namespaceName, (int32_t)ret);
        TlsThreadPoolDestroy(expOpt->labelOption.tp);
        TlsDisconnect(conn, stmt);
        return ret;
    }
    DbCreateList(&expOpt->labelOption.labelInfos, sizeof(WarmRebootLabelInfoT), stmt->memCtx);
    ret = ExportMultipleFilesBinData(stmt, (ExportLabelOptionT *)&expOpt->labelOption);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "Export file unsucc. ret = %d", (int32_t)ret);  // LCOV_EXCL_LINE
        TlsThreadPoolDestroy(expOpt->labelOption.tp);
        FreeLabelInfosList(stmt->memCtx, &expOpt->labelOption.labelInfos);
        TlsDisconnect(conn, stmt);
        return ret;
    }
    TlsThreadPoolDestroy(expOpt->labelOption.tp);
    FreeLabelInfosList(stmt->memCtx, &expOpt->labelOption.labelInfos);
    TlsDisconnect(conn, stmt);
    return ret;
}
#endif

static Status StartExportWithPerfEnv(ExportOptionT *tOpt, GmExpStartupRuleE ruleId)
{
    Status ret = TlsPerfEnvInit();
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "Init perf env unsucc when exporting. ret = %d. rule is %d,", (int32_t)ret,
            (int32_t)ruleId);
        // LCOV_EXCL_STOP
        return ret;
    }
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
    if (ruleId == GMEXP_RULE_EXPORT_BINDATA) {
        ret = ExportBinDataStart(tOpt);
        TlsPerfEnvUnInit();
        return ret;
    }
#endif
#ifdef TS_MULTI_INST
    if (ruleId == GMEXP_RULE_TS_EXPORT_SYSTBL) {
        ret = ExportTsSysTblDataStart(tOpt);
        TlsPerfEnvUnInit();
        return ret;
    }
#endif
    ret = ExportStart(tOpt);
    TlsPerfEnvUnInit();
    return ret;
}

Status GmExportEntry(int32_t argc, char **argv, DbOptionRuleT *optionRule)
{
    DB_POINTER(optionRule);
    Status ret = GmExpInit(optionRule, argc, argv);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "Export init args unsucc. ret = %" PRIu32, (uint32_t)ret);
        // LCOV_EXCL_STOP
        return ret;
    }

    int32_t ruleId = DbGetStartupRule(optionRule);
    if (ruleId == OPTION_NOT_FOUND) {
        ret = GMERR_INVALID_OPTION;
        DB_LOG_ERROR(GMERR_INVALID_OPTION, "unexpected rule id %d.", ruleId);                       // LCOV_EXCL_LINE
        PRINT_ERROR(DbPrintfDefault, "Invalid gmexport parameter. ret = %" PRIu32, (uint32_t)ret);  // LCOV_EXCL_LINE
        return ret;
    }
    ExportOptionT tOpt = {0};
    switch ((GmExpStartupRuleE)ruleId) {
        case GMEXP_RULE_HELP:
            GmExpGetHelp();
            return GMERR_OK;
        case GMEXP_RULE_VERSION:
            GmExpGetVersion();
            return GMERR_OK;
        case GMEXP_RULE_EXPORT_DATA:
            ret = ExportInitSchemaOption(optionRule, &tOpt);
            break;
        case GMEXP_RULE_EXPORT_RESPOOL:
            ret = ExportInitRespoolOption(optionRule, &tOpt);
            break;
        case GMEXP_RULE_BACKUP_CONF:
            ret = ExportInitBackupConfOption(optionRule, &tOpt);
            break;
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
        case GMEXP_RULE_EXPORT_BINDATA:
            ret = ExportInitBinDataOption(optionRule, &tOpt);
            break;
#endif
#ifdef TS_MULTI_INST
        case GMEXP_RULE_TS_EXPORT_SYSTBL:
            ret = ExportInitTsSysTblOption(optionRule, &tOpt);
            break;
#endif
        default:
            return GMERR_SYNTAX_ERROR;
    }
    if (ret != GMERR_OK) {
        return ret;
    }
    return StartExportWithPerfEnv(&tOpt, (GmExpStartupRuleE)ruleId);
}

Status GmExportMain(int32_t argc, char **argv)
{
#ifdef FEATURE_TOOLS_SYSLOG
    GmcLogAdptFuncsT tlsLogFuncs = {.userWriteFunc = TlsPrintLog, .handle = NULL};
    DbLogRegAdptFuncs(&tlsLogFuncs);
#endif
    DbOptionRuleT optionRule = {0};
    Status ret = GmExpAllocOptionRuleItems(&optionRule);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmExportEntry(argc, argv, &optionRule);
    DbReleaseOptionRuleItems(&optionRule);
    return ret;
}

#ifdef FEATURE_CLT_SERVER_SAME_PROCESS
inline int32_t GmsExportMain(int32_t argc, char *argv[])
{
    if (DbGetServerSameProcessStartFlag() > 0) {
        // LCOV_EXCL_START
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Unable to execute gms-tools in same process as gmserver.");
        // LCOV_EXCL_STOP
        return GMERR_DATA_EXCEPTION;
    }

    return GmExportMain(argc, argv);
}
#endif

#ifdef __cplusplus
}
#endif
