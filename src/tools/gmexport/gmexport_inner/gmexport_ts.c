/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: tsdb gmexport methods
 * Author: GMDBV5 Team
 * Create: 2024-10-17
 */

#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
#include "gmexport_ts.h"
#include "gmc_sql.h"
#include "gmc_graph.h"
#include "gmc_internal.h"
#include "clt_stmt.h"
#include "clt_stmt_extend.h"
#include "adpt_string.h"
#include "db_error.h"
#include "tool_buffer.h"
#include "gmexport_utils.h"
#include "gmc_namespace.h"
#include "db_mem_context.h"
#include "db_dynmem_algo.h"
#define OPTION_5 5
#define OPTION_6 6
#define OPTION_NUM_2 2

#define TS_DB_DEFAULT_DOMAIN_NAME "channel:ctl_channel_tsdb"
#define TS_DB_CLIENT_MSG_READ_TIMEOUT 300000000  // ms, default 300s
#define TS_CMD_LEN 64

#define BUFFER_INITIAL_CAPACITY 1024

#define TS_OPTION_CHECK_STEP 2
#define TS_EXPORT_OPTION_MIN_NUM 5
#define TS_EXPORT_OPTION_MAX_NUM 7
#define TS_OPTION_ARG_OPERATION "-c"

#ifdef __cplusplus
extern "C" {
#endif

typedef enum OptNameIdx {
    OPTION_ARG_OPERATION,
    OPTION_FILE_PATH,
    OPTION_LOCATOR,
    OPTION_USER_NAME,
    OPTION_BUTT,
} OptNameIdxE;

static uint32_t g_optNameIdx[OPTION_BUTT] = {0};

static Status VerifyOptionOperation(int32_t argc, char **argv, int32_t i)
{
    if (g_optNameIdx[OPTION_ARG_OPERATION] != 0) {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(
            DbPrintfDefault, GMERR_INVALID_OPTION, "Option %s is duplicate.", TS_EXPORT_OPTION_ARG_OPERATION);
        // LCOV_EXCL_STOP
        return GMERR_INVALID_OPTION;
    }
    if (i + 1 >= argc || strcasecmp(argv[i + 1], TS_EXPORT_OPTION_SYSTBL) != 0) {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_OPTION,
            "invalid option, ts only support systbl_data option after gmexport -c, ret = %" PRId32 ".",
            (int32_t)GMERR_INVALID_OPTION);
        // LCOV_EXCL_STOP
        return GMERR_INVALID_OPTION;
    }
    g_optNameIdx[OPTION_ARG_OPERATION] = (uint32_t)i;
    return GMERR_OK;
}

static Status VerifyOptionFilePath(int32_t argc, int32_t i)
{
    if (g_optNameIdx[OPTION_FILE_PATH] != 0) {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_OPTION, "Option %s is duplicate.", TS_EXPORT_OPTION_FILE_PATH);
        // LCOV_EXCL_STOP
        return GMERR_INVALID_OPTION;
    }
    if (i + 1 >= argc) {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_OPTION, "invalid option, gmexport -f should have path value.");
        // LCOV_EXCL_STOP
        return GMERR_INVALID_OPTION;
    }
    g_optNameIdx[OPTION_FILE_PATH] = (uint32_t)i;
    return GMERR_OK;
}

static Status VerifyOptionLocator(int32_t argc, int32_t i)
{
    if (g_optNameIdx[OPTION_LOCATOR] != 0) {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_OPTION, "Option %s is duplicate.", TS_EXPORT_OPTION_LOCATOR);
        // LCOV_EXCL_STOP
        return GMERR_INVALID_OPTION;
    }
    if (i + 1 >= argc) {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_OPTION, "invalid option, gmexport -s should have locator value.");
        // LCOV_EXCL_STOP
        return GMERR_INVALID_OPTION;
    }
    g_optNameIdx[OPTION_LOCATOR] = (uint32_t)i;
    return GMERR_OK;
}

static Status VerifyOptionUserName(int32_t argc, int32_t i)
{
    if (g_optNameIdx[OPTION_USER_NAME] != 0) {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_OPTION, "Option %s is duplicate.", TS_EXPORT_OPTION_USER_NAME);
        // LCOV_EXCL_STOP
        return GMERR_INVALID_OPTION;
    }
    if (i + 1 >= argc) {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_OPTION, "invalid option, gmexport -u should have user name.");
        // LCOV_EXCL_STOP
        return GMERR_INVALID_OPTION;
    }
    g_optNameIdx[OPTION_USER_NAME] = (uint32_t)i;
    return GMERR_OK;
}

static Status TsVerifyExportOption(int32_t argc, char **argv, DbPrintFunc printFunc)
{
    if (argc > TS_EXPORT_OPTION_MAX_NUM || argc < TS_EXPORT_OPTION_MIN_NUM) {
        TOOL_RUN_ERROR(printFunc, GMERR_INVALID_OPTION,
            "Option num is over limit, ts only support 3 options at most and 2 at least.");
    }
    for (uint32_t i = 0; i < OPTION_BUTT; i++) {
        g_optNameIdx[i] = 0;
    }
    int32_t i = 1;
    Status ret = GMERR_OK;
    while (i < argc) {
        if (strcmp(argv[i], TS_EXPORT_OPTION_ARG_OPERATION) == 0) {
            ret = VerifyOptionOperation(argc, argv, i);
            if (ret != GMERR_OK) {
                return ret;
            }
        } else if (strcmp(argv[i], TS_EXPORT_OPTION_FILE_PATH) == 0) {
            ret = VerifyOptionFilePath(argc, i);
            if (ret != GMERR_OK) {
                return ret;
            }
        } else if (strcmp(argv[i], TS_EXPORT_OPTION_LOCATOR) == 0) {
            ret = VerifyOptionLocator(argc, i);
            if (ret != GMERR_OK) {
                return ret;
            }
        } else if (strcmp(argv[i], TS_EXPORT_OPTION_USER_NAME) == 0) {
            ret = VerifyOptionUserName(argc, i);
            if (ret != GMERR_OK) {
                return ret;
            }
        } else {
            TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_OPTION,
                "invalid option, ts only support gmexport -c systbl_data -f dir_path -s <server_locator>.");
            return GMERR_INVALID_OPTION;
        }
        i += TS_OPTION_CHECK_STEP;  // skip option value to next opt start
    }

    char *filePath = argv[g_optNameIdx[OPTION_FILE_PATH] + 1];
    // 校验路径长度（最后生成的文件全路径为 /xxx/xxx/文件名.bindata+'\0' 不能超过 256 字节）
    if (DM_STR_LEN(filePath) > TS_EXPORT_DIR_PATH_LENGTH) {
        TOOL_RUN_ERROR(printFunc, GMERR_INVALID_PARAMETER_VALUE,
            "Import file path exceeds limit: %" PRIu32 ", ret = %" PRId32, TS_EXPORT_DIR_PATH_LENGTH,
            (int32_t)GMERR_INVALID_PARAMETER_VALUE);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

Status FetchVlDataToBuf(GmcStmtT *stmt, char *vlName, OutputBufferT *buffer, DbPrintFunc printFunc)
{
    Status ret = GmcUseNamespace(stmt, "system");
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(printFunc, ret, "Can not use system namespace, ret = %" PRId32, (int32_t)ret);
        return ret;
    }
    char selectCmd[TS_CMD_LEN] = {0};
    int32_t err = sprintf_s(selectCmd, TS_CMD_LEN, "select * from %s", vlName);
    if (err < 0) {
        TOOL_RUN_ERROR(printFunc, GMERR_MEMORY_OPERATE_FAILED,
            "Can not construct select cmd for label %s, err = %" PRId32, vlName, err);
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    // 系统表表定义含有 node 节点，sql 语句不支持此类表的数据查询
    ret = GmcExecDirect(stmt, selectCmd, DM_STR_LEN(selectCmd));
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(printFunc, ret, "Can not execute select cmd for label %s, ret = %" PRId32, vlName, (int32_t)ret);
        return ret;
    }

    // 循环读取数据并写 buffer
    while (stmt->cacheRows > 0) {
        TextT binDataBuf = {0};
        CltOperQlT *op = CltGetOperationContext(stmt);
        FixBufferT *fixBuf = &op->dynBuffer;
        ret = FixBufGetObject(fixBuf, &binDataBuf);
        if (SECUREC_UNLIKELY(ret != GMERR_OK)) {
            DB_LOG_AND_SET_LASERR(ret, "get vertex when get bin data.");  // LCOV_EXCL_LINE
            return ret;
        }
        stmt->cacheRows--;
        ret = OutPutBufferWrite(buffer, (const uint32_t *)&binDataBuf.len, sizeof(uint32_t));
        if (ret != GMERR_OK) {
            TOOL_RUN_ERROR(printFunc, ret, "Can not write bin data bufLen to buffer for label %s, ret = %" PRId32,
                vlName, (int32_t)ret);
            return ret;
        }
        ret = OutPutBufferWrite(buffer, (const char *)binDataBuf.str, binDataBuf.len);
        if (ret != GMERR_OK) {
            TOOL_RUN_ERROR(printFunc, ret,
                "Can not write bin data bufStr to buffer (len: %" PRIu32 ") for label %s, ret = %" PRId32,
                binDataBuf.len, vlName, (int32_t)ret);
            return ret;
        }
    }
    return GMERR_OK;
}

Status ExportSysTblDataToFile(GmcStmtT *stmt, char *dirPath, DbPrintFunc printFunc)
{
    DB_POINTER2(stmt, dirPath);
    // memCtx用途：gmexport工具导出系统表数据时，存储数据的缓存buffer
    // 生命周期：函数级别
    // 释放方式：兜底清空
    // 兜底清空措施：函数结束，生成导出的数据文件后，销毁该memctx
    DbMemCtxArgsT args = {0};
    DbTopDynMemCtxT *topDynMemCtx = DbGetTopDynMemCtx(NULL);
    DbMemCtxT *memCtx = DbCreateDynMemCtx(&(topDynMemCtx->header), false, "GMEXPORT_SYSTBL", &args);
    if (memCtx == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_MEMORY_OPERATE_FAILED,
            "Cannot create mem ctx for output buffer! ret = %u", (uint32_t)GMERR_MEMORY_OPERATE_FAILED);
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    Status ret = GMERR_OK;
    static char *sysTblName[] = {"GM_SYS_VL", "GM_SYS_PROP", "GM_SYS_USER", "GM_SYS_ROLE"};
    static char *exportFileName[] = {"gmdb-vl-systbl", "gmdb-prop-systbl", "gmdb-user-systbl", "gmdb-role-systbl"};
    for (uint32_t i = 0; i < ELEMENT_COUNT(sysTblName); i++) {
        OutputBufferT buffer;
        ret = OutPutBufferInit(&buffer, memCtx, BUFFER_INITIAL_CAPACITY);
        if (ret != GMERR_OK) {
            goto EXIT;
        }

        ret = FetchVlDataToBuf(stmt, sysTblName[i], &buffer, printFunc);
        if (ret != GMERR_OK) {
            goto EXIT;
        }

        ExportFileDescT *fileDesc = ExportInitFileDesc(
            memCtx, &(TextT){.str = buffer.buf, .len = buffer.writeCursor}, dirPath, exportFileName[i], "bindata");
        if (fileDesc == NULL) {
            ret = GMERR_OUT_OF_MEMORY;
            TOOL_RUN_ERROR(
                DbPrintfDefault, ret, "Init file desc unsucc. ret = %" PRIu32, (uint32_t)GMERR_OUT_OF_MEMORY);
            goto EXIT;
        }
        ret = ExportToFile(fileDesc);
        if (ret != GMERR_OK) {
            goto EXIT;
        }
    }

EXIT:
    DbDeleteDynMemCtx(memCtx);
    return ret;
}

static Status CreateTsStmt(GmcStmtT *stmt, DbPrintFunc printFunc)
{
    if (stmt->tsInfo != NULL) {
        return GMERR_OK;
    }
    stmt->tsInfo = (TsStmtT *)DbDynMemCtxAlloc(stmt->memCtx, sizeof(TsStmtT));
    if (stmt->tsInfo == NULL) {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(printFunc, GMERR_OUT_OF_MEMORY, "Unable to alloc space for tsInfo in gmexport, size %" PRIu32,
            (uint32_t)sizeof(TsStmtT));
        // LCOV_EXCL_STOP
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(stmt->tsInfo, sizeof(TsStmtT), 0, sizeof(TsStmtT));
    DbCreateList(&stmt->tsInfo->bindColStmts, sizeof(TsBindColStmtT), stmt->memCtx);

    stmt->stmtType = CLT_STMT_TYPE_TS;
    return GMERR_OK;
}

Status ExportTsSysTblDataStart(ExportOptionT *expOpt)
{
    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    TlsConnOptionT connOption = {
        .domainName = expOpt->domainName,
        .userName = expOpt->userName,
        .namespaceName = expOpt->namespaceName,
        .useReservedConn = false,
        .noMemLimit = false,
        .msgReadTimeoutMs = TS_DB_CLIENT_MSG_READ_TIMEOUT,
    };
    Status ret = TlsConnect(&conn, &stmt, &connOption);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "Cannot connect server, ret = %" PRId32, (int32_t)ret);  // LCOV_EXCL_LINE
        return ret;
    }

    ret = CreateTsStmt(stmt, DbPrintfDefault);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    stmt->tsInfo->sqlType = SYSTBL_SQL_TYPE;

    ret = ExportSysTblDataToFile(stmt, expOpt->labelOption.filePath, DbPrintfDefault);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

EXIT:
    TlsDisconnect(conn, stmt);
    return ret;
}

static void TsExpGetHelp(void)
{
    const char *gmexportOptionManual[][MANUAL_COL_NUM] = {{DB_OPTION_ARG_HELP, "", "print the online help manual"},
        {DB_OPTION_ARG_VERSION, "", "print the gmexport version"},
        {TS_OPTION_ARG_OPERATION, "<cmd_type>", "systbl_data"},
        {TS_EXPORT_OPTION_FILE_PATH, "<file_path>", "exported path, default value is the current path"},
        {DB_OPTION_ARG_DOMAIN_NAME, "<server_locator>", "serverLocator for connection"}};
    DbPrintManual("[OPTION]", gmexportOptionManual, ELEMENT_COUNT(gmexportOptionManual), NULL);
}

static void TsExpGetVersion(void)
{
    const char *gmemportVersion[][MANUAL_COL_NUM] = {
        {"gmexport GMDBV5", "", ""},
        {"Copyright (c) Huawei Technologies Co.", "", ""},
    };
    DbPrintManual("[VERSION]", gmemportVersion, ELEMENT_COUNT(gmemportVersion), NULL);
}

static Status TsVerifyExportOneOption(int32_t argc, char **argv, DbPrintFunc printFunc)
{
    // -h
    if (strcmp(argv[1], TS_EXPORT_OPTION_HELP) == 0) {
        TsExpGetHelp();
        return GMERR_OK;
    }

    // -v
    if (strcmp(argv[1], TS_EXPORT_OPTION_VERSION) == 0) {
        TsExpGetVersion();
        return GMERR_OK;
    }

    Status ret = GMERR_INVALID_OPTION;
    TOOL_RUN_ERROR(printFunc, ret, "Invalid option: %s, tsdb export support -h and -v option only.\n", argv[1]);
    return ret;
}

Status GmExportTsEntry(int32_t argc, char **argv, DbPrintFunc printFunc)
{
    if (argc == OPTION_NUM_2) {
        return TsVerifyExportOneOption(argc, argv, printFunc);
    }

    Status ret = TsVerifyExportOption(argc, argv, printFunc);
    if (ret != GMERR_OK) {
        return ret;
    }

    GmcConnT *conn = NULL;
    GmcStmtT *stmt = NULL;
    char *domainName = DbIsEulerEnv() ? DB_DEFAULT_DOMAIN_NAME : TS_DB_DEFAULT_DOMAIN_NAME;
    if (g_optNameIdx[OPTION_LOCATOR] != 0 && argv[g_optNameIdx[OPTION_LOCATOR] + 1] != NULL) {
        domainName = argv[g_optNameIdx[OPTION_LOCATOR] + 1];
    }
    char *userName = NULL;
    if (g_optNameIdx[OPTION_USER_NAME] != 0 && argv[g_optNameIdx[OPTION_USER_NAME] + 1] != NULL) {
        userName = argv[g_optNameIdx[OPTION_USER_NAME] + 1];
    }
    TlsConnOptionT connOption = {
        .domainName = domainName,
        .userName = userName,
        .namespaceName = NULL,
        .useReservedConn = false,
        .noMemLimit = false,
        .msgReadTimeoutMs = TS_DB_CLIENT_MSG_READ_TIMEOUT,
    };
    ret = TlsConnect(&conn, &stmt, &connOption);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(printFunc, ret, "Cannot connect server, ret = %" PRId32, (int32_t)ret);  // LCOV_EXCL_LINE
        return ret;
    }

    ret = CreateTsStmt(stmt, printFunc);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    stmt->tsInfo->sqlType = SYSTBL_SQL_TYPE;

    ret = ExportSysTblDataToFile(stmt, argv[g_optNameIdx[OPTION_FILE_PATH] + 1], printFunc);
    if (ret != GMERR_OK) {
        goto EXIT;
    }

EXIT:
    TlsDisconnect(conn, stmt);
    return ret;
}

Status GmExportMainTs(int32_t argc, char **argv)
{
#ifdef FEATURE_TOOLS_SYSLOG
    GmcLogAdptFuncsT tlsLogFuncs = {.userWriteFunc = TlsPrintLog, .handle = NULL};
    DbLogRegAdptFuncs(&tlsLogFuncs);
#endif
    Status ret = TlsPerfEnvInit();
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "Unable to initial gmexport env, ret = %" PRId32 ".", (int32_t)ret);
        // LCOV_EXCL_STOP
        return ret;
    }
    ret = GmExportTsEntry(argc, argv, DbPrintfDefault);
    TlsPerfEnvUnInit();
    return ret;
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
#endif
