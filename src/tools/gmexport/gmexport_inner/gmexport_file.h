/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: header file for export schema or data from file or directory
 * Author: chenjunyu
 * Create: 2021-3-9
 */
#ifndef GMEXPORT_FILE_H
#define GMEXPORT_FILE_H

#include "adpt_locator.h"
#include "gmexport_utils.h"
#include "db_text.h"
#include "tool_utils.h"
#include "tool_thread_pool.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define TMP_FILE_SUFFIX_WITH_DOT ".bin_tmp"
#define BIN_FILE_SUFFIX_WITH_DOT ".bin_data"

typedef struct ExportLabelInfo {
    char *oriLabelName;  // 原始表表名
    char *labelName;     // 表名
    char *namespace;     // 表名对应的命名空间
    uint32_t labelType;  // 表类型
    uint16_t propNum;    // 字段数
} ExportLabelInfoT;

typedef struct ExportOption {
    ExportLabelOptionT labelOption;
    char domainName[DOMAIN_NAME_MAX_LEN];
    char userName[MAX_USER_NAME_LEN];
    char namespaceName[MAX_NAMESPACE_LENGTH];
} ExportOptionT;

typedef struct CrcHeader {
    uint32_t headerSize;
    uint32_t fileLen;
    char *headerFormat;
} ExportCrcHeaderInfoT;

typedef struct ExportHmacFileInfo {
    char *fileBuff;
    uint32_t fileLen;
    HmacDataT hmacData;
} ExportHmacFileInfoT;

Status ExportFile(GmcStmtT *stmt, ExportLabelOptionT *labelOpt);

// warm reboot场景使用
Status ExportMultipleFilesBinData(GmcStmtT *stmt, ExportLabelOptionT *labelOpt);

void FreeLabelInfosList(void *memCtx, DbListT *list);

Status RemoveFilesWithSuffix(const char *path, uint32_t len, const char *suffix);

Status RemoveOldFilesAndRenameTmpFiles(DbListT *labelInfos, char *path, uint32_t len);

Status RemoveFileWithTableName(const char *path, uint32_t maxPathLen, const char *tableName, uint32_t maxNameLen);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
