/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: gmexport ts command
 * Author: GMDBV5 Team
 * Create: 2024-10-17
 */

#ifndef GMEXPORT_TS_H
#define GMEXPORT_TS_H

#include "adpt_locator.h"
#include "tool_utils.h"
#include "adpt_types.h"
#include "gmexport_file.h"

#ifdef __cplusplus
extern "C" {
#endif

#define OPTION_2 2
#define OPTION_3 3
#define OPTION_4 4
#define TS_EXPORT_DIR_PATH_LENGTH 230
#define TS_EXPORT_OPTION_SYSTBL "systbl_data"
#define TS_EXPORT_OPTION_ARG_OPERATION "-c"
#define TS_EXPORT_OPTION_HELP "-h"
#define TS_EXPORT_OPTION_VERSION "-v"
#define TS_EXPORT_OPTION_FILE_PATH "-f"
#define TS_EXPORT_OPTION_LOCATOR "-s"
#define TS_EXPORT_OPTION_USER_NAME "-u"

Status GmExportMainTs(int32_t argc, char **argv);

Status ExportTsSysTblDataStart(ExportOptionT *expOpt);

#ifdef __cplusplus
}
#endif

#endif
