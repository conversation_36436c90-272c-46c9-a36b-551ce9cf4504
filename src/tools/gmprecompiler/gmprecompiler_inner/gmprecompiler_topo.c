/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * File Name: gmprecompiler_topo.c
 * Description: file for gmprecompiler show topo (gmprecompiler topo -f xxx.d -t name)
 * Author:
 * Create: 2024-07-27
 */
#include "gmprecompiler_tool.h"

#ifdef __cplusplus
extern "C" {
#endif

#define GMCOMP<PERSON>ER_DEFAULT_OA_MAP_SIZE 10
#define GMCOMPILER_FAKE_LABEL_PREFIX "fake_"
#define GMCOMPILER_DISPLAY_STR_LEN 1024
#define GMCOMPILER_NS_TOPO_LOOP_STR "(loop)"

static const char *g_gmdbDatalogLabelType[] = {"TBM", "NOTIFY", "EXTERN", "UDF", "AGG", "INP", "MID", "DUMMY"};

typedef enum GmPrecompilerTopoTag { LABEL_TOPO, NS_TOPO } GmPrecompilerTopoTagE;
typedef enum GmPrecompilerTopoType { TBM, NOTIFY, EXTERN, UDF, AGG, INPUT, MIDDLE, DUMMY } GmPrecompilerTopoTypeE;

typedef struct {
    char *labelName;
    GmPrecompilerTopoTypeE type;
    uint32_t size;
} CompilerDtlItemT;

typedef struct CompilerNsItem {
    char *namespaceName;
    bool isVisited;
} CompilerNsItemT;

typedef Status (*CompilerPaddingMapsItf)(DtlPrecompilerCtxT *ctx, DtlRawAstT *program);
typedef Status (*CompilerDisplayTopologyItf)(DtlPrecompilerCtxT *ctx, PreCompilerArgumentT *toolOption);

Status CompilerCheckPara4Topology(PreCompilerArgumentT *toolOption)
{
    Status ret = GmPreCompilerCheckFile(toolOption->inputPath, GMCOMPILER_DATALOG_FILE);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (!toolOption->isNsTopo && toolOption->isGraphViz) {
        (void)DbPrintfDefault(
            "gmprecompiler: -graphViz should be used with -topoNs. ret = %" PRIu32 ".\n", GMERR_INVALID_OPTION);
        return GMERR_INVALID_OPTION;
    }

    return GMERR_OK;
}

Status CompilerInitCompileCtx4Topology(DbMemCtxT *memCtx, PreCompilerArgumentT *toolOption, DtlPrecompilerCtxT **ctx)
{
    size_t allocSize = sizeof(DtlPrecompilerCtxT) + sizeof(DtlPrecompilerTopoCtxT) + GMCOMPILER_DISPLAY_STR_LEN;
    DtlPrecompilerCtxT *tmpCtx = DbDynMemCtxAlloc(memCtx, allocSize);
    if (tmpCtx == NULL) {
        (void)DbPrintfDefault("gmprecompiler: alloc topology compile ctx.\n");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(tmpCtx, allocSize, 0x00, allocSize);

    tmpCtx->memCtx = memCtx;
    tmpCtx->toolOption = toolOption;
    tmpCtx->buffer = NULL;
    tmpCtx->topo = (DtlPrecompilerTopoCtxT *)(tmpCtx + 1);
    tmpCtx->topo->topoMap = NULL;
    tmpCtx->topo->labelMap = NULL;
    tmpCtx->topo->relatedLabels = NULL;
    DbCreateList(&tmpCtx->topo->terminator, sizeof(bool), tmpCtx->memCtx);
    tmpCtx->topo->content = (char *)((uint8_t *)tmpCtx + sizeof(DtlPrecompilerCtxT) + sizeof(DtlPrecompilerTopoCtxT));
    *ctx = tmpCtx;
    return GMERR_OK;
}

static Status CompilerCtxMapsCreate(DtlPrecompilerCtxT *ctx)
{
    // 内存统一在GmPreCompilerMain释放
    ctx->topo->topoMap = DbDynMemCtxAlloc(ctx->memCtx, sizeof(DbOamapT));
    if (ctx->topo->topoMap == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }

    Status ret =
        DbOamapInit(ctx->topo->topoMap, GMCOMPILER_DEFAULT_OA_MAP_SIZE, DbOamapStringCompare, ctx->memCtx, true);
    if (ret != GMERR_OK) {
        return ret;
    }

    ctx->topo->labelMap = DbDynMemCtxAlloc(ctx->memCtx, sizeof(DbOamapT));
    if (ctx->topo->labelMap == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }

    ret = DbOamapInit(ctx->topo->labelMap, GMCOMPILER_DEFAULT_OA_MAP_SIZE, DbOamapStringCompare, ctx->memCtx, true);
    if (ret != GMERR_OK) {
        return ret;
    }

    ctx->topo->relatedLabels = DbDynMemCtxAlloc(ctx->memCtx, sizeof(DbOamapT));
    if (ctx->topo->relatedLabels == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }

    ret =
        DbOamapInit(ctx->topo->relatedLabels, GMCOMPILER_DEFAULT_OA_MAP_SIZE, DbOamapStringCompare, ctx->memCtx, true);
    if (ret != GMERR_OK) {
        return ret;
    }

    ctx->topo->namespaceMap = DbDynMemCtxAlloc(ctx->memCtx, sizeof(DbOamapT));
    if (ctx->topo->namespaceMap == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }

    ret = DbOamapInit(ctx->topo->namespaceMap, GMCOMPILER_DEFAULT_OA_MAP_SIZE, DbOamapStringCompare, ctx->memCtx, true);
    if (ret != GMERR_OK) {
        return ret;
    }

    return ret;
}

static Status CompilerSetTopologyMap(DtlPrecompilerCtxT *ctx, char *keyName, char *valueName)
{
    uint32_t hash = DbHash32((uint8_t *)keyName, strlen(keyName));
    DbListT *list = (DbListT *)DbOamapLookup(ctx->topo->topoMap, hash, keyName, NULL);
    if (list != NULL) {
        return DbAppendListItem(list, &valueName);
    }
    list = (DbListT *)DbDynMemCtxAlloc(ctx->memCtx, sizeof(DbListT));
    if (list == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }
    DbCreateList(list, sizeof(char *), ctx->memCtx);
    Status ret = DbAppendListItem(list, &valueName);
    if (ret == GMERR_OK) {
        ret = DbOamapInsert(ctx->topo->topoMap, hash, keyName, list, NULL);
    }
    return ret;
}

GmPrecompilerTopoTypeE CompilerGetTopoType(DtlRelationT *relation)
{
    if (relation->relationType == DTL_FUNCTION) {
        return UDF;
    }
    if (relation->relationType == DTL_AGGREGATE) {
        return AGG;
    }
    if (relation->relationType == DTL_TABLE_TBM) {
        return TBM;
    }
    if (relation->relationType == DTL_TABLE_EXTERN) {
        return EXTERN;
    }
    if (relation->inOutType == DTL_INTERMEDIATE_TABLE) {
        return MIDDLE;
    }
    if (relation->inOutType == DTL_INPUT_TABLE) {
        return INPUT;
    }
    if (relation->table->parsedOption.postProc == DM_DTL_SUBSCRIPTION) {
        return NOTIFY;
    }
    return DUMMY;
}

uint32_t CompilerGetSizeFromFields(DbListT *list)
{
    uint32_t cnt = DbListGetItemCnt(list);
    uint32_t size = 0;
    for (uint32_t i = 0; i < cnt; ++i) {
        DtlFieldT *field = DbListItem(list, i);
        size += field->size;
    }
    return size;
}

uint32_t CompilerGetTopoSize(DtlRelationT *relation)
{
    if (relation->relationType == DTL_FUNCTION) {
        return CompilerGetSizeFromFields(&relation->function->fields);
    }
    if (relation->relationType == DTL_AGGREGATE) {
        return CompilerGetSizeFromFields(&relation->aggregate->fields);
    }
    if (relation->relationType == DTL_RESOURCE_SEQUENTIAL || relation->relationType == DTL_RESOURCE_PUBSUB) {
        return CompilerGetSizeFromFields(&relation->resource->fields);
    }
    uint32_t n = 0;
    DmPropertySchemaT *properties = relation->vertexLabel->metaVertexLabel->schema->properties;
    uint32_t propeNum = relation->vertexLabel->metaVertexLabel->schema->propeNum;
    for (uint32_t i = 0; i < propeNum; i++) {
        n += properties[i].size;
    }
    return n;
}

static Status CompilerSetLabelMap(DtlPrecompilerCtxT *ctx, char *name, DtlRelationT *relation)
{
    uint32_t hash = DbHash32((uint8_t *)name, strlen(name));
    CompilerDtlItemT *item = (CompilerDtlItemT *)DbOamapLookup(ctx->topo->labelMap, hash, name, NULL);
    if (item != NULL) {
        return GMERR_OK;
    }
    item = DbDynMemCtxAlloc(ctx->memCtx, sizeof(CompilerDtlItemT));
    if (item == NULL) {
        return GMERR_OUT_OF_MEMORY;
    };
    item->labelName = name;
    item->type = CompilerGetTopoType(relation);
    item->size = CompilerGetTopoSize(relation);
    return DbOamapInsert(ctx->topo->labelMap, hash, name, item, NULL);
}

static Status CompilerSetNsMap(DtlPrecompilerCtxT *ctx, char *name)
{
    uint32_t hash = DbHash32((uint8_t *)name, strlen(name));
    CompilerNsItemT *item = (CompilerNsItemT *)DbOamapLookup(ctx->topo->namespaceMap, hash, name, NULL);
    if (item != NULL) {
        return GMERR_OK;
    }

    item = DbDynMemCtxAlloc(ctx->memCtx, sizeof(CompilerNsItemT));
    if (item == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }

    item->namespaceName = name;
    item->isVisited = false;
    return DbOamapInsert(ctx->topo->namespaceMap, hash, name, item, NULL);
}

static Status CompilerTransferUniqueElementToMap(DbListT *list, DbOamapT *map)
{
    uint32_t cnt = DbListGetItemCnt(list);
    for (uint32_t i = 0; i < cnt; ++i) {
        char *rName = *(char **)DbListItem(list, i);
        if (rName == NULL) {
            continue;
        }
        uint32_t hash = DbHash32((uint8_t *)rName, strlen(rName));
        void *key = DbOamapLookupKey(map, hash, rName);
        if (key != NULL) {
            continue;
        }
        Status ret = DbOamapInsert(map, hash, rName, NULL, NULL);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status CompilerFillUniqueElementIntoList(DbOamapT *map, DbListT *list)
{
    char *rName = NULL;
    char *value = NULL;
    DbOamapIteratorT iter = 0;
    while (DbOamapFetch(map, &iter, (void **)&rName, (void **)&value) == GMERR_OK) {
        Status ret = DbAppendListItem(list, &rName);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    DbOamapClear(map);
    return GMERR_OK;
}

Status CompilerDeduplicateMap(DbOamapT *map)
{
    DbOamapT tmpMap = {};
    Status ret = DbOamapInit(&tmpMap, GMCOMPILER_DEFAULT_OA_MAP_SIZE, DbOamapStringCompare, map->memCtx, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    char *name = NULL;
    DbListT *list = NULL;
    DbOamapIteratorT iter = 0;
    while (DbOamapFetch(map, &iter, (void **)&name, (void **)&list) == GMERR_OK) {
        ret = CompilerTransferUniqueElementToMap(list, &tmpMap);
        if (ret != GMERR_OK) {
            (void)DbPrintfDefault("gmprecompiler: Transfer unique element(%s) to map. ret = %" PRId32 ".\n", name, ret);
            return ret;
        }
        DbClearList(list);
        ret = CompilerFillUniqueElementIntoList(&tmpMap, list);
        if (ret != GMERR_OK) {
            (void)DbPrintfDefault("gmprecompiler: Fill element(%s) into List. ret = %" PRId32 ".\n", name, ret);
            return ret;
        }
    }
    return GMERR_OK;
}

static Status CompilerPaddingMapsForLabelTopo(DtlPrecompilerCtxT *ctx, DtlRawAstT *program)
{
    Status ret = GMERR_OK;
    // 遍历规则，将所有规则涉及表项按<左表, 右边集合>插入topoMap中
    for (uint32_t i = 0; i < program->rules.count; i++) {
        DtlRuleT *dtlRule = (DtlRuleT *)DbListItem(&program->rules, i);
        for (uint32_t j = 0; j < dtlRule->tables.count; j++) {
            DtlNamedTableT *nameTbl = (DtlNamedTableT *)DbListItem(&dtlRule->tables, j);
            ret = CompilerSetTopologyMap(ctx, dtlRule->left.name, nameTbl->name);
            if (ret != GMERR_OK) {
                (void)DbPrintfDefault("gmprecompiler: Set topology map. ret = %" PRId32 ".\n", ret);
                return ret;
            }
        }

        if (dtlRule->aggregateStmt.isGroupByExist) {
            ret = CompilerSetTopologyMap(ctx, dtlRule->left.name, dtlRule->aggregateStmt.funcName);
            if (ret != GMERR_OK) {
                (void)DbPrintfDefault("gmprecompiler: Set aggFunc topology map. ret = %" PRId32 ".\n", ret);
                return ret;
            }
        }
    }
    // topoMap去重
    ret = CompilerDeduplicateMap(ctx->topo->topoMap);
    if (ret != GMERR_OK) {
        (void)DbPrintfDefault("gmprecompiler: Deduplicate map. ret = %" PRId32 ".\n", ret);
        return ret;
    }

    // 遍历获取表的基础信息
    DbOamapIteratorT iter = 0;
    char *name = NULL;
    DtlRelationT *relation = NULL;
    while (DbOamapFetch(&program->relationMap, &iter, (void **)&name, (void **)&relation) == GMERR_OK) {
        ret = CompilerSetLabelMap(ctx, name, relation);
        if (ret != GMERR_OK) {
            (void)DbPrintfDefault("gmprecompiler: Set label map. ret = %" PRId32 ".\n", ret);
            return ret;
        }
    }
    return GMERR_OK;
}

Status GetNamespaceName(DbMemCtxT *memCtx, const char *labelName, char **nspName)
{
    char *result = (char *)DbDynMemCtxAlloc(memCtx, MAX_NAMESPACE_NAME_LEN);  // 内存由工具统一释放
    if (result == NULL) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    (void)memset_s(result, MAX_NAMESPACE_NAME_LEN, 0, MAX_NAMESPACE_NAME_LEN);
    errno_t err = EOK;
    char *nspPos = strchr(labelName, DTL_NAMESPACE_DELIMITER);
    if (nspPos == NULL) {
        err = strcpy_s(result, MAX_NAMESPACE_NAME_LEN, DTL_REVERSED_NSP);
    } else {
        size_t len = nspPos - labelName + 1;  // 计算'.'之前的长度 + \0
        err = strncpy_s(result, MAX_NAMESPACE_NAME_LEN, labelName, len - 1);
    }

    if (err != EOK) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    *nspName = result;
    return GMERR_OK;
}

static Status CompilerSetNspTopoMap(DtlPrecompilerCtxT *ctx, DtlRawAstT *program)
{
    DB_POINTER4(ctx, ctx->memCtx, ctx->topo->topoMap, program);
    Status ret = GMERR_OK;
    uint32_t ruleCnt = DbListGetItemCnt(&program->rules);
    for (uint32_t i = 0; i < ruleCnt; i++) {
        DtlRuleT *dtlRule = (DtlRuleT *)DbListItem(&program->rules, i);
        if (dtlRule->left.isNull) {
            continue;
        }
        char *leftNsp = NULL;
        ret = GetNamespaceName(ctx->memCtx, dtlRule->left.name, &leftNsp);
        if (ret != GMERR_OK) {
            (void)DbPrintfDefault("gmprecompiler: get namespace name by name:%s. ret = %" PRId32 ".\n",
                dtlRule->left.name, GMERR_MEMORY_OPERATE_FAILED);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        uint32_t rightCnt = DbListGetItemCnt(&dtlRule->tables);
        for (uint32_t j = 0; j < rightCnt; j++) {
            DtlNamedTableT *nameTbl = (DtlNamedTableT *)DbListItem(&dtlRule->tables, j);
            char *rightNsp = NULL;
            ret = GetNamespaceName(ctx->memCtx, nameTbl->name, &rightNsp);
            if (ret != GMERR_OK) {
                (void)DbPrintfDefault(
                    "gmprecompiler: get namespace name by name:%s. ret = %" PRId32 ".\n", dtlRule->left.name, ret);
                return ret;
            }

            if (strcmp(leftNsp, rightNsp) == 0) {
                continue;  // 相同namespace
            }
            ret = CompilerSetTopologyMap(ctx, rightNsp, leftNsp);
            if (ret != GMERR_OK) {
                (void)DbPrintfDefault("gmprecompiler: Set topology map. ret = %" PRId32 ".\n", ret);
                return ret;
            }
        }
    }
    return GMERR_OK;
}

static Status CompilerSetReservedNsMap(DtlPrecompilerCtxT *ctx)
{
    // 内存工具退出时统一释放
    CompilerNsItemT *item = DbDynMemCtxAlloc(ctx->memCtx, sizeof(CompilerNsItemT));
    if (item == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }

    item->namespaceName = DTL_REVERSED_NSP;
    item->isVisited = false;
    uint32_t hash = DbHash32((uint8_t *)item->namespaceName, strlen(item->namespaceName));
    return DbOamapInsert(ctx->topo->namespaceMap, hash, item->namespaceName, item, NULL);
}

static Status CompilerPaddingMapsForNsTopo(DtlPrecompilerCtxT *ctx, DtlRawAstT *program)
{
    Status ret = CompilerSetNspTopoMap(ctx, program);
    if (ret != GMERR_OK) {
        (void)DbPrintfDefault("gmprecompiler: Unable to set topology map. ret = %" PRId32 ".\n", ret);
        return ret;
    }

    // topoMap去重
    ret = CompilerDeduplicateMap(ctx->topo->topoMap);
    if (ret != GMERR_OK) {
        (void)DbPrintfDefault("gmprecompiler: Deduplicate map. ret = %" PRId32 ".\n", ret);
        return ret;
    }

    // 遍历nsp设置nsp信息
    uint32_t nsCnt = DbListGetItemCnt(&program->namespaces);
    for (uint32_t i = 0; i < nsCnt; i++) {
        char *nsName = *(char **)DbListItem(&program->namespaces, i);
        ret = CompilerSetNsMap(ctx, nsName);
        if (ret != GMERR_OK) {
            (void)DbPrintfDefault(
                "gmprecompiler:Unable to set namespace:%s to namespace map. ret = %" PRId32 ".\n", nsName, ret);
            return ret;
        }
    }

    // public
    ret = CompilerSetReservedNsMap(ctx);
    if (ret != GMERR_OK) {
        (void)DbPrintfDefault("gmprecompiler:Unable to set reserved namespace to map. ret = %" PRId32 ".\n", ret);
        return ret;
    }

    uint32_t hash = DbHash32((uint8_t *)ctx->toolOption->filterName, strlen(ctx->toolOption->filterName));
    if (DbOamapLookup(ctx->topo->namespaceMap, hash, ctx->toolOption->filterName, NULL) == NULL) {
        (void)DbPrintfDefault("namespace \"%s\" is not defined. ret = %" PRId32 ".\n", ctx->toolOption->filterName,
            GMERR_UNDEFINED_OBJECT);
        return GMERR_UNDEFINED_OBJECT;
    }
    return GMERR_OK;
}

static Status CompilerPaddingMaps(DtlPrecompilerCtxT *ctx, DtlRawAstT *program)
{
    DB_POINTER3(ctx, ctx->toolOption, program);
    bool isNsTopo = ctx->toolOption->isNsTopo;
    const CompilerPaddingMapsItf method[] = {
        [LABEL_TOPO] = CompilerPaddingMapsForLabelTopo,
        [NS_TOPO] = CompilerPaddingMapsForNsTopo,
    };

    return method[isNsTopo](ctx, program);
}

Status CompilerAnalyzes4Topology(DtlPrecompilerCtxT *ctx, DtlRawAstT *dtlProgram, DbListT *executePlans)
{
    Status ret = CompilerCtxMapsCreate(ctx);
    if (ret != GMERR_OK) {
        (void)DbPrintfDefault("gmprecompiler:Unable to init context. ret = %" PRId32 ".\n", ret);
        return ret;
    }
    ret = CompilerPaddingMaps(ctx, dtlProgram);
    if (ret != GMERR_OK) {
        (void)DbPrintfDefault("gmprecompiler: Unable to Padding maps. ret = %" PRId32 ".\n", ret);
        return ret;
    }
    ctx->topo->relationMap = &dtlProgram->relationMap;
    return GMERR_OK;
}

DbListT *GetTargetLabelRuleList(DtlPrecompilerCtxT *ctx, char *labelName, char **innerLabelName)
{
    *innerLabelName = labelName;
    uint32_t hash = DbHash32((const uint8_t *)labelName, strlen(labelName));
    DbListT *list = (DbListT *)DbOamapLookup(ctx->topo->topoMap, hash, labelName, NULL);
    if (list != NULL) {
        return list;
    }

    size_t allocSize = strlen(labelName) + strlen(GMCOMPILER_FAKE_LABEL_PREFIX) + 1;
    char *fakeLabelName = DbDynMemCtxAlloc(ctx->memCtx, allocSize);
    if (fakeLabelName == NULL) {
        (void)DbPrintfDefault("gmprecompiler: alloc fake label name.\n");
        return NULL;
    }
    int32_t err = snprintf_s(fakeLabelName, allocSize, allocSize - 1, GMCOMPILER_FAKE_LABEL_PREFIX "%s", labelName);
    if (err < 0) {
        (void)DbPrintfDefault("gmprecompiler: snprintf worthless when handle fake %s.\n", labelName);
        return NULL;
    }
    hash = DbHash32((const uint8_t *)fakeLabelName, strlen(fakeLabelName));
    list = (DbListT *)DbOamapLookup(ctx->topo->topoMap, hash, fakeLabelName, NULL);
    *innerLabelName = fakeLabelName;
    return list;
}

/*
Hpf.ctrlif_map_fwdif(MID, 12)
├── Wmp.ConfigCapwapTunnel(PARTIALLY_UPD, 42)
├── Ifm.IfName(MID, 217)
│   └── Ifm.Agg_Attributes(MID, 145)
│       ├── Ifm.ConfigIf(PARTIALLY_UPD, 92)
│       ├── Ifm.PublishNif(PARTIALLY_UPD, 133)
│       ├── Ifm.GetIfMac(UDF, 18)
│       ├── Ifm.CalculateIfBw(UDF, 16)
│       ├── Ifm.ConfigAttributes(PARTIALLY_UPD, 122)
│       ├── Ifm.CalculateIfPhyState(UDF, 5)
│       └── Ifm.GetIfType(UDF, 18)
└── tbl_init(MID, 13)   isLast控制最后一个树节点的表示形式└
    ├── Ifm.ConfigIf(PARTIALLY_UPD, 92)
    └── state(INP, 13)
*/
Status DisplayStringInTree(DtlPrecompilerCtxT *ctx, int32_t column, const char *content, bool isLast)
{
    bool *terminate = NULL;
    if (column > 0) {
        terminate = (bool *)DbListItem(&ctx->topo->terminator, column - 1);
        if (terminate == NULL) {
            Status ret = DbAppendListItem(&ctx->topo->terminator, &isLast);
            if (ret != GMERR_OK) {
                return ret;
            }
            terminate = (bool *)DbListItem(&ctx->topo->terminator, column - 1);
        }
        *terminate = isLast;
    }
    if (column == 0) {
        (void)DbPrintfDefault("%s\n", content);
    } else {
        for (int32_t t = 0; t < column - 1; t++) {
            terminate = (bool *)DbListItem(&ctx->topo->terminator, t);
            *terminate ? (void)DbPrintfDefault("    ") : (void)DbPrintfDefault("│   ");
        }
        const char *direct = ctx->toolOption->isNsTopo ? "─>" : "─ ";
        isLast ? (void)DbPrintfDefault("└─%s%s\n", direct, content) :
                 (void)DbPrintfDefault("├─%s%s\n", direct, content);
    }
    return GMERR_OK;
}

static void CollectInDegree(DtlPrecompilerCtxT *ctx, CompilerDtlItemT *item)
{
    uint32_t hash = DbHash32((uint8_t *)item->labelName, strlen(item->labelName));
    CompilerDtlItemT *cur = (CompilerDtlItemT *)DbOamapLookup(ctx->topo->relatedLabels, hash, item->labelName, NULL);
    if (cur == NULL) {
        // 此处插入不了也不影响topo的展示
        (void)DbOamapInsert(ctx->topo->relatedLabels, hash, item->labelName, item, NULL);
    }
}

static Status DepthTrvLabelItem(DtlPrecompilerCtxT *ctx, CompilerDtlItemT *item, uint32_t column, bool last)
{
    if (item == NULL) {
        return GMERR_OK;
    }
    CollectInDegree(ctx, item);
    int32_t err = snprintf_s(ctx->topo->content, GMCOMPILER_DISPLAY_STR_LEN, GMCOMPILER_DISPLAY_STR_LEN - 1,
        "%s(%s, %u)", item->labelName, g_gmdbDatalogLabelType[item->type], item->size);
    if (err < 0) {
        (void)DbPrintfDefault("gmprecompiler: snprintf %s worthless when deep recursion.\n", item->labelName);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    Status ret = DisplayStringInTree(ctx, column, ctx->topo->content, last);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t hash = DbHash32((uint8_t *)item->labelName, strlen(item->labelName));
    DbListT *list = (DbListT *)DbOamapLookup(ctx->topo->topoMap, hash, item->labelName, NULL);
    if (list == NULL) {
        return GMERR_OK;
    }
    uint32_t cnt = DbListGetItemCnt(list);
    for (uint32_t i = 0; i < cnt; ++i) {
        char *rName = *(char **)DbListItem(list, i);
        hash = DbHash32((uint8_t *)rName, strlen(rName));
        CompilerDtlItemT *subItem = (CompilerDtlItemT *)DbOamapLookup(ctx->topo->labelMap, hash, rName, NULL);
        ret = DepthTrvLabelItem(ctx, subItem, column + 1, (bool)(i == cnt - 1));
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

void DisPlayUnionTables(DtlPrecompilerCtxT *ctx, const DbListT *list, const char *word)
{
    uint32_t c = DbListGetItemCnt(list);
    int32_t err = 0;
    for (uint32_t i = 0; i < c; i++) {
        char *name = *(char **)DbListItem(list, i);
        if (i == 0) {
            err = c == 1 ? snprintf_s(ctx->topo->content, GMCOMPILER_DISPLAY_STR_LEN, GMCOMPILER_DISPLAY_STR_LEN - 1,
                               "%s, %s(%s)", ctx->topo->content, word, name) :
                           snprintf_s(ctx->topo->content, GMCOMPILER_DISPLAY_STR_LEN, GMCOMPILER_DISPLAY_STR_LEN - 1,
                               "%s, %s(%s", ctx->topo->content, word, name);
        } else if (i == c - 1) {
            err = snprintf_s(ctx->topo->content, GMCOMPILER_DISPLAY_STR_LEN, GMCOMPILER_DISPLAY_STR_LEN - 1, "%s, %s)",
                ctx->topo->content, name);
        } else {
            err = snprintf_s(ctx->topo->content, GMCOMPILER_DISPLAY_STR_LEN, GMCOMPILER_DISPLAY_STR_LEN - 1, "%s, %s",
                ctx->topo->content, name);
        }
    }
    if (err < 0) {
        (void)DbPrintfDefault("gmprecompiler: snprintf worthless when display union tables.\n");
    }
}

uint32_t DisplayRelationLabel(DtlPrecompilerCtxT *ctx, GmPrecompilerTopoTypeE type)
{
    DbOamapIteratorT iter = 0;
    char *name = NULL;
    CompilerDtlItemT *item = NULL;
    bool firstDisplay = true;
    uint32_t cnt = 0;
    while (DbOamapFetch(ctx->topo->relatedLabels, &iter, (void **)&name, (void **)&item) == GMERR_OK) {
        if (item->type != type) {
            continue;
        }
        cnt++;
        if (firstDisplay) {
            (void)DbPrintfDefault("\n==========%s==========\n", g_gmdbDatalogLabelType[type]);
            firstDisplay = false;
        }
        uint32_t hash = DbHash32((uint8_t *)item->labelName, strlen(item->labelName));
        uint32_t idx;
        DtlRelationT *relation = DbOamapLookup(ctx->topo->relationMap, hash, item->labelName, &idx);
        int32_t err = snprintf_s(ctx->topo->content, GMCOMPILER_DISPLAY_STR_LEN, GMCOMPILER_DISPLAY_STR_LEN - 1,
            "%s, %u", item->labelName, item->size);
        if (err < 0) {
            (void)DbPrintfDefault(
                "gmprecompiler: snprintf %s worthless when display relation labels.\n", item->labelName);
            return cnt;
        }
        if (relation->relationType >= DTL_TABLE_BEGIN && relation->relationType <= DTL_TABLE_END) {
            DisPlayUnionTables(ctx, &relation->table->parsedOption.unionDeleteTables, "unionDelete");
        } else if (relation->relationType == DTL_FUNCTION) {
            DisPlayUnionTables(ctx, &relation->function->accessTable.originTables, "originTables");
            DisPlayUnionTables(ctx, &relation->function->accessTable.deltaTables, "deltaTables");
        } else if (relation->relationType == DTL_AGGREGATE) {
            DisPlayUnionTables(ctx, &relation->aggregate->aggregateOption.accessTable.originTables, "originTables");
            DisPlayUnionTables(ctx, &relation->aggregate->aggregateOption.accessTable.deltaTables, "deltaTables");
        }
        (void)DbPrintfDefault("%s\n", ctx->topo->content);
    }
    return cnt;
}

static Status CompilerDisplayTopologyForLabel(DtlPrecompilerCtxT *ctx, PreCompilerArgumentT *toolOption)
{
    char *innerLabelName = NULL;
    DbListT *list = GetTargetLabelRuleList(ctx, toolOption->filterName, &innerLabelName);
    uint32_t hash = DbHash32((const uint8_t *)innerLabelName, strlen(innerLabelName));
    CompilerDtlItemT *item = (CompilerDtlItemT *)DbOamapLookup(ctx->topo->labelMap, hash, innerLabelName, NULL);
    if (item == NULL) {
        (void)DbPrintfDefault("gmprecompiler: Can not found %s topology info.\n", toolOption->filterName);
        return GMERR_NO_DATA;
    }
    CollectInDegree(ctx, item);
    (void)DbPrintfDefault("\n========TOPOLOGY=========\n");
    int32_t err = snprintf_s(ctx->topo->content, GMCOMPILER_DISPLAY_STR_LEN, GMCOMPILER_DISPLAY_STR_LEN - 1,
        "%s(%s, %u)", toolOption->filterName, g_gmdbDatalogLabelType[item->type], item->size);
    if (err < 0) {
        (void)DbPrintfDefault("gmprecompiler: snprintf worthless.\n");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    Status ret = DisplayStringInTree(ctx, 0, ctx->topo->content, false);
    if (ret != GMERR_OK) {
        (void)DbPrintfDefault("gmprecompiler: append string, row 0, column 0. ret = %" PRId32 "\n", ret);
        return ret;
    }
    if (list == NULL) {
        return GMERR_OK;
    }
    uint32_t cnt = DbListGetItemCnt(list);
    for (uint32_t i = 0; i < cnt; ++i) {
        char *rName = *(char **)DbListItem(list, i);
        hash = DbHash32((uint8_t *)rName, strlen(rName));
        item = (CompilerDtlItemT *)DbOamapLookup(ctx->topo->labelMap, hash, rName, NULL);
        ret = DepthTrvLabelItem(ctx, item, 1, (bool)(i == cnt - 1));
        if (ret != GMERR_OK) {
            (void)DbPrintfDefault("gmprecompiler: Depth traversal Item. ret = %" PRId32 "\n", ret);
            return ret;
        }
    }
    uint32_t cnts[DUMMY] = {0};
    for (uint32_t n = 0; n < DUMMY; n++) {
        cnts[n] = DisplayRelationLabel(ctx, n);
    }
    (void)DbPrintfDefault("\nSUM: ");
    for (uint32_t n = 0; n < DUMMY; n++) {
        (void)DbPrintfDefault("%s-%" PRIu32 "", g_gmdbDatalogLabelType[n], cnts[n]);
        n == DUMMY - 1 ? (void)DbPrintfDefault(".\n") : (void)DbPrintfDefault(", ");
    }
    return GMERR_OK;
}

static Status DepthTrvNsItem(DtlPrecompilerCtxT *ctx, CompilerNsItemT *item, int32_t column, bool last)
{
    if (item == NULL) {
        return GMERR_OK;
    }
    errno_t err = EOK;
    if (!item->isVisited) {
        err = snprintf_s(
            ctx->topo->content, GMCOMPILER_DISPLAY_STR_LEN, GMCOMPILER_DISPLAY_STR_LEN - 1, "%s", item->namespaceName);
    } else {
        err = snprintf_s(ctx->topo->content, GMCOMPILER_DISPLAY_STR_LEN, GMCOMPILER_DISPLAY_STR_LEN - 1, "%s%s",
            item->namespaceName, GMCOMPILER_NS_TOPO_LOOP_STR);
    }
    if (err < 0) {
        (void)DbPrintfDefault("gmprecompiler: snprintf %s worthless when deep recursion.\n", item->namespaceName);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    Status ret = DisplayStringInTree(ctx, column, ctx->topo->content, last);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 存在环 不再继续深度遍历 或 已遍历过
    if (item->isVisited) {
        return GMERR_OK;
    }
    uint32_t hash = DbHash32((uint8_t *)item->namespaceName, strlen(item->namespaceName));
    DbListT *list = (DbListT *)DbOamapLookup(ctx->topo->topoMap, hash, item->namespaceName, NULL);
    if (list == NULL) {
        // 该节点没有入度，提前退出
        return GMERR_OK;
    }

    item->isVisited = true;
    uint32_t cnt = DbListGetItemCnt(list);
    for (uint32_t i = 0; i < cnt; ++i) {
        char *nspName = *(char **)DbListItem(list, i);
        hash = DbHash32((uint8_t *)nspName, strlen(nspName));
        CompilerNsItemT *subItem = (CompilerNsItemT *)DbOamapLookup(ctx->topo->namespaceMap, hash, nspName, NULL);
        ret = DepthTrvNsItem(ctx, subItem, column + 1, (bool)(i == cnt - 1));
        if (ret != GMERR_OK) {
            item->isVisited = false;
            return ret;
        }
    }
    item->isVisited = false;
    return GMERR_OK;
}

/*
Usf
├──>Mir
│   └──>Hpf
│       ├──>Sacl
│       │   ├──>Acl
│       │   │   ├──>Iptable
│       │   │   │   ├──>Acl(loop)
│       │   │   │   └──>Hpf(loop)
│       │   │   ├──>Sacl(loop)
│       │   │   ├──>Hpf(loop)
│       │   │   ├──>Ipsec
│       │   │   │   ├──>Acl(loop)
│       │   │   │   └──>Hpf(loop)
│       │   │   ├──>Usf(loop)
│       │   │   ├──>Ifm
│       │   │   │   ├──>Mir(loop)
│       │   │   │   ├──>Acl(loop)
*/
static Status CompilerDisplayTopologyForNsNormal(DtlPrecompilerCtxT *ctx, PreCompilerArgumentT *toolOption)
{
    char *nsName = toolOption->filterName;  // 复用-t后参数
    (void)DbPrintfDefault("\n========Namespace TOPOLOGY=========\n");
    int32_t err =
        snprintf_s(ctx->topo->content, GMCOMPILER_DISPLAY_STR_LEN, GMCOMPILER_DISPLAY_STR_LEN - 1, "%s", nsName);
    if (err < 0) {
        (void)DbPrintfDefault("gmprecompiler: snprintf worthless.\n");
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    Status ret = DisplayStringInTree(ctx, 0, ctx->topo->content, false);
    if (ret != GMERR_OK) {
        (void)DbPrintfDefault("gmprecompiler: append string, row 0, column 0. ret = %" PRId32 "\n", ret);
        return ret;
    }

    uint32_t hash = DbHash32((const uint8_t *)nsName, strlen(nsName));
    CompilerNsItemT *item = (CompilerNsItemT *)DbOamapLookup(ctx->topo->namespaceMap, hash, nsName, NULL);
    if (item == NULL) {
        // namespace为空
        return GMERR_OK;
    }

    DbListT *list = (DbListT *)DbOamapLookup(ctx->topo->topoMap, hash, item->namespaceName, NULL);
    if (list == NULL) {
        // 该节点没有入度，提前退出
        return GMERR_OK;
    }

    item->isVisited = true;
    uint32_t cnt = DbListGetItemCnt(list);
    for (uint32_t i = 0; i < cnt; ++i) {
        char *nspName = *(char **)DbListItem(list, i);
        hash = DbHash32((uint8_t *)nspName, strlen(nspName));
        CompilerNsItemT *subItem = (CompilerNsItemT *)DbOamapLookup(ctx->topo->namespaceMap, hash, nspName, NULL);
        ret = DepthTrvNsItem(ctx, subItem, 1, (bool)(i == cnt - 1));
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status DeduplicateGraphVizEdge(DtlPrecompilerCtxT *ctx, DbOamapT *edgeMap)
{
    uint32_t hash = DbHash32((uint8_t *)ctx->topo->content, strlen(ctx->topo->content));
    if (DbOamapLookup(edgeMap, hash, ctx->topo->content, NULL) != NULL) {
        return GMERR_OK;
    }

    (void)DbPrintfDefault("%s\n", ctx->topo->content);

    size_t len = strlen(ctx->topo->content) + 1;
    char *edge = (char *)DbDynMemCtxAlloc(ctx->memCtx, len);
    if (edge == NULL) {
        return GMERR_OUT_OF_MEMORY;
    }

    errno_t err = strncpy_s(edge, MAX_NAMESPACE_NAME_LEN, ctx->topo->content, len - 1);
    if (err != EOK) {
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    return DbOamapInsert(edgeMap, hash, edge, edge, NULL);
}

static Status DepthTrvNsItemGraphViz(
    DtlPrecompilerCtxT *ctx, CompilerNsItemT *parent, CompilerNsItemT *child, DbOamapT *edgeMap)
{
    Status ret = GMERR_OK;
    int32_t err = snprintf_s(ctx->topo->content, GMCOMPILER_DISPLAY_STR_LEN, GMCOMPILER_DISPLAY_STR_LEN - 1, "%s->%s",
        parent->namespaceName, child->namespaceName);
    if (err < 0) {
        (void)DbPrintfDefault("gmprecompiler: snprintf worthless.\n");
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    ret = DeduplicateGraphVizEdge(ctx, edgeMap);
    if (ret != GMERR_OK) {
        return ret;
    }

    if (child->isVisited) {
        return GMERR_OK;
    }

    uint32_t hash = DbHash32((uint8_t *)child->namespaceName, strlen(child->namespaceName));
    DbListT *list = (DbListT *)DbOamapLookup(ctx->topo->topoMap, hash, child->namespaceName, NULL);
    if (list == NULL) {
        // 该节点没有出度，提前退出
        return GMERR_OK;
    }

    child->isVisited = true;
    uint32_t cnt = DbListGetItemCnt(list);
    for (uint32_t i = 0; i < cnt; ++i) {
        char *nspName = *(char **)DbListItem(list, i);
        hash = DbHash32((uint8_t *)nspName, strlen(nspName));
        CompilerNsItemT *childSuccessor =
            (CompilerNsItemT *)DbOamapLookup(ctx->topo->namespaceMap, hash, nspName, NULL);
        ret = DepthTrvNsItemGraphViz(ctx, child, childSuccessor, edgeMap);
        if (ret != GMERR_OK) {
            child->isVisited = false;
            return ret;
        }
    }
    child->isVisited = false;

    return GMERR_OK;
}

/*
digraph topology{
Usf->Mir
Mir->Hpf
Hpf->Sacl
Sacl->Acl
Acl->Iptable
Acl->Ipsec
Acl->Ifm
Ifm->Fib6
}
*/
static Status CompilerDisplayTopologyForNsGraphViz(DtlPrecompilerCtxT *ctx, PreCompilerArgumentT *toolOption)
{
    char *nsName = toolOption->filterName;  // 复用-t后参数
    DbOamapT edgeMap = {0};
    Status ret = DbOamapInit(&edgeMap, GMCOMPILER_DEFAULT_OA_MAP_SIZE, DbOamapStringCompare, ctx->memCtx,
        true);  // key:edge(char *) value:edge(char *)
    if (ret != GMERR_OK) {
        (void)DbPrintfDefault("gmprecompiler: Unable to init edge map for GraphViz format.\n");
        return ret;
    }

    (void)DbPrintfDefault("\n========Namespace TOPOLOGY GraphViz=========\ndigraph topology{\n");

    uint32_t hash = DbHash32((const uint8_t *)nsName, strlen(nsName));
    CompilerNsItemT *item = (CompilerNsItemT *)DbOamapLookup(ctx->topo->namespaceMap, hash, nsName, NULL);
    if (item == NULL) {
        (void)DbPrintfDefault("gmprecompiler: Unable to find namespace. ret:%" PRIu32 ".\n", GMERR_UNDEFINED_OBJECT);
        return GMERR_UNDEFINED_OBJECT;
    }

    DbListT *list = (DbListT *)DbOamapLookup(ctx->topo->topoMap, hash, item->namespaceName, NULL);
    if (list == NULL) {
        // 该节点没有出度，提前退出
        (void)DbPrintfDefault("%s\n}\n", nsName);
        return GMERR_OK;
    }

    item->isVisited = true;
    uint32_t cnt = DbListGetItemCnt(list);
    for (uint32_t i = 0; i < cnt; ++i) {
        char *nspName = *(char **)DbListItem(list, i);
        hash = DbHash32((uint8_t *)nspName, strlen(nspName));
        CompilerNsItemT *subItem = (CompilerNsItemT *)DbOamapLookup(ctx->topo->namespaceMap, hash, nspName, NULL);
        if (item == NULL) {
            (void)DbPrintfDefault(
                "gmprecompiler: Unable to find namespace. ret:%" PRIu32 ".\n", GMERR_UNDEFINED_OBJECT);
            return GMERR_UNDEFINED_OBJECT;
        }
        ret = DepthTrvNsItemGraphViz(ctx, item, subItem, &edgeMap);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    (void)DbPrintfDefault("}\n");

    return GMERR_OK;
}

static Status CompilerDisplayTopologyForNs(DtlPrecompilerCtxT *ctx, PreCompilerArgumentT *toolOption)
{
    CompilerDisplayTopologyItf method[] = {
        CompilerDisplayTopologyForNsNormal,
        CompilerDisplayTopologyForNsGraphViz,
    };

    uint32_t type = (uint32_t)ctx->toolOption->isGraphViz;
    return method[type](ctx, toolOption);
}

Status CompilerDisplayTopology(DtlPrecompilerCtxT *ctx, PreCompilerArgumentT *toolOption)
{
    DB_POINTER2(ctx, toolOption);
    CompilerDisplayTopologyItf method[] = {
        [LABEL_TOPO] = CompilerDisplayTopologyForLabel,
        [NS_TOPO] = CompilerDisplayTopologyForNs,
    };

    return method[toolOption->isNsTopo](ctx, toolOption);
}

#ifdef __cplusplus
}
#endif
