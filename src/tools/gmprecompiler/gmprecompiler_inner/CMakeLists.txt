project(gmprecompilerFuzz)

include_sub_directories_recursively(${CMAKE_CURRENT_SOURCE_DIR})
include_directories(${GMDB_ROOT_DIR}/src/compiler/include)

aux_source_directory(${gmprecompiler_SOURCE_DIR}/gmprecompiler_inner SRC_GMPRECOMPILER_LIST)

add_library(gmprecompilerFuzz SHARED ${SRC_GMPRECOMPILER_LIST})

target_link_libraries(gmprecompilerFuzz ${TOOL_NAME} gmdatalogStatic)

install(TARGETS gmprecompilerFuzz LIBRARY DESTINATION lib)
