/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * Description: gmsysview count command
 * Author: wa<PERSON><PERSON><PERSON> gaohaiyang
 * Create: 2021-12-21
 */

#include "db_option_parser.h"
#include "adpt_string.h"
#include "tool_utils.h"
#include "gmc_graph.h"
#include "gmc_kv.h"
#include "gmsysview_sysview.h"
#include "gmsysview_count.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

// 获取gmsysview count/count_table_kv用到的参数
static Status GetCountParaInner(SysviewContextT *ctx, const char *countViewName)
{
    DB_POINTER2(ctx, countViewName);
    ArgInfoT *argInfo = &ctx->argInfo;
    Status ret = SysviewGetConnectionPara(ctx->optionRule, argInfo);
    // count|count_table_kv <tableName>
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SysviewSetStrArgInfo(
        ctx->optionRule, argInfo->vertexLabelName, MAX_TABLE_NAME_LEN, VIEW_OPTION_ARG_COUNT, FIRST_PARAM);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SysviewSetStrArgInfo(
        ctx->optionRule, argInfo->kvTableName, MAX_TABLE_NAME_LEN, VIEW_OPTION_ARG_COUNT_KV, FIRST_PARAM);
    // -ns <namespaceName>
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SysviewSetStrArgInfo(
        ctx->optionRule, argInfo->namespaceName, MAX_NAMESPACE_LENGTH, DB_OPTION_ARG_NAMESPACE, FIRST_PARAM);
    // 解析top参数到-o的optionItem，倒序排序
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = SysviewSetStrArgInfo(
        ctx->optionRule, argInfo->recordNum, RECORD_PROPER_NUM_LEN_MAX, VIEW_OPTION_ARG_COUNT_LIMIT, FIRST_PARAM);
    return ret;
}
static Status GetCountPara(SysviewContextT *ctx, const char *countViewName)
{
    DB_POINTER(ctx);
    ArgInfoT *argInfo = &ctx->argInfo;
    Status ret = GetCountParaInner(ctx, countViewName);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Unable to get CountParaInner.");
        return ret;
    }
    if (strlen(argInfo->recordNum) == 0) {
        if (sprintf_s(argInfo->recordNum, RECORD_PROPER_NUM_LEN_MAX, "%" PRId32, (int16_t)DB_MAX_INT16) < 0) {
            TOOL_WARN(ctx->printFunc, GMERR_FIELD_OVERFLOW, "Unable to parse log str for count view.");
            return GMERR_FIELD_OVERFLOW;
        }
    } else {
        if (strlen(ctx->argInfo.vertexLabelName) != 0) {
            TOOL_WARN(ctx->printFunc, GMERR_FEATURE_NOT_SUPPORTED, "Unable to count vertex name with param of %s",
                VIEW_OPTION_ARG_COUNT_LIMIT);
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
        uint64_t limit = DB_MAX_UINT64;
        if (DbStrToUint64(argInfo->recordNum, &limit) != GMERR_OK || limit == 0 || limit > MAX_TOP_NUM) {
            TOOL_WARN(ctx->printFunc, GMERR_FEATURE_NOT_SUPPORTED, "Param of %s should be [1, %" PRId32 "]",
                VIEW_OPTION_ARG_COUNT_LIMIT, (int32_t)MAX_TOP_NUM);
            return GMERR_FEATURE_NOT_SUPPORTED;
        }
    }

    DbOptionItemT *optionItem = DbGetOptionItemByName(ctx->optionRule, VIEW_OPTION_ARG_ORDER);
    if (optionItem == NULL) {
        TOOL_WARN(ctx->printFunc, GMERR_INVALID_PARAMETER_VALUE, "Can not count view get option");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    optionItem->isUsed = true;
    DbOptionParamT *params = DbGetParamsByOptionItem(optionItem);
    params->paramNum = COUNT_VIEW_TOP_OPT_NUM;
    params->paramVal[0].strVal =
        DbStrCmp(countViewName, V_STORAGE_VERTEX_COUNT, false) == 0 ? COUNT_VERTEX_COL : COUNT_KV_COL;
    params->paramVal[1].strVal = COUNT_DESC;

    optionItem = DbGetOptionItemByName(ctx->optionRule, VIEW_OPTION_ARG_LIMIT);
    if (optionItem == NULL) {
        TOOL_WARN(ctx->printFunc, GMERR_INVALID_PARAMETER_VALUE, "Can not sysview get option");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    optionItem->isUsed = true;

    if (sprintf_s(argInfo->printFlatFmt, RECORD_FLAT_DESC_LEN, "flat_truncate") < 0) {
        TOOL_WARN(ctx->printFunc, GMERR_FIELD_OVERFLOW, "Unable to parse printFlatFmt for count view.");
        return GMERR_FIELD_OVERFLOW;
    }
    return ret;
}

static Status GetTableCountImplInner(SysviewContextT *ctx, bool isVertexLabel)
{
    Status ret = SysviewSetOrder(ctx);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Unable to set sort order for count view.");
        return ret;
    }
    ret = SysviewSetLimit(ctx);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Unable to set limit for count view.");
        return ret;
    }
    ret = GmcExecute(ctx->stmt);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "sysview exec scan view count view");
        return ret;
    }
    ret = SysviewPrint(ctx, true, isVertexLabel);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Unable to get count view inner");
    }
    return ret;
}

static Status InitFilter(SysviewContextT *ctx, char *filterStr, size_t filterStrLen, const TlsConnOptionT *option)
{
    int32_t ret = 0;
    if (strlen(ctx->argInfo.vertexLabelName) != 0) {
        char *vertexName = ctx->argInfo.vertexLabelName;
        if (DbStrCmp(option->namespaceName, DB_DEFAULT_NAMESPACE, false) == 0 || strlen(option->namespaceName) == 0) {
            ret = sprintf_s(filterStr, filterStrLen, "%s='%s'", COUNT_VERTEX_NAME_COL, vertexName);
        } else {
            ret = sprintf_s(
                filterStr, filterStrLen, "%s='%s.%s'", COUNT_VERTEX_NAME_COL, option->namespaceName, vertexName);
        }
    }
    if (ret < 0) {
        return GMERR_FIELD_OVERFLOW;
    }
    if (strlen(ctx->argInfo.kvTableName) != 0) {
        if (DbStrCmp(option->namespaceName, DB_DEFAULT_NAMESPACE, false) == 0 || strlen(option->namespaceName) == 0) {
            ret = sprintf_s(filterStr, filterStrLen, "%s='%s'", COUNT_KV_NAME_COL, ctx->argInfo.kvTableName);
        } else {
            ret = sprintf_s(filterStr, filterStrLen, "%s='%s.%s'", COUNT_KV_NAME_COL, option->namespaceName,
                ctx->argInfo.kvTableName);
        }
    }
    return ret < 0 ? GMERR_FIELD_OVERFLOW : GMERR_OK;
}

// 按需建表过程中，count时和record流程保持一致prepare一下进行建表，sysview count + 按需建表能读出来(on demand)
static Status GetTableCountPrepareStmtByLabelName(SysviewContextT *ctx, const TlsConnOptionT *option)
{
    GmcStmtT *stmt = ctx->stmt;
    // prepare会触发按需建表操作，只返回9010和3000错误码，其余错误不影响count视图后续流程。
    if (strlen(ctx->argInfo.vertexLabelName) != 0) {
        Status ret = GmcPrepareStmtByLabelName(stmt, ctx->argInfo.vertexLabelName, GMC_OPERATION_SCAN);
        if (ret == GMERR_FEATURE_NOT_SUPPORTED || ret == GMERR_UNDEFINED_TABLE) {
            TOOL_WARN(ctx->printFunc, ret, "Unable to prepare stmt for vertex table.");
            return ret;
        }
    }

    if (strlen(ctx->argInfo.kvTableName) != 0) {
        Status ret = GmcKvPrepareStmtByLabelName(stmt, ctx->argInfo.kvTableName);
        if (ret == GMERR_FEATURE_NOT_SUPPORTED || ret == GMERR_UNDEFINED_TABLE) {
            TOOL_WARN(ctx->printFunc, ret, "Unable to prepare stmt for kv table.");
            return ret;
        }
    }
    return GMERR_OK;
}

static Status GetTableCountImpl(SysviewContextT *ctx, const char *countViewName, const TlsConnOptionT *option)
{
    DB_POINTER2(ctx, countViewName);
    // step1：使用指定的命名空间
    Status ret = TlsUseNameSpace(ctx->stmt, option->namespaceName);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Can not find the given namespace for count, ret = %" PRId32 ".", (int32_t)ret);
        return ret;
    }
    ret = GetTableCountPrepareStmtByLabelName(ctx, option);
    if (ret != GMERR_OK) {
        return ret;
    }

    // step2：打开vertexLabel
    GmcStmtT *stmt = ctx->stmt;
    ret = GmcPrepareStmtByLabelName(stmt, countViewName, GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Unable to prepare stmt for sysview.");
        return ret;
    }

    // count view for only one label
    char filterStr[SYSVIEW_FILTER_KEY_MAX_LENGTH] = {0};
    ret = InitFilter(ctx, filterStr, SYSVIEW_FILTER_KEY_MAX_LENGTH, option);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Unable to parse filer str for count view.");
        return ret;
    }
    ret = GmcSetFilter(stmt, filterStr);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Unable to set filer for count view.");
        return ret;
    }
    ret = GetTableCountImplInner(ctx, DbStrCmp(countViewName, V_STORAGE_VERTEX_COUNT, false) == 0);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Unable to get TableCountImplInner.");
        return ret;
    }
    return ret;
}

static Status GetTableCount(SysviewContextT *ctx, const char *countViewName)
{
    DB_POINTER2(ctx, countViewName);
    Status ret = GetCountPara(ctx, countViewName);
    if (ret != GMERR_OK) {
        return ret;
    }
    TlsConnOptionT connOption = {
        .domainName = ctx->argInfo.serverLocator,
        .userName = ctx->argInfo.userName,
        .namespaceName = ctx->argInfo.namespaceName,
        .useReservedConn = ctx->useReservedConn,
        .noMemLimit = false,
    };

    ret = TlsConnect(&ctx->conn, &ctx->stmt, &connOption);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Can not connect sysview, ret = %" PRId32, (int32_t)ret);
        return ret;
    }
    ret = GetTableCountImpl(ctx, countViewName, &connOption);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Can not get sysview count records, ret = %" PRId32, (int32_t)ret);
    }
    TlsDisconnect(ctx->conn, ctx->stmt);
    return ret;
}

Status SysviewVertexCount(SysviewContextT *ctx)
{
    return GetTableCount(ctx, V_STORAGE_VERTEX_COUNT);
}

Status SysviewKvCount(SysviewContextT *ctx)
{
    return GetTableCount(ctx, V_STORAGE_KV_COUNT);
}

void SysviewInitCount(SysviewRulesMgrT *rulesMgr)
{
    DB_POINTER(rulesMgr);
    SysviewRuleT countRules[] = {
        {SYSVIEW_RULE_COUNT_VERTEX_LABEL, SysviewVertexCount}, {SYSVIEW_RULE_COUNT_KV_TABLE, SysviewKvCount}};
    SysviewRegistRules(rulesMgr, countRules, ELEMENT_COUNT(countRules));
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
