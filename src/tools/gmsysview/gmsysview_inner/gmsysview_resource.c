/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: gmsysview resource command
 * Author: cuijinxin
 * Create: 2022-10-25
 */

#include "gmsysview_resource.h"
#include "gmc.h"
#include "clt_stmt.h"
#include "clt_graph_vertex_struct.h"
#include "dm_meta_res_col_pool.h"
#include "dm_data_tree.h"
#include "adpt_string.h"
#include "gmc_internal.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define SPACE_BITS 10
#define BYTE_BINARY_PRINT_SPACE_LEN 2  // 每打印4个比特位打印一个空格，一个字节会打印两个空格

// 资源id分配类型 V3移植
typedef enum {
    DB_RES_POOL_ALLOC_TYPE_ANY = 0, /**< 无限制 */
    DB_RES_POOL_ALLOC_TYPE_EVEN,    /**< 首个资源id必须为偶数 */
    DB_RES_POOL_ALLOC_TYPE_INVALID  /**< 无效 */
} DbResPoolAllocTypeE;

// 资源id分配顺序 V3移植
typedef enum {
    DB_RES_POOL_ORDER_CYCLE = 0, /**< 循环申请，即每次从当前位置往后申请 */
    DB_RES_POOL_ORDER_SEQUENCE,  /**< 顺序申请，每次从起始位置申请 */
    DB_RES_POOL_ORDER_INVALID    /**< 无效 */
} DbResPoolOrderE;

typedef struct {
    char poolName[MAX_RES_POOL_NAME_LEN];  // 资源池名称，用户设置
    uint32_t reference;                    // 引用次数
    uint32_t poolId;                       // 资源池id，唯一性
    uint32_t startId;                      // 起始的res_id
    uint32_t range;                        // 资源池总容量
    uint32_t freeSize;                     // 资源池剩余容量
    uint32_t externPoolId;                 // 扩展资源池id
    DbResPoolAllocTypeE alloc;             // 资源池分配方式，多个，多个且首id=2n
    DbResPoolOrderE order;                 // 资源池分配顺序
    uint32_t memSize;                      // 资源池内存占用信息(共享+动态)
    char status[MAX_RES_POOL_NAME_LEN];    // 资源池状态
    char userName[MAX_USER_NAME_LEN];      // 绑定的表用户名
    char tableName[MAX_TABLE_NAME_LEN];    // 绑定的表名
    uint32_t tableId;                      // 绑定的表id
    DbValueT bitmap;                       // 资源池bitmap
} ResPoolT;

// 找到当前执行的resource命令
inline static uint32_t GetOptionIdxOfCtx(SysviewContextT *ctx)
{
    for (uint32_t i = 0; i < ctx->optionRule->optionItemNum; i++) {
        if (ctx->optionRule->optionItems[i].isUsed &&
            strcmp(ctx->optionRule->optionItems[i].optionName, VIEW_OPTION_ARG_RESOURCE_POOL) != 0) {
            return i;
        }
    }
    return DB_MAX_UINT32;
}

void ResourceErrorPrint(const SysviewContextT *ctx, Status ret)
{
    ctx->printFunc("\n");
    ctx->printFunc("option:\n");
    ctx->printFunc(" resource          <help | -h>                     to get information of resource pool\n");
    ctx->printFunc("\n");
    ctx->printFunc("ErrorCode:%" PRIu32 "\n", ret);
}

Status ResourcePoolPrintHelp(SysviewContextT *ctx)
{
    DB_POINTER(ctx);
    const char *resourceOption[][MANUAL_COL_NUM] = {
        {VIEW_OPTION_ARG_RESOURCE_POOL, "help|-h", "to show this help info"},
        {VIEW_OPTION_ARG_RESOURCE_POOL, "", "to get info of all resource pool"},
        {VIEW_OPTION_ARG_RESOURCE_POOL, "-p [pool_name]", "to get info of resource pool for special pool_name"},
        {VIEW_OPTION_ARG_RESOURCE_POOL, "-e <extern_pool_name>",
            "to get info of extern resource pool for special extern_pool_name"},
        {VIEW_OPTION_ARG_RESOURCE_POOL, "-p <pool_name> <start_index> [count]",
            "to get use status of start_index for resource pool"},
        {VIEW_OPTION_ARG_RESOURCE_POOL, "-b [pool_name]",
            "to get info and bitmap of resource pool for special pool_name"},
        {VIEW_OPTION_ARG_RESOURCE_POOL, "-r <res_id>", "to get info of res_id for resource pool"},
        {VIEW_OPTION_ARG_RESOURCE_POOL, "-t <table_name>", "to get info of resource pool bond to table_name"}};
    DbPrintManual("options:", resourceOption, ELEMENT_COUNT(resourceOption), ctx->printFunc);
    return GMERR_OK;
}

inline static void PrintResourcePoolHead(SysviewContextT *ctx)
{
    SysviewPrintBorderLine(ctx->printFunc, SYSVIEW_PRINT_LETTER_MAX_COUNT, SYSVIEW_SHORT_LINE);
    ctx->printFunc("%s" YELLOW "%-32s" NONE "%s" YELLOW "%10s" NONE "%s" YELLOW "%10s" NONE "%s" YELLOW "%10s" NONE
                   "%s" YELLOW "%5s" NONE "%s" YELLOW "%8s" NONE "%s" YELLOW "%10s" NONE "%s" YELLOW "%10s" NONE
                   "%s" YELLOW "%14s" NONE "%s" YELLOW "%11s" NONE "%s\n",
        "|", "pool_name", "|", "pool_id", "|", "start_id", "|", "range", "|", "alloc", "|", "order", "|", "free", "|",
        "reference", "|", "extern_pool_id", "|", "mem_size(B)", "|");
    SysviewPrintBorderLine(ctx->printFunc, SYSVIEW_PRINT_LETTER_MAX_COUNT, SYSVIEW_SHORT_LINE);
}

// 对资源池赋值
static void SetPoolPropertyValue(DbValueT *propertyValue, char *propeName, ResPoolT *pool)
{
    if (propeName == NULL) {
        return;
    }
    if (strcmp(propeName, "POOL_NAME") == 0) {
        if (strncpy_s(pool->poolName, MAX_RES_POOL_NAME_LEN, propertyValue->value.strAddr,
                propertyValue->value.length) != EOK) {
            return;
        }
    } else if (strcmp(propeName, "POOL_ID") == 0) {
        pool->poolId = propertyValue->value.uintValue;
    } else if (strcmp(propeName, "START_INDEX") == 0 || strcmp(propeName, "START_ID") == 0) {
        pool->startId = propertyValue->value.uintValue;
    } else if (strcmp(propeName, "RANGE") == 0) {
        pool->range = propertyValue->value.uintValue;
    } else if (strcmp(propeName, "ORDER") == 0) {
        pool->order = (DbResPoolOrderE)propertyValue->value.ushortValue;
    } else if (strcmp(propeName, "ALLOC") == 0) {
        pool->alloc = (DbResPoolAllocTypeE)propertyValue->value.ushortValue;
    } else if (strcmp(propeName, "FREE") == 0) {
        pool->freeSize = propertyValue->value.uintValue;
    } else if (strcmp(propeName, "REFERENCE") == 0) {
        pool->reference = propertyValue->value.uintValue;
    } else if (strcmp(propeName, "EXTERN_POOL_ID") == 0) {
        pool->externPoolId = propertyValue->value.uintValue;
    } else if (strcmp(propeName, "STATUS") == 0) {
        if (strncpy_s(pool->status, MAX_RES_POOL_NAME_LEN, propertyValue->value.strAddr, propertyValue->value.length) !=
            EOK) {
            return;
        }
    } else if (strcmp(propeName, "SHARE_MEM_SIZE") == 0 || strcmp(propeName, "DYNAMIC_MEM_SIZE") == 0) {
        pool->memSize += propertyValue->value.uintValue;
    } else if (strcmp(propeName, "BITMAP") == 0) {
        // 位图转换
        pool->bitmap.value.length = propertyValue->value.length;
        pool->bitmap.value.strAddr = propertyValue->value.strAddr;
    } else if (strcmp(propeName, "USER_NAME") == 0 && propertyValue->value.strAddr != NULL &&
               propertyValue->value.length > 0) {
        if (strncpy_s(pool->userName, MAX_USER_NAME_LEN, propertyValue->value.strAddr,
                propertyValue->value.length + 1) != EOK) {
            return;
        }
    } else if (strcmp(propeName, "TABLE_NAME") == 0 && propertyValue->value.strAddr != NULL) {
        if (strncpy_s(pool->tableName, MAX_TABLE_NAME_LEN, propertyValue->value.strAddr,
                propertyValue->value.length + 1) != EOK) {
            return;
        }
    } else if (strcmp(propeName, "TABLE_ID") == 0) {
        pool->tableId = propertyValue->value.uintValue;
    }
}

static Status GetOneResourcePoolInfoFromVertex(DmVertexT *vertex, ResPoolT *pool, bool needJump)
{
    Status ret = GMERR_OK;
    uint32_t propeNum = DmVertexGetPropNum(vertex, false);
    uint32_t jumpNum = 2;  // 末尾两个空值node
    propeNum = needJump ? propeNum - jumpNum : propeNum;
    DmValueT propertyValue = {0};
    for (uint32_t i = 0; i < propeNum; i++) {
        ret = DmVertexGetPropeByIdNoCopy(vertex, i, &propertyValue);
        if (ret != GMERR_OK) {
            return ret;
        }
        char *propeName;
        ret = DmVertexGetPropNameById(vertex, i, &propeName);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (propertyValue.type == DB_DATATYPE_NULL) {
            continue;
        }
        SetPoolPropertyValue(&propertyValue, propeName, pool);
    }
    return ret;
}

static Status GetInfoFromVertexNode(DmNodeT *node, ResPoolT *pool)
{
    Status ret = GMERR_OK;
    uint32_t i = 0;
    uint32_t maxPropNum = DB_MAX_UINT32;
    while (i < maxPropNum) {
        DmValueT propertyValue = {0};
        ret = DmNodeGetPropeByIdNoCopy(node, i, &propertyValue);
        // 未扫描到数据认为是遍历节点结束
        if (ret == GMERR_INVALID_PROPERTY) {
            return GMERR_OK;
        }
        if (ret != GMERR_OK) {
            return ret;
        }
        char *propeName;
        ret = DmNodeGetPropNameById(node, i, &propeName);
        if (ret != GMERR_OK) {
            return ret;
        }
        i++;
        if (propertyValue.type == DB_DATATYPE_NULL) {
            continue;
        }
        SetPoolPropertyValue(&propertyValue, propeName, pool);
    }
    return ret;
}

static void AssignPoolData(const SysviewContextT *ctx, const ResPoolT pool)
{
    DB_POINTER(ctx);
    ctx->printFunc("%s%-32s%s%10u%s%10u%s%10u%s%5s%s%8s%s%10u%s%10u%s%14u%s%11u%s\n", "|", pool.poolName, "|",
        pool.poolId, "|", pool.startId, "|", pool.range, "|",
        pool.alloc == DB_RES_POOL_ALLOC_TYPE_EVEN ? "EVEN" : "ANY", "|",
        pool.order == DB_RES_POOL_ORDER_SEQUENCE ? "SEQUENCE" : "CYCLE", "|", pool.freeSize, "|", pool.reference, "|",
        pool.externPoolId, "|", pool.memSize, "|");
}

static Status PrintResourcePoolInfo(SysviewContextT *ctx, DmVertexT *vertex, uint32_t *totalSize, bool needJump)
{
    DB_POINTER(ctx);
    Status ret = GMERR_OK;
    bool isFinish = false;
    while (vertex != NULL) {
        ret = SysviewFetchVertex(ctx, &isFinish);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (isFinish) {
            break;
        }
        ResPoolT pool = {0};
        ret = GetOneResourcePoolInfoFromVertex(vertex, &pool, needJump);
        if (ret != GMERR_OK) {
            return ret;
        }
        AssignPoolData(ctx, pool);
        *totalSize = *totalSize + pool.memSize;
    }
    return ret;
}

static Status GetResourcePoolInfoByName(SysviewContextT *ctx, ArgInfoT *argInfo, char *viewName)
{
    Status ret = GmcPrepareStmtByLabelName(ctx->stmt, viewName, GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Unsucc to prepare stmt for sysview.");
        return ret;
    }
    char filterStr[SYSVIEW_RESOURCE_FILTER_STR_MAX_LENGTH] = "";
    // POOL_NAME
    if (strlen(argInfo->poolName) > 0) {
        char *condition = ".POOL_NAME='";
        errno_t err = strcpy_s(filterStr, SYSVIEW_RESOURCE_FILTER_STR_MAX_LENGTH, viewName);
        if (err != EOK) {
            return GMERR_FIELD_OVERFLOW;
        }
        err = strcat_s(filterStr, SYSVIEW_RESOURCE_FILTER_STR_MAX_LENGTH, condition);
        if (err != EOK) {
            return GMERR_DATA_EXCEPTION;
        }
        err = strncat_s(
            filterStr, SYSVIEW_RESOURCE_FILTER_STR_MAX_LENGTH, argInfo->poolName, strlen(argInfo->poolName) + 1);
        if (err != EOK) {
            return GMERR_DATA_EXCEPTION;
        }
        char *mark = "'";
        err = strcat_s(filterStr, SYSVIEW_RESOURCE_FILTER_STR_MAX_LENGTH, mark);
        if (err != EOK) {
            return GMERR_DATA_EXCEPTION;
        }
        ret = GmcSetFilter(ctx->stmt, filterStr);
        if (ret != GMERR_OK) {
            TOOL_WARN(ctx->printFunc, GMERR_INVALID_PARAMETER_VALUE, "sysview set filter unsuccessful");
            return ret;
        }
    }
    ret = GmcExecute(ctx->stmt);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "sysview exec scan view unsuccessful");
        return ret;
    }
    return ret;
}

static Status PrintResourcePoolBondTable(SysviewContextT *ctx, DmVertexT *vertex)
{
    DB_POINTER2(ctx, vertex);
    Status ret = GMERR_OK;
    ctx->printFunc("%s res_pool bond to these tables :\n", ctx->argInfo.poolName);
    SysviewPrintBorderLine(ctx->printFunc, SYSVIEW_PRINT_LETTER_LEN_90, SYSVIEW_SHORT_LINE);
    ctx->printFunc("|%-32s", "user_name");
    ctx->printFunc("|%-32s", "table_name");
    ctx->printFunc("|%-20s", "table_id");
    ctx->printFunc("|\n");
    SysviewPrintBorderLine(ctx->printFunc, SYSVIEW_PRINT_LETTER_LEN_90, SYSVIEW_SHORT_LINE);
    DmNodeT *node = vertex->nodes[0];
    if (node) {
        // 绑定的表信息
        ResPoolT pool = {0};
        // 可能没有绑定的表
        DmValueT propertyValue = {0};
        ret = DmNodeGetPropeByIdNoCopy(node, 0, &propertyValue);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (propertyValue.type == DB_DATATYPE_NULL) {
            return GMERR_OK;
        }
        // 解析node
        ret = GetInfoFromVertexNode(vertex->nodes[0], &pool);
        if (ret != GMERR_OK) {
            ResourceErrorPrint(ctx, ret);
            return ret;
        }
        ctx->printFunc("|%-32s", pool.userName);
        ctx->printFunc("|%-32s", pool.tableName);
        ctx->printFunc("|%-20u", pool.tableId);
        ctx->printFunc("|\n");
        SysviewPrintBorderLine(ctx->printFunc, SYSVIEW_PRINT_LETTER_LEN_90, SYSVIEW_SHORT_LINE);
    }
    return GMERR_OK;
}

// resource -p
Status GetAllResourcePoolInfo(SysviewContextT *ctx)
{
    DB_POINTER(ctx);
    ArgInfoT *argInfo = &ctx->argInfo;
    GmcStmtT *stmt = ctx->stmt;
    char *viewName = "V$STORAGE_RESOURCE_ALL_POOL_STAT";
    Status ret = GetResourcePoolInfoByName(ctx, argInfo, viewName);
    if (ret != GMERR_OK) {
        ResourceErrorPrint(ctx, ret);
        return ret;
    }
    ctx->printFunc("the all resource pool information:\n");
    PrintResourcePoolHead(ctx);
    DmVertexT *vertex = CltGetVertexInStmt(stmt);
    // 打印资源池数据
    uint32_t totalSize = 0;
    ret = PrintResourcePoolInfo(ctx, vertex, &totalSize, false);
    ctx->printFunc("the resource pool total size :%" PRIu32 "\n", totalSize);
    return ret;
}

// -p POOL_NAME
Status GetResourcePoolInfo(SysviewContextT *ctx)
{
    DB_POINTER(ctx);
    ArgInfoT *argInfo = &ctx->argInfo;
    GmcStmtT *stmt = ctx->stmt;
    char *viewName = "V$STORAGE_RESOURCE_SINGLE_POOL_BITMAP_STAT";
    Status ret = GetResourcePoolInfoByName(ctx, argInfo, viewName);
    if (ret != GMERR_OK) {
        ResourceErrorPrint(ctx, ret);
        return ret;
    }
    ctx->printFunc("the resource pool information of pool(%s):\n", argInfo->poolName);
    PrintResourcePoolHead(ctx);
    DmVertexT *vertex = CltGetVertexInStmt(stmt);
    // 打印资源池数据
    uint32_t totalSize = 0;
    ret = PrintResourcePoolInfo(ctx, vertex, &totalSize, true);
    SysviewPrintBorderLine(ctx->printFunc, SYSVIEW_PRINT_LETTER_MAX_COUNT, SYSVIEW_SHORT_LINE);
    uint32_t optionIdx = GetOptionIdxOfCtx(ctx);
    char *optionName = ctx->optionRule->optionItems[optionIdx].optionName;
    if (ret != GMERR_OK || strcmp(optionName, VIEW_OPTION_RESOURCE_POOL_EXTEND) == 0) {
        return ret;
    }
    ctx->printFunc("\n");
    ret = PrintResourcePoolBondTable(ctx, vertex);
    ctx->printFunc("\n");
    return ret;
}

// V3 输出兼容
inline static char *ConvertStatusDescription(const char *status)
{
    if (strcmp(status, "USED") == 0) {
        return "used";
    }
    if (strcmp(status, "FREE") == 0) {
        return "free";
    }
    return "invalid";
}

// 通过V$STORAGE_RESOURCE_START_INDEX_USED_STAT视图获取资源池信息
static Status GetResourcePoolFromStartIdxStat(SysviewContextT *ctx, GmcStmtT *stmt, ArgInfoT *argInfo)
{
    DB_POINTER3(ctx, stmt, argInfo);
    char *viewName = "V$STORAGE_RESOURCE_START_INDEX_USED_STAT";
    Status ret = GmcPrepareStmtByLabelName(stmt, viewName, GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Unsuccess to prepare stmt for sysview.");
        return ret;
    }
    char filterStr[SYSVIEW_RESOURCE_FILTER_STR_MAX_LENGTH] = "V$STORAGE_RESOURCE_START_INDEX_USED_STAT.[p]";
    // POOL_NAME V$STORAGE_RESOURCE_START_INDEX_USED_STAT.[p]ResourcePool1[s]0/1

    errno_t err = strcat_s(filterStr, SYSVIEW_RESOURCE_FILTER_STR_MAX_LENGTH, argInfo->poolName);
    if (err != EOK) {
        return GMERR_DATA_EXCEPTION;
    }
    size_t condLen1 = 4;  // "[s]"长度
    err = strncat_s(filterStr, SYSVIEW_RESOURCE_FILTER_STR_MAX_LENGTH, "[s]", condLen1);
    if (err != EOK) {
        return GMERR_DATA_EXCEPTION;
    }
    err = strcat_s(filterStr, SYSVIEW_RESOURCE_FILTER_STR_MAX_LENGTH, argInfo->startIdx);
    if (err != EOK) {
        return GMERR_DATA_EXCEPTION;
    }
    size_t condLen2 = 2;  // "/"长度
    char *count = strlen(argInfo->recordNum) > 0 ? argInfo->recordNum : "1";
    err = strncat_s(filterStr, SYSVIEW_RESOURCE_FILTER_STR_MAX_LENGTH, "/", condLen2);
    if (err != EOK) {
        return GMERR_DATA_EXCEPTION;
    }
    err = strcat_s(filterStr, SYSVIEW_RESOURCE_FILTER_STR_MAX_LENGTH, count);
    if (err != EOK) {
        return GMERR_DATA_EXCEPTION;
    }
    ret = GmcSetFilter(ctx->stmt, filterStr);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, GMERR_INVALID_PARAMETER_VALUE, "sysview set filter unsuccessful");
        return ret;
    }

    return GmcExecute(stmt);
}

inline static void GetStartIdxAndCount(ArgInfoT *argInfo, uint32_t *startIdx, uint32_t *count)
{
    Status ret = DbStrToUint32(argInfo->startIdx, startIdx);
    if (ret != GMERR_OK) {
        *startIdx = 0;
    }
    if (strlen(argInfo->recordNum) != 0) {
        ret = DbStrToUint32(argInfo->recordNum, count);
        if (ret != GMERR_OK) {
            *count = 0;
        }
    }
}

// -p pool_name start_index <count>
Status GetResourcePoolIndexStatus(SysviewContextT *ctx)
{
    DB_POINTER(ctx);
    ArgInfoT *argInfo = &ctx->argInfo;
    GmcStmtT *stmt = ctx->stmt;
    // 获取资源池
    Status ret = GetResourcePoolFromStartIdxStat(ctx, stmt, argInfo);
    uint32_t startIdx = 0;
    uint32_t count = 1;
    GetStartIdxAndCount(argInfo, &startIdx, &count);
    if (ret != GMERR_OK) {
        ctx->printFunc("start_index:%" PRIu32 "(%x) and count(%" PRIu32 ") of %s inv, ret=%" PRId32 "\n", startIdx,
            startIdx, count, argInfo->poolName, ret);
        return ret;
    }
    DmVertexT *vertex = CltGetVertexInStmt(stmt);
    // 打印资源池数据(此处资源池应只有一条)
    if (vertex == NULL) {
        return ret;
    }
    bool isFinish = false;
    ret = SysviewFetchVertex(ctx, &isFinish);
    if (ret != GMERR_OK) {
        ResourceErrorPrint(ctx, ret);
        return ret;
    }
    if (isFinish) {
        return ret;
    }
    ResPoolT pool = {0};
    ret = GetOneResourcePoolInfoFromVertex(vertex, &pool, false);
    if (ret != GMERR_OK) {
        ResourceErrorPrint(ctx, ret);
        return ret;
    }
    ctx->printFunc("start_index:%" PRIu32 "(%x) and count(%" PRIu32 ") of %s is %s\n", pool.startId, pool.startId,
        count, pool.poolName, ConvertStatusDescription(pool.status));
    return ret;
}

inline static bool GetBitmapBit(const uint8_t *bitmap, uint32_t site)
{
    return ((bitmap)[(site) / (BYTE_LENGTH)] & (1 << (site % BYTE_LENGTH))) == 0;
}

static Status ResourcePrintBitMap(SysviewContextT *ctx, DmVertexT *vertex, ResPoolT *pool)
{
    uint32_t bitEachLine = 100;
    // 只取节点2中bitMap数据
    Status ret = GetInfoFromVertexNode(vertex->nodes[1], pool);
    if (ret != GMERR_OK) {
        ResourceErrorPrint(ctx, ret);
        return ret;
    }
    ctx->printFunc("the bitmap of %s(each line with %" PRIu32 "bit):\n", pool->poolName, bitEachLine);
    uint32_t printLen = pool->bitmap.value.length * (BYTE_LENGTH + BYTE_BINARY_PRINT_SPACE_LEN) - 1;
    uint32_t bit = 0;
    uint32_t line = 0;
    uint32_t runCount = printLen / 8 + 1;
    for (uint32_t n = 0; n < runCount; ++n) {
        for (uint32_t j = 0; j < BITS_PER_BYTE && bit <= printLen; ++j) {
            if (bit % bitEachLine == 0) {
                ctx->printFunc("\n%4u:", ++line);
            }
            if (bit % SPACE_BITS == 0) {
                ctx->printFunc(" ");
            }
            // 防止越界
            if (j >= printLen || GetBitmapBit((const uint8_t *)pool->bitmap.value.strAddr, bit)) {
                ctx->printFunc("%" PRIu32 "", 0);
            } else {
                ctx->printFunc("%" PRIu32 "", 1);
            }
            ++bit;
        }
    }
    ctx->printFunc("\n");
    return ret;
}

// -b
Status GetResourcePoolBitMap(SysviewContextT *ctx)
{
    DB_POINTER(ctx);
    ArgInfoT *argInfo = &ctx->argInfo;
    char *viewName = "V$STORAGE_RESOURCE_SINGLE_POOL_BITMAP_STAT";
    Status ret = GetResourcePoolInfoByName(ctx, argInfo, viewName);
    if (ret != GMERR_OK) {
        ResourceErrorPrint(ctx, ret);
        return ret;
    }

    PrintResourcePoolHead(ctx);
    DmVertexT *vertex = CltGetVertexInStmt(ctx->stmt);
    bool isFinish = false;
    while (vertex != NULL) {
        ret = SysviewFetchVertex(ctx, &isFinish);
        if (ret != GMERR_OK) {
            ResourceErrorPrint(ctx, ret);
            return ret;
        }
        if (isFinish) {
            break;
        }
        ResPoolT pool = {0};
        ret = GetOneResourcePoolInfoFromVertex(vertex, &pool, true);
        if (ret != GMERR_OK) {
            ResourceErrorPrint(ctx, ret);
            return ret;
        }
        ctx->printFunc("%s%-32s%s%10u%s%10u%s%10u%s%5s%s%8s%s%10u%s%10u%s%14u%s%11d%s\n", "|", pool.poolName, "|",
            pool.poolId, "|", pool.startId, "|", pool.range, "|",
            pool.alloc == DB_RES_POOL_ALLOC_TYPE_EVEN ? "EVEN" : "ANY", "|",
            pool.order == DB_RES_POOL_ORDER_SEQUENCE ? "SEQUENCE" : "CYCLE", "|", pool.freeSize, "|", pool.reference,
            "|", pool.externPoolId, "|", pool.memSize, "|");
        if (strlen(argInfo->poolName) > 0) {
            ctx->printFunc("\n");
            ret = PrintResourcePoolBondTable(ctx, vertex);
            if (ret != GMERR_OK) {
                return ret;
            }
            ctx->printFunc("\n");
            ret = ResourcePrintBitMap(ctx, vertex, &pool);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    return ret;
}

// -e 参数必选，但是为了兼容V3于此处打印帮助信息
Status GetExternResourcePoolInfo(SysviewContextT *ctx)
{
    DB_POINTER(ctx);
    Status ret = GMERR_OK;
    ArgInfoT *argInfo = &ctx->argInfo;
    if (strlen(argInfo->poolName) == 0) {
        ResourceErrorPrint(ctx, GMERR_NULL_VALUE_NOT_ALLOWED);
        return ret;
    }
    ret = GetResourcePoolInfo(ctx);
    return ret;
}

// 通过V$STORAGE_RESOURCE_BIND_TO_LABEL_STAT视图获取资源池
static Status GetResourceFromBindLabelStat(SysviewContextT *ctx, ArgInfoT *argInfo)
{
    DB_POINTER2(ctx, argInfo);
    char *viewName = "V$STORAGE_RESOURCE_BIND_TO_LABEL_STAT";
    Status ret = GmcPrepareStmtByLabelName(ctx->stmt, viewName, GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Unsucc to prepare stmt for sysview.");
        return ret;
    }

    char *condition = ".[t]";
    char filterStr[SYSVIEW_RESOURCE_FILTER_STR_MAX_LENGTH] = "";
    errno_t err = strcpy_s(filterStr, SYSVIEW_RESOURCE_FILTER_STR_MAX_LENGTH, viewName + 1);
    if (err != EOK) {
        return GMERR_FIELD_OVERFLOW;
    }
    err = strcat_s(filterStr, SYSVIEW_RESOURCE_FILTER_STR_MAX_LENGTH, condition);
    if (err != EOK) {
        return GMERR_DATA_EXCEPTION;
    }
    err = strcat_s(filterStr, SYSVIEW_RESOURCE_FILTER_STR_MAX_LENGTH, argInfo->vertexLabelName);
    if (err != EOK) {
        return GMERR_DATA_EXCEPTION;
    }
    ret = GmcSetFilter(ctx->stmt, filterStr);
    if (ret != GMERR_OK) {
        TOOL_WARN(
            ctx->printFunc, GMERR_INVALID_PARAMETER_VALUE, "search bond resource pool unsuccessful when set filter");
        return ret;
    }
    return GmcExecute(ctx->stmt);
}

// -t 参数必选，但是为了兼容V3于此处打印帮助信息
Status GetResourcePoolTable(SysviewContextT *ctx)
{
    DB_POINTER(ctx);
    Status ret = GMERR_OK;
    ArgInfoT *argInfo = &ctx->argInfo;
    if (strlen(argInfo->vertexLabelName) == 0) {
        ResourceErrorPrint(ctx, GMERR_NULL_VALUE_NOT_ALLOWED);
        return ret;
    }
    // 获取资源池
    ret = GetResourceFromBindLabelStat(ctx, argInfo);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "sysview exec scan view unsucc");
        return ret;
    }
    ctx->printFunc("the resource pool information bond to table(%s):\n", argInfo->vertexLabelName);
    PrintResourcePoolHead(ctx);
    DmVertexT *vertex = CltGetVertexInStmt(ctx->stmt);
    if (vertex != NULL) {
        bool isFinish = false;
        ret = SysviewFetchVertex(ctx, &isFinish);
        if (ret != GMERR_OK || isFinish) {
            return ret;
        }
        ResPoolT pool = {0};
        // 固定解析下标为0的node
        ret = GetInfoFromVertexNode(vertex->nodes[0], &pool);
        if (ret != GMERR_OK) {
            return ret;
        }
        AssignPoolData(ctx, pool);
    }
    return ret;
}

// 通过V$STORAGE_RESOURCE_RESID_STAT视图获取资源池
static Status GetResourceFromResIdStat(SysviewContextT *ctx, ArgInfoT *argInfo)
{
    DB_POINTER2(ctx, argInfo);
    char *viewName = "V$STORAGE_RESOURCE_RESID_STAT";
    Status ret = GmcPrepareStmtByLabelName(ctx->stmt, viewName, GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Unsucc to prepare stmt for sysview.");
        return ret;
    }
    char filterStr[SYSVIEW_RESOURCE_FILTER_STR_MAX_LENGTH] = "V$STORAGE_RESOURCE_RESID_STAT.[r]";
    // V$STORAGE_RESOURCE_RESID_STAT.[r]137438953600
    errno_t r = strcat_s(filterStr, SYSVIEW_RESOURCE_FILTER_STR_MAX_LENGTH, argInfo->resId);
    if (r != EOK) {
        return GMERR_DATA_EXCEPTION;
    }
    ret = GmcSetFilter(ctx->stmt, filterStr);
    if (ret != GMERR_OK) {
        TOOL_WARN(
            ctx->printFunc, GMERR_INVALID_PARAMETER_VALUE, "search resource pool by resid unsucc when set filter");
        return ret;
    }
    return GmcExecute(ctx->stmt);
}

// -r 参数必选，但是为了兼容V3于此处打印帮助信息
Status GetResourcePoolInfoByResId(SysviewContextT *ctx)
{
    DB_POINTER(ctx);
    Status ret = GMERR_OK;
    ArgInfoT *argInfo = &ctx->argInfo;
    if (strlen(argInfo->resId) == 0) {
        ResourceErrorPrint(ctx, GMERR_NULL_VALUE_NOT_ALLOWED);
        return ret;
    }
    // 获取资源池
    ret = GetResourceFromResIdStat(ctx, argInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint64_t resId;
    ret = DbStrToUint64(argInfo->resId, &resId);
    if (ret != GMERR_OK) {
        resId = 0;
    }
    uint32_t poolId = DmResColGetPoolIdFromResId(resId);
    uint32_t count = DmResColGetCountFromResId(resId);
    uint32_t startIndex = DmResColGetStartIndexFromResId(resId);
    ctx->printFunc("res_id(%" PRIu64 "): pool_id:%" PRIu32 ", count:%" PRIu32 ", start_index:%" PRIu32 "\n", resId,
        poolId, count, startIndex);
    PrintResourcePoolHead(ctx);
    DmVertexT *vertex = CltGetVertexInStmt(ctx->stmt);
    // 打印资源池数据(此处资源池应只有一条)
    if (vertex == NULL) {
        return ret;
    }
    bool isFinish = false;
    ret = SysviewFetchVertex(ctx, &isFinish);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (isFinish) {
        return ret;
    }
    ResPoolT pool = {0};
    ret = GetOneResourcePoolInfoFromVertex(vertex, &pool, true);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 固定解析下标为0的node
    ret = GetInfoFromVertexNode(vertex->nodes[0], &pool);
    ctx->printFunc("%s%-32s%s%10u%s%10u%s%10u%s%5s%s%8s%s%10u%s%10u%s%14u%s%11u%s\n", "|", pool.poolName, "|",
        pool.poolId, "|", pool.startId, "|", pool.range, "|",
        pool.alloc == DB_RES_POOL_ALLOC_TYPE_EVEN ? "EVEN" : "ANY", "|",
        pool.order == DB_RES_POOL_ORDER_SEQUENCE ? "SEQUENCE" : "CYCLE", "|", pool.freeSize, "|", pool.reference, "|",
        pool.externPoolId, "|", pool.memSize, "|");
    ctx->printFunc("start_index:%" PRIu32 "(%x) and count(%" PRIu32 ") of %s is %s\n", startIndex, startIndex, count,
        pool.poolName, ConvertStatusDescription(pool.status));

    return ret;
}

// resource 命令每个分支走不同逻辑，参数不复用
static Status GetResourceParamsAndFunc(SysviewContextT *ctx, char *optionName, SysviewRuleProc *execfunc)
{
    ArgInfoT *argInfo = &ctx->argInfo;
    DbOptionRuleT *optionRule = ctx->optionRule;
    Status ret = SysviewGetConnectionPara(optionRule, argInfo);
    if (ret != GMERR_OK) {
        return ret;
    }
    *execfunc = &ResourcePoolPrintHelp;
    // optionRule下标不固定，不能通过switch case (int) 获取参数
    if (strcmp(optionName, VIEW_OPTION_RESOURCE_POOL_POINT) == 0) {
        // -p <pool_name> || -p pool_name start_index <count>
        ret = SysviewSetStrArgInfo(
            optionRule, argInfo->poolName, MAX_RES_POOL_NAME_LEN, VIEW_OPTION_RESOURCE_POOL_POINT, FIRST_PARAM);
        if (ret == GMERR_OK) {
            ret = SysviewSetStrArgInfo(optionRule, argInfo->startIdx, RECORD_PROPER_NUM_LEN_MAX,
                VIEW_OPTION_RESOURCE_POOL_POINT, SECOND_PARAM);
        }
        if (ret == GMERR_OK) {
            ret = SysviewSetStrArgInfo(optionRule, argInfo->recordNum, RECORD_PROPER_NUM_LEN_MAX,
                VIEW_OPTION_RESOURCE_POOL_POINT, THIRD_PARAM);
        }
        if (strlen(argInfo->startIdx) > 0) {
            *execfunc = &GetResourcePoolIndexStatus;
            return ret;
        }
        if (strlen(argInfo->poolName) > 0) {
            *execfunc = &GetResourcePoolInfo;
            return ret;
        }
        *execfunc = &GetAllResourcePoolInfo;
    } else if (strcmp(optionName, VIEW_OPTION_RESOURCE_POOL_EXTEND) == 0) {
        // -e extern_pool_name
        ret = SysviewSetStrArgInfo(
            optionRule, argInfo->poolName, MAX_RES_POOL_NAME_LEN, VIEW_OPTION_RESOURCE_POOL_EXTEND, FIRST_PARAM);
        *execfunc = &GetExternResourcePoolInfo;
    } else if (strcmp(optionName, VIEW_OPTION_RESOURCE_POOL_BIND) == 0) {
        // -b <pool_name>
        ret = SysviewSetStrArgInfo(
            optionRule, argInfo->poolName, MAX_RES_POOL_NAME_LEN, VIEW_OPTION_RESOURCE_POOL_BIND, FIRST_PARAM);
        *execfunc = &GetResourcePoolBitMap;
    } else if (strcmp(optionName, VIEW_OPTION_RESOURCE_POOL_TABLE) == 0) {
        // -t table_name
        ret = SysviewSetStrArgInfo(
            optionRule, argInfo->vertexLabelName, MAX_TABLE_NAME_LEN, VIEW_OPTION_RESOURCE_POOL_TABLE, FIRST_PARAM);
        *execfunc = &GetResourcePoolTable;
    } else if (strcmp(optionName, VIEW_OPTION_RESOURCE_POOL_RESID) == 0) {
        // -r res_id(pool_id count start_index)
        ret = SysviewSetStrArgInfo(
            optionRule, argInfo->resId, RESOURCE_POOL_RES_ID_LEN_MAX, VIEW_OPTION_RESOURCE_POOL_RESID, FIRST_PARAM);
        *execfunc = &GetResourcePoolInfoByResId;
    }
    return ret;
}

static Status DisplayResourceInfo(SysviewContextT *ctx)
{
    DB_POINTER(ctx);
    Status ret = GMERR_OK;
    SysviewRuleProc execfunc;
    uint32_t optionIdx = GetOptionIdxOfCtx(ctx);
    // 未找到命令则执行resource查询所有资源池
    if (optionIdx == DB_MAX_UINT32) {
        execfunc = GetAllResourcePoolInfo;
    } else {
        char *optionName = ctx->optionRule->optionItems[optionIdx].optionName;
        if (strcmp(optionName, DB_OPTION_ARG_LONG_HELP) == 0 || strcmp(optionName, DB_OPTION_ARG_HELP) == 0) {
            (void)ResourcePoolPrintHelp(ctx);
            return GMERR_OK;
        }
        ret = GetResourceParamsAndFunc(ctx, optionName, &execfunc);
        if (ret != GMERR_OK) {
            DB_LOG_WARN(ret, "Exec gmsysview resource, ret=%" PRIu32 ".", ret);
            (void)ResourcePoolPrintHelp(ctx);
            return ret;
        }
    }
    // 建连
    if (!ctx->isConn) {
        TlsConnOptionT option = {
            .domainName = DbIsTcp() ? ctx->argInfo.serverLocator : DB_DEFAULT_DOMAIN_NAME,
            .userName = DbIsTcp() ? ctx->argInfo.userName : NULL,
            .useReservedConn = ctx->useReservedConn,
            .namespaceName = NULL,
            .noMemLimit = false,
        };
        ret = TlsConnect(&ctx->conn, &ctx->stmt, &option);
        if (ret != GMERR_OK) {
            return ret;
        }
        ctx->isConn = true;
    }
    // 执行分支函数
    execfunc(ctx);
    // 断连
    TlsDisconnect(ctx->conn, ctx->stmt);
    return ret;
}

void SysviewInitResource(SysviewRulesMgrT *rulesMgr)
{
    DB_POINTER(rulesMgr);
    SysviewRuleT resourceRules[] = {{SYSVIEW_RULE_RESOURCE, DisplayResourceInfo}};
    // 规则注册
    SysviewRegistRules(rulesMgr, resourceRules, ELEMENT_COUNT(resourceRules));
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
