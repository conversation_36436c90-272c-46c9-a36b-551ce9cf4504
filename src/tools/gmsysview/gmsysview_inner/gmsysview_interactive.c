/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * Description: source file for gmsysview interactive
 * Author: gaohaiyang
 * Create: 2021-12-21
 */

#include "db_option_parser.h"
#include "adpt_string.h"
#include "tool_interactive.h"
#include "gmsysview_sysview.h"
#include "gmsysview_interactive.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

Status SysviewClientConnectWrapper(void *argCtx, const char *str, bool *isFinish)
{
    DB_POINTER2(argCtx, isFinish);
    *isFinish = false;
    SysviewContextT *ctx = (SysviewContextT *)argCtx;
    if (str == NULL) {
        TOOL_RUN_ERROR(ctx->printFunc, GMERR_INVALID_PARAMETER_VALUE, "sysview connect unsucc: command is empty.\n");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (ctx->isConn) {
        (void)ctx->printFunc("Already connected.\n");
        return GMERR_CONNECTION_EXCEPTION;
    }
    errno_t err = strcpy_s(ctx->argInfo.serverLocator, SYSVIEW_SERVER_MAX_LEN, str);
    if (err != EOK) {
        return GMERR_FIELD_OVERFLOW;
    }
    TlsConnOptionT option = {
        .domainName = ctx->argInfo.serverLocator,
        .userName = ctx->argInfo.userName,
        .namespaceName = NULL,
        .useReservedConn = ctx->useReservedConn,
        .noMemLimit = false,
    };

    Status ret = TlsConnect(&ctx->conn, &ctx->stmt, &option);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(ctx->printFunc, ret, "Can not connect sysview, ret = %" PRId32, (int32_t)ret);
        ctx->conn = NULL;
        return ret;
    }
    (void)ctx->printFunc("connect success!\n");
    ctx->isConn = true;
    return GMERR_OK;
}

Status SysviewDisConnectWrapper(void *argCtx, const char *disConnArg, bool *isFinish)
{
    DB_POINTER2(argCtx, isFinish);
    SysviewContextT *ctx = (SysviewContextT *)argCtx;
    if (disConnArg != NULL && strlen(disConnArg) != 0) {
        (void)ctx->printFunc("command not found\n");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (!ctx->isConn) {
        (void)ctx->printFunc("Not connect yet.\n");
    } else {
        TlsDisconnect(ctx->conn, ctx->stmt);
        (void)ctx->printFunc("Disconnect success!\n");
    }
    ctx->argInfo.serverLocator[0] = 0;

    ctx->isConn = false;
    *isFinish = false;
    return GMERR_OK;
}

Status SysviewQueryViewWrapper(void *argCtx, const char *str, bool *isFinish)
{
    DB_POINTER2(argCtx, isFinish);
    SysviewContextT *ctx = (SysviewContextT *)argCtx;
    *isFinish = false;
    // 未连接
    if (!ctx->isConn) {
        TOOL_RUN_ERROR(ctx->printFunc, GMERR_CLIENT_UNABLE_TO_ESTABLISH_CONNECTION, "No conn in gmsysview.");
        return GMERR_CLIENT_UNABLE_TO_ESTABLISH_CONNECTION;
    }
    if (str == NULL) {
        (void)ctx->printFunc("command is empty.\n");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    char viewArg[TLS_LINE_MAX_LEN] = {0};
    if (strcpy_s(viewArg, TLS_LINE_MAX_LEN, str) != EOK) {
        return GMERR_FIELD_OVERFLOW;
    }
    // lastStr指向viewArg ，用于操作viewArg
    char *lastStr = viewArg;
    // 视图名
    char *viewName = strtok_r(lastStr, " ", &lastStr);
    if (viewName == NULL || strlen(viewName) == 0) {
        DB_LOG_ERROR(GMERR_INVALID_NAME, "sysview name.");
        return GMERR_INVALID_NAME;
    }
    // 固定字段filter
    char *filter = strtok_r(lastStr, " ", &lastStr);
    if (filter != NULL && DbStrCmp(filter, "filter", false) != 0) {
        (void)ctx->printFunc("command not found\n");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    // 过滤条件
    char *filterCond = ((filter == NULL) ? "" : lastStr);
    // 查询
    return QuerySysviewImpl(ctx, viewName, filterCond, true);
}

Status SysviewPrintInteractionQuit(void *argCtx, const char *quitArg, bool *isFinish)
{
    DB_POINTER2(argCtx, isFinish);
    SysviewContextT *ctx = (SysviewContextT *)argCtx;
    if (quitArg != NULL && strlen(quitArg) != 0) {
        (void)ctx->printFunc("command not found\n");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    *isFinish = true;
    return GMERR_OK;
}

Status SysviewPrintInteractionHelp(void *argCtx, const char *helpArg, bool *isFinish)
{
    DB_POINTER2(argCtx, isFinish);
    SysviewContextT *ctx = (SysviewContextT *)argCtx;
    if (helpArg != NULL && strlen(helpArg) != 0) {
        (void)ctx->printFunc("command not found\n");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    *isFinish = false;
    TlsPrintKeywordInfo();
    return GMERR_OK;
}

Status InitInteraction(void)
{
    TlsFuncMappingT syvFuncMapping[] = {{"help", "help", SysviewPrintInteractionHelp},
        {"connect", "connect <server_locator>", SysviewClientConnectWrapper},
        {"disconnect", "disconnect", SysviewDisConnectWrapper},
        {"query", "query <table_name> [filter <filter_condition>]", SysviewQueryViewWrapper},
        {"quit", "quit", SysviewPrintInteractionQuit}};
    Status ret = TlsInitFuncMapping(syvFuncMapping, ELEMENT_COUNT(syvFuncMapping));
    if (ret != GMERR_OK) {
        return ret;
    }
    char *syvExtraKeyword[] = {"filter", NULL};
    for (uint32_t i = 0; syvExtraKeyword[i]; i++) {
        ret = TlsAddKeyWord(syvExtraKeyword[i]);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

Status EnterInteraction(SysviewContextT *ctx)
{
    DB_POINTER(ctx);
    ctx->isInteractive = true;
    // 初始化交互式上下文
    Status ret = InitInteraction();
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(ctx->printFunc, ret, "sysview init interaction unsucc, %" PRId32 ".", (int32_t)ret);
        return ret;
    }
    // 开启交互式模式
    (void)TlsInteractiveProcess((void *)ctx, (char *)"gmsysview>> ");
    if (ctx->isConn) {
        TlsDisconnect(ctx->conn, ctx->stmt);
        (void)ctx->printFunc("Automatic disconnection.\n");
    }
    return ret;
}

void SysviewInitInteraction(SysviewRulesMgrT *ruleMgr)
{
    DB_POINTER(ruleMgr);
    SysviewRuleT interactionRules[] = {{SYSVIEW_RULE_E, EnterInteraction}};
    SysviewRegistRules(ruleMgr, interactionRules, ELEMENT_COUNT(interactionRules));
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
