/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * Description: source file for gmsysview entry
 * Author: gaohaiyang
 * Create: 2021-12-22
 */

#include "gmsysview_entry.h"
#include "gmsysview_sysview.h"
#include "gmsysview_record.h"
#include "gmsysview_count.h"
#include "gmsysview_alarm.h"
#include "gmsysview_interactive.h"
#include "gmsysview_estimate.h"
#include "db_option_parser.h"
#include "adpt_memory.h"
#include "db_error.h"
#include "adpt_string.h"
#include "gmc_sysview.h"
#include "gmsysview_subtree.h"
#include "gmsysview_resource.h"
#include "gmsysview_show.h"
#include "gmsysview_systable.h"
#if defined(FEATURE_TS) || defined(TS_MULTI_INST)
#include "gmsysview_ts.h"
#endif
#ifdef FEATURE_STREAM
#include "gmsysview_streamgraphvis.h"
#endif
#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define SYSVIEW_RECORD_TABLE_KV_MIN_PARAM_NUM 3  // record_table_kv命令最少参数个数
#define SYSVIEW_DIAGNOSE_MAX_INTERVAL_MS 5000    // 一键诊断视图最大查询间隔时间ms

static DbRuleItemT g_gmdbSysviewRuleItems[] = {
    {0, (int32_t)SYSVIEW_RULE_H, {DB_OPTION_ARG_HELP, DB_OPTION_ARG_USE_RESERVED_CONN, DB_OPTION_ARG_OSC},
        {true, false, false}},
    {0, (int32_t)SYSVIEW_RULE_HELP, {DB_OPTION_ARG_LONG_HELP, DB_OPTION_ARG_USE_RESERVED_CONN, DB_OPTION_ARG_OSC},
        {true, false, false}},
    {0, (int32_t)SYSVIEW_RULE_V, {DB_OPTION_ARG_VERSION, DB_OPTION_ARG_USE_RESERVED_CONN, DB_OPTION_ARG_OSC},
        {true, false, false}},
    {0, (int32_t)SYSVIEW_RULE_QUERY,
        {VIEW_OPTION_ARG_NAME, DB_OPTION_ARG_DOMAIN_NAME, VIEW_OPTION_ARG_FILTER, VIEW_OPTION_ARG_ORDER,
            VIEW_OPTION_ARG_LIMIT, DB_OPTION_ARG_USE_RESERVED_CONN, VIEW_OPTION_ARG_FLAT, DB_OPTION_ARG_OSC,
            VIEW_OPTION_ARG_FIXED_FORMAT, DB_OPTION_ARG_USER_NAME},
        {true, false, false, false, false, false, false, false, false, false}},
    {0, (int32_t)SYSVIEW_RULE_E, {DB_OPTION_ARG_USE_RESERVED_CONN}, {false, false}},
    {0, (int32_t)SYSVIEW_RULE_RECORD_VERTEX_LABEL,
        {VIEW_OPTION_ARG_RECORD, DB_OPTION_ARG_NAMESPACE, VIEW_OPTION_ARG_CONDITION, VIEW_OPTION_ARG_FILTER,
            DB_OPTION_ARG_DOMAIN_NAME, DB_OPTION_ARG_USE_RESERVED_CONN, VIEW_OPTION_ARG_FLAT, DB_OPTION_ARG_OSC,
            DB_OPTION_ARG_USER_NAME},
        {true, false, false, false, false, false, false, false, false}},
    {0, (int32_t)SYSVIEW_RULE_RECORD_KV_TABLE,
        {VIEW_OPTION_ARG_RECORD_KV, DB_OPTION_ARG_NAMESPACE, VIEW_OPTION_ARG_CONDITION, VIEW_OPTION_ARG_FILTER,
            DB_OPTION_ARG_DOMAIN_NAME, DB_OPTION_ARG_USE_RESERVED_CONN, DB_OPTION_ARG_OSC, DB_OPTION_ARG_USER_NAME},
        {true, false, false, false, false, false, false, false, false}},
    {0, (int32_t)SYSVIEW_RULE_COUNT_VERTEX_LABEL,
        {VIEW_OPTION_ARG_COUNT, DB_OPTION_ARG_NAMESPACE, DB_OPTION_ARG_DOMAIN_NAME, VIEW_OPTION_ARG_COUNT_LIMIT,
            DB_OPTION_ARG_USE_RESERVED_CONN, DB_OPTION_ARG_OSC, DB_OPTION_ARG_USER_NAME},
        {true, false, false, false, false, false, false, false}},
    {0, (int32_t)SYSVIEW_RULE_COUNT_KV_TABLE,
        {VIEW_OPTION_ARG_COUNT_KV, DB_OPTION_ARG_NAMESPACE, DB_OPTION_ARG_DOMAIN_NAME, VIEW_OPTION_ARG_COUNT_LIMIT,
            DB_OPTION_ARG_USE_RESERVED_CONN, DB_OPTION_ARG_OSC, DB_OPTION_ARG_USER_NAME},
        {true, false, false, false, false, false, false, false}},
    {0, (int32_t)SYSVIEW_RULE_ALARM,
        {VIEW_OPTION_ARG_ALARM, DB_OPTION_ARG_DOMAIN_NAME, DB_OPTION_ARG_USE_RESERVED_CONN, DB_OPTION_ARG_OSC,
            DB_OPTION_ARG_USER_NAME},
        {true, false, false, false}},
    {0, (int32_t)SYSVIEW_RULE_ESTIMATE,
        {VIEW_OPTION_ARG_ESTIMATE, DB_OPTION_ARG_DOMAIN_NAME, DB_OPTION_ARG_USE_RESERVED_CONN, DB_OPTION_ARG_OSC},
        {true, false, false, false, false}},
    {0, (int32_t)SYSVIEW_RULE_SUBTREE,
        {VIEW_OPTION_ARG_SUBTREE, DB_OPTION_ARG_HELP, DB_OPTION_ARG_LONG_HELP, DB_OPTION_ARG_NAMESPACE,
            VIEW_OPTION_ARG_ROOT_NAME, VIEW_OPTION_ARG_FILTER_MODE, VIEW_OPTION_ARG_MAX_DEPTH,
            VIEW_OPTION_ARG_DEFAULT_MODE, VIEW_OPTION_ARG_CONFIG_FLAG, VIEW_OPTION_ARG_SUBTREE_JSON,
            DB_OPTION_ARG_DOMAIN_NAME, DB_OPTION_ARG_USE_RESERVED_CONN, DB_OPTION_ARG_USER_NAME},
        {true, false, false, false, false, false, false, false, false}},
    {0, (int32_t)SYSVIEW_RULE_RESOURCE,
        {VIEW_OPTION_ARG_RESOURCE_POOL, DB_OPTION_ARG_HELP, DB_OPTION_ARG_LONG_HELP, VIEW_OPTION_RESOURCE_POOL_POINT,
            VIEW_OPTION_RESOURCE_POOL_EXTEND, VIEW_OPTION_RESOURCE_POOL_BIND, VIEW_OPTION_RESOURCE_POOL_RESID,
            VIEW_OPTION_RESOURCE_POOL_TABLE, DB_OPTION_ARG_OSC, DB_OPTION_ARG_DOMAIN_NAME, DB_OPTION_ARG_USER_NAME,
            DB_OPTION_ARG_USE_RESERVED_CONN},
        {true, false, false, false, false, false, false, false, false, false, false, false}},
    {0, (int32_t)SYSVIEW_RULE_SHOW,
        {VIEW_OPTION_ARG_SHOW, DB_OPTION_ARG_OSC, DB_OPTION_ARG_DOMAIN_NAME, DB_OPTION_ARG_USE_RESERVED_CONN,
            DB_OPTION_ARG_USER_NAME},
        {true, false, false, false, false}},
    {0, (int32_t)SYSVIEW_RULE_DIAGNOSE,
        {VIEW_OPTION_ARG_DIAGNOSE, VIEW_OPTION_ARG_DIAGNOSE_INTERVAL, DB_OPTION_ARG_OSC, DB_OPTION_ARG_DOMAIN_NAME,
            DB_OPTION_ARG_USE_RESERVED_CONN, DB_OPTION_ARG_USER_NAME},
        {true, false, false, false, false, false, false}},
    {0, (int32_t)SYSVIEW_RULE_SYSTABLE, {VIEW_OPTION_SYSTABLE, DB_OPTION_ARG_NAME, DB_OPTION_ARG_USE_RESERVED_CONN},
        {true, true, false}},
#ifdef TS_MULTI_INST
    {0, (int32_t)SYSVIEW_RULE_TS_SQL, {VIEW_OPTION_ARG_SQL, DB_OPTION_ARG_DOMAIN_NAME, DB_OPTION_ARG_USER_NAME},
        {true, false, false}},
    {0, (int32_t)SYSVIEW_RULE_TS_EXPLAIN, {VIEW_OPTION_ARG_EXPLAIN, DB_OPTION_ARG_DOMAIN_NAME, DB_OPTION_ARG_USER_NAME},
        {true, false, false}},
    {0, (int32_t)SYSVIEW_RULE_TS_FMT,
        {VIEW_OPTION_ARG_FORMAT, DB_OPTION_ARG_DOMAIN_NAME, VIEW_OPTION_ARG_SQL, DB_OPTION_ARG_USER_NAME},
        {true, false, true, false}},
#endif
#ifdef FEATURE_STREAM
    {0, (int32_t)SYSVIEW_RULE_STREAM, {VIEW_OPTION_ARG_STREAM, VIEW_OPTION_VERTICALGRAPH}, {true, false}},
#endif
};

Status GmSysviewAllocOptionRuleItems(DbOptionRuleT *optionRule)
{
    DB_POINTER(optionRule);
    return DbAllocOptionRuleItems(optionRule, GMSYSVIEW_OPTION_NUM, ELEMENT_COUNT(g_gmdbSysviewRuleItems));
}

static void GmsysviewInitRules(SysviewRulesMgrT *rulesMgr)
{
    SysviewInitView(rulesMgr);
    SysviewInitRecord(rulesMgr);
    SysviewInitCount(rulesMgr);
    SysviewInitInteraction(rulesMgr);
    SysviewInitEstimate(rulesMgr);
    SysviewInitAlarm(rulesMgr);
    SysviewInitResource(rulesMgr);
    SysviewInitShow(rulesMgr);
    SysviewInitDiagnoticView(rulesMgr);
#ifdef FEATURE_YANG
    SysviewInitSubtree(rulesMgr);
#endif
    SysviewInitSystable(rulesMgr);
#ifdef TS_MULTI_INST
    SysviewInitTsView(rulesMgr);
#endif
#ifdef FEATURE_STREAM
    SysviewInitStream(rulesMgr);
#endif
}

Status GmSysviewExecWithRule(int32_t gmSyvRule, SysviewContextT *ctx)
{
    DB_POINTER(ctx);
    ctx->useReservedConn = DbGetUseReservedConn(ctx->optionRule);
    SysviewRulesMgrT rulesMgr;
    GmsysviewInitRules(&rulesMgr);

    SysviewRuleProc sysviewRuleProc = SysviewGetRuleProc(&rulesMgr, gmSyvRule);
    if (sysviewRuleProc == NULL) {
        TOOL_RUN_ERROR(ctx->printFunc, GMERR_INVALID_PARAMETER_VALUE, "sysview get rule unsucc.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return sysviewRuleProc(ctx);
}

static bool NeedParseRecordNum(DbOptionRuleT *optionRule, int32_t argc, char **argv)
{
    // 参数小于4个不用处理
    if (argc <= SYSVIEW_RECORD_TABLE_KV_MIN_PARAM_NUM) {
        return false;
    }
    // 不是record_table_kv选项，则不涉及处理
    if (DbStrCmp(argv[1], VIEW_OPTION_ARG_RECORD_KV, false) != 0) {
        return false;
    }
    int32_t val;
    Status ret = DbStrToInt32(argv[argc - 1], &val);
    // 最后一个参数不是数字，则不涉及处理
    if (ret == GMERR_DATA_EXCEPTION || val < 0) {
        return false;
    }

    // 最后一个参数是数字且属于某个选项，则不涉及处理
    int32_t secToLastIdx = argc - 2;
    DbOptionItemT *item = DbGetOptionItemByName(optionRule, argv[secToLastIdx]);
    if (item != NULL) {
        return false;
    }
    return true;
}

// 兼容V3的record_num放在最后的命令格式,特殊处理record_num
// record_table_kv <table_name> [-f <key | value> to ipv4 | ipv6 | string | mac | time | hex] [record_num]
static Status SysviewSetRecordNumArg(DbOptionRuleT *optionRule, ArgInfoT *argInfo, int32_t *argc, char **argv)
{
    if (*argc < 1 || *argc > MANUAL_INPUT_OPTION_PARAM_MAX_NUM || argv == NULL) {
        return GMERR_INVALID_OPTION;
    }
    if (!NeedParseRecordNum(optionRule, *argc, argv)) {
        return GMERR_OK;
    }
    errno_t err = strncpy_s(
        argInfo->recordNum, (size_t)RECORD_PROPER_NUM_LEN_MAX, argv[*argc - 1], (size_t)strlen(argv[*argc - 1]));
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_INVALID_PARAMETER_VALUE, "copy str when sysview set record arg.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    (*argc)--;
    return GMERR_OK;
}

// 存储gmSysview工具的cmd命令，新增命令要同步新增该处注释以及修改GMSYSVIEW_OPTION_NUM确保数组不会越界
// 格式：{ OptionName, Index, Param Type, Min value, Max Value, Min Param Count, Max Param Count,
// Used, Repeatable, Repeat Times, Params }
// {DB_OPTION_ARG_HELP, 0, PARAM_TYPE_STR, 0, DM_MAX_NAME_LENGTH, 0, 1, 0, 0, 0, {}}
// {DB_OPTION_ARG_LONG_HELP, 0, PARAM_TYPE_STR, 0, DM_MAX_NAME_LENGTH, 0, 1, 0, 0, 0, {}}
// {DB_OPTION_ARG_VERSION, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}}
// {VIEW_OPTION_ARG_NAME, 0, PARAM_TYPE_STR, 1, SYSVIEW_VIEWNAME_MAX_LENGTH, 1, 1, 0, 0, 0, {}}
// {VIEW_OPTION_ARG_FILTER, 0, PARAM_TYPE_STR, 1, SYSVIEW_FILTER_MAX_LENGTH, 1, 3, 0, 1, 0, {}}
// {VIEW_OPTION_ARG_ORDER, 0, PARAM_TYPE_STR, 1, DM_MAX_NAME_LENGTH, 2, OPTION_PARAM_MAX_NUM, 0, 0, 0, {}}
// {VIEW_OPTION_ARG_LIMIT, 0, PARAM_TYPE_STR, 1, RECORD_PROPER_NUM_LEN_MAX, 1, 1, 0, 0, 0, {}}
// {DB_OPTION_ARG_DOMAIN_NAME, 0, PARAM_TYPE_STR, 1, SYSVIEW_SERVER_MAX_LEN, 1, 1, 0, 0, 0, {}}
// {DB_OPTION_ARG_OSC, 0, PARAM_TYPE_STR, 1, SYSVIEW_SERVER_MAX_LEN, 1, 1, 0, 0, 0, {}}
// {VIEW_OPTION_ARG_RECORD, 0, PARAM_TYPE_STR, 1, MAX_TABLE_NAME_LEN, 1, 2, 0, 0, 0, {}}
// {VIEW_OPTION_ARG_RECORD_KV, 0, PARAM_TYPE_STR, 1, MAX_TABLE_NAME_LEN, 0, 1, 0, 0, 0, {}}
// {VIEW_OPTION_ARG_COUNT, 0, PARAM_TYPE_STR, 1, MAX_TABLE_NAME_LEN, 0, 2, 0, 0, 0, {}}
// {VIEW_OPTION_ARG_COUNT_KV, 0, PARAM_TYPE_STR, 1, MAX_TABLE_NAME_LEN, 0, 2, 0, 0, 0, {}}
// {VIEW_OPTION_ARG_COUNT_LIMIT, 0, PARAM_TYPE_STR, 1, RECORD_PROPER_NUM_LEN_MAX, 1, 1, 0, 0, 0, {}}
// {DB_OPTION_ARG_NAMESPACE, 0, PARAM_TYPE_STR, 1, MAX_NAMESPACE_LENGTH, 1, 1, 0, 0, 0, {}}
// {VIEW_OPTION_ARG_CONDITION, 0, PARAM_TYPE_STR, 1, MAX_KEY_LEN, 1, 24, 0, 0, 0, {}}
// {VIEW_OPTION_ARG_FLAT, 0, PARAM_TYPE_STR, 1, RECORD_FLAT_DESC_LEN, 1, 1, 0, 0, 0, {}}
// {DB_OPTION_ARG_USE_RESERVED_CONN, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}}
// {VIEW_OPTION_ARG_ALARM, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}}
// {VIEW_OPTION_ARG_ESTIMATE, 0, PARAM_TYPE_STR, 1, ESTIMATE_MAX_FILE_PATH_LEN, 1, 1, 0, 0, 0, {}}
// {VIEW_OPTION_ARG_SUBTREE, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}}
// {VIEW_OPTION_ARG_ROOT_NAME, 0, PARAM_TYPE_STR, 1, MAX_TABLE_NAME_LEN, 1, 1, 0, 0, 0, {}}
// {VIEW_OPTION_ARG_FILTER_MODE, 0, PARAM_TYPE_STR, 1, SUBTREE_FILTER_MODE_LEN_MAX, 1, 1, 0, 0, 0, {}}
// {VIEW_OPTION_ARG_MAX_DEPTH, 0, PARAM_TYPE_STR, 1, SUBTREE_MAX_DEPTH_LEN_MAX, 1, 1, 0, 0, 0, {}}
// {VIEW_OPTION_ARG_DEFAULT_MODE, 0, PARAM_TYPE_STR, 1, SUBTREE_DEFAULT_MODE_LEN_MAX, 1, 1, 0, 0, 0, {}}
// {VIEW_OPTION_ARG_CONFIG_FLAG, 0, PARAM_TYPE_STR, 1, SUBTREE_CONFIG_FLAG_LEN_MAX, 1, 1, 0, 0, 0, {}}
// {VIEW_OPTION_ARG_SUBTREE_JSON, 0, PARAM_TYPE_STR, 1, SUBTREE_JSON_LEN_MAX, 1, 1, 0, 0, 0, {}}
// {VIEW_OPTION_ARG_RESOURCE_POOL, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}}
// {VIEW_OPTION_RESOURCE_POOL_POINT, 0, PARAM_TYPE_STR, 0, MAX_RES_POOL_NAME_LEN, 0, 3, 0, 0, 0, {}}
// {VIEW_OPTION_RESOURCE_POOL_EXTEND, 0, PARAM_TYPE_STR, 0, MAX_RES_POOL_NAME_LEN, 0, 1, 0, 0, 0, {}}
// {VIEW_OPTION_RESOURCE_POOL_BIND, 0, PARAM_TYPE_STR, 0, MAX_RES_POOL_NAME_LEN, 0, 1, 0, 0, 0, {}}
// {VIEW_OPTION_RESOURCE_POOL_RESID, 0, PARAM_TYPE_STR, 0, MAX_RES_POOL_NAME_LEN, 0, 3, 0, 0, 0, {}}
// {VIEW_OPTION_RESOURCE_POOL_TABLE, 0, PARAM_TYPE_STR, 0, MAX_TABLE_NAME_LEN, 0, 1, 0, 0, 0, {}}
// {VIEW_OPTION_ARG_SHOW, 0, PARAM_TYPE_STR, 1, MAX_TABLE_NAME_LEN, 1, 4, 0, 0, 0, {}}
// {VIEW_OPTION_ARG_DIAGNOSE, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}}
// {VIEW_OPTION_ARG_DIAGNOSE_INTERVAL, 0, PARAM_TYPE_INT32, 0, SYSVIEW_DIAGNOSE_MAX_INTERVAL_MS, 1, 1, 0, 0, 0, {}}
// {VIEW_OPTION_SYSTABLE, 0, PARAM_TYPE_STR, 1, MAX_TABLE_NAME_LEN, 2, 2, 0, 0, 0, {}}
// {DB_OPTION_ARG_NAME, 0, PARAM_TYPE_STR, 1, MAX_TABLE_NAME_LEN, 1, 1, 0, 0, 0, {}}
// {VIEW_OPTION_ARG_FIXED_FORMAT, 0, PARAM_TYPE_STR, 1, SYSVIEW_FILTER_MAX_LENGTH, 1, 3, 0, 1, 0, {}}
// 以下为时序特性所需，仅在多实例下生效
// {VIEW_OPTION_ARG_SQL, 0, PARAM_TYPE_STR, 1, MAX_TS_SQL_LENGTH, 1, 1, 0, 0, 0, {}}
// {VIEW_OPTION_ARG_EXPLAIN, 0, PARAM_TYPE_STR, 1, MAX_TS_EXPLAIN_LENGTH, 1, 1, 0, 0, 0, {}}
// 结束
// 以下仅流计算特性所需
// {VIEW_OPTION_ARG_STREAM, 0, PARAM_TYPE_STR, 1, MAX_TABLE_NAME_LEN, 1, 1, 0, 0, 0, {}}
// {VIEW_OPTION_VERTICALGRAPH, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}}
// 结束
// {DB_OPTION_ARG_USER_NAME, 0, PARAM_TYPE_STR, MIN_USER_NAME_LEN, MAX_USER_NAME_LEN, 1, 1, 0, 0, 0, {}}

NO_INLINE void DbSysviewHelpOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems = (DbOptionItemT){DB_OPTION_ARG_HELP, 0, PARAM_TYPE_STR, 0, DM_MAX_NAME_LENGTH, 0, 1, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewLongHelpOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems =
        (DbOptionItemT){DB_OPTION_ARG_LONG_HELP, 0, PARAM_TYPE_STR, 0, DM_MAX_NAME_LENGTH, 0, 1, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewVersionOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems = (DbOptionItemT){DB_OPTION_ARG_VERSION, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewNameViewOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems =
        (DbOptionItemT){VIEW_OPTION_ARG_NAME, 0, PARAM_TYPE_STR, 1, SYSVIEW_VIEWNAME_MAX_LENGTH, 1, 1, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewFilterOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems =
        (DbOptionItemT){VIEW_OPTION_ARG_FILTER, 0, PARAM_TYPE_STR, 1, SYSVIEW_FILTER_MAX_LENGTH, 1, 3, 0, 1, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewOrderOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems = (DbOptionItemT){
        VIEW_OPTION_ARG_ORDER, 0, PARAM_TYPE_STR, 1, DM_MAX_NAME_LENGTH, 2, OPTION_PARAM_MAX_NUM, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewLimitOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems =
        (DbOptionItemT){VIEW_OPTION_ARG_LIMIT, 0, PARAM_TYPE_STR, 1, RECORD_PROPER_NUM_LEN_MAX, 1, 1, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewDomainOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems =
        (DbOptionItemT){DB_OPTION_ARG_DOMAIN_NAME, 0, PARAM_TYPE_STR, 1, SYSVIEW_SERVER_MAX_LEN, 1, 1, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewOscOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems =
        (DbOptionItemT){DB_OPTION_ARG_OSC, 0, PARAM_TYPE_STR, 1, SYSVIEW_SERVER_MAX_LEN, 1, 1, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewRecordOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems =
        (DbOptionItemT){VIEW_OPTION_ARG_RECORD, 0, PARAM_TYPE_STR, 1, MAX_TABLE_NAME_LEN, 1, 2, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewRecordKvOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems =
        (DbOptionItemT){VIEW_OPTION_ARG_RECORD_KV, 0, PARAM_TYPE_STR, 1, MAX_TABLE_NAME_LEN, 0, 1, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewCountOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems =
        (DbOptionItemT){VIEW_OPTION_ARG_COUNT, 0, PARAM_TYPE_STR, 1, MAX_TABLE_NAME_LEN, 0, 2, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewCountKvOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems =
        (DbOptionItemT){VIEW_OPTION_ARG_COUNT_KV, 0, PARAM_TYPE_STR, 1, MAX_TABLE_NAME_LEN, 0, 2, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewCountLimitOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems = (DbOptionItemT){
        VIEW_OPTION_ARG_COUNT_LIMIT, 0, PARAM_TYPE_STR, 1, RECORD_PROPER_NUM_LEN_MAX, 1, 1, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewNamespaceOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems =
        (DbOptionItemT){DB_OPTION_ARG_NAMESPACE, 0, PARAM_TYPE_STR, 1, MAX_NAMESPACE_LENGTH, 1, 1, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewConditionOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems =
        (DbOptionItemT){VIEW_OPTION_ARG_CONDITION, 0, PARAM_TYPE_STR, 1, MAX_KEY_LEN, 1, 24, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewFlatOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems =
        (DbOptionItemT){VIEW_OPTION_ARG_FLAT, 0, PARAM_TYPE_STR, 1, RECORD_FLAT_DESC_LEN, 1, 1, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewUseReservedConnOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems = (DbOptionItemT){DB_OPTION_ARG_USE_RESERVED_CONN, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewAlarmOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems = (DbOptionItemT){VIEW_OPTION_ARG_ALARM, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewEstimateOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems =
        (DbOptionItemT){VIEW_OPTION_ARG_ESTIMATE, 0, PARAM_TYPE_STR, 1, ESTIMATE_MAX_FILE_PATH_LEN, 1, 1, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewSubTreeOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems = (DbOptionItemT){VIEW_OPTION_ARG_SUBTREE, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewRootNameOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems =
        (DbOptionItemT){VIEW_OPTION_ARG_ROOT_NAME, 0, PARAM_TYPE_STR, 1, MAX_TABLE_NAME_LEN, 1, 1, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewFilterModeOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems = (DbOptionItemT){
        VIEW_OPTION_ARG_FILTER_MODE, 0, PARAM_TYPE_STR, 1, SUBTREE_FILTER_MODE_LEN_MAX, 1, 1, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewMaxDepthOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems =
        (DbOptionItemT){VIEW_OPTION_ARG_MAX_DEPTH, 0, PARAM_TYPE_STR, 1, SUBTREE_MAX_DEPTH_LEN_MAX, 1, 1, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewDefaultModeOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems = (DbOptionItemT){
        VIEW_OPTION_ARG_DEFAULT_MODE, 0, PARAM_TYPE_STR, 1, SUBTREE_DEFAULT_MODE_LEN_MAX, 1, 1, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewConfigFlagOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems = (DbOptionItemT){
        VIEW_OPTION_ARG_CONFIG_FLAG, 0, PARAM_TYPE_STR, 1, SUBTREE_CONFIG_FLAG_LEN_MAX, 1, 1, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewSubTreeJsonOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems =
        (DbOptionItemT){VIEW_OPTION_ARG_SUBTREE_JSON, 0, PARAM_TYPE_STR, 1, SUBTREE_JSON_LEN_MAX, 1, 1, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewResourcePoolOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems = (DbOptionItemT){VIEW_OPTION_ARG_RESOURCE_POOL, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewResourcePoolPointOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems = (DbOptionItemT){
        VIEW_OPTION_RESOURCE_POOL_POINT, 0, PARAM_TYPE_STR, 0, MAX_RES_POOL_NAME_LEN, 0, 3, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewResourcePoolExtendOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems = (DbOptionItemT){
        VIEW_OPTION_RESOURCE_POOL_EXTEND, 0, PARAM_TYPE_STR, 0, MAX_RES_POOL_NAME_LEN, 0, 1, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewResourcePoolBindOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems =
        (DbOptionItemT){VIEW_OPTION_RESOURCE_POOL_BIND, 0, PARAM_TYPE_STR, 0, MAX_RES_POOL_NAME_LEN, 0, 1, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewResourcePoolResidOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems = (DbOptionItemT){
        VIEW_OPTION_RESOURCE_POOL_RESID, 0, PARAM_TYPE_STR, 0, MAX_RES_POOL_NAME_LEN, 0, 3, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewResourcePoolTableOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems =
        (DbOptionItemT){VIEW_OPTION_RESOURCE_POOL_TABLE, 0, PARAM_TYPE_STR, 0, MAX_TABLE_NAME_LEN, 0, 1, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewShowOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems =
        (DbOptionItemT){VIEW_OPTION_ARG_SHOW, 0, PARAM_TYPE_STR, 1, MAX_TABLE_NAME_LEN, 1, 4, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewDiagnoseOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems = (DbOptionItemT){VIEW_OPTION_ARG_DIAGNOSE, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewDiagnoseInterOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems = (DbOptionItemT){
        VIEW_OPTION_ARG_DIAGNOSE_INTERVAL, 0, PARAM_TYPE_INT32, 0, SYSVIEW_DIAGNOSE_MAX_INTERVAL_MS, 1, 1, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewSysTableOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems =
        (DbOptionItemT){VIEW_OPTION_SYSTABLE, 0, PARAM_TYPE_STR, 1, MAX_TABLE_NAME_LEN, 2, 2, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewNameOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems = (DbOptionItemT){DB_OPTION_ARG_NAME, 0, PARAM_TYPE_STR, 1, MAX_TABLE_NAME_LEN, 1, 1, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
NO_INLINE void DbSysviewFixedFormatOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems = (DbOptionItemT){
        VIEW_OPTION_ARG_FIXED_FORMAT, 0, PARAM_TYPE_STR, 1, SYSVIEW_FILTER_MAX_LENGTH, 1, 3, 0, 1, 0, {}};
    (*tmpOptionItems)++;
}
#ifdef TS_MULTI_INST
NO_INLINE void DbSysviewSqlOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems = (DbOptionItemT){VIEW_OPTION_ARG_SQL, 0, PARAM_TYPE_STR, 1, MAX_TS_SQL_LENGTH, 1, 1, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}

NO_INLINE void DbSysviewExplainOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems =
        (DbOptionItemT){VIEW_OPTION_ARG_EXPLAIN, 0, PARAM_TYPE_STR, 1, MAX_TS_EXPLAIN_LENGTH, 1, 1, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
#endif
#ifdef FEATURE_STREAM
NO_INLINE void DbSysviewStreamOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems =
        (DbOptionItemT){VIEW_OPTION_ARG_STREAM, 0, PARAM_TYPE_STR, 1, MAX_TABLE_NAME_LEN, 1, 1, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}

NO_INLINE void DbSysviewVerticalGraphOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems = (DbOptionItemT){VIEW_OPTION_VERTICALGRAPH, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
#endif
NO_INLINE void DbSysviewUserNameOptionItems(DbOptionItemT **tmpOptionItems)
{
    **tmpOptionItems = (DbOptionItemT){
        DB_OPTION_ARG_USER_NAME, 0, PARAM_TYPE_STR, MIN_USER_NAME_LEN, MAX_USER_NAME_LEN, 1, 1, 0, 0, 0, {}};
    (*tmpOptionItems)++;
}
Status DbInitSysviewOptionItems(DbOptionRuleT *optionRule, uint32_t num)
{
    DB_POINTER(optionRule);
    if (num > OPTION_ITEM_MAX_NUM) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    DbOptionItemT *tmpOptionItems = optionRule->optionItems;
    DbSysviewHelpOptionItems(&tmpOptionItems);
    DbSysviewLongHelpOptionItems(&tmpOptionItems);
    DbSysviewVersionOptionItems(&tmpOptionItems);
    DbSysviewNameViewOptionItems(&tmpOptionItems);
    DbSysviewFilterOptionItems(&tmpOptionItems);
    DbSysviewOrderOptionItems(&tmpOptionItems);
    DbSysviewLimitOptionItems(&tmpOptionItems);
    DbSysviewDomainOptionItems(&tmpOptionItems);
    DbSysviewOscOptionItems(&tmpOptionItems);
    DbSysviewRecordOptionItems(&tmpOptionItems);
    DbSysviewRecordKvOptionItems(&tmpOptionItems);
    DbSysviewCountOptionItems(&tmpOptionItems);
    DbSysviewCountKvOptionItems(&tmpOptionItems);
    DbSysviewCountLimitOptionItems(&tmpOptionItems);
    DbSysviewNamespaceOptionItems(&tmpOptionItems);
    DbSysviewConditionOptionItems(&tmpOptionItems);
    DbSysviewFlatOptionItems(&tmpOptionItems);
    DbSysviewUseReservedConnOptionItems(&tmpOptionItems);
    DbSysviewAlarmOptionItems(&tmpOptionItems);
    DbSysviewEstimateOptionItems(&tmpOptionItems);
    DbSysviewSubTreeOptionItems(&tmpOptionItems);
    DbSysviewRootNameOptionItems(&tmpOptionItems);
    DbSysviewFilterModeOptionItems(&tmpOptionItems);
    DbSysviewMaxDepthOptionItems(&tmpOptionItems);
    DbSysviewDefaultModeOptionItems(&tmpOptionItems);
    DbSysviewConfigFlagOptionItems(&tmpOptionItems);
    DbSysviewSubTreeJsonOptionItems(&tmpOptionItems);
    DbSysviewResourcePoolOptionItems(&tmpOptionItems);
    DbSysviewResourcePoolPointOptionItems(&tmpOptionItems);
    DbSysviewResourcePoolExtendOptionItems(&tmpOptionItems);
    DbSysviewResourcePoolBindOptionItems(&tmpOptionItems);
    DbSysviewResourcePoolResidOptionItems(&tmpOptionItems);
    DbSysviewResourcePoolTableOptionItems(&tmpOptionItems);
    DbSysviewShowOptionItems(&tmpOptionItems);
    DbSysviewDiagnoseOptionItems(&tmpOptionItems);
    DbSysviewDiagnoseInterOptionItems(&tmpOptionItems);
    DbSysviewSysTableOptionItems(&tmpOptionItems);
    DbSysviewNameOptionItems(&tmpOptionItems);
    DbSysviewFixedFormatOptionItems(&tmpOptionItems);
#ifdef TS_MULTI_INST
    DbSysviewSqlOptionItems(&tmpOptionItems);
    DbSysviewExplainOptionItems(&tmpOptionItems);
#endif
#ifdef FEATURE_STREAM
    DbSysviewStreamOptionItems(&tmpOptionItems);
    DbSysviewVerticalGraphOptionItems(&tmpOptionItems);
#endif
    DbSysviewUserNameOptionItems(&tmpOptionItems);
    return DbInitOptionItemsEntry(optionRule, num);
}

Status SysviewInitOptAndPara(
    SysviewContextT *ctx, int32_t argc, char **argv, int32_t(printFunc)(const char *format, ...))
{
    DB_POINTER(ctx);
    if (printFunc != NULL) {
        ctx->printFunc = printFunc;
    } else {
        ctx->printFunc = DbPrintfDefault;
    }
    Status ret = DbInitSysviewOptionItems(ctx->optionRule, GMSYSVIEW_OPTION_NUM);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(ctx->printFunc, ret, "sysview initial option items unsucc, ret = %" PRId32 ".", (int32_t)ret);
        return ret;
    }
    ret = DbInitRuleItems(ctx->optionRule, g_gmdbSysviewRuleItems, ELEMENT_COUNT(g_gmdbSysviewRuleItems));
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(ctx->printFunc, ret, "sysview initial rule items unsucc, ret = %" PRId32 ".", (int32_t)ret);
        return ret;
    }

    ret = SysviewSetRecordNumArg(ctx->optionRule, &ctx->argInfo, &argc, argv);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(
            ctx->printFunc, ret, "sysview initial move kv record param unsucc, ret = %" PRId32 ".", (int32_t)ret);
    }

    ret = DbInitOptionParam(ctx->optionRule, argc, argv);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(ctx->printFunc, ret, "sysview initial option param unsucc, ret = %" PRId32 ".", (int32_t)ret);
    }
    return ret;
}

void GmSysviewInitCtx(SysviewContextT *ctx)
{
    DB_POINTER(ctx);
    ctx->conn = NULL;
    ctx->stmt = NULL;
    ctx->isConn = false;
    ctx->isInteractive = false;
    ctx->useReservedConn = false;
    ctx->isDiagnosticView = false;
    ctx->printFunc = NULL;
    ctx->printedHead = false;
    (void)memset_s(&ctx->argInfo, sizeof(ArgInfoT), 0, sizeof(ArgInfoT));
}

static Status GmcSysviewEntry(
    int32_t argc, char **argv, int32_t(printFunc)(const char *format, ...), DbOptionRuleT *optionRule)
{
    DB_POINTER(optionRule);
    if (DbIsHpeEnv()) {
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    SysviewContextT *ctx = NULL;
    ctx = (SysviewContextT *)DB_MALLOC(sizeof(SysviewContextT));
    if (ctx == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_OUT_OF_MEMORY, "out of mem.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(ctx, sizeof(SysviewContextT), 0, sizeof(SysviewContextT));
    GmSysviewInitCtx(ctx);
    ctx->optionRule = optionRule;
    Status ret = SysviewInitOptAndPara(ctx, argc, argv, printFunc);
    if (ret != GMERR_OK) {
        DB_FREE(ctx);
        return ret;
    }
    int32_t rule = DbGetStartupRule(ctx->optionRule);
    if (rule == OPTION_NOT_FOUND) {
        TOOL_RUN_ERROR(
            ctx->printFunc, GMERR_INVALID_PARAMETER_VALUE, "Inv option, please use 'gmsysview -h' for more info.");
        DB_FREE(ctx);
        return GMERR_SYNTAX_ERROR;
    }
    ret = GmSysviewExecWithRule(rule, ctx);
    DB_FREE(ctx);
    return ret;
}

void PrintErrorCodeDetail(Status errCode, const DbPrintFunc printFunction)
{
    if (errCode == GMERR_OK) {
        return;
    }
    DbPrintFunc printFunc = (printFunction == NULL) ? DbPrintfDefault : printFunction;
    const DbErrDetailT *errDetail = ErrGetDetailedErrInfo(errCode);
    if (errDetail == NULL) {
        return;
    }
    printFunc("ErrorCode: %" PRId32 "\n", (int32_t)errCode);
    printFunc("ErrorCodeDescription: %s\n", errDetail->brief);
    printFunc("Solution: %s\n", errDetail->solution);
}

Status GmcSysview(int32_t argc, char **argv, int32_t(printFunc)(const char *format, ...))
{
    DbOptionRuleT optionRule = {0};
    Status ret = GMERR_OK;
    // 多实例情况下新增判断
#if !defined(FEATURE_FASTPATH) && !defined(TS_MULTI_INST) && defined(FEATURE_TS) && !defined(FEATURE_STREAM)
    ret = GmSysviewTsEntry(argc, argv, printFunc);
#else
    ret = GmSysviewAllocOptionRuleItems(&optionRule);
    if (ret != GMERR_OK) {
        PrintErrorCodeDetail(ret, printFunc);
        return ret;
    }
    ret = GmcSysviewEntry(argc, argv, printFunc, &optionRule);
#endif
    PrintErrorCodeDetail(ret, printFunc);
    DbReleaseOptionRuleItems(&optionRule);
    return ret;
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
