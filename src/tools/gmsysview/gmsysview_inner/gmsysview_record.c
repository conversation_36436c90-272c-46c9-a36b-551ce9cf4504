/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * Description: gmsysview record command
 * Author: gaohaiyang
 * Create: 2021-12-20
 */
#include "dm_data_print.h"
#include "dm_data_prop.h"
#include "db_option_parser.h"
#include "adpt_string.h"
#include "tool_utils.h"
#include "gmc.h"
#include "gmc_internal.h"
#include "clt_stmt.h"
#include "clt_graph_vertex_struct.h"
#include "sysview_tool.h"
#include "gmsysview_record.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define RECORD_LEN_DOUBLE 2
#define PARAM_TRIPLOID 3

static void RecordVrtxSetDefaultViewFmt(DbOptionRuleT *optionRule)
{
    if (!DbCheckIsUsedByOptionName(optionRule, VIEW_OPTION_ARG_FLAT)) {
        DbOptionItemT *viewFmtItem = DbGetOptionItemByName(optionRule, VIEW_OPTION_ARG_FLAT);
        if (viewFmtItem == NULL) {
            DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Find view format item");
            return;
        }
        viewFmtItem->isUsed = true;
        viewFmtItem->params[0].paramNum++;
        DbOptionParamT *params = DbGetParamsByOptionName(optionRule, VIEW_OPTION_ARG_FLAT);
        if (params == NULL) {
            DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "Find view format params");
            return;
        }
        params->paramVal[FIRST_PARAM].strVal = "json";
    }
}

static Status RecordVrtxGetParam(DbOptionRuleT *optionRule, ArgInfoT *argInfo)
{
    DB_POINTER2(optionRule, argInfo);
    // record <tableName>
    Status ret = SysviewSetStrArgInfo(
        optionRule, argInfo->vertexLabelName, MAX_TABLE_NAME_LEN, VIEW_OPTION_ARG_RECORD, FIRST_PARAM);
    // record [recordNum]
    if (ret == GMERR_OK) {
        ret = SysviewSetStrArgInfo(
            optionRule, argInfo->recordNum, RECORD_PROPER_NUM_LEN_MAX, VIEW_OPTION_ARG_RECORD, SECOND_PARAM);
    }
    // -ns <namespaceName>
    if (ret == GMERR_OK) {
        ret = SysviewSetStrArgInfo(
            optionRule, argInfo->namespaceName, MAX_NAMESPACE_LENGTH, DB_OPTION_ARG_NAMESPACE, FIRST_PARAM);
    }
    // -f property1 to A, -f property2 to B
    if (ret == GMERR_OK) {
        ret = SysviewGetFmtInfo(optionRule, &argInfo->formatInfo, VIEW_OPTION_ARG_FILTER);
    }
    // -view_fmt <json | flat_full | flat_truncate>
    RecordVrtxSetDefaultViewFmt(optionRule);
    if (ret == GMERR_OK) {
        ret = SysviewSetStrArgInfo(
            optionRule, argInfo->printFlatFmt, RECORD_FLAT_DESC_LEN, VIEW_OPTION_ARG_FLAT, FIRST_PARAM);
    }
    return ret;
}

#define RECORD_KV_ARG_F_CNT_MAX 2
static Status RecordKvValidateParam(DbOptionRuleT *optionRule, FormatInfoT *fmtInfos)
{
    DB_POINTER2(optionRule, fmtInfos);
    uint32_t repeatCnt = DbGetRepeatOptionCntByName(optionRule, VIEW_OPTION_ARG_FILTER);
    // -f未使用
    if (repeatCnt == 0) {
        return GMERR_OK;
    }
    // 查询kv表，-f最多只可以重复2次
    if (repeatCnt > RECORD_KV_ARG_F_CNT_MAX) {
        DB_LOG_WARN(GMERR_INVALID_PARAMETER_VALUE, "op param num -f must be 2 for record kv table.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    // 查询kv表，-f的第一个参数必须为key或者value
    for (uint32_t i = 0; i < repeatCnt; i++) {
        if (DbStrCmp(fmtInfos->fmtInfos[i].propeName, "key", false) != 0 &&
            DbStrCmp(fmtInfos->fmtInfos[i].propeName, "value", false) != 0) {
            DB_LOG_WARN(GMERR_INVALID_PARAMETER_VALUE,
                "The first op param -f must be \"key\" or \"value\" for record kv table.");
            return GMERR_INVALID_PARAMETER_VALUE;
        }
    }
    // 重复2次时候，第一个参数不允许相同
    if (repeatCnt == RECORD_KV_ARG_F_CNT_MAX &&
        DbStrCmp(fmtInfos->fmtInfos[0].propeName, fmtInfos->fmtInfos[1].propeName, false) == 0) {
        DB_LOG_WARN(GMERR_INVALID_PARAMETER_VALUE, "The second param of the two option -f cannot be the same.");
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

static Status RecordKvGetParam(DbOptionRuleT *optionRule, ArgInfoT *argInfo)
{
    DB_POINTER(argInfo);
    // record_table_kv <kvTableName>
    Status ret = SysviewSetStrArgInfo(
        optionRule, argInfo->kvTableName, MAX_TABLE_NAME_LEN, VIEW_OPTION_ARG_RECORD_KV, FIRST_PARAM);
    // -ns <namespaceName>
    if (ret == GMERR_OK) {
        ret = SysviewSetStrArgInfo(
            optionRule, argInfo->namespaceName, MAX_NAMESPACE_LENGTH, DB_OPTION_ARG_NAMESPACE, FIRST_PARAM);
    }
    // -f key to A, -f value to B
    if (ret == GMERR_OK) {
        ret = SysviewGetFmtInfo(optionRule, &argInfo->formatInfo, VIEW_OPTION_ARG_FILTER);
    }
    if (ret == GMERR_OK) {
        ret = RecordKvValidateParam(optionRule, &argInfo->formatInfo);
    }
    return ret;
}

// 获取gmsysview record/record_table_kv用到的参数
static Status GetRecordPara(SysviewContextT *ctx, bool isKv)
{
    DB_POINTER(ctx);
    Status ret = SysviewGetConnectionPara(ctx->optionRule, &ctx->argInfo);
    if (ret != GMERR_OK) {
        DB_LOG_WARN(GMERR_INVALID_PARAMETER_VALUE, "inv conn param.");
        return ret;
    }
    return isKv ? RecordKvGetParam(ctx->optionRule, &ctx->argInfo) : RecordVrtxGetParam(ctx->optionRule, &ctx->argInfo);
}

typedef struct Operator {
    char *desc;
    char *mark;
} OperatorT;

static const OperatorT g_gmdbOperator[] = {
    {"-gt", ">"}, {"-ge", ">="}, {"-lt", "<"}, {"-le", "<="}, {"-eq", "="}, {"-ne", "!="}};

static char *RecordVrtxOpDesc2Op(const char *opDesc)
{
    DB_POINTER(opDesc);
    for (uint32_t i = 0; i < ELEMENT_COUNT(g_gmdbOperator); i++) {
        if (DbStrCmp(opDesc, g_gmdbOperator[i].desc, false) == 0) {
            return g_gmdbOperator[i].mark;
        }
    }
    return NULL;
}

static bool RecordVrtxOpDescIsValid(const char *param)
{
    DB_POINTER(param);
    for (uint32_t i = 0; i < ELEMENT_COUNT(g_gmdbOperator); i++) {
        if (DbStrCmp(param, g_gmdbOperator[i].desc, false) == 0) {
            return true;
        }
    }
    return false;
}

// 保留第一个字段加双引号; 保留0起始的fixed的16进制判断(初步判断)；保留其他场景加单引号或不加符号
// 修复：在数字场景加一个空格，主要区分'<-'场景
// 修复：数字场景判断：添加对负数判断
static inline int32_t RecordVrtxAppendOpStr(uint32_t paramIdx, char *strDest, size_t destMax, const char *opStr)
{
    if (paramIdx == 0) {
        return sprintf_s(strDest, destMax, "\"%s\"", opStr);
    } else if ((paramIdx + 1) % PARAM_TRIPLOID == 0) {
        if (IsValidNumber(opStr) || (opStr[0] == '0')) {
            return sprintf_s(strDest, destMax, " %s", opStr);
        }
        return sprintf_s(strDest, destMax, "'%s'", opStr);
    }
    return sprintf_s(strDest, destMax, "%s", opStr);
}

// -c单独处理  eq | ge | gt  | le | lt>
static char *RecordVrtxAllocFilter(SysviewContextT *ctx, const char *condOption)
{
    DB_POINTER2(ctx, condOption);
    DbOptionParamT *param = DbGetParamsByOptionName(ctx->optionRule, condOption);  // 选项的参数值
    if (param == NULL) {
        return NULL;
    }
    uint32_t paramNum = DbGetParamNumByOptionName(ctx->optionRule, condOption);
    if (paramNum % 3 != 0) {  // 查vertex时，-c后面的条件个数必须是3的倍数
        TOOL_WARN(ctx->printFunc, GMERR_INVALID_PARAMETER_VALUE, "Param num of %s is not multiple of 3.", condOption);
        return NULL;
    }
    // 根据-c后面的参数个数来增加单引号的长度
    uint32_t dstMax = paramNum * DM_MAX_NAME_LENGTH + (paramNum / PARAM_TRIPLOID) * 2;
    if (dstMax == 0) {
        return NULL;
    }
    char *filterStr = DbDynMemCtxAlloc(ctx->stmt->memCtx, dstMax);
    if (filterStr == NULL) {
        TOOL_WARN(ctx->printFunc, GMERR_OUT_OF_MEMORY, "Alloc mem for record filter unsuccessful.");
        return NULL;
    }
    (void)memset_s(filterStr, dstMax, 0, dstMax);
    int32_t ret;
    // 遍历拼接， A lt 5 B lt 3--->"A>5 and B>3"
    for (uint32_t paramIdx = 0; paramIdx < paramNum; paramIdx++) {
        uint32_t filterLen = (uint32_t)strlen(filterStr);
        if (paramIdx != 0 && paramIdx % PARAM_TRIPLOID == 0) {  // 参数序号为3的倍数时，代表参数是属性值
            if (RecordVrtxOpDescIsValid(param->paramVal[paramIdx].strVal)) {
                // 局部变量，外部函数无法获得，未置NULL，无UAF风险
                DbDynMemCtxFree(ctx->stmt->memCtx, filterStr);
                return NULL;
            }
            ret = sprintf_s(filterStr + filterLen, dstMax - filterLen, " and \"%s\"", param->paramVal[paramIdx].strVal);
        } else {
            char *opStr = RecordVrtxOpDescIsValid(param->paramVal[paramIdx].strVal) ?
                              RecordVrtxOpDesc2Op(param->paramVal[paramIdx].strVal) :
                              param->paramVal[paramIdx].strVal;
            ret = RecordVrtxAppendOpStr(paramIdx, filterStr + filterLen, dstMax - filterLen, opStr);
        }
        if (ret < 0) {
            // 局部变量，外部函数无法获得，未置NULL，无UAF风险
            DbDynMemCtxFree(ctx->stmt->memCtx, filterStr);
            return NULL;
        }
    }
    return filterStr;
}

#define MAX_PATITION_ID 255

static char *RecordVrtxGetNormalStr(SysviewContextT *ctx, const DmVertexT *vertex, const FormatInfoT *fmtInfo)
{
    DB_POINTER3(ctx, vertex, ctx->stmt);
    uint32_t printLen;
    Status ret = DmVertexGetPrintLength(vertex, fmtInfo, &printLen);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Get print len of vertex unsuccessful.");
        return NULL;
    }
    uint32_t vertexLen = printLen * RECORD_LEN_DOUBLE;
    char *vertexStr = DbDynMemCtxAlloc(ctx->stmt->memCtx, vertexLen);
    if (vertexStr == NULL) {
        TOOL_WARN(ctx->printFunc, GMERR_OUT_OF_MEMORY, "Alloc mem for vertex str unsuccessful when try to print it.");
        return NULL;
    }
    (void)memset_s(vertexStr, vertexLen, 0, vertexLen);
    DbMemCtxT *oldMemCtx = DbMemCtxSwitchTo(ctx->stmt->memCtx);
    // DmVertexPrint 接口需要借用当前的memCtx来申请内存
    ret = DmVertexPrint(vertex, vertexLen, vertexStr, fmtInfo);
    if (oldMemCtx == NULL) {
        DbSetCurrMemCtxToNull();
    } else {
        (void)DbMemCtxSwitchTo(oldMemCtx);
    }
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Get vertex str or unsupported formatted output unsuccessful.");
        // 局部变量，外部函数无法获得，未置NULL，无UAF风险
        DbDynMemCtxFree(ctx->stmt->memCtx, vertexStr);
        return NULL;
    }
    return vertexStr;
}

static Status RecordVrtxGetPrintFlatType(SysviewContextT *ctx, bool *isJson)
{
    DB_POINTER2(ctx, isJson);
    DmPrintFlatE viewType = SysviewPrintFlatType(ctx->argInfo.printFlatFmt);
    if (viewType != PRINT_NORMAL && viewType != PRINT_JSON) {
        TOOL_WARN(ctx->printFunc, GMERR_FEATURE_NOT_SUPPORTED, "Can not print flat for record.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    *isJson = ((viewType == PRINT_NORMAL) ? false : true);
    return GMERR_OK;
}

Status RecordVrtxGetRecordVersion(const SysviewContextT *ctx, DmVertexT *vertex, uint64_t *recordVersion)
{
    DB_POINTER2(recordVersion, vertex);
    const CltOperVertexT *cltVertex = CltGetConstOperationContext(ctx->stmt);
    DmVertexLabelT *vrtxLabel = cltVertex->cltCataLabel->vertexLabel;
    VertexLabelCommonInfoT *commonInfo = vrtxLabel->commonInfo;
    if (commonInfo == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "get commonInfo.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    if (!commonInfo->dlrInfo.isDataSyncLabel || DmVertexLabelIsDatalogLabel(vrtxLabel)) {
        return GMERR_OK;
    }
    uint32_t vertexBufLen = 0;
    Status ret = DmVertexGetSeriBufLength(vertex, &vertexBufLen);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint8_t *buf = (uint8_t *)DbDynMemCtxAlloc(vertex->memCtx, vertexBufLen);
    if (buf == NULL) {
        TOOL_WARN(ctx->printFunc, GMERR_OUT_OF_MEMORY, "Alloc buf unsuccessful.");
        return GMERR_OUT_OF_MEMORY;
    }
    ret = DmSerializeVertex2InvokerBuf(vertex, vertexBufLen, buf);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Serialize vertex buf unsuccessful.");
        // 局部变量，外部函数无法获得，未置NULL，无UAF风险
        DbDynMemCtxFree(vertex->memCtx, buf);
        return ret;
    }
    DmValueT propertyValue = {0};
    propertyValue.type = DB_DATATYPE_UINT64;
    DmVertexBufGetSysPrope(DATA_SYNC_VERSION, buf, &propertyValue, cltVertex->cltCataLabel->vertexLabel->vertexDesc);
    *recordVersion = propertyValue.value.ulongValue;
    // 局部变量，外部函数无法获得，未置NULL，无UAF风险
    DbDynMemCtxFree(vertex->memCtx, buf);
    return ret;
}

char *RecordVrtxGetFmtStr(SysviewContextT *ctx, DmVertexT *vertex, const FormatInfoT *fmtInfo)
{
    DB_POINTER3(ctx, fmtInfo, ctx->stmt);
    bool isJson = false;
    Status ret = RecordVrtxGetPrintFlatType(ctx, &isJson);
    if (ret != GMERR_OK) {
        return NULL;
    }

    if (isJson) {
        char *jsonObjText = NULL;
        ret = DmVertexCreateJsonStr(ctx->stmt->memCtx, vertex, DM_JSON_VIEW_PRINT_FLAG, fmtInfo, &jsonObjText, false);
        if (ret != GMERR_OK) {
            TOOL_WARN(ctx->printFunc, ret, "Record print json unsucc.");
            return NULL;
        }
        return jsonObjText;
    }
    return RecordVrtxGetNormalStr(ctx, vertex, fmtInfo);
}

DmVertexT *RecordVrtxGetVertex(SysviewContextT *ctx)
{
    DB_POINTER(ctx);
    DmVertexT *vertex = CltGetVertexInStmt(ctx->stmt);
    if (vertex == NULL) {
        return NULL;
    }
    Status ret = CltCheckAndDeseriData(ctx->stmt);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "sysview deseri vertex unsucc");
        return NULL;
    }
    return vertex;
}

static Status RecordVrtxPrintRecordHeader(SysviewContextT *ctx, DmVertexT *vertex, uint32_t index, uint8_t partitionId)
{
    // step：逐条fetch数据并打印，直到取到指定条数或取完数据或过程出错
    // 临时方案：当前客户端在进行一次通信后会重置stmt，因此fetch中如果涉及新的cs流程，需要新申请一个stmt
    // 防止对fetch使用的stmt产生影响；该问题已提issue1401
    GmcStmtT *checkInfoStmt = NULL;
    Status ret = GmcAllocStmt(ctx->conn, &checkInfoStmt);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Finish to alloc stmt when try to get check info for record.");
        return ret;
    }
    do {
        GmcCheckInfoT *checkInfo;
        ret = CltGetCheckInfo(checkInfoStmt, ctx->argInfo.vertexLabelName, partitionId, &checkInfo);
        if (ret != GMERR_OK) {
            TOOL_WARN(
                ctx->printFunc, ret, "Get check version for record unsuccessful. ret = %" PRId32 ".", (int32_t)ret);
            break;
        }
        uint16_t checkVersion = checkInfo == NULL ? 0 : checkInfo->checkVersion;
        uint64_t recordVersion = DB_INVALID_ID64;
        ret = RecordVrtxGetRecordVersion(ctx, vertex, &recordVersion);
        if (ret != GMERR_OK) {
            TOOL_WARN(ctx->printFunc, ret, "Print record vserion unsuccessful.");
            break;
        }
        const CltOperVertexT *cltVertex = CltGetConstOperationContext(ctx->stmt);
        DmVertexLabelT *vrtxLabel = cltVertex->cltCataLabel->vertexLabel;
        VertexLabelCommonInfoT *commonInfo = vrtxLabel->commonInfo;
        if (commonInfo == NULL) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "get commonInfo.");
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        if (commonInfo->dlrInfo.isDataSyncLabel && !DmVertexLabelIsDatalogLabel(vrtxLabel)) {
            ctx->printFunc("index = %" PRIu32 ", check_version = %" PRIu16 ", record_version = %" PRIu64 "\n", index,
                checkVersion, recordVersion);
        } else {
            ctx->printFunc("index = %" PRIu32 ", check_version = %" PRIu16 "\n", index, checkVersion);
        }
    } while (false);
    GmcFreeStmt(checkInfoStmt);
    return ret;
}

#define RECORD_PRINT_CHAR_MAX_SIZE (SIZE_M(8))
// 设备前台单次PRINT打印最多8M内容(包含结束符和预留换行符，共2个字符)
#define RECORD_PRINT_VISIBLE_CHAR_MAX_SIZE (RECORD_PRINT_CHAR_MAX_SIZE - 2)

inline static void RecordPrintStrFunc(SysviewContextT *ctx, char *str2Print)
{
    uint32_t recordSize = (uint32_t)strlen(str2Print);
    if (recordSize <= RECORD_PRINT_VISIBLE_CHAR_MAX_SIZE) {
        ctx->printFunc("%s\n", str2Print);
        return;
    }

    char *tmpStr = DB_MALLOC(RECORD_PRINT_CHAR_MAX_SIZE);
    if (tmpStr == NULL) {
        return;
    }

    uint32_t printRound = recordSize % RECORD_PRINT_VISIBLE_CHAR_MAX_SIZE != 0 ?
                              (recordSize / RECORD_PRINT_VISIBLE_CHAR_MAX_SIZE + 1) :
                              recordSize / RECORD_PRINT_VISIBLE_CHAR_MAX_SIZE;
    for (uint32_t i = 0; i < printRound; i++) {
        (void)memset_s(tmpStr, RECORD_PRINT_CHAR_MAX_SIZE, 0, RECORD_PRINT_CHAR_MAX_SIZE);
        errno_t err = strncpy_s(tmpStr, RECORD_PRINT_CHAR_MAX_SIZE, str2Print + i * RECORD_PRINT_VISIBLE_CHAR_MAX_SIZE,
            RECORD_PRINT_VISIBLE_CHAR_MAX_SIZE);
        if (err != EOK) {
            TOOL_WARN(ctx->printFunc, GMERR_FIELD_OVERFLOW, "Record str copy unsuccessful.");
            break;
        }
        ctx->printFunc("%s", tmpStr);
    }
    // str2Print打印完成后，打印换行
    ctx->printFunc("\n");
    DB_FREE(tmpStr);
}

static Status RecordVrtxGetPartitionId(SysviewContextT *ctx, DmVertexT *vertex, DmValueT *propertyValue)
{
    VertexLabelCommonInfoT *commonInfo = vertex->vertexDesc->commonInfo;
#if defined(EXPERIMENTAL_NERGC)
    DmAccCheckT *accCheckAddr = commonInfo->accCheckAddr;
#else
    DmAccCheckT *accCheckAddr = (DmAccCheckT *)DbShmPtrToAddr(commonInfo->accCheckShm);
#endif
    if (accCheckAddr == NULL) {
        TOOL_WARN(ctx->printFunc, GMERR_UNEXPECTED_NULL_VALUE, "AccCheckAddr shmPtr is inv.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    Status ret = DmVertexGetPropeById(vertex, accCheckAddr->partitionPropeId, propertyValue);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Get partition id unsuccessful.");
        return ret;
    }
    return ret;
}

static Status RecordVrtxFetchRegularTable(SysviewContextT *ctx, uint32_t recordNum, bool isPartition)
{
    DB_POINTER(ctx);
    Status ret = GMERR_OK;
    bool isFinish = false;
    for (uint32_t i = 0; i < recordNum; i++) {
        ret = GmcFetch(ctx->stmt, &isFinish);
        if (isFinish) {
            // 有数据则打印扫描结果
            if (i > 0 || !isPartition) {
                // 兼容v3，只记录日志，不打屏
                DB_LOG_INFO("Finish fetch %" PRIu32 " vertex for record.", i);
            }
            ret = GMERR_OK;
            break;
        }
        if (ret != GMERR_OK) {
            TOOL_WARN(ctx->printFunc, ret, "Fetch the %" PRIu32 " vertex for record unsuccessful. ret = %" PRId32 ".",
                i, (int32_t)ret);
            break;
        }
        DmVertexT *vertex = RecordVrtxGetVertex(ctx);
        if (vertex == NULL) {
            TOOL_WARN(ctx->printFunc, GMERR_UNEXPECTED_NULL_VALUE, "Get vertex unsuccessful.");
            ret = GMERR_UNEXPECTED_NULL_VALUE;
            break;
        }

        // step：打印每条记录头部信息
        if (isPartition) {
            DmValueT propertyValue;
            ret = RecordVrtxGetPartitionId(ctx, vertex, &propertyValue);
            if (ret != GMERR_OK) {
                TOOL_WARN(ctx->printFunc, ret, "Get vertex partitionId unsuccessful.");
                return ret;
            }
            ret = RecordVrtxPrintRecordHeader(ctx, vertex, i, propertyValue.value.partitionValue);
        } else {
            ret = RecordVrtxPrintRecordHeader(ctx, vertex, i, GMC_FULL_TABLE);
        }
        if (ret != GMERR_OK) {
            TOOL_WARN(ctx->printFunc, ret, "Print record header unsuccessful.");
            break;
        }

        // step：获取格式化信息
        char *vertexStr = RecordVrtxGetFmtStr(ctx, vertex, &ctx->argInfo.formatInfo);
        if (vertexStr == NULL) {
            TOOL_WARN(ctx->printFunc, GMERR_UNEXPECTED_NULL_VALUE, "Get format vertex for record unsuccessful.");
            ret = GMERR_UNEXPECTED_NULL_VALUE;
            break;
        }
        RecordPrintStrFunc(ctx, vertexStr);
        // 局部变量，外部函数无法获得，未置NULL，无UAF风险
        DbDynMemCtxFree(ctx->stmt->memCtx, vertexStr);
    }
    return ret;
}

static Status RecordVrtxPrint(SysviewContextT *ctx)
{
    DB_POINTER(ctx);
    // step：读vertex，看下是否有数据可fetch
    if (GmcVertexLabelIsEmpty(ctx->stmt)) {
        TOOL_RUN_INFO(ctx->printFunc, "Empty vertex label to scan for record.");
        return GMERR_OK;
    }
    Status ret;
    // step：记录需要获取多少数据
    uint32_t recordNum = DB_INVALID_UINT32;
    if (strlen(ctx->argInfo.recordNum) != 0) {
        ret = DbStrToUint32(ctx->argInfo.recordNum, &recordNum);
        if (ret != GMERR_OK) {
            TOOL_WARN(ctx->printFunc, ret, "Convert record num to int for record unsuccessful.");
            return ret;
        }
    }

    // step:判断vertex是否属于分区表
    bool isPartition = false;
    ret = GmcVertexLabelIsPartition(ctx->stmt, &isPartition);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Get vertex type for record unsuccessful.");
        return ret;
    }
    return RecordVrtxFetchRegularTable(ctx, recordNum, isPartition);
}

static Status RecordVrtxPrapareScan(SysviewContextT *ctx)
{
    DB_POINTER(ctx);
    if (!DbCheckIsUsedByOptionName(ctx->optionRule, VIEW_OPTION_ARG_CONDITION)) {
        return GmcExecute(ctx->stmt);
    }
    // 拼接条件
    char *filter = RecordVrtxAllocFilter(ctx, VIEW_OPTION_ARG_CONDITION);
    if (filter == NULL) {
        TOOL_WARN(ctx->printFunc, GMERR_MEMORY_OPERATE_FAILED,
            "Get query condition for for record unsuccessful, ret = %" PRId32, (int32_t)GMERR_MEMORY_OPERATE_FAILED);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    Status ret = GmcSetFilter(ctx->stmt, filter);
    if (ret != GMERR_OK) {
        // 局部变量，外部函数无法获得，未置NULL，无UAF风险
        DbDynMemCtxFree(ctx->stmt->memCtx, filter);
        return ret;
    }
    ret = GmcExecute(ctx->stmt);
    // 局部变量，外部函数无法获得，未置NULL，无UAF风险
    DbDynMemCtxFree(ctx->stmt->memCtx, filter);
    return ret;
}

static Status RecordVrtxImpl(SysviewContextT *ctx, const TlsConnOptionT *option)
{
    DB_POINTER2(ctx, option);
    // step1：使用指定的命名空间
    Status ret = TlsUseNameSpace(ctx->stmt, option->namespaceName);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Find given namespace for record unsuccessful.");
        return ret;
    }
    // step2：打开vertexLabel
    ret = GmcPrepareStmtByLabelNameWithVersion(
        ctx->stmt, ctx->argInfo.vertexLabelName, DB_MAX_UINT32, GMC_OPERATION_SCAN);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Open vertex label for record unsuccessful, ret = %" PRId32, (int32_t)ret);
        return ret;
    }
    GmcSetStmtIsSysviewRecordCmd(ctx->stmt, true);
    // step3：扫描：条件扫描或普通扫描
    ret = RecordVrtxPrapareScan(ctx);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Scan vertex label for record unsuccessful, ret = %" PRId32, (int32_t)ret);
        return ret;
    }
    // step4：打印vertex记录
    return RecordVrtxPrint(ctx);
}

static Status RecordKvGetCond(
    DbOptionRuleT *optionRule, const DbPrintFunc printFunc, char *key, uint32_t *keyLen, const char *condOption)
{
    DB_POINTER3(key, keyLen, condOption);
    uint32_t paramNum = DbGetParamNumByOptionName(optionRule, condOption);
    if (paramNum > 1) {
        TOOL_WARN(printFunc, GMERR_INVALID_PARAMETER_VALUE, "Param num of %s should be one.", condOption);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    DbOptionParamT *param = DbGetParamsByOptionName(optionRule, condOption);  // 选项的参数值
    /* 内部接口，外层保证了该值不为NULL */
    if (param == NULL) {
        TOOL_WARN(printFunc, GMERR_INVALID_PARAMETER_VALUE, "no param of %s in record option.", condOption);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    int32_t err = snprintf_s(key, MAX_KEY_LEN, MAX_KEY_LEN - 1, "%s", param->paramVal[0].strVal);
    if (err < 0) {
        TOOL_WARN(printFunc, GMERR_INVALID_PARAMETER_VALUE, "Find no param of %s in record.", condOption);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    *keyLen = (uint32_t)strlen(key) + 1;
    return GMERR_OK;
}

typedef enum RecordKvPrintMode { RECORD_PRINT_KEY = 0, RECORD_PRINT_VALUE = 1 } RecordKvPrintModeE;

static Status RecordKvPrintByFmt(
    SysviewContextT *ctx, char *src, uint32_t srcLen, DmPrintFmtE printFmt, RecordKvPrintModeE printfMode)
{
    DB_POINTER2(ctx, ctx->stmt);
    if (printFmt >= BUTT_FORMAT) {
        TOOL_WARN(ctx->printFunc, GMERR_INVALID_PARAMETER_VALUE, "Inv print format for record. ret = %" PRId32,
            (int32_t)GMERR_INVALID_PARAMETER_VALUE);
        return GMERR_INVALID_PARAMETER_VALUE;
    }

    uint32_t printLen = DmGetPropertyValueFormatLen(srcLen, printFmt, DB_DATATYPE_NULL);
    char *printStr = DbDynMemCtxAlloc(ctx->stmt->memCtx, printLen);
    if (printStr == NULL) {
        TOOL_WARN(ctx->printFunc, GMERR_OUT_OF_MEMORY, "Alloc print str for record unsuccessful, ret = %" PRId32,
            (int32_t)GMERR_OUT_OF_MEMORY);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(printStr, printLen, 0, printLen);
    DmPrintLenT printLength = {.realLen = srcLen, .typeLen = srcLen};
    Status ret = DmPrintFormat(src, printLength, printStr, printLen, printFmt, true, DB_DATATYPE_NULL);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Unsupport to convert src str to the give type. ret = %" PRId32, (int32_t)ret);
        // 局部变量，外部函数无法获得，未置NULL，无UAF风险
        DbDynMemCtxFree(ctx->stmt->memCtx, printStr);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    if (printfMode == RECORD_PRINT_KEY) {
        ctx->printFunc("|key_len   |%-64" PRIu32 "|\n|key       |%-64s|\n", srcLen, printStr);
    } else if (printfMode == RECORD_PRINT_VALUE) {
        ctx->printFunc("|value_len |%-64" PRIu32 "|\n|value     |%-64s|\n", srcLen, printStr);
    }
    // 局部变量，外部函数无法获得，未置NULL，无UAF风险
    DbDynMemCtxFree(ctx->stmt->memCtx, printStr);
    return GMERR_OK;
}

// 获取kv表的输出格式，kv的列名只有key或value
static DmPrintFmtE RecordKvGetPrintFmt(const FormatInfoT *fmtInfo, const char *columnName)
{
    DB_POINTER(fmtInfo);
    for (uint32_t i = 0; i < fmtInfo->fmtCnt; i++) {
        DB_POINTER(fmtInfo->fmtInfos[i].propeName);
        if (DbStrCmp(fmtInfo->fmtInfos[i].propeName, columnName, false) == 0) {
            return fmtInfo->fmtInfos[i].fmt;
        }
    }
    return HEX_FORMAT;
}

static Status RecordKvFormatPrint(SysviewContextT *ctx, char *key, uint32_t keyLen, char *value, uint32_t valueLen)
{
    DB_POINTER(ctx);
    SysviewPrintBorderLine(ctx->printFunc, SYSVIEW_PRINT_LETTER_LEN_77, SYSVIEW_SHORT_LINE);

    DmPrintFmtE keyFmt = RecordKvGetPrintFmt(&ctx->argInfo.formatInfo, "key");
    Status ret = RecordKvPrintByFmt(ctx, key, keyLen, keyFmt, RECORD_PRINT_KEY);
    if (ret != GMERR_OK) {
        return ret;
    }

    DmPrintFmtE valueFmt = RecordKvGetPrintFmt(&ctx->argInfo.formatInfo, "value");
    ret = RecordKvPrintByFmt(ctx, value, valueLen, valueFmt, RECORD_PRINT_VALUE);
    if (ret != GMERR_OK) {
        TOOL_WARN(
            ctx->printFunc, ret, "Print value for record_table_kv unsuccessful, ret = %" PRId32 ".", (int32_t)ret);
    }
    SysviewPrintBorderLine(ctx->printFunc, SYSVIEW_PRINT_LETTER_LEN_77, SYSVIEW_SHORT_LINE);
    return ret;
}

static Status RecordKvCondPrint(DbOptionRuleT *optionRule, SysviewContextT *ctx)
{
    DB_POINTER2(ctx, ctx->stmt);
    char *key = DbDynMemCtxAlloc(ctx->stmt->memCtx, MAX_KEY_LEN);
    if (key == NULL) {
        TOOL_WARN(ctx->printFunc, GMERR_OUT_OF_MEMORY, "Alloc mem of key for record_table_kv unsuccessful.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(key, MAX_KEY_LEN, 0, MAX_KEY_LEN);
    char *value = DbDynMemCtxAlloc(ctx->stmt->memCtx, MAX_VALUE_LEN);
    if (value == NULL) {
        // 局部变量，外部函数无法获得，未置NULL，无UAF风险
        DbDynMemCtxFree(ctx->stmt->memCtx, key);
        TOOL_WARN(ctx->printFunc, GMERR_OUT_OF_MEMORY, "Alloc mem of value for record_table_kv unsuccessful.");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(value, MAX_VALUE_LEN, 0, MAX_VALUE_LEN);
    uint32_t keyLen = 0;
    Status ret = RecordKvGetCond(optionRule, ctx->printFunc, key, &keyLen, VIEW_OPTION_ARG_CONDITION);
    if (ret != GMERR_OK) {
        // 局部变量，外部函数无法获得，未置NULL，无UAF风险
        DbDynMemCtxFree(ctx->stmt->memCtx, key);
        DbDynMemCtxFree(ctx->stmt->memCtx, value);
        TOOL_WARN(ctx->printFunc, ret, "Get given key for record_table_kv unsuccessful. ret = %" PRId32, (int32_t)ret);
        return ret;
    }
    uint32_t valueLen = MAX_VALUE_LEN;
    ret = GmcKvGet(ctx->stmt, key, keyLen, value, &valueLen);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Query kv with the given key for record_table_kv unsuccessful. ret = %" PRId32,
            (int32_t)ret);
    } else {
        ret = RecordKvFormatPrint(ctx, key, keyLen, value, valueLen);
    }
    // 局部变量，外部函数无法获得，未置NULL，无UAF风险
    DbDynMemCtxFree(ctx->stmt->memCtx, key);
    DbDynMemCtxFree(ctx->stmt->memCtx, value);
    return ret;
}

static uint32_t RecordKvGetNum(const SysviewContextT *ctx)
{
    DB_POINTER(ctx);
    // 参数缺省，默认值
    if (strlen(ctx->argInfo.recordNum) == 0) {
        return RECORD_KV_NUM_MAX;
    }

    uint32_t recordNum = DB_INVALID_UINT32;
    Status ret = DbStrToUint32(ctx->argInfo.recordNum, &recordNum);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "The input record_num inv.");
        return DB_INVALID_UINT32;
    }
    // 校验边界，有效范围[1,RECORD_KV_NUM_MAX]
    if (recordNum == 0 || recordNum > RECORD_KV_NUM_MAX) {
        TOOL_WARN(ctx->printFunc, ret, "The input record_num: %" PRIu32 " out of range:[1, %" PRId32 "].", recordNum,
            RECORD_KV_NUM_MAX);
        return DB_INVALID_UINT32;
    }
    return recordNum;
}

static Status RecordKvPrint(SysviewContextT *ctx)
{
    DB_POINTER(ctx);
    // step：kv是否等值过滤
    if (DbCheckIsUsedByOptionName(ctx->optionRule, VIEW_OPTION_ARG_CONDITION)) {
        return RecordKvCondPrint(ctx->optionRule, ctx);
    }
    // step：扫描数据数量
    uint32_t recordNum = RecordKvGetNum(ctx);
    if (recordNum == DB_INVALID_UINT32) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    // step：执行扫描，并逐条fetch，打印
    Status ret = GmcKvScan(ctx->stmt, 0);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Scan kv table for record_table_kv unsuccessful. ret = %" PRId32, (int32_t)ret);
        return ret;
    }
    bool isFinish = false;
    char *key = NULL;
    uint32_t keyLen = 0;
    char *value = NULL;
    uint32_t valueLen = 0;
    for (uint32_t i = 0; i < recordNum; i++) {
        ret = GmcFetch(ctx->stmt, &isFinish);
        if (isFinish) {
            // 兼容v3，只记录日志，不打屏
            DB_LOG_INFO("Finish to fetch all %" PRIu32 " kv data for record_table_kv.", i);
            return GMERR_OK;
        }
        if (ret != GMERR_OK) {
            TOOL_WARN(ctx->printFunc, ret,
                "Fetch the %" PRIu32 " kv data for record_table_kv unsuccessful. ret = %" PRId32 ".", i, (int32_t)ret);
            return ret;
        }
        ret = GmcKvGetFromStmt(ctx->stmt, (void **)&key, &keyLen, (void **)&value, &valueLen);
        if (ret != GMERR_OK) {
            TOOL_WARN(
                ctx->printFunc, ret, "Finish to get the %" PRIu32 " kv data from statment for record_table_kv.", i);
            return ret;
        }
        // 格式化打印kv
        ret = RecordKvFormatPrint(ctx, key, keyLen, value, valueLen);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status RecordKvImpl(SysviewContextT *ctx, const TlsConnOptionT *option)
{
    DB_POINTER2(ctx, option);
    // step1：使用指定的命名空间
    Status ret = TlsUseNameSpace(ctx->stmt, option->namespaceName);
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Find the given namespace for record_table_kv unsuccessful, ret = %" PRId32 ".",
            (int32_t)ret);
        return ret;
    }
    // step2：打开kv表
    if (strlen(ctx->argInfo.kvTableName) == 0) {
        ret = GmcKvPrepareStmtByLabelName(ctx->stmt, NULL);
    } else {
        ret = GmcKvPrepareStmtByLabelName(ctx->stmt, ctx->argInfo.kvTableName);
    }
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Open kv table %s for record_table_kv unsuccessful, ret = %" PRId32 ".",
            ctx->argInfo.kvTableName, (int32_t)ret);
        return ret;
    }
    // step3：打印kv表记录
    return RecordKvPrint(ctx);
}

Status RecordLabel(SysviewContextT *ctx, bool isKv)
{
    DB_POINTER(ctx);
    Status ret = GetRecordPara(ctx, isKv);
    if (ret != GMERR_OK) {
        (void)SysviewPrintHelp(ctx);
        return ret;
    }
    TlsConnOptionT option = {
        .domainName = ctx->argInfo.serverLocator,
        .userName = ctx->argInfo.userName,
        .namespaceName = ctx->argInfo.namespaceName,
        .useReservedConn = ctx->useReservedConn,
        .noMemLimit = false,
    };
    ret = TlsConnect(&ctx->conn, &ctx->stmt, &option);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (isKv) {
        ret = RecordKvImpl(ctx, &option);
    } else {
        ret = RecordVrtxImpl(ctx, &option);
    }
    if (ret != GMERR_OK) {
        TOOL_WARN(ctx->printFunc, ret, "Exec gmsysview record unsuccessful, ret = %" PRId32, (int32_t)ret);
    }
    TlsDisconnect(ctx->conn, ctx->stmt);
    return ret;
}

Status RecordVertexLabel(SysviewContextT *ctx)
{
    return RecordLabel(ctx, false);
}

Status RecordKvTable(SysviewContextT *ctx)
{
    return RecordLabel(ctx, true);
}

void SysviewInitRecord(SysviewRulesMgrT *rulesMgr)
{
    DB_POINTER(rulesMgr);
    SysviewRuleT recordRules[] = {
        {SYSVIEW_RULE_RECORD_VERTEX_LABEL, RecordVertexLabel}, {SYSVIEW_RULE_RECORD_KV_TABLE, RecordKvTable}};
    SysviewRegistRules(rulesMgr, recordRules, ELEMENT_COUNT(recordRules));
}
#ifdef __cplusplus
}
#endif /* __cplusplus */
