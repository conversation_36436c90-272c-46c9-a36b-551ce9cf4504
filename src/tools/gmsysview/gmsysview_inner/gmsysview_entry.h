/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2022. All rights reserved.
 * Description: header file for gmsysview entry
 * Author: gaohaiyang
 * Create: 2021-12-22
 */

#ifndef GMSYSVIEW_ENTRY_H
#define GMSYSVIEW_ENTRY_H

#include "sysview_tool.h"
#include "tool_internal_error.h"
#ifdef __cplusplus
extern "C" {
#endif

#ifdef TS_MULTI_INST
#define GMSYSVIEW_TS_OPTION_NUM 2
#else
#define GMSYSVIEW_TS_OPTION_NUM 0
#endif

#ifdef FEATURE_STREAM
#define GMSYSVIEW_STREAM_OPTION_NUM 2
#else
#define GMSYSVIEW_STREAM_OPTION_NUM 0
#endif

#define GMSYSVIEW_OPTION_NUM (40 + GMSYSVIEW_TS_OPTION_NUM + GMSYSVIEW_STREAM_OPTION_NUM)

Status GmSysviewAllocOptionRuleItems(DbOptionRuleT *optionRule);

Status GmSysviewExecWithRule(int32_t gmSyvRule, SysviewContextT *ctx);

Status SysviewInitOptAndPara(
    SysviewContextT *ctx, int32_t argc, char **argv, int32_t(printFunc)(const char *format, ...));
void GmSysviewInitCtx(SysviewContextT *ctx);

void PrintErrorCodeDetail(Status errCode, const DbPrintFunc printFunction);

#ifdef __cplusplus
}
#endif

#endif
