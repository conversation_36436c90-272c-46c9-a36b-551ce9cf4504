/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: gmsysview resource command header
 * Author: cuijinxin
 * Create: 2022-10-25
 */

#ifndef GMSYSVIEW_RESOURCE_H
#define GMSYSVIEW_RESOURCE_H

#include "sysview_tool.h"

#ifdef __cplusplus
extern "C" {
#endif

#define VIEW_OPTION_RESOURCE_POOL_POINT "-p"
#define VIEW_OPTION_RESOURCE_POOL_EXTEND "-e"
#define VIEW_OPTION_RESOURCE_POOL_BIND "-b"
#define VIEW_OPTION_RESOURCE_POOL_RESID "-r"
#define VIEW_OPTION_RESOURCE_POOL_TABLE "-t"

void SysviewInitResource(SysviewRulesMgrT *rulesMgr);

#ifdef __cplusplus
}
#endif /* __cplusplus */
#endif /* GMSYSVIEW_RESOURCE_H */
