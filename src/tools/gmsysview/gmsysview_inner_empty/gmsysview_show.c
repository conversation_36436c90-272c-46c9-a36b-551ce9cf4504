/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: gmsysview show command - empty
 * Author: <PERSON> - The streaming engine team
 * Create: 2025-02-27
 */

#include "gmsysview_show.h"
#include "adpt_printf.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

Status ShowConfigTableEmpty(SysviewContextT *ctx)
{
    DbPrintfDefault("Inactive function.\n");
    return GMERR_FEATURE_NOT_SUPPORTED;
}

void SysviewInitShow(SysviewRulesMgrT *rulesMgr)
{
    DB_POINTER(rulesMgr);
    SysviewRuleT resourceRules[] = {{SYSVIEW_RULE_SHOW, ShowConfigTableEmpty}};
    // 规则注册
    SysviewRegistRules(rulesMgr, resourceRules, ELEMENT_COUNT(resourceRules));
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
