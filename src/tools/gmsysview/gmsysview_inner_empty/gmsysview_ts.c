/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: gmsysview ts command - empty
 * Author: GMDBV5 Team
 * Create: 2024-02-28
 */

#include "gmsysview_ts.h"
#include "adpt_printf.h"

Status GmSysviewTsEntry(int32_t argc, char **argv, DbPrintFunc dbPrintFunc)
{
    DbPrintfDefault("Inactive function.\n");
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status GmSysviewMainTs(int32_t argc, char **argv)
{
    DbPrintfDefault("Inactive function.\n");
    return GMERR_FEATURE_NOT_SUPPORTED;
}
