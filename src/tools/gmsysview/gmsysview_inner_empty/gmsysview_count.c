/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: gmsysview count command - empty
 * Author: <PERSON> - The streaming engine team
 * Create: 2025-02-27
 */

#include "gmsysview_count.h"
#include "adpt_printf.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

Status SysviewVertexCountEmpty(SysviewContextT *ctx)
{
    DbPrintfDefault("Inactive function.\n");
    return GMERR_FEATURE_NOT_SUPPORTED;
}

Status SysviewKvCountEmpty(SysviewContextT *ctx)
{
    DbPrintfDefault("Inactive function.\n");
    return GMERR_FEATURE_NOT_SUPPORTED;
}

void SysviewInitCount(SysviewRulesMgrT *rulesMgr)
{
    DB_POINTER(rulesMgr);
    SysviewRuleT countRules[] = {
        {SYSVIEW_RULE_COUNT_VERTEX_LABEL, SysviewVertexCountEmpty}, {SYSVIEW_RULE_COUNT_KV_TABLE, SysviewKvCountEmpty}};
    SysviewRegistRules(rulesMgr, countRules, ELEMENT_COUNT(countRules));
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
