/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2021. All rights reserved.
 * File Name: gmimport_typedef.h
 * Description: Header file for gmimport typedef.
 * Author: <PERSON><PERSON><PERSON><PERSON><PERSON>an
 * Create: 2020-9-16
 */

#ifndef GMIMPORT_TYPEDEF_H
#define GMIMPORT_TYPEDEF_H

#include "gmc_errno.h"
#include "adpt_types.h"
#include "db_option_parser.h"
#include "gmimport_file.h"
#ifdef TS_MULTI_INST
#include "gmimport_ts.h"
#endif

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define IMP_OPTION_ARG_OPERATION "-c"
#define IMP_OPTION_ARG_FILE_PATH "-f"
#define IMP_OPTION_ARG_INPUT_PATH "-i"
#define IMP_OPTION_ARG_OUTPUT_PATH "-o"
#define IMP_OPTION_ARG_FILE_SIZE_LIMIT "-l"
#define IMP_OPTION_ARG_TABLE_NAME "-t"
#define IMP_OPTION_ARG_RES_POOL_NAME "-r"
#define IMP_OPTION_ARG_EXT_RES_POOL_NAME "-er"
#define IMP_OPTION_ARG_UNINSTALL_SO "-d"
#define IMP_OPTION_ARG_VERIFY_MODE "-m"
#define IMP_OPTION_ARG_CMD_INCLUDE "--include"
#define IMP_OPTION_ARG_UPGRADE_SO "-upgrade"
#define IMP_OPTION_ARG_ROLLBACK_UPGRADE_SO "-rollback"
#define IMP_OPTION_ARG_IGNORE_SELECTION "-ignore"
#define IMP_OPTION_ARG_IGNORE_HIGHVERION "high_ver_data"
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
#define IMP_OPTION_ARG_DIR_TYPE "-dt"
#endif
#define IMP_OPTION_ARG_DTL_DISTRIBUTE "--distribute"

typedef enum GmImpStartupRule {
    GMIMP_RULE_HELP = 0,
    GMIMP_RULE_VERSION,
    GMIMP_RULE_IMP_FILE,
    GMIMP_RULE_BIND_LABEL,
    GMIMP_RULE_BIND_EXTERNAL_RESPOOL,
    GMIMP_RULE_RESTORE_CONF,
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
    GMIMP_RULE_IMP_BINDATA,
#endif
#ifdef FEATURE_DATALOG
    GMIMP_RULE_UNINSTALL_FILE,
#endif
    GMIMP_RULE_UPGRADE_FILE,
    GMIMP_RULE_ROLLBACK_UPGRADE_FILE,
#ifdef TS_MULTI_INST
    GMIMP_RULE_SQL_FILE,
#endif
#ifdef FEATURE_RSMEM
    GMIMP_RULE_IMP_V1_BIN,
    GMIMP_RULE_IMP_V1_RSM,
    GMIMP_RULE_IMP_RSM_MIGRATION,
#endif
    GMIMP_RULE_BOTTOM
} GmImpStartupRuleE;

#if (defined(WARM_REBOOT) || defined(FEATURE_RSMEM)) && defined(FEATURE_DATALOG)
#define GMIMP_OPTION_NUM 23
#elif (defined(WARM_REBOOT) || defined(FEATURE_RSMEM)) && !defined(FEATURE_DATALOG)
#define GMIMP_OPTION_NUM 22
#elif (!defined(WARM_REBOOT) && !defined(FEATURE_RSMEM)) && defined(FEATURE_DATALOG)
#define GMIMP_OPTION_NUM 21
#else
#define GMIMP_OPTION_NUM 20
#endif

extern const char *g_gmdbImpCmdName[];

Status GmImpAllocOptionRuleItems(DbOptionRuleT *optionRule);
Status GmImpInit(DbOptionRuleT *optionRule, int32_t argc, char *argv[]);
Status GmImpGetHelp(DbOptionRuleT *optionRule, ImportOptionT *toolOption);
Status GmImpGetVersion(DbOptionRuleT *optionRule, ImportOptionT *toolOption);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
