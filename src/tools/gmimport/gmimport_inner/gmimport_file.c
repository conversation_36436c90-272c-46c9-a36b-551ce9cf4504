/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2021. All rights reserved.
 * Description: source file for importing schema from  file or directory
 * Author: zhangyoujian
 * Create: 2020-8-24
 */
#include "gmimport_file.h"
#include "db_file.h"
#include "adpt_string.h"
#include "db_json.h"
#include "gmc.h"
#include "gmimport_transform.h"
#include "gmimport_utils.h"
#include "tool_utils.h"
#include "clt_batch.h"
#include "clt_utils.h"
#include "tool_interactive.h"
#include "gmimport_typedef.h"
#include "gmc_internal.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#if !defined(WARM_REBOOT) && !defined(FEATURE_RSMEM)
#define DATA_FILE_LEN_SIZE 10
#define CRC_CODE_LEN 8
#endif
#define NORMAL_VERTEX_NUM_LIMIT 1024
#define BATCH_CACHE_FILE_NUM 1024

typedef struct ImpPropertyRespoolInfo {
    char labelName[MAX_TABLE_NAME_LEN];
    char properties[DM_RES_COL_MAX_COUNT][MAX_TABLE_NAME_LEN];
    char resPoolNames[DM_RES_COL_MAX_COUNT][MAX_RES_POOL_NAME_LEN];
    int32_t count;
} ImpPropertyRespoolInfoT;

typedef struct ImportCacheFileItem {
    char *filePath;
    ImpFileTypeE fileType;
} ImportCacheFileItemT;

typedef struct FilePathInfo {
    ImportCacheFileItemT *fileItems;
    uint32_t total;
    uint32_t count;
} ImportCacheFilesT;

#if !defined(WARM_REBOOT) && !defined(FEATURE_RSMEM)
// 用途：存储打印LOG的导入成功/失败文件个数的信息
// 是否并发初始化：否
// 是否并发读写：否
// 并发方案：无
ImpFileNumStatT g_gmdbTotalImpNumStat = {0};
#endif

Status ImportBatchExecute(GmcBatchT *batch)
{
    GmcBatchRetT batchRet;
    Status executeRet = GmcBatchExecute(batch, &batchRet);
    if (executeRet != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)executeRet, "import batch exec unsucc. ret = %d", (int32_t)executeRet);
        return executeRet;
    }
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    Status ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "Import batch deparse unsucc. ret = %d", (int32_t)ret);
    } else {
        if (successNum == totalNum) {
            ret = GMERR_OK;
        } else {
            ret = (executeRet == GMERR_OK) ? GMERR_INTERNAL_ERROR : executeRet;
        }
        g_gmdbTotalImpNumStat.successFileNums += successNum;
    }
    Status resetRet = GmcBatchReset(batch);
    if (resetRet != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "import batch reset unsucc. ret = %d", (int32_t)ret);
    }
    return resetRet == GMERR_OK ? ret : resetRet;
}

Status ImportBatchExecuteGetPara(GmcBatchT *batch, ImpVertexLabelParasT *para)
{
    GmcBatchRetT batchRet;
    Status executeRet = GmcBatchExecute(batch, &batchRet);
    if (executeRet != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)executeRet, "import batch exec unsucc. ret = %d", (int32_t)executeRet);
    }
    uint32_t totalNum = 0;
    uint32_t successNum = 0;
    Status ret = GmcBatchDeparseRet(&batchRet, &totalNum, &successNum);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "Import batch deparse unsucc. ret = %d", (int32_t)ret);
    } else {
        (para->handleNum) += totalNum;
        (para->successNum) += successNum;
        if (successNum == totalNum) {
            ret = GMERR_OK;
        } else {
            ret = (executeRet == GMERR_OK) ? GMERR_INTERNAL_ERROR : executeRet;
        }
    }
    Status resetRet = GmcBatchReset(batch);
    if (resetRet != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)resetRet, "import batch reset unsucc. ret = %d", (int32_t)ret);
    }
    return resetRet == GMERR_OK ? ret : resetRet;
}

static void ResetTotalImpNumStat(void)
{
    (void)memset_s(&g_gmdbTotalImpNumStat, sizeof(ImpFileNumStatT), 0, sizeof(ImpFileNumStatT));
}

static Status ImportMultipleFiles(GmcStmtT *stmt, ImportArgumentT *args, uint32_t recurDepth);

Status ExtractAllFilesPath(GmcStmtT *stmt, ImportArgumentT *args, ImportCacheFilesT *filesInfo,
    ImpFileNumStatT *impFileStat, uint32_t recurDepth);

Status ImportPreparedScanFile(const ImportArgumentT *args, DIR **dir, char *dirPath, uint32_t len)
{
    *dir = opendir(args->file.path);
    if (*dir == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_DIRECTORY_OPERATE_FAILED,
            "Open directory %s unsucc, os no %" PRId32 ".", args->file.path, (int32_t)errno);
        return GMERR_DIRECTORY_OPERATE_FAILED;
    }
    // scan all the directory
    if (strcpy_s(dirPath, len, args->file.path) != EOK) {
        (void)closedir(*dir);
        *dir = NULL;
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_MEMORY_OPERATE_FAILED, "copy path worthless.");
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

Status ImportPrepareBatch(GmcStmtT *stmt, GmcBatchT **batch)
{
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        PRINT_ERROR(DbPrintfDefault, "import batch init failed. ret = %d", (int32_t)ret);
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    if (ret != GMERR_OK) {
        PRINT_ERROR(DbPrintfDefault, "import batch set exec order failed. ret = %d", (int32_t)ret);
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, 2048U);
    if (ret != GMERR_OK) {
        PRINT_ERROR(DbPrintfDefault, "import batch set buf limit size failed. ret = %d", (int32_t)ret);
        return ret;
    }
    ret = GmcBatchPrepare(stmt->conn, &batchOption, batch);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "import batch prepare unsucc. ret = %d", (int32_t)ret);
        return ret;
    }
    return GMERR_OK;
}

static Status ImportScanFileFromDirInner(
    GmcStmtT *stmt, ImportArgumentT *args, ImpFileNumStatT *impfileStat, ImpFileTypeE fileType)
{
    Status status = ImportPrepareJsonInfoForDir(stmt, args, fileType);
    if (status != GMERR_OK) {
        return status;
    }
    impfileStat->totalFileNums++;
    g_gmdbTotalImpNumStat.totalFileNums++;
    args->file.isImport = true;  // 默认文件导入
    status = ImportSingleFile(stmt, args);
    if (status == GMERR_OK) {
        if (args->file.isImport) {
            // 和ImportScanFolderFromDirInner函数中不同，
            // g_gmdbTotalImpNumStat中的 statistical数据 successFileNums在batch操作中将在GmcBatchParseRet之后更新；
            // 在其他单步操作中将在对应gmc接口调用结束后更新
            impfileStat->successFileNums++;
        } else {
            impfileStat->skipFileNums++;
            g_gmdbTotalImpNumStat.skipFileNums++;
            PRINT_INFO(DbPrintfDefault, "Insert file from \"%s\" max_record_count is 0, this file skip import\n",
                args->file.path);
            args->file.isImport = true;  // 重置为true，对于批量文件，标记下一个文件默认导入
        }
    } else {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)status, "Import file from \"%s\" unsucc. ret = %d", args->file.path,
            (int32_t)status);
    }
    return status;
}

void ImportScanFolderFromDirInner(
    GmcStmtT *stmt, ImportArgumentT *args, ImpFileNumStatT *impfileStat, uint32_t recurDepth)
{
    impfileStat->totalDirNums++;
    g_gmdbTotalImpNumStat.totalDirNums++;
    Status status = ImportMultipleFiles(stmt, args, recurDepth);
    if (status == GMERR_OK) {
        impfileStat->successDirNums++;
        g_gmdbTotalImpNumStat.successDirNums++;
    } else {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)status, "Import folder \"%s\" unsucc. ret = %d", args->file.path,
            (int32_t)status);
    }
}

static Status ImportScanFileFromDir(GmcStmtT *stmt, ImportArgumentT *args, ImpFileTypeE fileType, uint32_t recurDepth)
{
    DB_POINTER2(stmt, args);
    ImpFileNumStatT impfileStat = {0};
    DIR *dir = NULL;
    char dirPath[DB_MAX_PATH] = {0};
    Status ret = ImportPreparedScanFile(args, &dir, dirPath, DB_MAX_PATH);
    if (ret != GMERR_OK || dir == NULL) {
        return ret;
    }
    while (true) {
        struct dirent *dirName = readdir(dir);
        if (dirName == NULL) {
            break;
        }
        if (DbStrCmp(dirName->d_name, ".", true) == 0 || DbStrCmp(dirName->d_name, "..", true) == 0) {
            continue;
        }
        ret = ImportSetFullPathStr(args, dirPath, dirName);
        if (ret != GMERR_OK) {
            continue;
        }
        /* file */
        if (DbFileExist(args->file.path) && fileType < FILE_FOLDER) {
            (void)ImportScanFileFromDirInner(stmt, args, &impfileStat, fileType);
            /* folder */
        } else if (DbDirExist(args->file.path) && fileType == FILE_FOLDER) {
            ImportScanFolderFromDirInner(stmt, args, &impfileStat, recurDepth + 1);
        }
    }
    (void)closedir(dir);
    errno_t err = strcpy_s(args->file.path, sizeof(args->file.path), dirPath);
    if (err != EOK) {
        TOOL_RUN_ERROR(
            DbPrintfDefault, (int32_t)GMERR_INTERNAL_ERROR, "strcpy %s unsucc. err = %d", dirPath, (int32_t)err);
        return GMERR_INTERNAL_ERROR;
    }
    return ImportResultDisplay(&impfileStat, fileType);
}

static void ImportTransformSchema(char *labelJson, char *configJson, const DbJsonT *jsonRoot, GmImpTransformT *schema)
{
    if (configJson != NULL) {
        schema->configJson.str = configJson;
        schema->configJson.len = (uint32_t)(strlen(configJson) + 1);
    }
    // schema文件是一个object对象，则导入时未指定表名，表名为文件名；如果schema文件是一个array对象，未指定表名，则表名为单个schema
    // json对应的首个node名
    if (DbJsonIsObject(jsonRoot)) {
        schema->isLabelJsonRef = false;
    } else {
        schema->isLabelJsonRef = true;
    }
    schema->labelJsons.str = labelJson;
    schema->labelJsons.len = (uint32_t)(strlen(labelJson) + 1);
}

Status CreateVertexLabel(GmcStmtT *stmt, const GmImpTransformT *transformCtx)
{
    Status ret;
    if (transformCtx->isInputLabelName || !(transformCtx->isLabelJsonRef)) {
        ret = GmcCreateVertexLabelWithName(
            stmt, transformCtx->labelJsons.str, transformCtx->configJson.str, transformCtx->labelName);
    } else {
        ret = GmcCreateVertexLabel(stmt, transformCtx->labelJsons.str, transformCtx->configJson.str);
    }
    return ret;
}

static Status ImportBatchAddDDL(GmcStmtT *stmt, GmImpTransformT *transformCtx, GmcOperationTypeE type)
{
    Status ret = GMERR_OK;
    char *configJson = (type == GMC_OPERATION_CREATE_VERTEX_LABEL) ? transformCtx->configJson.str : NULL;
    do {
        // 如果达到GmcBatchAddDDL的上限，执行当前batch
        if (ret == GMERR_BATCH_BUFFER_FULL) {
            ret = ImportBatchExecute(stmt->batch);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
        if (transformCtx->isInputLabelName || !(transformCtx->isLabelJsonRef)) {
            ret = GmcBatchAddDDL(stmt->batch, type, transformCtx->labelName, transformCtx->labelJsons.str, configJson);
        } else {
            ret = GmcBatchAddDDL(stmt->batch, type, NULL, transformCtx->labelJsons.str, configJson);
        }
    } while (ret == GMERR_BATCH_BUFFER_FULL);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret,
            "import batch add ddl operation unsucc. operation type is %d, ret = %d", (int32_t)type, (int32_t)ret);
        return ret;
    }
    return GMERR_OK;
}

Status CheckFileisImport(ImportArgumentT *args, DbJsonT *object, bool *isImport)
{
    *isImport = true;
    DbJsonT *jsonMaxRecordCount = NULL;
    DbJsonT *jsonMaxRecordCountFromConfig = NULL;
    DbJsonT *jsonConfig = NULL;
    // 获取object中的max_record_count，获取不到直接返回，默认插入
    if (object != NULL) {
        jsonMaxRecordCount = DbJsonObjectGet(object, "max_record_count");
        jsonConfig = DbJsonObjectGet(object, "config");
        if (jsonConfig != NULL) {
            jsonMaxRecordCountFromConfig = DbJsonObjectGet(jsonConfig, "max_record_count");
        }
    } else {
        return GMERR_OK;
    }

    // 没有取到该值，直接返回
    if (jsonMaxRecordCount == NULL && jsonMaxRecordCountFromConfig == NULL) {
        return GMERR_OK;
    }

    // 判断值的类型是否合法
    if ((jsonMaxRecordCount != NULL && DbJsonGetType(jsonMaxRecordCount) != DB_JSON_INTEGER) ||
        (jsonMaxRecordCountFromConfig != NULL && DbJsonGetType(jsonMaxRecordCountFromConfig) != DB_JSON_INTEGER)) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_DATATYPE_MISMATCH,
            "Type of max_record_count should be uint. file: %s. ret = %d", args->file.path,
            (int32_t)GMERR_DATATYPE_MISMATCH);
        return GMERR_DATATYPE_MISMATCH;
    }

    // max_record_count=0时，不进行导入，isImport=false
    if ((jsonMaxRecordCount != NULL && DbJsonIntegerValue(jsonMaxRecordCount) == 0) ||
        (jsonMaxRecordCountFromConfig != NULL && DbJsonIntegerValue(jsonMaxRecordCountFromConfig) == 0)) {
        *isImport = false;
    }
    return GMERR_OK;
}

static Status ArrayObjectCheck(ImportArgumentT *args, uint32_t labelNum, GmImpTransformT *transformCtx)
{
    Status ret = GMERR_OK;
    if (labelNum == 0 || labelNum > NORMAL_VERTEX_NUM_LIMIT) {  // 目前不考虑yang表
        ret = GMERR_ARRAY_SUBSCRIPT_ERROR;
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "unexpected vertex label array num %u. file is %s", labelNum,
            args->file.path);
        return ret;
    }
    if (labelNum > 1 && transformCtx->isInputLabelName) {
        ret = GMERR_INVALID_JSON_CONTENT;
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret,
            "Incorrect json content. Vertex label array num should be just one when appointed the labelName. file is "
            "%s",
            args->file.path);
        return ret;
    }
    return GMERR_OK;
}

static Status ImportArrayBatchInner(
    GmcStmtT *stmt, GmImpTransformT *transformCtx, GmcOperationTypeE type, ImpVertexLabelParasT *para)
{
    Status ret = GMERR_OK;
    char *configJson = (type == GMC_OPERATION_CREATE_VERTEX_LABEL) ? transformCtx->configJson.str : NULL;
    ImpVertexLabelParasT tmpPara = {0};
    if (transformCtx->isInputLabelName) {
        ret = GmcBatchAddDDL(stmt->batch, type, transformCtx->labelName, transformCtx->labelJsons.str, configJson);
        (para->handleNum)++;
    } else {
        ret = GmcBatchAddDDL(stmt->batch, type, NULL, transformCtx->labelJsons.str, configJson);
        (para->handleNum)++;
    }
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret,
            "import batch add ddl operation unsucc. operation type is %d, ret = %d", (int32_t)type, (int32_t)ret);
        return ret;
    }
    ret = ImportBatchExecuteGetPara(stmt->batch, &tmpPara);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret,
            "import batch execute operation unsucc. operation type is %d, ret = %d", (int32_t)type, (int32_t)ret);
        return ret;
    }
    (para->successNum) += (tmpPara.successNum);
    return GMERR_OK;
}

static Status EncodeObjectForBatchAdd(GmcStmtT *stmt, DbJsonT *jsonField, GmImpTransformT *transformCtx,
    GmcOperationTypeE type, ImpVertexLabelParasT *exePara)
{
    transformCtx->labelJsons.str = DbJsonDumps(jsonField, 0);
    Status ret = ImportArrayBatchInner(stmt, transformCtx, type, exePara);
    DbJsonFree(transformCtx->labelJsons.str);
    return ret;
}

static Status HandleArrayLabelImportResult(
    GmcStmtT *stmt, ImportArgumentT *args, ImpVertexLabelParasT *exePara, uint32_t skipNum, uint32_t labelNum)
{
    if (skipNum == labelNum) {
        args->file.isImport = false;
        return GMERR_OK;
    }
    if (labelNum == (exePara->successNum + skipNum)) {
        g_gmdbTotalImpNumStat.successFileNums++;
        return GMERR_OK;
    }
    return GMERR_DATA_EXCEPTION;
}

static Status ImportLabelFromArray(
    GmcStmtT *stmt, ImportArgumentT *args, GmImpTransformT *transformCtx, GmcOperationTypeE type, uint32_t labelNum)
{
    Status ret = GMERR_OK;
    uint32_t skipNum = 0;
    ImpVertexLabelParasT exePara = {0};
    for (uint32_t i = 0; i < labelNum; i++) {
        bool isImport = true;
        DbJsonT *jsonField = DbJsonArrayGet(args->jsonObject, i);
        if (!DbJsonIsObject(jsonField)) {
            ret = GMERR_INVALID_JSON_CONTENT;
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret,
                "Incorrect json content. Incorrect Vertex label json in array. file is "
                "%s",
                args->file.path);
            return ret;
        }
        // max_record_count解析校验
        ret = CheckFileisImport(args, jsonField, &isImport);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (!isImport) {
            // 打印并统计 导入表的数量，导入文件的数量。
            PRINT_INFO(DbPrintfDefault, "Insert label from file \"%s\" max_record_count is 0, this label skip import\n",
                args->file.name);
            skipNum++;
            continue;
        }
        // encode object to jsonStr
        ret = EncodeObjectForBatchAdd(stmt, jsonField, transformCtx, type, &exePara);
        if (ret != GMERR_OK) {
            continue;
        }
    }
    return HandleArrayLabelImportResult(stmt, args, &exePara, skipNum, labelNum);
}

static Status CreateLabelBatchAddDDL(
    GmcStmtT *stmt, ImportArgumentT *args, GmImpTransformT *transformCtx, GmcOperationTypeE type)
{
    Status ret = GMERR_OK;
    bool isImport = true;
    ret = CheckFileisImport(args, args->configDbJsonObject, &isImport);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (!isImport) {
        args->file.isImport = false;
        return GMERR_OK;
    }
    // schema文件是json对象
    if (!(transformCtx->isLabelJsonRef)) {
        // step 1. max_record_count解析校验
        ret = CheckFileisImport(args, args->jsonObject, &isImport);
        if (ret != GMERR_OK) {
            return ret;
        }
        if (!isImport) {
            args->file.isImport = false;
            return GMERR_OK;
        }
        // step 2. import json object
        ret = ImportBatchAddDDL(stmt, transformCtx, type);
        if (ret != GMERR_OK) {
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret,
                "import batch add ddl operation unsucc. operation type is %d, ret = %d", (int32_t)type, (int32_t)ret);
        }
        ret = ImportBatchExecute(stmt->batch);
    } else {  // schema文件是array对象
        // step 1. array check
        uint32_t labelNum = (uint32_t)DbJsonGetArraySize(args->jsonObject);
        ret = ArrayObjectCheck(args, labelNum, transformCtx);
        if (ret != GMERR_OK) {
            return ret;
        }
        // step 2. get object form array and import the object one by one
        ret = ImportLabelFromArray(stmt, args, transformCtx, type, labelNum);
    }
    return ret;
}

static Status GetValueFromConfHeader(char *buff, size_t valueSize, uint32_t *value, char *format)
{
    char strBuff[CONF_HEADER_SIZE + CRC_CODE_MAX_SIZE] = {0};
    errno_t secRet = strncpy_s(strBuff, CONF_HEADER_SIZE + CRC_CODE_MAX_SIZE, buff, valueSize);
    if (secRet != EOK) {
        return GMERR_FIELD_OVERFLOW;
    }
    secRet = sscanf_s(strBuff, format, value);
    if (secRet == -1) {
        return GMERR_FIELD_OVERFLOW;
    }
    return GMERR_OK;
}

Status CheckBackupFileCRC(char *fileBuff, uint32_t fileLen, uint32_t *confFileBeg, uint32_t *confFileSize)
{
    if (fileLen < CONF_HEADER_SIZE) {
        return GMERR_DATA_CORRUPTED;
    }

    if (fileBuff[CODE_TYPE_SIZE - 1] != CRC32_TYPE) {
        return GMERR_DATA_CORRUPTED;
    }

    uint32_t codeLen, confFileLen, crcCodeInFile;

    // get code len
    Status ret = GetValueFromConfHeader(fileBuff + 1, CODE_LEN_SIZE, &codeLen, "%d");
    if (ret != GMERR_OK) {
        return ret;
    }
    if (fileLen < CONF_HEADER_SIZE + codeLen + 1) {
        return GMERR_DATA_CORRUPTED;
    }
    // get file len
    ret = GetValueFromConfHeader(fileBuff + CODE_TYPE_SIZE + CODE_LEN_SIZE, CONF_FILE_LEN_SIZE, &confFileLen, "%d");
    if (ret != GMERR_OK) {
        return ret;
    }
    // get code of Reliability algorithm
    ret = GetValueFromConfHeader(
        fileBuff + CODE_TYPE_SIZE + CODE_LEN_SIZE + CONF_FILE_LEN_SIZE, codeLen, &crcCodeInFile, "%x");
    if (ret != GMERR_OK) {
        return ret;
    }

    if (CONF_HEADER_SIZE + codeLen + 1 + confFileLen != fileLen) {
        return GMERR_DATA_CORRUPTED;
    } else {
        uint32_t crcCodeGen = DbCRC32(fileBuff + CONF_HEADER_SIZE + 1 + codeLen, confFileLen);
        if (crcCodeInFile != crcCodeGen) {
            uint32_t crcCodeGenLegacy = DbCRC32Legacy(fileBuff + CONF_HEADER_SIZE + 1 + codeLen, confFileLen);
            if (crcCodeInFile != crcCodeGenLegacy) {
                return GMERR_DATA_CORRUPTED;
            }
        }
    }

    *confFileBeg = CODE_TYPE_SIZE + CODE_LEN_SIZE + CONF_FILE_LEN_SIZE + codeLen + 1;
    *confFileSize = confFileLen;

    return GMERR_OK;
}

Status CheckBackupFileHmac(
    char *fileBuff, uint32_t fileLen, TextT userKey, uint32_t *confFileBeg, uint32_t *confFileSize)
{
    if (fileLen < CONF_HEADER_SIZE) {
        return GMERR_DATA_CORRUPTED;
    }
    uint32_t buffCursor = CODE_TYPE_SIZE + CODE_LEN_SIZE;
    uint32_t confFileLen = 0;
    Status ret = GetValueFromConfHeader(fileBuff + buffCursor, CONF_FILE_LEN_SIZE, &confFileLen, "%d");
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get conf file len.");
        return ret;
    }
    buffCursor += CONF_FILE_LEN_SIZE;

    HmacDataT hmacDataInFile = {0};
    uint32_t hmacCursor = 0;
    ret = GetHmacInFileBuff(fileBuff, fileLen, &hmacDataInFile, &hmacCursor);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get hmac.");
        return ret;
    }
    buffCursor = hmacCursor;

    ret = TlsVerifyFileHmacByBuff(fileBuff + buffCursor, confFileLen, userKey.str, userKey.len, &hmacDataInFile);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(GMERR_DATA_CORRUPTED, "Verify file's hmac.");
        return ret;
    }
    if (buffCursor + confFileLen != fileLen) {
        DB_LOG_ERROR(GMERR_DATA_CORRUPTED, "Inv hmac contents");
        return GMERR_DATA_CORRUPTED;
    }
    *confFileBeg = buffCursor;
    *confFileSize = confFileLen;
    return GMERR_OK;
}

static bool ImportValidFileCrc(ImportArgumentT *args)
{
    DB_POINTER(args);
    char crcHexCode[CRC_CODE_LEN + 1] = {0};
    uint32_t crcCodeGen = DbCRC32(args->jsonStr, (uint32_t)args->jsonStrLen);
    errno_t err = snprintf_s(crcHexCode, CRC_CODE_LEN + 1, CRC_CODE_LEN, "%08x", crcCodeGen);
    if (err <= 0) {
        return false;
    }
    if (DbStrCmp(args->digest.digest + CODE_LEN_SIZE + DATA_FILE_LEN_SIZE, crcHexCode, false) != 0) {
        return false;
    }
    return true;
}

static bool ImportValidFileHmac(GmcStmtT *stmt, ImportArgumentT *args)
{
    DB_POINTER2(stmt, args);
    TlsDigestInfoT *digest = &args->digest;
    EVP_MAC_CTX *hmacCtx = NULL;
    EVP_MAC *macCtx = NULL;
    char *digestCode = NULL;
    bool isValid = false;
    // 验证hmac
    do {
        hmacCtx = DbHmacInitCtx(digest->password, digest->salt, &macCtx);
        if (digest->clearPassword) {
            // 敏感信息使用完需要立即清理, 如果对文件夹进行操作保留密码，方便下个文件使用
            (void)memset_s(digest->password, MAX_HMAC_KEY_LENGTH, '\0', MAX_HMAC_KEY_LENGTH);
        }
        if (hmacCtx == NULL) {
            break;
        }
        Status ret = DbHmacOffer(hmacCtx, args->jsonStr, args->jsonStrLen);
        if (ret != GMERR_OK) {
            break;
        }
        digestCode = DbHmacDigest(hmacCtx);
        if (digestCode == NULL) {
            break;
        }
        isValid = DbStrCmp(digestCode, args->digest.digest, false) == 0;
    } while (0);
    DbHmacFreeCtx(hmacCtx, macCtx);
    DbAdptDynamicMemFree(digestCode);  // 只在函数内部使用，内存指针没有往外传递，不用置空
    return isValid;
}

Status ImportVerifyFileContent(GmcStmtT *stmt, ImportArgumentT *args)
{
    DB_POINTER2(stmt, args);
    // 大部分场景不用，则直接返回
    if (args->digest.algorithm == DB_DIGEST_ALGORITHM_BUTT) {
        return GMERR_OK;
    }
    if (args->digest.algorithm == DB_DIGEST_ALGORITHM_CRC && ImportValidFileCrc(args)) {
        return GMERR_OK;
    }
    if (args->digest.algorithm == DB_DIGEST_ALGORITHM_HMAC) {
        TlsDigestInfoT *digest = &args->digest;
        // 交互式获取hmac用户口令, 对文件夹进行操作只输入一次密码
        // 没有密码时获取密码，有密码时进行下一步验证
        if (strlen(digest->password) == 0) {
            uint32_t len = 0;
            Status ret = TlsEnterKeyInteractive(digest->password, &len);
            if (ret != GMERR_OK) {
                // 敏感信息使用完需要立即清理
                (void)memset_s(digest->password, MAX_HMAC_KEY_LENGTH, '\0', MAX_HMAC_KEY_LENGTH);
                PRINT_ERROR(DbPrintfDefault,
                    "Input key invalid, The complexity may not meet requirements.. ret = %" PRIu32,
                    (uint32_t)GMERR_INVALID_PARAMETER_VALUE);
                return GMERR_INVALID_PARAMETER_VALUE;
            }
        }
        if (ImportValidFileHmac(stmt, args)) {
            return GMERR_OK;
        }
    }
    return GMERR_DATA_CORRUPTED;
}

Status ImportLabel(GmcStmtT *stmt, ImportArgumentT *args)
{
    DB_POINTER4(stmt, stmt->conn, stmt->memCtx, args);
    char *filePath = args->file.path;
    ImpFileTypeE labelType = args->file.type;
    char *inputLabelName = args->labelName;
    char *cfgJson = args->configJsonStr;
    char *labelJson = args->jsonStr;
    GmImpTransformT transformCtx = {0};
    Status ret = GmImpTransformInit(&transformCtx, stmt->memCtx, inputLabelName, filePath);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR((int32_t)ret, "init transform ctx.");
        return ret;
    }

    if (labelType == FILE_VSCHEMA || labelType == FILE_VSCHEMA_OLD) {
        // convert schema from kv-lite to gmdbv5 if necessary
        ret = ImportVerifyFileContent(stmt, args);
        if (ret != GMERR_OK) {
            TOOL_RUN_ERROR(DbPrintfDefault, ret, "File integrity check unsucc. ret = %d", (int32_t)ret);
            return ret;
        }
        ImportTransformSchema(labelJson, cfgJson, args->jsonObject, &transformCtx);
        ret = CreateLabelBatchAddDDL(stmt, args, &transformCtx, GMC_OPERATION_CREATE_VERTEX_LABEL);
    } else if (labelType == FILE_ESCHEMA) {
        ret = GmcCreateEdgeLabel(stmt, labelJson, cfgJson);
        if (ret == GMERR_OK) {
            g_gmdbTotalImpNumStat.successFileNums++;
        }
    }
    if (ret != GMERR_OK) {
        TlsGetLastErrorFromServer(ret);
        DB_LOG_ERROR((int32_t)ret, "create label, filePath: %s, LabelTypeE = %d, ret = %d", filePath,
            (int32_t)labelType, (int32_t)ret);
    }
    return ret;
}

static Status ImportKvTable(GmcStmtT *stmt, ImportArgumentT *args)
{
    DB_POINTER2(stmt, args);
    char *cfgJson = NULL;
    // 校验文件大小
    char *tableName = (strlen(args->labelName) == 0) ? args->file.name : args->labelName;
    ImpFileTypeE fileType = args->file.type;
    if (fileType == FILE_VSCHEMA_OLD) {
        cfgJson = args->configJsonStr;
    } else {
        // fileType == FILE_CONFIG
        cfgJson = args->jsonStr;
    }
    bool isImport = true;
    Status ret = CheckFileisImport(args, args->configDbJsonObject, &isImport);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (!isImport) {
        args->file.isImport = false;
        return GMERR_OK;
    }
    ret = GmcBatchAddDDL(stmt->batch, GMC_OPERATION_CREATE_KV_TABLE, tableName, NULL, cfgJson);
    // 如果达到GmcBatchAddDDL的上限，执行当前batch并reset
    if (ret == GMERR_BATCH_BUFFER_FULL) {
        ret = ImportBatchExecute(stmt->batch);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = GmcBatchAddDDL(stmt->batch, GMC_OPERATION_CREATE_KV_TABLE, tableName, NULL, cfgJson);
        if (ret != GMERR_OK) {
            TOOL_RUN_ERROR(
                DbPrintfDefault, (int32_t)ret, "import batch add ddl create kvTable unsucc. ret = %d", (int32_t)ret);
            return ret;
        }
    }
    ret = ImportBatchExecute(stmt->batch);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(
            DbPrintfDefault, (int32_t)ret, "import batch exec ddl create kvTable unsucc. ret = %d", (int32_t)ret);
        return ret;
    }
    return ret;
}

inline static size_t ImportGetMaxHandleNum(size_t lastHandleNum)
{
    if (lastHandleNum == 0) {
        return 1;  // 每次至少处理1条
    }
    // 上次成功批量插入N条数据后，本次插入2N条数据, 但是最多处理 CLT_BATCH_CMD_MAX_SIZE
    return DB_MIN(lastHandleNum * 2, CLT_BATCH_CMD_MAX_SIZE);
}

inline static bool IsJsonTypeObjectOrArray(DbJsonT *obj)
{
    return DbJsonIsArray(obj) || DbJsonIsObject(obj);
}

static void ImportIgnoreJsonObject(DbJsonT *vertexObj, DmSchemaT *schema)
{
    uint32_t totalPropeNum = schema->propeNum - schema->sysPropeNum;
    uint32_t nodeNum = schema->nodeNum;
    uint32_t propeNum = totalPropeNum - nodeNum;
    uint32_t nodeIter = 0;
    uint32_t propeIter = 0;
    for (void *it = DbJsonObjectIter(vertexObj); it != NULL; it = DbJsonObjectIterNext(vertexObj, it)) {
        DbJsonT *currJson = DbJsonObjectIterValue(it);
        const char *name = DbJsonObjectIterKey(it);
        if (IsJsonTypeObjectOrArray(currJson)) {
            if (nodeIter >= nodeNum) {
                DbJsonDeleteObjectItem(vertexObj, name);
            } else {
                ++nodeIter;
            }
        } else {
            if (propeIter >= propeNum) {
                DbJsonDeleteObjectItem(vertexObj, name);
            } else {
                ++propeIter;
            }
        }
    }
}

static Status ImportIgnoreExtraNodeAndFiled(DbJsonT *vertexObj, DmSchemaT *schema)
{
    if (!IsJsonTypeObjectOrArray(vertexObj)) {
        return GMERR_OK;
    }
    Status ret = GMERR_OK;

    if (DbJsonIsObject(vertexObj)) {
        ImportIgnoreJsonObject(vertexObj, schema);
    } else if (DbJsonIsArray(vertexObj)) {
        uint32_t arraySize = (uint32_t)DbJsonGetArraySize(vertexObj);
        for (uint32_t i = 0; i < arraySize; ++i) {
            DbJsonT *singleObj = DbJsonArrayGet(vertexObj, i);
            ret = ImportIgnoreExtraNodeAndFiled(singleObj, schema);
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    // 递归剩余的内容
    for (void *it = DbJsonObjectIter(vertexObj); it != NULL; it = DbJsonObjectIterNext(vertexObj, it)) {
        const char *keyName = DbJsonObjectIterKey(it);
        DbJsonT *json = DbJsonObjectIterValue(it);
        DbJsonTypeE type = DbJsonGetType(json);
        if (type != DB_JSON_OBJECT && type != DB_JSON_ARRAY) {
            continue;
        }
        DmNodeSchemaT *nodeSchema;
        ret = DmSchemaGetNodeByName(schema, keyName, &nodeSchema);
        if (ret != GMERR_OK) {
            return ret;
        }
        ret = ImportIgnoreExtraNodeAndFiled(json, MEMBER_PTR(nodeSchema, schema));
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    return GMERR_OK;
}

static Status ImportIgnoreHighVerVertex(GmcStmtT *stmt, DbJsonT *vertexObj, bool isIgnore)
{
    if (!isIgnore || !DbJsonIsObject(vertexObj)) {
        // 非ignore模式不处理
        return GMERR_OK;
    }
    CltCataLabelT *cltCataLabel = CltGetCltCataLabelInStmt(stmt);
    if (cltCataLabel == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Get cataLabel from stmt.");
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DmVertexLabelT *vertexLabel = cltCataLabel->vertexLabel;
    DmSchemaT *vertexLabelSchema = MEMBER_PTR(vertexLabel->metaVertexLabel, schema);
    return ImportIgnoreExtraNodeAndFiled(vertexObj, vertexLabelSchema);
}

static Status ImportSetVertexByJson(
    GmcStmtT *stmt, DbJsonT *obj, const ImpVertexParasT *paras, uint32_t idx, char **vertexJson)
{
    *vertexJson = NULL;
    // 对obj进行裁剪
    Status ret = ImportIgnoreHighVerVertex(stmt, obj, paras->ignoreHighVersionData);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, (uint32_t)ret,
            "insert high version data unsucc, ret = %" PRIu32 ", index = %" PRIu32 ".", (uint32_t)ret, idx);
        return ret;
    }
    char *tmpVertexJson = DbJsonDumps(obj, DB_JSON_PRESERVE_ORDER);
    ret = GmcSetVertexByJson(stmt, GMC_JSON_REJECT_DUPLICATES, tmpVertexJson);
    if (ret != GMERR_OK) {
        DbJsonFree(tmpVertexJson);
        TOOL_RUN_ERROR(DbPrintfDefault, (uint32_t)ret,
            "insert partial data unsucc when parse json object, ret = %" PRIu32 ", index = %" PRIu32 "", (uint32_t)ret,
            idx);
        return ret;
    }
    *vertexJson = tmpVertexJson;
    return GMERR_OK;
}

static void ImportPrepareStmtData(
    GmcBatchT *batch, GmcStmtT *stmt, const ImpVertexParasT *paras, size_t *startIdx, size_t *handleNum)
{
    size_t maxHandleNum = ImportGetMaxHandleNum(*handleNum);  // 根据上次处理的数量, 决定本次处理的数量
    (void)GmcResetVertex(stmt, true);  // 重置一下 stmt 中的 vertex, 避免上次失败的残留.
    size_t idx = *startIdx;
    size_t handledNum = 0;
    while (idx < paras->totalVertexNum) {
        (void)GmcResetVertex(stmt, true);
        DbJsonT *obj = DbJsonArrayGet(paras->json, idx);
        if (obj == NULL) {
            DB_POINTER(obj);  // idx 应该是有效的, 如果没有取出 obj, debug情况下应该需要定位
            TOOL_RUN_ERROR(DbPrintfDefault, (uint32_t)GMERR_INVALID_JSON_CONTENT,
                "insert partial data unsucc when get json object, ret = %u, index=%zu",
                (uint32_t)GMERR_INVALID_JSON_CONTENT, idx);
            ++idx;  // 略过这一条
            continue;
        }

        char *vertexJson = NULL;
        Status ret = ImportSetVertexByJson(stmt, obj, paras, idx, &vertexJson);
        if (ret != GMERR_OK) {
            ++idx;  // 略过这一条
            continue;
        }
        ret = GmcBatchAddDML(batch, stmt);
        if (ret != GMERR_OK) {
            DbJsonFree(vertexJson);
            if (handledNum == 0) {  // 一条都无法push, 返回失败
                ++idx;
                TOOL_RUN_ERROR(DbPrintfDefault, (uint32_t)ret,
                    "insert partial data unsucc when client batch add DML, ret = %u", (uint32_t)ret);
                continue;
            }
            break;  // 有部分成功. 先返回OK, 并执行 本次批量. 下次再重试.
        }
        DbJsonFree(vertexJson);

        handledNum++;
        idx++;
        if (handledNum >= maxHandleNum) {  // 不处理超过指定的数量
            break;
        }
    }
    *handleNum = handledNum;
    *startIdx = idx;  // 记录下次处理的位置
}

static Status HandleExecDataNum(
    const GmcStmtT *stmt, Status retEx, size_t handleNum, ImpVertexLabelParasT *handleDataNum, GmcBatchRetT *batchRet)
{
    uint32_t execSuccessNum = 0;
    uint32_t execTotalNum = 0;
    if (stmt->isDataService && retEx != GMERR_OK) {
        // Datalog的同步插入相当于一批事务，要么都成功，要么都失败，服务端不会填充batchRet，故此处做特殊处理
        execTotalNum = (uint32_t)handleNum;
        execSuccessNum = 0;
    } else {
        Status ret = GmcBatchDeparseRet(batchRet, &execTotalNum, &execSuccessNum);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    if (execTotalNum != handleNum) {
        TOOL_RUN_ERROR(DbPrintfDefault, (uint32_t)GMERR_FIELD_OVERFLOW,
            "import data unsucc when handle batch num is incorrect, ret = %u, execTotalNum = %u, handleNum = %u",
            (uint32_t)GMERR_FIELD_OVERFLOW, (uint32_t)execTotalNum, (uint32_t)handleNum);
    }
    handleDataNum->handleNum = execTotalNum;
    handleDataNum->successNum = execSuccessNum;
    return GMERR_OK;
}

Status ImportVertexDataInner(GmcStmtT *stmt, const ImpVertexParasT *paras, ImpVertexBatchParasT *execParams)
{
    size_t startIdx = 0;
    size_t handleNum = 0;
    GmcBatchT *batch = NULL;
    Status ret = ImportBatchInit(stmt->conn, &batch);  // GMC_BATCH_ORDER_STRICT 严格顺序执行模式
    if (ret != GMERR_OK) {
        return ret;
    }

    GmcBatchRetT batchRet;
    while (startIdx < paras->totalVertexNum) {
        ImpVertexLabelParasT handleDataNum = {0};
        ImportPrepareStmtData(batch, stmt, paras, &startIdx, &handleNum);
        if (handleNum == 0) {
            break;
        }
        bool isConflict = false;
        Status retEx = GmcBatchExecute(batch, &batchRet);
        if (retEx == GMERR_UNIQUE_VIOLATION || retEx == GMERR_PRIMARY_KEY_VIOLATION) {
            isConflict = true;
        }
        ret = HandleExecDataNum(stmt, retEx, handleNum, &handleDataNum, &batchRet);
        if (ret != GMERR_OK) {
            break;
        }
        (execParams->successNum) += handleDataNum.successNum;
        if (isConflict) {
            (execParams->dupNum)++;  // 严格顺序执行模式 模式下, 只有一个失败就停止.
            startIdx = (startIdx - handleNum) + handleDataNum.successNum + 1;  // 由于中途冲突了, 恢复到冲突的下一个位置
        } else {
            // do nothing.
            if (retEx != GMERR_OK) {
                ret = retEx;
                break;
            }
        }
    }
    if (ret == GMERR_OK && execParams->successNum == 0) {
        ret = GMERR_DATA_EXCEPTION;
        TOOL_RUN_ERROR(
            DbPrintfDefault, (uint32_t)ret, "No data insert actually, please re-check. ret = %" PRIu32, (uint32_t)ret);
    }
    (void)GmcBatchDestroy(batch);
    return ret;
}
static inline void InitImpVertexParasT(ImpVertexParasT *paras, const ImportArgumentT *args)
{
    paras->labelName = (strlen(args->labelName) == 0) ? args->file.name : args->labelName;
    paras->opType = GMC_OPERATION_REPLACE;
}

static void HandleImportResult(Status ret, ImpVertexParasT *paras, ImpVertexBatchParasT *execParams)
{
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret,
            "insert data unsucc. totalNum: %zu, successNum: %" PRIu32 ", duplicateNum: %" PRIu32 ", ret = %" PRIu32,
            paras->allTotalVertexNum, execParams->successNum, execParams->dupNum, (uint32_t)ret);
    } else {
        if (paras->allTotalVertexNum == execParams->successNum) {
            g_gmdbTotalImpNumStat.successFileNums++;
            PRINT_INFO(DbPrintfDefault,
                "Insert data succeed. totalNum: %zu, successNum: %" PRIu32 ", duplicateNum: %" PRIu32 "\n",
                paras->allTotalVertexNum, execParams->successNum, execParams->dupNum);
        } else {
            TOOL_RUN_ERROR(DbPrintfDefault, ret,
                "insert partial data unsucc. totalNum: %zu, successNum: %" PRIu32 ", duplicateNum: %" PRIu32,
                paras->allTotalVertexNum, execParams->successNum, execParams->dupNum);
        }
    }
}

static void CalculateTotalNum(ImportArgumentT *args, ImpVertexParasT *paras)
{
    // 失败场景下直接load json 获取导入totalVertexNum
    DbJsonT *jsonRoot = NULL;
    jsonRoot = DbLoadJsonClt(args->jsonStr, DB_JSON_REJECT_DUPLICATES);
    if (jsonRoot == NULL) {
        DB_LOG_DBG_ERROR(GMERR_INVALID_JSON_CONTENT, "Inv json str.");
        return;
    }
    paras->allTotalVertexNum = DbJsonGetArraySize(jsonRoot);
    DbJsonDelete(jsonRoot);
}

static Status ImportVertexData(GmcStmtT *stmt, ImportArgumentT *args)
{
    DB_POINTER2(stmt, args);
    Status ret = ImportVerifyFileContent(stmt, args);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "File integrity check unsucc. ret = %d", (int32_t)ret);
        return ret;
    }
    ImpVertexParasT paras = {};
    InitImpVertexParasT(&paras, args);
    ImpVertexBatchParasT execParams = {0};

    // gmimport的数据导入功能需要使用GMC_OPERATION_REPLACE准备元数据，完成才知道表类型，才能使用GMC_OPERATION_INSERT
    stmt->isTools = true;
    ret = GmcPrepareStmtByLabelNameWithVersion(stmt, paras.labelName, DB_MAX_UINT32, paras.opType);
    stmt->isTools = false;
    if (ret != GMERR_OK) {
        goto END;
    }
    if (stmt->isDataService) {
        DmVertexLabelT *vertexLabel = ((CltOperVertexT *)(stmt->operationContext))->cltCataLabel->vertexLabel;
        VertexLabelCommonInfoT *commonInfo = vertexLabel->commonInfo;
        if (SECUREC_UNLIKELY(commonInfo == NULL)) {
            DB_LOG_ERROR((ret = GMERR_MEMORY_OPERATE_FAILED), "Get vertexlabel commonInfo.");
            goto END;
        }
        DmDtlInOutE inoutType = MEMBER_2(commonInfo, datalogLabelInfo, inoutType);
        DmDtlLabelTypeE labelType = MEMBER_2(commonInfo, datalogLabelInfo, labelType);
        if ((inoutType == DM_DTL_INTERMEDIATE_LABEL && labelType != DM_DTL_RESOURCE_PUBSUB) ||
            inoutType == DM_DTL_OUTPUT_LABEL) {
            ret = GMERR_SEMANTIC_ERROR;
            DB_LOG_ERROR(ret, "Inv datalog label type, filePath: %s", args->file.path);
            goto END;
        }
        stmt->operationType = GMC_OPERATION_INSERT;
        paras.opType = GMC_OPERATION_INSERT;
    }
    ret = MultipleTimesImportVertexData(stmt, args, &paras, &execParams);
    goto END2;
END:
    CalculateTotalNum(args, &paras);
END2:
    HandleImportResult(ret, &paras, &execParams);
    return ret;
}

Status ImportParseKvInfo(char *buffer, uint64_t bufferSize, uint64_t *offset, GmcKvTupleT *kvInfo)
{
    DB_POINTER2(buffer, offset);
    uint64_t keyLenOffset = *offset;
    if (keyLenOffset + sizeof(uint32_t) > bufferSize) {
        goto ERROR_EXIT;
    }
    kvInfo->keyLen = *(uint32_t *)(buffer + keyLenOffset);
    uint64_t keyOffset = keyLenOffset + sizeof(uint32_t);
    if (keyOffset + kvInfo->keyLen > bufferSize) {
        goto ERROR_EXIT;
    }
    kvInfo->key = (uint8_t *)(buffer + keyOffset);
    uint64_t valueLenOffset = keyOffset + kvInfo->keyLen;
    if (valueLenOffset + sizeof(uint32_t) > bufferSize) {
        goto ERROR_EXIT;
    }
    kvInfo->valueLen = *(uint32_t *)(buffer + valueLenOffset);
    uint64_t valueOffset = valueLenOffset + sizeof(uint32_t);
    if (valueOffset + kvInfo->valueLen > bufferSize) {
        goto ERROR_EXIT;
    }
    kvInfo->value = (uint8_t *)(buffer + valueOffset);
    *offset = *offset + kvInfo->keyLen + kvInfo->valueLen + sizeof(uint32_t) + sizeof(uint32_t);
    return GMERR_OK;

ERROR_EXIT:
    TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_DATA_EXCEPTION,
        "Parse kv data unsucc at %" PRIu64 ", buffer size is %" PRIu64 ".", *offset, bufferSize);
    return GMERR_DATA_EXCEPTION;
}

static Status ImportKvDataInner(GmcStmtT *stmt, char *kvBuffer, uint64_t fileSize, uint32_t *successNum)
{
    uint32_t tempTotal = 0;
    uint64_t offset = 0;
    uint32_t batchCount = 0;
    GmcBatchT *batch = NULL;
    Status ret = ImportBatchInit(stmt->conn, &batch);
    if (ret != GMERR_OK) {
        return ret;
    }
    GmcKvTupleT kvInfo = {0};
    GmcBatchRetT batchRet;
    while (offset < fileSize) {
        uint32_t tempSuccess = 0;
        bool setWithoutBatch = false;
        for (batchCount = 0; batchCount < CLT_BATCH_CMD_MAX_SIZE && offset < fileSize; batchCount++) {
            ret = ImportParseKvInfo(kvBuffer, fileSize, &offset, &kvInfo);
            if (ret != GMERR_OK) {  // 解析错误, 导入结束.
                goto END;
            }
            ret = GmcKvInputToStmt(stmt, kvInfo.key, kvInfo.keyLen, kvInfo.value, kvInfo.valueLen);
            if (ret != GMERR_OK) {  // 批量接口 不支持 较大的对象. 遇到大对象, 单独处理.
                setWithoutBatch = true;
                break;
            }
            ret = GmcBatchAddKvDML(batch, stmt, GMC_OPERATION_INSERT);
            if (ret != GMERR_OK) {  // 批量接口 也有内存上限限制,
                setWithoutBatch = true;
                break;
            }
        }
        if (batchCount > 0) {
            (void)GmcBatchExecute(batch, &batchRet);
            ret = GmcBatchDeparseRet(&batchRet, &tempTotal, &tempSuccess);
            if (ret != GMERR_OK) {
                goto END;
            }
        }
        if (setWithoutBatch) {
            ret = GmcKvSet(stmt, kvInfo.key, kvInfo.keyLen, kvInfo.value, kvInfo.valueLen);
            if (ret != GMERR_OK) {
                goto END;
            }
            tempSuccess += 1;
        }
        (*successNum) += tempSuccess;
    }
END:
    (void)GmcBatchDestroy(batch);
    return ret;
}

static Status ImportKvData(GmcStmtT *stmt, ImportArgumentT *args)
{
    DB_POINTER2(stmt, args);
    char *labelName = (strlen(args->labelName) == 0) ? args->file.name : args->labelName;
    Status ret = GmcKvPrepareStmtByLabelName(stmt, labelName);
    if (ret != GMERR_OK) {
        return ret;
    }
    uint32_t successNum = 0;
    ret = ImportVerifyFileContent(stmt, args);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "File integrity check unsucc. ret = %d", (int32_t)ret);
        return ret;
    }
    ret = ImportKvDataInner(stmt, args->jsonStr, args->jsonStrLen, &successNum);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "insert data unsucc, ret = %d", (int32_t)ret);
    } else {
        g_gmdbTotalImpNumStat.successFileNums++;
        PRINT_INFO(DbPrintfDefault, "Insert data succeed. successNum: %" PRIu32 "\n", successNum);
    }
    return ret;
}

static Status ParseNspCfgTrxInfo(const DbJsonT *singleJson, GmcNspCfgT *nspCfg)
{
    // 解析创建namespace必须可选的字段transaction_type
    DbJsonT *jsonObj = DbJsonObjectGet(singleJson, "transaction_type");
    if (jsonObj != NULL) {
        if (!DbJsonIsInteger(jsonObj) || DbJsonIntegerValue(jsonObj) > GMC_DEFAULT_TRX ||
            DbJsonIntegerValue(jsonObj) < GMC_PESSIMISITIC_TRX) {
            return GMERR_INVALID_JSON_CONTENT;
        }
        nspCfg->trxCfg.trxType = (GmcTrxTypeE)DbJsonIntegerValue(jsonObj);
    } else {
        nspCfg->trxCfg.trxType = GMC_DEFAULT_TRX;
    }
    // 解析创建namespace必须可选的字段isolation_level
    jsonObj = DbJsonObjectGet(singleJson, "isolation_level");
    if (jsonObj != NULL) {
        if (!DbJsonIsInteger(jsonObj) || DbJsonIntegerValue(jsonObj) >= GMC_TX_ISOLATION_BUTT ||
            DbJsonIntegerValue(jsonObj) < GMC_TX_ISOLATION_UNCOMMITTED) {
            return GMERR_INVALID_JSON_CONTENT;
        }
        nspCfg->trxCfg.isolationLevel = (GmcIsolationTypeE)DbJsonIntegerValue(jsonObj);
    } else {
        nspCfg->trxCfg.isolationLevel = GMC_TX_ISOLATION_DEFAULT;
    }
    return GMERR_OK;
}

static Status ParseNspCfgInfo(const DbJsonT *singleJson, GmcNspCfgT *nspCfg)
{
    // 解析创建namespace必须包含的字段namespace_name
    DbJsonT *jsonObj = DbJsonObjectGet(singleJson, "namespace_name");
    if (jsonObj == NULL) {
        return GMERR_INVALID_JSON_CONTENT;
    }
    nspCfg->namespaceName = DbJsonStringValue(jsonObj);
    if (nspCfg->namespaceName == NULL) {
        return GMERR_INVALID_JSON_CONTENT;
    }
    // 解析创建namespace必须可选的字段tablespace_name
    jsonObj = DbJsonObjectGet(singleJson, "tablespace_name");
    if (jsonObj == NULL) {
        nspCfg->tablespaceName = NULL;
    } else {
        nspCfg->tablespaceName = DbJsonStringValue(jsonObj);
        if (nspCfg->tablespaceName == NULL) {
            return GMERR_INVALID_JSON_CONTENT;
        }
    }
    // 解析创建namespace必须可选的字段user_name
    jsonObj = DbJsonObjectGet(singleJson, "user_name");
    if (jsonObj == NULL) {
        nspCfg->userName = NULL;
    } else {
        nspCfg->userName = DbJsonStringValue(jsonObj);
        if (nspCfg->userName == NULL) {
            return GMERR_INVALID_JSON_CONTENT;
        }
    }
    return ParseNspCfgTrxInfo(singleJson, nspCfg);
}

static Status ImportSingleNsp(GmcStmtT *stmt, const DbJsonT *singleJson)
{
    GmcNspCfgT nspCfg = {0};
    Status ret = ParseNspCfgInfo(singleJson, &nspCfg);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GmcCreateNamespaceWithCfg(stmt, &nspCfg);
}

static Status ImportNsp(GmcStmtT *stmt, ImportArgumentT *args)
{
    DB_POINTER2(stmt, args);
    uint32_t successNum = 0, nspNum = 0;
    Status ret = GMERR_DATATYPE_MISMATCH;
    if (DbJsonIsObject(args->jsonObject)) {
        nspNum = 1;
        ret = ImportSingleNsp(stmt, args->jsonObject);
        if (ret == GMERR_OK) {
            successNum++;
        }
    } else if (DbJsonIsArray(args->jsonObject)) {
        nspNum = (uint32_t)DbJsonGetArraySize(args->jsonObject);
        for (uint32_t i = 0; i < nspNum; i++) {
            DbJsonT *singleJson = DbJsonArrayGet(args->jsonObject, i);
            ret = ImportSingleNsp(stmt, singleJson);
            if (ret != GMERR_OK) {
                continue;
            }
            successNum++;
        }
    }
    if (nspNum == 0) {
        PRINT_ERROR(DbPrintfDefault, "Create namespace from \"%s\", Total %" PRIu32 " namespaces were found.\n",
            args->file.path, nspNum);
        return ret;
    }
    if (successNum == 0) {
        // 由于该错误码并不反馈给用户，故此处标记为内部错误，用于上层做失败文件数统计
        // 同一个json文件中，仅全部创建失败的情况下才返回错误码，上层认为整个文件失败
        return ret;
    }
    // namespace单个文件的批量导入只要有1个namespace创建成功，就认为该文件导入成功，但是会打印单个文件中的失败信息
    g_gmdbTotalImpNumStat.successFileNums++;
    if (successNum != nspNum) {
        // 当成功数与失败数不同时，针对单json文件打印报错提供给用户
        PRINT_ERROR(DbPrintfDefault,
            "Create namespace from \"%s\", Total %" PRIu32 " namespaces were found, %" PRIu32 " OK, %" PRIu32
            " failed.\n",
            args->file.path, nspNum, successNum, (uint32_t)(nspNum - successNum));
    }
    return GMERR_OK;
}

Status ImportRespool(GmcStmtT *stmt, ImportArgumentT *args)
{
    char *resPoolStr = NULL;
    Status ret = GmImpTransformResourcePoolJson(stmt->memCtx, args->jsonObject, &resPoolStr);
    if (ret != GMERR_OK) {
        DB_LOG_DBG_ERROR((int32_t)ret, "Transform res pool json.");
        return ret;
    }
    DB_POINTER(resPoolStr);
    // create resource pool
    ret = GmcCreateResPool(stmt, resPoolStr);
    if (ret != GMERR_OK) {
        DB_LOG_DBG_ERROR((int32_t)ret, "Create res pool from %s", args->file.path);
    } else {
        g_gmdbTotalImpNumStat.successFileNums++;
    }
    ImportFreeJsonStr(stmt, resPoolStr);
    return ret;
}

static Status ImportPrepareFilePathInfo(DbMemCtxT *memCtx, ImportCacheFileItemT *filePathInfo, ImportArgumentT *args)
{
    char *filePath = (char *)DbDynMemCtxAlloc(memCtx, DB_MAX_PATH);
    if (filePath == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Alloc filePath");
        return GMERR_OUT_OF_MEMORY;
    }
    if (strcpy_s(filePath, DB_MAX_PATH, args->file.path) != EOK) {
        DbDynMemCtxFree(memCtx, filePath);
        return GMERR_INTERNAL_ERROR;
    }
    filePathInfo->filePath = filePath;
    filePathInfo->fileType = args->file.type;
    return GMERR_OK;
}

static Status ExpandCacheFileItemT(DbMemCtxT *memCtx, ImportCacheFilesT *filesInfo)
{
    uint32_t newTotal = filesInfo->total + BATCH_CACHE_FILE_NUM;
    size_t newSize = newTotal * sizeof(ImportCacheFileItemT);
    ImportCacheFileItemT *fileItems = DbDynMemCtxAlloc(memCtx, newSize);
    if (fileItems == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_OUT_OF_MEMORY, "Unsucc to alloc fileItems. need size = %" PRIu64 "",
            (uint64_t)newSize);
        return GMERR_OUT_OF_MEMORY;
    }
    if (filesInfo->fileItems != NULL) {
        if (memcpy_s(fileItems, newSize, filesInfo->fileItems, filesInfo->total * sizeof(ImportCacheFileItemT)) !=
            EOK) {
            TOOL_RUN_ERROR(DbPrintfDefault, GMERR_MEMORY_OPERATE_FAILED, "Unsucc to move fileItems.");
            DbDynMemCtxFree(memCtx, fileItems);
            return GMERR_MEMORY_OPERATE_FAILED;
        }
        DbDynMemCtxFree(memCtx, filesInfo->fileItems);
    }
    filesInfo->fileItems = fileItems;
    filesInfo->total = newTotal;
    return GMERR_OK;
}

Status ExtractFilePath(GmcStmtT *stmt, ImportArgumentT *args, ImportCacheFilesT *filesInfo,
    ImpFileNumStatT *impFileStat, uint32_t recurDepth)
{
    Status status;
    /* file */
    if (DbFileExist(args->file.path)) {
        status = ImportParseFileType((ImportArgumentT *)args);
        if (status != GMERR_OK) {
            return status;
        }
        if (!args->enabledFileTypes[args->file.type]) {
            return status;
        }
        if (filesInfo->count >= filesInfo->total) {
            status = ExpandCacheFileItemT(stmt->memCtx, filesInfo);
            if (status != GMERR_OK) {
                return status;
            }
        }
        status = ImportPrepareFilePathInfo(stmt->memCtx, &filesInfo->fileItems[filesInfo->count], args);
        if (status != GMERR_OK) {
            return status;
        }
        filesInfo->count++;

        /* folder */
    } else if (DbDirExist(args->file.path)) {
        impFileStat->totalDirNums++;
        g_gmdbTotalImpNumStat.totalDirNums++;
        status = ExtractAllFilesPath(stmt, args, filesInfo, impFileStat, recurDepth + 1);
        if (status == GMERR_OK) {
            impFileStat->successDirNums++;
            g_gmdbTotalImpNumStat.successDirNums++;
        } else {
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)status, "Extract folder \"%s\" unsucc. ret = %d", args->file.path,
                (int32_t)status);
        }
    }
    return GMERR_OK;
}

Status ExtractAllFilesPath(GmcStmtT *stmt, ImportArgumentT *args, ImportCacheFilesT *filesInfo,
    ImpFileNumStatT *impFileStat, uint32_t recurDepth)
{
    if (recurDepth > MAX_RECURSION_DEPTH) {
        TOOL_RUN_ERROR(
            DbPrintfDefault, (int32_t)GMERR_OUT_OF_MEMORY, "stack overflow, ret = %d", (int32_t)GMERR_OUT_OF_MEMORY);
        return GMERR_OUT_OF_MEMORY;
    }
    DIR *dir = NULL;
    char dirPath[DB_MAX_PATH] = {0};
    Status status = ImportPreparedScanFile(args, &dir, dirPath, DB_MAX_PATH);
    if (status != GMERR_OK || dir == NULL) {
        return status;
    }
    while (true) {
        struct dirent *dirName = readdir(dir);
        if (dirName == NULL) {
            break;
        }
        if (DbStrCmp(dirName->d_name, ".", true) == 0 || DbStrCmp(dirName->d_name, "..", true) == 0) {
            continue;
        }
        status = ImportSetFullPathStr(args, dirPath, dirName);
        if (status != GMERR_OK) {
            continue;
        }
        (void)ExtractFilePath(stmt, args, filesInfo, impFileStat, recurDepth);
    }
    (void)closedir(dir);
    if (strcpy_s(args->file.path, sizeof(args->file.path), dirPath) != EOK) {
        return GMERR_INTERNAL_ERROR;
    }
    return status;
}

/* filter and sort files by order:
 * FILE_NSP, FILE_VSCHEMA_OLD, FILE_VSCHEMA, FILE_CONFIG, FILE_ESCHEMA, FILE_VDATA, FILE_GMDATA,
 * FILE_KVDATA
 */
void MergeFilesPath(ImportCacheFileItemT *fileItems, uint32_t count)
{
    ImpFileTypeE fileTypeOrder[] = {
        FILE_NSP, FILE_VSCHEMA_OLD, FILE_VSCHEMA, FILE_CONFIG, FILE_ESCHEMA, FILE_VDATA, FILE_GMDATA, FILE_KVDATA};

    uint32_t curPos = 0, i = 0, j = 0;
    while (i < count && curPos < ELEMENT_COUNT(fileTypeOrder)) {
        if (fileItems[i].fileType == fileTypeOrder[curPos]) {
            i++;
            continue;
        }
        for (j = i + 1; j < count; j++) {
            if (fileItems[j].fileType == fileTypeOrder[curPos]) {
                ImportCacheFileItemT tmp = fileItems[i];
                fileItems[i] = fileItems[j];
                fileItems[j] = tmp;
                i++;
                break;
            }
        }
        if (j == count) {
            curPos++;
        }
    }
}

static inline bool ImportIsFileSupported(ImpFileTypeE fileType)
{
#ifndef NDEBUG
    return true;
#else
    return fileType != FILE_ESCHEMA;
#endif
}

static inline bool ImportIsScheamFile(ImpFileTypeE fileType)
{
    return fileType == FILE_VSCHEMA_OLD || fileType == FILE_VSCHEMA || fileType == FILE_ESCHEMA || fileType == FILE_NSP;
}

static Status ImportCacheFiles(
    GmcStmtT *stmt, ImportArgumentT *args, ImportCacheFilesT *filePathInfos, ImpFileNumStatT *impFileStat)
{
    DB_POINTER4(stmt, args, filePathInfos, impFileStat);
    char originFilePath[DB_MAX_PATH] = {0};
    if (strcpy_s(originFilePath, sizeof(originFilePath), args->file.path) != EOK) {
        return GMERR_INTERNAL_ERROR;
    }
    Status ret = GMERR_OK;
    for (uint32_t i = 0; i < filePathInfos->count; i++) {
        ImportCacheFileItemT *fileItem = &filePathInfos->fileItems[i];
        if (!args->enabledFileTypes[fileItem->fileType]) {
            continue;
        }
        if (!ImportIsScheamFile(fileItem->fileType)) {
            if (stmt->batch->batchState != CLT_BATCH_STATE_PREPARED) {
                ret = ImportBatchExecute(stmt->batch);
            }
        }
        if (strcpy_s(args->file.path, sizeof(args->file.path), fileItem->filePath) != EOK) {
            ret = GMERR_INTERNAL_ERROR;
            break;
        }
        if (ImportIsFileSupported(fileItem->fileType)) {
            (void)ImportScanFileFromDirInner(stmt, args, impFileStat, fileItem->fileType);
        }
    }
    if (strcpy_s(args->file.path, sizeof(args->file.path), originFilePath) != EOK) {
        return GMERR_INTERNAL_ERROR;
    }
    return ret;
}

void FreeFilesPath(GmcStmtT *stmt, ImportCacheFilesT *filePathInfos)
{
    if (filePathInfos->fileItems == NULL) {
        return;
    }

    for (uint32_t idx = 0; idx < filePathInfos->count; idx++) {
        DbDynMemCtxFree(stmt->memCtx, filePathInfos->fileItems[idx].filePath);
        filePathInfos->fileItems[idx].filePath = NULL;
    }
}

static Status ImportCacheFolder(GmcStmtT *stmt, ImportArgumentT *args)
{
    ImpFileNumStatT impFileStat = {0};
    ImportCacheFilesT *cacheFiles = DbDynMemCtxAlloc(stmt->memCtx, sizeof(ImportCacheFilesT));
    if (cacheFiles == NULL) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_OUT_OF_MEMORY, "unsucc to alloc cacheFiles");
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(cacheFiles, sizeof(ImportCacheFilesT), 0, sizeof(ImportCacheFilesT));
    // tranversal folder recursively and extract all files path
    Status ret = ExtractAllFilesPath(stmt, args, cacheFiles, &impFileStat, 1);
    if (ret != GMERR_OK) {
        FreeFilesPath(stmt, cacheFiles);
        DbDynMemCtxFree(stmt->memCtx, cacheFiles);
        return ret;
    }
    // merge files in order
    MergeFilesPath(cacheFiles->fileItems, cacheFiles->count);
    // tranversal fileType and import file
    ret = ImportCacheFiles(stmt, args, cacheFiles, &impFileStat);
    FreeFilesPath(stmt, cacheFiles);
    DbDynMemCtxFree(stmt->memCtx, cacheFiles);
    return ret;
}

static Status GetFileTypeRet(GmcStmtT *stmt, ImportArgumentT *args, ImpFileTypeE fileType, uint32_t recurDepth)
{
    Status fileTypeRet;
    if (fileType >= FILE_GMFILEDPOOL) {
        return GMERR_FEATURE_NOT_SUPPORTED;
    } else if (fileType == FILE_VSCHEMA) {
        fileTypeRet = ImportScanFileFromDir(stmt, args, FILE_VSCHEMA, recurDepth);
        Status tmpRet = ImportScanFileFromDir(stmt, args, FILE_VSCHEMA_OLD, recurDepth);
        fileTypeRet = (fileTypeRet == GMERR_OK) ? tmpRet : fileTypeRet;
    } else if (fileType == FILE_VDATA) {
        fileTypeRet = ImportScanFileFromDir(stmt, args, FILE_VDATA, recurDepth);
        Status tmpRet = ImportScanFileFromDir(stmt, args, FILE_GMDATA, recurDepth);
        fileTypeRet = (fileTypeRet == GMERR_OK) ? tmpRet : fileTypeRet;
    } else if (fileType == FILE_KVDATA) {
        fileTypeRet = ImportScanFileFromDir(stmt, args, FILE_KVDATA, recurDepth);
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
    } else if (fileType == FILE_BINDATA) {
        fileTypeRet = ImportScanBinFileFromDir(stmt, args, FILE_BINDATA, recurDepth);
#endif
    } else {
        fileTypeRet = ImportScanFileFromDir(stmt, args, fileType, recurDepth);
    }
    return fileTypeRet;
}

static Status ImportMultipleFiles(GmcStmtT *stmt, ImportArgumentT *args, uint32_t recurDepth)
{
    if (recurDepth > MAX_RECURSION_DEPTH) {
        TOOL_RUN_ERROR(
            DbPrintfDefault, (int32_t)GMERR_OUT_OF_MEMORY, "stack overflow, ret = %d", (int32_t)GMERR_OUT_OF_MEMORY);
        return GMERR_OUT_OF_MEMORY;
    }
    if (!DbDirExist(args->file.path)) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_DIRECTORY_OPERATE_FAILED,
            "No such file or directory: %s. ret = %d", args->file.path, (int32_t)GMERR_DIRECTORY_OPERATE_FAILED);
        return GMERR_DIRECTORY_OPERATE_FAILED;
    }
    ImpFileTypeE fileType = ImportGetFileTypeByCmdType(args->cmdType);
    Status fileTypeRet = GetFileTypeRet(stmt, args, fileType, recurDepth);
    Status folderTypeRet = ImportScanFileFromDir(stmt, args, FILE_FOLDER, recurDepth);
    if (fileTypeRet != GMERR_OK) {
        return fileTypeRet;
    }
    return folderTypeRet;
}

static bool ImpGmDbJsonIsKvTable(GmcStmtT *stmt, ImportArgumentT *args)
{
    DbJsonT *jsonConfigs = DbJsonObjectGet(args->jsonObject, "configs");
    return jsonConfigs != NULL ? true : false;
}

static Status ImportGmJsonFile(GmcStmtT *stmt, ImportArgumentT *args)
{
    if (ImpGmDbJsonIsKvTable(stmt, args)) {
        if (args->cmdType == GMIMP_CMD_KVTABLE || args->cmdType == GMIMP_CMD_IMP_SCHEMA ||
            ImportIsCacheCommandIncludeKvtable(args->cmdType, args->includeCmdFlag)) {
            // import kvTable
            return ImportKvTable(stmt, args);
        } else {
            TOOL_RUN_ERROR(DbPrintfDefault, GMERR_DATATYPE_MISMATCH, "unexpected .gmjson file. ret = %d",
                (int32_t)GMERR_DATATYPE_MISMATCH);
            return GMERR_DATATYPE_MISMATCH;
        }
    } else {
        // when importing a single kvtable file
        if (args->cmdType == GMIMP_CMD_KVTABLE ||
            (args->cmdType == GMIMP_CMD_CACHE &&
                !ImportIsCacheCommandIncludeVschema(args->cmdType, args->includeCmdFlag))) {
            TOOL_RUN_ERROR(DbPrintfDefault, GMERR_DATATYPE_MISMATCH,
                "Import kvtable file does not contain configs unsucc. ret = %d", (int32_t)GMERR_DATATYPE_MISMATCH);
            return GMERR_DATATYPE_MISMATCH;
        }
        // import label
        return ImportLabel(stmt, args);
    }
}

static bool NeedGetConfigJson(const ImportArgumentT *args)
{
    // 仅导表功能需要读取config json
    if (args->file.type == FILE_VSCHEMA_OLD || args->file.type == FILE_VSCHEMA || args->file.type == FILE_ESCHEMA) {
        return true;
    }
    // 其他导入功能没有config json, 故不用读取
    return false;
}

Status ImportLoadJson(GmcStmtT *stmt, ImportArgumentT *args)
{
    DB_POINTER2(stmt, args);
    args->jsonObject = NULL;  // label的json结构体
    args->jsonStr = NULL;
    args->configJsonStr = NULL;
    Status ret;
    if (NeedGetConfigJson(args)) {
        ret = ImportReadCfgDataFromFile(stmt, args);
        if (ret != GMERR_OK) {
            return ret;
        }
    }
    // load label json file
    ret = ImportParseFileContent(stmt, args);
    if (ret != GMERR_OK) {
        return ret;
    }
    // kv不用转json
    // FILE_VDATA 以及 FILE_GMDATA 在导数据时再分块转json，一次性转json内存膨胀太大
    if (args->file.type == FILE_KVDATA || args->file.type == FILE_VDATA || args->file.type == FILE_GMDATA) {
        return GMERR_OK;
    }
    // load json structure
    DbJsonT *jsonRoot = NULL;
    jsonRoot = DbLoadJsonClt(args->jsonStr, DB_JSON_REJECT_DUPLICATES);
    if (jsonRoot == NULL) {
        ImportFreeJsonInfoForFile(stmt, args);
        DB_LOG_DBG_ERROR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "Read labeljson from %s.", args->file.path);
        return GMERR_INVALID_JSON_CONTENT;
    }
    args->jsonObject = jsonRoot;
    if (!(DbJsonIsObject(jsonRoot) || DbJsonIsArray(jsonRoot))) {
        ImportFreeJsonInfoForFile(stmt, args);
        DB_LOG_DBG_ERROR((int32_t)GMERR_INVALID_JSON_CONTENT, "%s is not standard labeljson.", args->file.path);
        return GMERR_INVALID_JSON_CONTENT;
    }
    return GMERR_OK;
}

Status ImportPrepareJsonInfoForFile(GmcStmtT *stmt, ImportArgumentT *args)
{
    args->file.isImport = true;  // 默认导入
    if (!DbFileExist(args->file.path)) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_FILE_OPERATE_FAILED, "No such file or directory: %s. ret = %d",
            args->file.path, (int32_t)GMERR_FILE_OPERATE_FAILED);
        return GMERR_FILE_OPERATE_FAILED;
    }
    if (!ImportIsFileTypeMatchCmdType(args)) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_DATATYPE_MISMATCH,
            "Import file type does not match the commond type. ret = %d", (int32_t)GMERR_DATATYPE_MISMATCH);
        return GMERR_DATATYPE_MISMATCH;
    }
    Status ret = ImportCheckFileSizeWithLimit(args->file.path, args->file.type, args->file.sizeLimit);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(
            DbPrintfDefault, (int32_t)ret, "Check file size unsucc, file: %s. ret = %d", args->file.path, (int32_t)ret);
        return ret;
    }
    if ((ret = ImportLoadJson(stmt, args)) != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "load json content unsucc, file: %s. ret = %d", args->file.path,
            (int32_t)ret);
        return ret;
    }
    return GMERR_OK;
}

Status ImportFileErr(GmcStmtT *stmt, ImportArgumentT *args)
{
    DB_UNUSED(stmt);
    DB_UNUSED(args);
    return GMERR_FILE_OPERATE_FAILED;
}

Status ImportSingleFile(GmcStmtT *stmt, ImportArgumentT *args)
{
    DB_POINTER2(stmt, args);
    Status ret = ImportPrepareJsonInfoForFile(stmt, args);
    if (ret != GMERR_OK) {
        return ret;
    }
    Status (*importFuncTbl[FILE_BOTTOM + 1])(GmcStmtT * stmt, ImportArgumentT * args) = {
        [FILE_VSCHEMA_OLD] = ImportGmJsonFile,
        [FILE_GMRESP] = ImportRespool,
        [FILE_VSCHEMA] = ImportGmJsonFile,
        [FILE_ESCHEMA] = ImportGmJsonFile,
        [FILE_VDATA] = ImportVertexData,
        [FILE_GMDATA] = ImportVertexData,
        [FILE_CONFIG] = ImportKvTable,
        [FILE_KVDATA] = ImportKvData,
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
        [FILE_BINDATA] = ImportFileErr,
#endif
        [FILE_NSP] = ImportNsp,
        [FILE_EDATA] = ImportFileErr,
        [FILE_GMFILEDPOOL] = ImportFileErr,
        [FILE_FOLDER] = ImportFileErr,
        [FILE_DATALOG] = ImportFileErr,
        [FILE_BACKUP] = ImportFileErr,
#ifdef TS_MULTI_INST
        [FILE_GMSQL] = ImportFileErr,
#endif
        [FILE_BOTTOM] = ImportFileErr,
    };
    ret = importFuncTbl[args->file.type](stmt, args);
    ImportFreeJsonInfoForFile(stmt, args);
    return ret;
}

void HandleImportFolderResult(void)
{
    // clang-format off
    PRINT_INFO(DbPrintfDefault, "Total %" PRIu32 " files were found, %" PRIu32 " files OK, %" PRIu32
        " files skip, %" PRIu32 " files failed. %" PRIu32 " folder were found, %" PRIu32
        " folder OK, %" PRIu32 " folder failed.\n ",
        g_gmdbTotalImpNumStat.totalFileNums, g_gmdbTotalImpNumStat.successFileNums,
        g_gmdbTotalImpNumStat.skipFileNums,
        ((g_gmdbTotalImpNumStat.totalFileNums) - (g_gmdbTotalImpNumStat.successFileNums) -
        (g_gmdbTotalImpNumStat.skipFileNums)),
        g_gmdbTotalImpNumStat.totalDirNums, g_gmdbTotalImpNumStat.successDirNums,
        g_gmdbTotalImpNumStat.totalDirNums - g_gmdbTotalImpNumStat.successDirNums);
    // clang-format on
}

Status ImportFolder(ImportArgumentT *args, GmcStmtT *stmt)
{
    Status ret = GMERR_OK;
    ResetTotalImpNumStat();
    args->digest.clearPassword = false;
    if (args->cmdType == GMIMP_CMD_CACHE) {
        ret = ImportCacheFolder(stmt, args);
        if (ret != GMERR_OK) {
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "Import cache folder from dir \"%s\" unsucc. ret = %d",
                args->file.path, (int32_t)ret);
        }
    } else {
        ret = ImportMultipleFiles(stmt, args, 1);
        if (ret != GMERR_OK) {
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "Import multiple files from dir \"%s\" unsucc. ret = %d",
                args->file.path, (int32_t)ret);
        }
    }
    // 敏感信息使用完需要立即清理，文件夹中的文件处理完成，清理密码
    (void)memset_s(args->digest.password, MAX_HMAC_KEY_LENGTH, '\0', MAX_HMAC_KEY_LENGTH);
    if (stmt->batch->batchState != CLT_BATCH_STATE_PREPARED) {
        ret = ImportBatchExecute(stmt->batch);
    }
    // 打印汇总信息
    HandleImportFolderResult();
    return ret;
}

static Status RestoreRenameFile(const char *alterFilePath, const char *inputFilePath, const char *fileName)
{
    bool isNull = strlen(inputFilePath) == 0;
    char tempFilePath[DB_MAX_PATH] = {0};
    // clang-format off
    if (snprintf_s(tempFilePath, DB_MAX_PATH, DB_MAX_PATH - 1, "%s%s%s", isNull ? "" : inputFilePath, isNull ? "" : "/",
        fileName) < 0) {
        return GMERR_FIELD_OVERFLOW;
    }
    // clang-format on
    if (DbAdptRename(alterFilePath, tempFilePath) != GMERR_OK) {
        return GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
    }
    return GMERR_OK;
}

Status RestoreToConfFile(char *fileBuff, char *outputPath, uint32_t confFileBeg, uint32_t confFileSize)
{
    char tmpOutputPath[DB_MAX_PATH] = {0};
    int32_t ret = snprintf_s(tmpOutputPath, DB_MAX_PATH, DB_MAX_PATH - 1, "gmserverini-%u.tmp", DbAdptGetpid());
    if (ret < 0) {
        return GMERR_FIELD_OVERFLOW;
    }
    int32_t fd = -1;
    Status status = DbOpenFile(tmpOutputPath, CREATE_FILE | WRITE_ONLY, PERM_USRRW, &fd);
    if (status != GMERR_OK) {
        return status;
    }
    do {
        status = DbWriteFile(fd, fileBuff + confFileBeg, confFileSize);
        if (status != GMERR_OK) {
            TOOL_RUN_ERROR(DbPrintfDefault, status, "db write file unsucc. ret = %d.", status);
            break;
        }
        bool isNull = strlen(outputPath) == 0;
        char tmpFile[DB_MAX_PATH] = {0};
        // clang-format off
        if (snprintf_s(tmpFile, DB_MAX_PATH, DB_MAX_PATH - 1, "%s%s%s", isNull ? "./" : outputPath, isNull ? "" : "/",
            SYSCONF_FILENAME) < 0) {
            status = GMERR_FIELD_OVERFLOW;
            break;
        }
        // clang-format on
        if (DbFileExist(tmpFile)) {
            TOOL_RUN_ERROR(
                DbPrintfDefault, GMERR_GET_PATH_FAILED, "config file already exist. outputPath: %s", tmpFile);
            status = GMERR_GET_PATH_FAILED;
            break;
        }
        status = RestoreRenameFile(tmpOutputPath, outputPath, SYSCONF_FILENAME);
        if (status != GMERR_OK) {
            TOOL_RUN_ERROR(DbPrintfDefault, GMERR_GET_PATH_FAILED,
                "restore file unsucc. ret = %" PRId32 ", outputPath: %s", status, tmpFile);
            break;
        }
    } while (0);
    DbCloseFile(fd);
    if (DbFileExist(tmpOutputPath)) {
        Status rmRet = DbRemoveFile(tmpOutputPath);
        if (rmRet != GMERR_OK) {
            TOOL_RUN_ERROR(DbPrintfDefault, rmRet, "remove temp file unsucc.");
        }
    }
    return status;
}

static Status ImportFileHmacProc(
    char *fileBuff, uint32_t fileLen, ImportArgumentT *args, uint32_t *confFileBeg, uint32_t *confFileSize)
{
    uint32_t keyLen = 0;
    Status ret = TlsEnterKeyInteractive(args->digest.password, &keyLen);
    if (ret != GMERR_OK) {
        // 敏感信息使用完需要立即清理
        (void)memset_s(args->digest.password, MAX_HMAC_KEY_LENGTH, '\0', MAX_HMAC_KEY_LENGTH);
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_PARAMETER_VALUE,
            "Input key unexpected, The complexity may not meet requirements.. ret = %" PRIu32,
            (uint32_t)GMERR_INVALID_PARAMETER_VALUE);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    TextT keyText = {.str = args->digest.password, .len = keyLen};
    ret = CheckBackupFileHmac(fileBuff, fileLen, keyText, confFileBeg, confFileSize);
    // 敏感信息使用完需要立即清理
    (void)memset_s(args->digest.password, MAX_HMAC_KEY_LENGTH, '\0', MAX_HMAC_KEY_LENGTH);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret,
            "Check HMAC code unsucc, make sure the key is right, or backupFile may be modified: filePath %s. "
            "ret = %d",
            args->file.path, (int32_t)ret);
        return ret;
    }
    return GMERR_OK;
}

Status RestoreConf(GmcStmtT *stmt, ImportOptionT *toolOption)
{
    DB_POINTER2(stmt, toolOption);
    ImportArgumentT *args = &toolOption->args;
    if (args->cmdType >= GMIMP_CMD_BOTTOM) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_INVALID_PARAMETER_VALUE, "unexpected command type: %d. ret = %d",
            (int32_t)args->cmdType, (int32_t)GMERR_INVALID_PARAMETER_VALUE);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    Status ret = TlsFormatAndCheckFileName(args->file.path, sizeof(args->file.path));
    if (ret != GMERR_OK) {
        return ret;
    }
    char *fileBuff = NULL;
    uint32_t fileLen = 0;
    do {
        if ((ret = TlsReadConfFile(stmt->memCtx, args->file.path, &fileBuff, &fileLen)) != GMERR_OK) {
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "Read backup conf file unsucc: filePath %s. ret = %d",
                args->file.path, (int32_t)ret);
            break;
        }
        uint32_t confFileBeg, confFileSize;
        if (args->digest.algorithm == DB_DIGEST_ALGORITHM_HMAC) {
            ret = ImportFileHmacProc(fileBuff, fileLen, args, &confFileBeg, &confFileSize);
            if (ret != GMERR_OK) {
                break;
            }
        } else {
            ret = CheckBackupFileCRC(fileBuff, fileLen, &confFileBeg, &confFileSize);
            if (ret != GMERR_OK) {
                TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret,
                    "Check reliability algorithm code unsucc: filePath %s. ret = %d", args->file.path, (int32_t)ret);
                break;
            }
        }
        ret = RestoreToConfFile(fileBuff, args->outputPath, confFileBeg, confFileSize);
        if (ret != GMERR_OK) {
            TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret,
                "Restore conf file unsucc: filePath %s. ret = %d, os ret no = %d", args->file.path, (int32_t)ret,
                DbAptGetErrno());
            break;
        }
    } while (0);
    DbDynMemCtxFree(stmt->memCtx, fileBuff);  // 只在函数内部使用，内存指针没有往外传递，不用置空
    if (ret == GMERR_OK) {
        PRINT_SUCCESS(
            DbPrintfDefault, "Command type: %s, restore conf file successfully", g_gmdbImpCmdName[args->cmdType]);
    }
    return ret;
}

Status ImportFormatAndCheckFileName(ImportArgumentT *args)
{
    Status ret = TlsFormatAndCheckFileName(args->file.path, sizeof(args->file.path));
    if (ret != GMERR_OK) {
        return ret;
    }
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
    if (args->cmdType == GMIMP_CMD_CACHE || args->cmdType == GMIMP_CMD_BINDATA) {
#else
    if (args->cmdType == GMIMP_CMD_CACHE) {
#endif
        for (uint32_t idx = 0; idx < args->repeatFileNum; idx++) {
            ret = TlsFormatAndCheckFileName(args->repeatFileArray[idx].path, sizeof(args->repeatFileArray[idx].path));
            if (ret != GMERR_OK) {
                return ret;
            }
        }
    }
    return GMERR_OK;
}

Status ImportMultiFolder(GmcStmtT *stmt, ImportArgumentT *args)
{
    Status ret = ImportFolder(args, stmt);
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
    if (args->cmdType == GMIMP_CMD_CACHE || args->cmdType == GMIMP_CMD_BINDATA) {
#else
    if (args->cmdType == GMIMP_CMD_CACHE) {
#endif
        for (uint32_t idx = 0; idx < args->repeatFileNum; idx++) {
            args->file = args->repeatFileArray[idx];
            ret = ImportFolder(args, stmt);
        }
    }
    return ret;
}

void HandleImportSingleFileResult(Status ret, ImportArgumentT *args)
{
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "Import single file from \"%s\" unsucc. ret = %d",
            args->file.path, (int32_t)ret);
    } else {
        if (args->file.isImport) {
            PRINT_INFO(DbPrintfDefault, "Import single file from \"%s\" success.\n", args->file.path);
        } else {
            PRINT_INFO(DbPrintfDefault, "Insert file from \"%s\" max_record_count is 0, this file skip import\n",
                args->file.path);
        }
    }
}

Status ImportFile(GmcStmtT *stmt, ImportOptionT *toolOption)
{
    DB_POINTER2(stmt, toolOption);
    ImportArgumentT *args = &toolOption->args;
    if (args->cmdType >= GMIMP_CMD_BOTTOM) {
        // should not be here
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_INVALID_PARAMETER_VALUE,
            "Unexpected command type: %d. ret = %d, when import file.", (int32_t)args->cmdType,
            (int32_t)GMERR_INVALID_PARAMETER_VALUE);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    Status ret = ImportFormatAndCheckFileName(args);
    if (ret != GMERR_OK) {
        return ret;
    }
    // 用于批量导入vertex表
    GmcBatchT *batch = NULL;
    (void)ImportPrepareBatch(stmt, &batch);
    if (SECUREC_UNLIKELY(batch == NULL)) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INTERNAL_ERROR, "Import batch operation unsucc.");
        return GMERR_INTERNAL_ERROR;
    }
    // 这里把batch保存到stmt上，只是起到了方便传参的作用，下面的流程里不用一直把batch传下去，和结构化没啥关系
    // 然后这里如果调GmcBatchBindStmt接口的话stmt状态是不符合的，就直接这么写了
    stmt->batch = batch;
    batch->bindStmt = stmt;
    if (args->file.type == FILE_FOLDER) {
        ret = ImportMultiFolder(stmt, args);
    } else {
        ret = ImportSingleFile(stmt, args);
        if (ret != GMERR_OK) {
            TOOL_RUN_ERROR(DbPrintfDefault, ret, "Import single file unsucc.");
        }
        if (batch->batchState != CLT_BATCH_STATE_PREPARED) {
            ret = ImportBatchExecute(batch);
        }
        HandleImportSingleFileResult(ret, args);
    }

    GmcBatchUnbindStmt(batch, stmt);
    (void)GmcBatchDestroy(batch);
    if (ret == GMERR_OK) {
        PRINT_SUCCESS(DbPrintfDefault, "Command type: %s, Import file from \"%s\" successfully",
            g_gmdbImpCmdName[args->cmdType], args->file.path);
    }
    return ret;
}

inline static bool ImportValidResPropertyCnt(uint32_t size)
{
    return size > 0 && size <= DM_RES_COL_MAX_COUNT;
}

Status ImportBindExtResPool(GmcStmtT *stmt, ImportOptionT *toolOption)
{
    ImportArgumentT *args = &toolOption->args;
    Status ret = GmcBindExtResPool(stmt, args->resPoolName, args->extResPoolName);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret,
            "Bind external resource pool \"%s\" to resource pool"
            "\"%s\" unsucc. ret = %d",
            args->extResPoolName, args->resPoolName, (int32_t)ret);
    } else {
        const char **cmdName = g_gmdbImpCmdName;
        PRINT_SUCCESS(DbPrintfDefault,
            "Command type: %s, Bind external resource pool \"%s\" to resource pool \"%s\" successfully",
            cmdName[args->cmdType], args->extResPoolName, args->resPoolName);
    }
    return ret;
}

Status ImportDatalog(GmcStmtT *stmt, ImportOptionT *toolOption)
{
    DB_POINTER2(stmt, toolOption);
    ImportArgumentT *args = &toolOption->args;
    DB_POINTER(args);

    if (!ImportIsFileTypeMatchCmdType(args)) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_DATATYPE_MISMATCH,
            "Import file type does not match the commond type. ret = %d", (int32_t)GMERR_DATATYPE_MISMATCH);
        return GMERR_DATATYPE_MISMATCH;
    }

    Status ret = TlsFormatAndCheckFileName(args->file.path, sizeof(args->file.path));
    if (ret != GMERR_OK) {
        return ret;
    }

    const char *filePath = args->file.path;
    const char *namespace = toolOption->namespaceName;
    // 加载datalog时如果未指定-ns，则默认加载到public下
    if (IsStringEmpty(namespace)) {
        namespace = DB_DEFAULT_NAMESPACE;
    }
    if (toolOption->args.isRollbackUpgrade) {
        ret = GmcImportDatalogSo(stmt, filePath, MSG_OP_RPC_ROLLBACK_DATALOG, namespace, false);
    } else {
        bool isUpgrade = toolOption->args.isUpgrade;

        ret = isUpgrade ? GmcImportDatalogSo(stmt, filePath, MSG_OP_RPC_UPGRADE_DATALOG, namespace, false) :
                          GmcImportDatalogSo(
                              stmt, filePath, MSG_OP_RPC_IMPORT_DATALOG, namespace, toolOption->args.isDtlDistribute);
    }
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "Import datalog file unsucc, filePath = %s, ret = %d, namespace = %s.",
            args->file.path, (int32_t)ret, namespace);
    }
    if (ret == GMERR_GET_THIRD_PARTY_FUNCTION_FAILED) {
        const char *lastError = GmcGetLastError();
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "%s ret = %" PRIi32 ".", lastError, (int32_t)ret);
    }
    return ret;
}

#ifdef FEATURE_DATALOG
Status UninstallDatalog(GmcStmtT *stmt, ImportOptionT *toolOption)
{
    DB_POINTER2(stmt, toolOption);
    ImportArgumentT *args = &toolOption->args;

    if (IsStringEmpty(args->file.name)) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_PARAMETER_VALUE,
            "Uninstall datalog file unsucc, file info is NULL. ret = %d", GMERR_INVALID_PARAMETER_VALUE);
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    // 不应该包含路径或后缀名 /xxx/namespaceName.so不合法
    char *filePath = args->file.path;
    size_t length = strlen(filePath);
    for (size_t i = 0; i < length; i++) {
        if (filePath[i] == '/' || filePath[i] == '.') {
            TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_PARAMETER_VALUE,
                "Uninstall datalog file unsucc, namespaceName = %s is a path or a file. ret = %d", filePath,
                GMERR_INVALID_PARAMETER_VALUE);
            return GMERR_INVALID_PARAMETER_VALUE;
        }
    }

    const char *fileName = args->file.name;
    const char *namespace = toolOption->namespaceName;
    // 卸载datalog时如果未指定-ns，则默认卸载public下的so
    if (IsStringEmpty(namespace)) {
        namespace = DB_DEFAULT_NAMESPACE;
    }
    Status ret = GmcUnimportDatalogSo(stmt, fileName, namespace);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "Uninstall datalog file unsucc, fileName = %s, ret = %d, namespace = %s.",
            fileName, (int32_t)ret, namespace);
    }
    return ret;
}
#endif

#ifdef __cplusplus
}
#endif /* __cplusplus */
