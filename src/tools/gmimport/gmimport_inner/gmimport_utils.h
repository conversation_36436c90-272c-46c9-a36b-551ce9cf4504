/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: header file for importing schema from  file or directory
 * Create: 2021-11-27
 */

#ifndef GMIMPORT_UTILS_H
#define GMIMPORT_UTILS_H

#include "gmimport_file.h"
#include "clt_stmt.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#if !defined(WARM_REBOOT) && !defined(FEATURE_RSMEM)
typedef struct {
    uint32_t totalFileNums;
    uint32_t successFileNums;
    uint32_t skipFileNums;  // 仅当max_record_count=0时，统计到skipNum
    uint32_t totalDirNums;
    uint32_t successDirNums;
} ImpFileNumStatT;
#endif

/* *
 * @brief check file size, is valid or invalid
 * @param fileSize  file size
 * @return GMERR_OK or failed
 */
Status ImportFileSizeVerify(uint64_t fileSize);

// parse json file type
Status ImportParseFileType(ImportArgumentT *args);

void ImportSetMaxFileSizeLimit(ImpFileTypeE fileType, uint32_t fileSize);

Status ImportResultDisplay(const ImpFileNumStatT *stat, ImpFileTypeE fileType);

#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
Status ImportBinResultDisplay(
    const ImpFileNumStatT *stat, ImpFileTypeE fileType, uint32_t succeBinFileNums, uint32_t totalBatch);
#endif

Status ImportPrepareJsonInfoForDir(GmcStmtT *stmt, ImportArgumentT *args, ImpFileTypeE fileType);

Status ImportGetCfgFilePath(const char *fileName, char *cfgFilePath, uint32_t cfgFilePathLen, bool *isFound);

Status ImportCheckFileSizeWithLimit(const char *fileName, ImpFileTypeE fileType, int32_t fileSizeLimit);

void ImportFreeJsonStr(GmcStmtT *stmt, char *jsonStr);

char *ImportAllocJsonStrByFilePath(GmcStmtT *stmt, const char *filePath, DbJsonT **jsonObject);

Status ImportSetFullPathStr(ImportArgumentT *args, const char *dirPath, const struct dirent *dirName);

char *ImportReadDataFromFile(DbMemCtxT *memCtx, const char *filePath, size_t *fileSize);

Status ImportBatchInit(GmcConnT *conn, GmcBatchT **batch);

Status ImportReadCfgDataFromFile(GmcStmtT *stmt, ImportArgumentT *args);

Status ImportParseFileContent(GmcStmtT *stmt, ImportArgumentT *args);

#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
Status ImportRedirectNewBuffer(DbMemCtxT *memCtx, const char *src, size_t srcLen, char **out);
#endif

bool ImportIsFileTypeMatchCmdType(const ImportArgumentT *args);

void ImportFreeJsonInfoForFile(GmcStmtT *stmt, ImportArgumentT *args);

Status MultipleTimesImportVertexData(
    GmcStmtT *stmt, ImportArgumentT *args, ImpVertexParasT *paras, ImpVertexBatchParasT *execParams);

#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
Status ImportCheckFileType(ImportArgumentT *args, ImpFileTypeE cmpFileType);

Status ImportSetFileName(ImportArgumentT *args);
#endif

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
