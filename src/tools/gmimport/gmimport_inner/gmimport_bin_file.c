/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 * Description: source file for importing schema from  file or directory
 * Author: gumengmeng
 * Create: 2023-9-27
 */

#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)

#include <dirent.h>
#include <sys/mman.h>
#include "gmimport_file.h"
#include "db_file.h"
#include "adpt_string.h"
#include "db_json.h"
#include "gmc.h"
#include "gmimport_transform.h"
#include "gmimport_utils.h"
#include "clt_batch.h"
#include "clt_check.h"
#include "clt_utils.h"
#include "tool_interactive.h"
#include "gmimport_typedef.h"
#include "gmc_internal.h"
#include "dm_data_record.h"
#include "tool_thread_pool.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

// warm reboot场景多线程执行任务入参结构体
typedef struct ImportThreadArg {
    GmcStmtT *stmt;
    ImportArgumentT *args;
    ImpFileNumStatT *impfileStat;
    ImpFileTypeE fileType;
    DbMemCtxT *memCtx;
} ImportThreadArgT;

/***********************************仅warm reboot场景使用************************************/

#define BATCH_LIMIT_SIZE 2048          // 批量操作batch初始化limitSize为2048
#define BATCH_NUM_WITH_VERTEXBUF 1024  // 批量操作执行记录上限1024条
#define MOVE_NO_TYPE_HEADER_LENTH 2  // 不进行计算可靠性校验时，文件头记录“n”与换行符，需要忽略
#define MOVE_RC_TYPE_HEADER_LENTH \
    6  // 计算可靠性校验码时，文件头还需记录CRC_CODE_LEN, fileDesc->fileContent.len, crcCod, isUpOrDe
#define TRY_COUNT 7200000                      // 等待回调完成尝试次数
#define SLEEP_USEC 200                         // 异步发报文失败等待时长
#define BATCH_SEND_BUFFER_LIMIT (2 * DB_MEBI)  // 非大报文连接，报文数据不能超过2MB

ImpFileNumStatT g_gmdbTotalImpNumStat = {0};
static uint32_t g_expectNum = 0;
static uint32_t g_actualNum = 0;

static uint32_t g_successBinFileNums = 0;

// warm reboot场景批量操作初始化函数，采用GMC_BATCH_ORDER_SEMI模式
static Status ImportBatchInitBin(GmcConnT *conn, GmcBatchT **batch)
{
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_SEMI);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, BATCH_LIMIT_SIZE);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcBatchPrepare(conn, &batchOption, batch);
    return ret;
}

// 调用mmap函数将文件中的数据映射到内存中
static Status ImportMmapFile(const char *fileName, char **addr, uint32_t *fileSize)
{
    char canonicalPath[PATH_MAX] = {0};
    if (realpath(fileName, canonicalPath) == NULL) {
        DB_LOG_DBG_ERROR(GMERR_FILE_OPERATE_FAILED, "Inv file path, %s.", fileName);  // LCOV_EXCL_LINE
        return GMERR_FILE_OPERATE_FAILED;
    }
    int fd;
    if ((fd = open(canonicalPath, READ_ONLY)) < 0) {
        DB_LOG_DBG_ERROR(GMERR_FILE_OPERATE_FAILED, "Open file, file path=%s", canonicalPath);  // LCOV_EXCL_LINE
        return GMERR_FILE_OPERATE_FAILED;
    }

    struct stat sb;
    if ((fstat(fd, &sb)) == -1) {
        DB_LOG_DBG_ERROR(GMERR_FILE_OPERATE_FAILED, "Stat file, file path=%s", canonicalPath);  // LCOV_EXCL_LINE
        (void)close(fd);
        return GMERR_FILE_OPERATE_FAILED;
    }

    char *mapped;
    if ((mapped = (char *)mmap(NULL, (size_t)sb.st_size, 1, 1, fd, (off_t)0)) == (void *)-1) {
        // LCOV_EXCL_START
        DB_LOG_DBG_ERROR(
            GMERR_FILE_OPERATE_FAILED, "mmap file, file path=%s, sys_no=%d", canonicalPath, DbAptGetErrno());
        // LCOV_EXCL_STOP
        (void)close(fd);
        return GMERR_FILE_OPERATE_FAILED;
    }
    *addr = mapped;
    *fileSize = (uint32_t)sb.st_size;
    (void)close(fd);
    return GMERR_OK;
}

// 调用mmap函数释放内存指针
static Status ImportUmmapFile(char *addr, uint32_t fileSize)
{
    if ((munmap((void *)addr, fileSize)) == -1) {
        return GMERR_FILE_OPERATE_FAILED;
    }
    return GMERR_OK;
}

// 检测是否是有效的可靠性校验算法
static inline bool ImportCheckBinFileDigestValid(const char *algorithmCode)
{
    return (*algorithmCode == CRC32_TYPE);
}

// warm reboot场景解析数据文件头部的完整性校验码
static Status ImportParseBinFileHeader(
    DbMemCtxT *memCtx, char **binFileContent, uint32_t *fileContentLen, TlsDigestInfoT *digest)
{
    DB_POINTER4(memCtx, *binFileContent, fileContentLen, digest);
    digest->salt = NULL;
    digest->digest = NULL;
    Status ret = GMERR_OK;
    do {
        char *headerDelim = strchr(*binFileContent, '\n');
        if (headerDelim == NULL) {
            break;
        }
        // 添加'/0'分割文件校验文本和内容
        *headerDelim = '\0';
        *fileContentLen -= (uint32_t)strlen(*binFileContent) + 1;
        if (!ImportCheckBinFileDigestValid(*binFileContent)) {
            break;
        }
        // 加一移除首位校验类型
        char *digestBinInfo = *binFileContent + 1;
        digest->algorithm = DB_DIGEST_ALGORITHM_CRC;
        ret = ImportRedirectNewBuffer(memCtx, digestBinInfo, strlen(digestBinInfo) + 1, &digest->digest);
        if (ret != GMERR_OK) {
            break;
        }
        // 加一移除首位换行符
        *binFileContent = headerDelim + 1;
        return GMERR_OK;
    } while (0);
    TOOL_RUN_ERROR(DbPrintfDefault, GMERR_DATA_EXCEPTION, "unexpected file content when parse digest info.");
    return GMERR_DATA_EXCEPTION;
}

// warm reboot场景解析数据:RC_TYPE(n/c)|RC_CODE(如果没有校验的话没有校验码)|LABEL_TYPE(k/v)|dataBuf
static Status ImportParseBinFileContent(GmcStmtT *stmt, ImportArgumentT *args)
{
    DB_POINTER2(stmt, args);
    char *fileContent = args->importBuf;
    if (*fileContent == NO_TYPE) {
        // 忽略n与换行符
        args->jsonStr = fileContent + MOVE_NO_TYPE_HEADER_LENTH;
        args->importSize -= MOVE_NO_TYPE_HEADER_LENTH;
        args->digest.algorithm = DB_DIGEST_ALGORITHM_BUTT;
        return GMERR_OK;
    }
    char *fileContentPtr = fileContent;
    Status ret = ImportParseBinFileHeader(stmt->memCtx, &fileContentPtr, &args->importSize, &args->digest);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "unexpected file content %s.", args->file.path);  // LCOV_EXCL_LINE
        return ret;
    }
    args->jsonStr = fileContentPtr;
    return GMERR_OK;
}

static void NamespaceAsyncCb(void *userData, Status status, const char *errMsg)
{
    if (status == GMERR_OK) {
        (void)DbSemPost((DbSemT *)userData);
    }
}

static Status ImportVertexBinDataPrepare(GmcStmtT *stmt, ImportArgumentT *args, GmcBatchT *batch)
{
    DbSemT sem = {};
    (void)DbSemInit(&sem, 0, 0);
    Status ret = GmcUseNamespaceAsync(stmt, args->fileNameInfos.nameSpace, NamespaceAsyncCb, &sem);
    if (ret != GMERR_OK) {
        return ret;
    }
    (void)DbSemTimedWait(&sem, USECONDS_IN_SECOND);
    (void)DbSemDestroy(&sem);
    stmt->isTools = true;
    ret = GmcPrepareStmtByLabelName(stmt, args->fileNameInfos.labelName, GMC_OPERATION_REPLACE);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Prepare label name.");  // LCOV_EXCL_LINE
        return ret;
    }
    stmt->isTools = false;
    return GmcBatchBindStmt(batch, stmt);
}

static Status AddImportBufForSchemaChange(GmcStmtT *stmt, TextT *buf, DmVertexT *vertex)
{
    if (vertex == NULL) {
        return CltBatchAddDmlWithVertexBuf(stmt->batch, stmt, buf);
    }
    Status ret = DmDeSerialize2ExistsVertex((void *)buf->str, buf->len, vertex, false);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmVertexGetSeriBufLength(vertex, &buf->len);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DmSerializeVertex2InvokerBuf(vertex, buf->len, (void *)buf->str);
    if (ret != GMERR_OK) {
        return ret;
    }
    return CltBatchAddDmlWithVertexBuf(stmt->batch, stmt, buf);
}

static void BatchCb(void *userData, GmcBatchRetT *batchRet, Status status, const char *errMsg)
{
    (void)DbAtomicInc(userData);
    if (status == GMERR_OK) {
        (void)DbAtomicInc(&g_successBinFileNums);
    }
}

static inline Status SendBatchExecuteMsgAsync(GmcBatchT *batch, ImportArgumentT *args)
{
    Status ret = GMERR_OK;
    while ((ret = GmcBatchExecuteAsync(batch, BatchCb, &g_actualNum)) != GMERR_OK) {
        if (ret != GMERR_REQUEST_TIME_OUT && ret != GMERR_CONNECTION_SEND_BUFFER_FULL) {
            return ret;
        }
        DbUsleep(SLEEP_USEC);
    }
    (void)DbAtomicInc(&g_expectNum);
    return GMERR_OK;
}

static Status VertexBinDataPrepareInit(GmcStmtT *stmt, ImportArgumentT *args, GmcBatchT **batch)
{
    Status ret = ImportBatchInitBin(stmt->conn, batch);  // GMC_BATCH_ORDER_STRICT 严格顺序执行模式
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Batch bind stmt.");  // LCOV_EXCL_LINE
        return ret;
    }
    return ImportVertexBinDataPrepare(stmt, args, *batch);
}

static inline bool CheckBatchLimit(GmcStmtT *stmt, Status ret, int index, char *beginImportBuf, char *tail)
{
    return index < BATCH_NUM_WITH_VERTEXBUF && beginImportBuf < tail && ret == GMERR_OK &&
           FixBufGetTotalLength(&stmt->batch->batchSendBuf) + *(uint32_t *)(void *)beginImportBuf <
               BATCH_SEND_BUFFER_LIMIT;
}

// 导入vertex数据文件入口
static Status ImportVertexBinData(GmcStmtT *stmt, ImportArgumentT *args)
{
    GmcBatchT *batch = NULL;
    Status ret = VertexBinDataPrepareInit(stmt, args, &batch);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, " Prepare vertex bin data.");  // LCOV_EXCL_LINE
        return ret;
    }

    CltOperVertexT *op = (CltOperVertexT *)CltGetOperationContext(stmt);
    uint32_t actualPropNum = op->vertex->record->recordDesc->propeNum;
    uint32_t offset = sizeof(uint32_t) + sizeof(uint8_t) + 1;
    char *beginImportBuf = (char *)(args->jsonStr + offset);
    char *tail = args->jsonStr + args->importSize;
    TextT vertexBuf;
    DmVertexT *vertex = NULL;
    if (args->isUpOrDe || actualPropNum < args->propNum) {
        // 返回值在下方while条件中判断，减少圈复杂度
        ret = DmCreateEmptyVertexWithMemCtx(stmt->memCtx, op->cltCataLabel->vertexLabel, &vertex);
    }
    while (beginImportBuf < tail && ret == GMERR_OK) {
        // 返回值在for条件中判断，减少圈复杂度
        ret = CltCheckStmtAndBatch(stmt->batch, stmt);
        stmt->batch->isDataService = stmt->isDataService;
        for (int i = 0; (ret == GMERR_OK) && CheckBatchLimit(stmt, ret, i, beginImportBuf, tail); i++) {
            vertexBuf.len = *(uint32_t *)(void *)beginImportBuf;
            beginImportBuf += sizeof(uint32_t);
            vertexBuf.str = (void *)beginImportBuf;
            beginImportBuf += vertexBuf.len;
            ret = AddImportBufForSchemaChange(stmt, &vertexBuf, vertex);
        }
        if (ret != GMERR_OK) {
            break;
        }
        ret = SendBatchExecuteMsgAsync(stmt->batch, args);
    }
    DmDestroyVertex(vertex);
    return ret;
}

// crc算法校验数据可靠性
static bool ImportValidBinFileCrc(ImportArgumentT *args)
{
    DB_POINTER(args);
    if (args->importSize < (uint32_t)MOVE_RC_TYPE_HEADER_LENTH) {
        return false;
    }
    char crcHexCode[CRC_CODE_LEN + 1] = {0};
    uint32_t crcCodeGen = DbCrcOrHash(
        (char *)(args->jsonStr + MOVE_RC_TYPE_HEADER_LENTH), (uint32_t)(args->importSize - MOVE_RC_TYPE_HEADER_LENTH));
    errno_t err = snprintf_s(crcHexCode, CRC_CODE_LEN + 1, CRC_CODE_LEN, "%08x", crcCodeGen);
    if (err <= 0) {
        return false;
    }
    if (DbStrCmp(args->digest.digest + CODE_LEN_SIZE + DATA_FILE_LEN_SIZE, crcHexCode, false) != 0) {
        return false;
    }
    return true;
}

// warm reboot导入场景校验完整性校验码，验证导入数据是否可靠
static Status ImportVerifyBinFile(GmcStmtT *stmt, ImportArgumentT *args)
{
    DB_POINTER2(stmt, args);
    // 大部分场景不用，则直接返回
    if (args->digest.algorithm == DB_DIGEST_ALGORITHM_BUTT) {
        return GMERR_OK;
    }
    if (args->digest.algorithm == DB_DIGEST_ALGORITHM_CRC && ImportValidBinFileCrc(args)) {
        return GMERR_OK;
    }
    return GMERR_DATA_CORRUPTED;
}

// warm reboot场景导入解析校验文件内容
Status ImportPrepareBinForFileInner(GmcStmtT *stmt, ImportArgumentT *args, char *mapped, uint32_t fileSize)
{
    Status ret = ImportParseBinFileContent(stmt, args);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Parse binFile content.");  // LCOV_EXCL_LINE
        return ret;
    }
    ret = ImportVerifyBinFile(stmt, args);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Verify binFile.");  // LCOV_EXCL_LINE
        return ret;
    }
    return ImportUmmapFile(mapped, fileSize);
}

// warm reboot场景映射.bin_data文件数据
Status ImportPrepareBinForFile(GmcStmtT *stmt, ImportArgumentT *args)
{
    args->file.isImport = true;  // 默认导入
    if (!DbFileExist(args->file.path)) {
        DB_LOG_DBG_ERROR((int32_t)GMERR_FILE_OPERATE_FAILED, "No such file or directory: %s.", args->file.path);
        return GMERR_FILE_OPERATE_FAILED;
    }
    if (!ImportIsFileTypeMatchCmdType(args)) {
        PRINT_ERROR(DbPrintfDefault, "Import file type does not match the commond type. ret = %d",
            (int32_t)GMERR_DATATYPE_MISMATCH);
        return GMERR_DATATYPE_MISMATCH;
    }
    Status ret = ImportCheckFileSizeWithLimit(args->file.path, args->file.type, args->file.sizeLimit);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Check file size limit.");  // LCOV_EXCL_LINE
        return ret;
    }
    uint32_t fileSize = 0;
    char *mapped = NULL;
    ret = ImportMmapFile(args->file.path, &mapped, &fileSize);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Mmap file.");  // LCOV_EXCL_LINE
        return ret;
    }
    if (mapped == NULL) {
        DB_LOG_ERROR(ret, "Mmaped unsucc.");  // LCOV_EXCL_LINE
        return GMERR_FILE_OPERATE_FAILED;
    }
    args->importSize = fileSize;
    args->importBuf = DbDynMemCtxAlloc(stmt->memCtx, args->importSize);
    if (args->importBuf == NULL) {
        DB_LOG_ERROR(ret, "Alloc import buff.");  // LCOV_EXCL_LINE
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t err = memcpy_s(args->importBuf, args->importSize, mapped, args->importSize);
    if (err != EOK) {
        DB_LOG_ERROR(ret, "Memcpy mapped buff.");  // LCOV_EXCL_LINE
        DbDynMemCtxFree(stmt->memCtx, args->importBuf);
        args->importBuf = NULL;
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return ImportPrepareBinForFileInner(stmt, args, mapped, fileSize);
}

static Status ImportParseKvBinData(
    GmcStmtT *stmt, char *kvBuffer, uint64_t fileSize, uint64_t *offset, GmcKvTupleT *kvInfo)
{
    Status ret = ImportParseKvInfo(kvBuffer, fileSize, offset, kvInfo);
    if (ret != GMERR_OK) {                           // 解析错误, 导入结束.
        DB_LOG_ERROR(ret, "Import parse kv info.");  // LCOV_EXCL_LINE
        return ret;
    }
    return GmcKvInputToStmt(stmt, kvInfo->key, kvInfo->keyLen, kvInfo->value, kvInfo->valueLen);
}

// 导入kv数据文件入口内部实现，避免超大函数拆分此函数
static Status ImportKvBinDataInner(
    GmcStmtT *stmt, char *kvBuffer, ImportArgumentT *args, uint64_t fileSize, uint32_t *successNum)
{
    uint64_t offset = 0;
    uint32_t batchCount = 0;
    GmcBatchT *batch = NULL;
    Status ret = ImportBatchInitBin(stmt->conn, &batch);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Import batch init.");  // LCOV_EXCL_LINE
        return ret;
    }
    GmcKvTupleT kvInfo = {0};
    while (offset < fileSize) {
        for (batchCount = 0; batchCount < CLT_BATCH_CMD_MAX_SIZE && offset < fileSize && ret == GMERR_OK;
             batchCount++) {
            ret = ImportParseKvBinData(stmt, kvBuffer, fileSize, &offset, &kvInfo);
            if (ret != GMERR_OK) {  // 批量接口 不支持 较大的对象. 遇到大对象, 单独处理.
                break;
            }
            ret = GmcBatchAddKvDML(batch, stmt, GMC_OPERATION_INSERT);
        }
        if (ret != GMERR_OK) {
            break;
        }
        ret = SendBatchExecuteMsgAsync(batch, args);
    }
    return ret;
}

// 导入kv数据文件入口
static Status ImportKvBinData(GmcStmtT *stmt, ImportArgumentT *args)
{
    DB_POINTER2(stmt, args);
    DbSemT sem = {};
    (void)DbSemInit(&sem, 0, 0);
    Status ret = GmcUseNamespaceAsync(stmt, args->fileNameInfos.nameSpace, NamespaceAsyncCb, &sem);
    if (ret != GMERR_OK) {
        (void)DbSemDestroy(&sem);
        DB_LOG_ERROR(ret, "Use nameSpace async.");  // LCOV_EXCL_LINE
        return ret;
    }
    (void)DbSemTimedWait(&sem, USECONDS_IN_SECOND);
    (void)DbSemDestroy(&sem);
    ret = GmcKvPrepareStmtByLabelName(stmt, args->fileNameInfos.labelName);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Prepare stmt.");  // LCOV_EXCL_LINE
        return ret;
    }
    uint32_t successNum = 0;
    uint32_t offset = sizeof(uint32_t) + sizeof(uint8_t) + 1;
    char *beginImportBuf = (char *)(args->jsonStr + offset);
    return ImportKvBinDataInner(stmt, beginImportBuf, args, (uint64_t)(args->importSize) - offset, &successNum);
}

// 导入单个数据文件入口
static Status ImportSingleBinFile(GmcStmtT *stmt, ImportArgumentT *args)
{
    DB_POINTER2(stmt, args);
    Status ret = ImportPrepareBinForFile(stmt, args);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "prepare bin file unsucc, ret = %d", (int32_t)ret);
        // LCOV_EXCL_STOP
        return ret;
    }
    if (*(args->jsonStr) == 'v') {
        args->propNum = *(uint16_t *)(void *)(args->jsonStr + 1);
        args->isUpOrDe = *(uint8_t *)(void *)(args->jsonStr + sizeof(uint32_t) + 1);
        ret = ImportVertexBinData(stmt, args);
    } else {
        ret = ImportKvBinData(stmt, args);
    }
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "insert data unsucc, ret = %d", (int32_t)ret);
        // LCOV_EXCL_STOP
    } else {
        (void)DbAtomicInc(&g_gmdbTotalImpNumStat.successFileNums);
        PRINT_INFO(
            DbPrintfDefault, "Insert data succeed. successNum: %" PRIu32 "\n", g_gmdbTotalImpNumStat.successFileNums);
    }
    DbDynMemCtxFree(stmt->memCtx, args->importBuf);
    return ret;
}

static void FreeThreadArgs(
    DbMemCtxT *memCtx, ImportThreadArgT *threadArg, ImportArgumentT *args, char *nameSpace, char *labelName)
{
    DbDynMemCtxFree(memCtx, nameSpace);
    DbDynMemCtxFree(memCtx, labelName);
    DbDynMemCtxFree(memCtx, args);
    DbDynMemCtxFree(memCtx, threadArg);
}

// 多线程导入线程池执行函数入口
static void ImportTaskScanBinFileFromDir(void *arg)
{
    ImportThreadArgT *threadArg = (ImportThreadArgT *)arg;
    threadArg->impfileStat->totalFileNums++;
    threadArg->args->file.isImport = true;  // 默认文件导入
    Status ret = ImportSingleBinFile(threadArg->stmt, threadArg->args);
    if (ret == GMERR_OK) {
        if (!threadArg->args->file.isImport) {
            threadArg->impfileStat->skipFileNums++;
            (void)DbAtomicInc(&g_gmdbTotalImpNumStat.skipFileNums);
            PRINT_INFO(DbPrintfDefault, "Insert file from \"%s\" max_record_count is 0, this file skip import\n",
                threadArg->args->file.path);
            threadArg->args->file.isImport = true;  // 重置为true，对于批量文件，标记下一个文件默认导入
        }
    } else {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "Import file from \"%s\" unsucc. ret = %d",
            threadArg->args->file.path, (int32_t)ret);
        // LCOV_EXCL_STOP
    }
    FreeThreadArgs(threadArg->memCtx, threadArg, threadArg->args, threadArg->args->fileNameInfos.nameSpace,
        threadArg->args->fileNameInfos.labelName);
    return;
}

static Status CreateDynamicTable(GmcStmtT *stmt, ImportArgumentT *args)
{
    Status ret = GMERR_OK;
    ret = ImportSetFileName(args);
    if (ret != GMERR_OK) {
        return ret;
    }
    char *nspTableName = NULL;
    char *tableMutName = NULL;
    char *tableNum = NULL;
    args->fileNameInfos.oriLabelName = strtok_r(args->file.name, " ", &nspTableName);
    args->fileNameInfos.nameSpace = strtok_r(nspTableName, " ", &tableMutName);
    args->fileNameInfos.labelName = strtok_r(tableMutName, " ", &tableNum);
    ret = GmcUseNamespace(stmt, args->fileNameInfos.nameSpace);
    if (ret != GMERR_OK) {
        return ret;
    }
    if ((strcmp(args->fileNameInfos.oriLabelName, args->fileNameInfos.labelName) != 0) &&
        (strncmp(tableNum, "0", strlen("0")) == 0)) {
        ret = GmcDuplicateVertexLabelWithName(
            stmt, args->fileNameInfos.oriLabelName, args->fileNameInfos.labelName, NULL);
    }
    return ret;
}

static Status AllocAndMemcpyNspForImport(DbMemCtxT *memCtx, char *oriNsp, char **nameSpace)
{
    char *destNsp = DbDynMemCtxAlloc(memCtx, strlen(oriNsp) + 1);
    if (destNsp == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Alloc nameSpace.");  // LCOV_EXCL_LINE
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    errno_t err = memcpy_s(destNsp, strlen(oriNsp) + 1, oriNsp, strlen(oriNsp) + 1);
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Memcpy namespace, namespace %s.", oriNsp);  // LCOV_EXCL_LINE
        DbDynMemCtxFree(memCtx, destNsp);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    *nameSpace = destNsp;
    return GMERR_OK;
}

static Status AllocAndMemcpyLabelForImport(DbMemCtxT *memCtx, char *oriLabel, char **labelName)
{
    char *destLabel = DbDynMemCtxAlloc(memCtx, strlen(oriLabel) + 1);
    if (destLabel == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Alloc labelName.");  // LCOV_EXCL_LINE
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    errno_t err = memcpy_s(destLabel, strlen(oriLabel) + 1, oriLabel, strlen(oriLabel) + 1);
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Memcpy namespace, namespace is %s.", oriLabel);  // LCOV_EXCL_LINE
        DbDynMemCtxFree(memCtx, destLabel);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    *labelName = destLabel;
    return GMERR_OK;
}

static Status ImportScanBinFileFromDirInner(
    GmcStmtT *stmt, ImportArgumentT *args, ImpFileTypeE fileType, ImpFileNumStatT *impfileStat, TlsThreadPoolT *tp)
{
    Status ret = CreateDynamicTable(stmt, args);
    if (ret != GMERR_OK) {
        return ret;
    }
    /* file */
    DbMemCtxT *memCtx = TlsGetThreadPoolSharedDynMem(tp);
    ImportThreadArgT *threadArgs = DbDynMemCtxAlloc(memCtx, sizeof(ImportThreadArgT));
    if (threadArgs == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Alloc threadArgs.");  // LCOV_EXCL_LINE
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    ImportArgumentT *importArgu = DbDynMemCtxAlloc(memCtx, sizeof(ImportArgumentT));
    if (importArgu == NULL) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Alloc importArgu.");  // LCOV_EXCL_LINE
        FreeThreadArgs(memCtx, threadArgs, NULL, NULL, NULL);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    char *nameSpace = NULL;
    ret = AllocAndMemcpyNspForImport(memCtx, args->fileNameInfos.nameSpace, &nameSpace);
    if (ret != GMERR_OK) {
        FreeThreadArgs(memCtx, threadArgs, importArgu, NULL, NULL);
        return ret;
    }
    char *labelName = NULL;
    ret = AllocAndMemcpyLabelForImport(memCtx, args->fileNameInfos.labelName, &labelName);
    if (ret != GMERR_OK) {
        FreeThreadArgs(memCtx, threadArgs, importArgu, nameSpace, NULL);
        return ret;
    }
    *importArgu = *args;
    threadArgs->stmt = stmt;
    threadArgs->args = importArgu;
    threadArgs->args->fileNameInfos.nameSpace = nameSpace;
    threadArgs->args->fileNameInfos.labelName = labelName;
    threadArgs->impfileStat = impfileStat;
    threadArgs->fileType = fileType;
    threadArgs->memCtx = memCtx;
    (void)DbAtomicInc(&g_gmdbTotalImpNumStat.totalFileNums);
    ret = TlsThreadPoolTaskPost(tp, ImportTaskScanBinFileFromDir, threadArgs);
    if (ret != GMERR_OK) {
        FreeThreadArgs(stmt->memCtx, threadArgs, importArgu, nameSpace, labelName);
    }
    return ret;
}

static inline Status AsyncExecEndWait(void)
{
    for (uint32_t i = 0; g_expectNum != g_actualNum; i++) {
        if (i > TRY_COUNT) {
            DB_LOG_ERROR(GMERR_INTERNAL_ERROR, "Async replace exception.");  // LCOV_EXCL_LINE
            return GMERR_INTERNAL_ERROR;
        }
        DbUsleep(USECONDS_IN_MSECOND);
    }
    return GMERR_OK;
}

static inline void ScanBinFileReleaseExecData(DIR *dir, TlsThreadPoolT *tp)
{
    if (dir != NULL) {
        (void)closedir(dir);
    }
    if (tp != NULL) {
        (void)TlsThreadPoolWait(tp);
        TlsThreadPoolDestroy(tp);
    }
}

// warm reboot场景多文件导入函数入口,导入的文件格式由导出决定，数据量大的文件可能被拆分成了多个
Status ImportScanBinFileFromDir(GmcStmtT *stmt, ImportArgumentT *args, ImpFileTypeE fileType, uint32_t recurDepth)
{
    DB_POINTER2(stmt, args);
    ImpFileNumStatT impfileStat = {0};
    DIR *dir = NULL;
    char dirPath[DB_MAX_PATH] = {0};
    Status ret = ImportPreparedScanFile(args, &dir, dirPath, DB_MAX_PATH);
    if (ret != GMERR_OK || dir == NULL) {
        (void)closedir(dir);
        return ret;
    }
    TlsThreadPoolT *tp = NULL;
    ret = TlsThreadPoolInit(stmt->conn->memCtx, args->threadNum, args->domainName, GMC_CONN_TYPE_ASYNC, &tp);
    if (ret != GMERR_OK) {
        ScanBinFileReleaseExecData(dir, tp);
        return ret;
    }
    for (struct dirent *direntName = readdir(dir); direntName != NULL; direntName = readdir(dir)) {
        if (DbStrCmp(direntName->d_name, ".", true) == 0 || DbStrCmp(direntName->d_name, "..", true) == 0 ||
            ImportSetFullPathStr(args, dirPath, direntName) != GMERR_OK) {
            continue;
        }
        /* file */
        if (DbFileExist(args->file.path) && fileType < FILE_FOLDER && ImportCheckFileType(args, fileType) == GMERR_OK) {
            ret = ImportScanBinFileFromDirInner(stmt, args, fileType, &impfileStat, tp);
            if (ret != GMERR_OK) {
                ScanBinFileReleaseExecData(dir, tp);
                return ret;
            }
            /* folder */
        } else if (DbDirExist(args->file.path) && fileType == FILE_FOLDER) {
            ImportScanFolderFromDirInner(stmt, args, &impfileStat, recurDepth + 1);
        }
    }
    // 等待发完报文
    ret = TlsThreadPoolWait(tp);
    if (ret != GMERR_OK) {
        ScanBinFileReleaseExecData(dir, tp);
        return ret;
    }
    // 等待异步回调完成
    ret = AsyncExecEndWait();
    if (ret != GMERR_OK) {
        ScanBinFileReleaseExecData(dir, tp);
        return ret;
    }

    TlsThreadPoolDestroy(tp);

    (void)closedir(dir);
    errno_t err = strcpy_s(args->file.path, sizeof(args->file.path), dirPath);
    if (err != EOK) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INTERNAL_ERROR, "strcpy %s unsucc. err = %d", dirPath, (int32_t)err);
        return GMERR_INTERNAL_ERROR;
    }
    return ImportBinResultDisplay(&impfileStat, fileType, g_successBinFileNums, g_expectNum);
}

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
