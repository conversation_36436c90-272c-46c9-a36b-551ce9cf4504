/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2021-2021. All rights reserved.
 * Description: source file for commands
 * Create: 2021-11-27
 */

#include "gmimport_utils.h"
#include "db_json.h"
#include "gmc_batch.h"
#include "db_file.h"
#include "adpt_string.h"
#include "tool_utils.h"
#include "tool_internal_error.h"
#include "gmc.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define MAX_MEM_INFO_SIZE 128
#define MAX_MEM_UNIT_LEN 32

typedef struct {
    char name[DB_MAX_NAME_LEN];
    char unit[MAX_MEM_UNIT_LEN];
    uint64_t value;
    bool isSet;
} MemInfoT;

/* ImpFileTypeE 对应 */
// 用途：存储gmimport工具导出的文件前缀
// 是否并发初始化：否
// 是否并发读写：否
// 并发方案：无
const char *g_gmdbImpFileSuffix[] = {
    "gmjson",      // FILE_VSCHEMA_OLD 兼容原版文件类型
    "vertexjson",  // FILE_VSCHEMA
    "edgejson",    // FILE_ESCHEMA
    "gmconfig",    // FILE_CONFIG
    "vertexdata",  // FILE_VDATArespool
    "gmdata",      // FILE_GMDATA
    "edgedata",    // FILE_EDATA
    "gmkv",        // FILE_KVDATA
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
    "bin_data",  // FILE_BINDATA
#endif
    "gmrespool",       // FILE_GMRESP
    "gmnsp",           // FILE_NSP
    "gmresfieldpool",  // FILE_GMFILEDPOOL
    "so",              // FILE_DATALOG
    "gmbackup",        // FILE_BACKUP
#ifdef TS_MULTI_INST
    "gmsql",
#endif
#ifdef FEATURE_RSMEM
    "v1bin",  // FILE_V1_BIN
#endif
    "gmfolder"  // FILE_FOLDER
};
// 用途：存储gmimport工具导入文件的大小限制
// 是否并发初始化：否
// 是否并发读写：否
// 并发方案：无
static uint64_t g_importMaxFileSizeLimit = MAX_DEFAULT_FILE_SIZE;

Status ImportCheckFileType(ImportArgumentT *args, ImpFileTypeE cmpFileType)
{
    Status ret = ImportParseFileType((ImportArgumentT *)args);
    if (ret != GMERR_OK) {
        return ret;
    }
    if (cmpFileType != args->file.type) {
        DB_LOG_ERROR((int32_t)GMERR_FILE_OPERATE_FAILED, "Unexpected File suffix.");  // LCOV_EXCL_LINE
        return GMERR_FILE_OPERATE_FAILED;
    }
    return GMERR_OK;
}

Status ImportSetFullPathStr(ImportArgumentT *args, const char *dirPath, const struct dirent *dirName)
{
    int32_t ret;
    if (strlen(dirPath) == 0) {
        ret = snprintf_s(args->file.path, DB_MAX_PATH, DB_MAX_PATH - 1, "%s", dirName->d_name);
    } else {
        ret = snprintf_s(args->file.path, DB_MAX_PATH, DB_MAX_PATH - 1, "%s/%s", dirPath, dirName->d_name);
    }
    return ret < 0 ? GMERR_MEMORY_OPERATE_FAILED : GMERR_OK;
}

/* 检查文件大小是否超出限制 */
Status ImportCheckFileSizeWithLimit(const char *fileName, ImpFileTypeE fileType, int32_t fileSizeLimit)
{
    DB_POINTER(fileName);
    size_t fileSize;
    Status ret = DbFileSize(fileName, &fileSize);
    if (ret != GMERR_OK) {
        DB_LOG_DBG_ERROR((int32_t)ret, "Get file Size of %s", fileName);  // LCOV_EXCL_LINE
        return ret;
    }
    // 设置导入文件大小的上限
    ImportSetMaxFileSizeLimit(fileType, (uint32_t)fileSizeLimit);
    ret = (fileSize > 0 && fileSize < g_importMaxFileSizeLimit) ? GMERR_OK : GMERR_FILE_OPERATE_FAILED;
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "%s is file size unexpected. file size = %zu", fileName, fileSize);
        // LCOV_EXCL_STOP
        return ret;
    }
    // import data文件需要判断系统内存
    if (fileType == FILE_VDATA || fileType == FILE_GMDATA || fileType == FILE_KVDATA) {
        return ImportFileSizeVerify(fileSize);
    }
    return GMERR_OK;
}

void ImportFreeJsonStr(GmcStmtT *stmt, char *jsonStr)
{
    GmcStmtT *stmtPtr = (GmcStmtT *)stmt;
    if (jsonStr != NULL) {
        DbDynMemCtxFree(stmtPtr->memCtx, jsonStr);  // 正常释放内存，后续不会再用到
    }
}

/*
  1.得到标准json格式字符串，若文件内容非标准json，返回NULL，同时返回对应的DbJsonObject
  2.调用成功后需要调用ImportFreeJsonStr释放内存资源
  3.DbJsonObject不使用时需要单独释放
*/
char *ImportAllocJsonStrByFilePath(GmcStmtT *stmt, const char *filePath, DbJsonT **jsonObject)
{
    char *jsonStr;
    GmcStmtT *stmtPtr = (GmcStmtT *)stmt;
    // load label json file
    jsonStr = ImportReadDataFromFile(stmtPtr->memCtx, filePath, NULL);
    if (jsonStr == NULL) {
        // LCOV_EXCL_START
        DB_LOG_DBG_ERROR(GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE, "Read data from %s.", filePath);
        // LCOV_EXCL_STOP
        return NULL;
    }
    // 校验json格式
    *jsonObject = DbLoadJsonClt(jsonStr, DB_JSON_REJECT_DUPLICATES);
    if (!(DbJsonIsObject(*jsonObject) || DbJsonIsArray(*jsonObject))) {
        DbJsonDelete(*jsonObject);
        *jsonObject = NULL;
        ImportFreeJsonStr(stmt, jsonStr);
        // LCOV_EXCL_START
        DB_LOG_DBG_ERROR((int32_t)GMERR_INVALID_JSON_CONTENT, "%s is not a standard json file.", filePath);
        // LCOV_EXCL_STOP
        return NULL;
    }
    return jsonStr;
}

/* 获取schema文件所在路径下同名的gmconfig文件 */
Status ImportGetCfgFilePath(const char *fileName, char *cfgFilePath, uint32_t cfgFilePathLen, bool *isFound)
{
    DB_POINTER2(fileName, cfgFilePath);

    errno_t err = strcpy_s(cfgFilePath, cfgFilePathLen, fileName);
    if (err != EOK) {
        return GMERR_FIELD_OVERFLOW;
    }

    char *suffix = DbGetFileNameInfo(cfgFilePath, cfgFilePathLen, DB_FILENAME_SUFFIX);
    if (suffix == NULL) {
        return GMERR_FILE_OPERATE_FAILED;
    }

    err = strcpy_s(cfgFilePath, cfgFilePathLen, fileName);
    if (err != EOK) {
        return GMERR_FIELD_OVERFLOW;
    }

    uint32_t suffixOffset = (uint32_t)(suffix - cfgFilePath);
    err = strcpy_s(suffix, cfgFilePathLen - suffixOffset, g_gmdbImpFileSuffix[FILE_CONFIG]);
    if (err != EOK) {
        return GMERR_FIELD_OVERFLOW;
    }

    *isFound = DbFileExist(cfgFilePath);
    return GMERR_OK;
}

bool ImportIsFileTypeMatchCmdType(const ImportArgumentT *args)
{
    if (args->cmdType == GMIMP_CMD_CACHE || args->file.type == FILE_FOLDER) {
        return true;
    } else if (args->cmdType == GMIMP_CMD_NSP) {
        return args->file.type == FILE_NSP;
    } else if (args->cmdType == GMIMP_CMD_VSCHEMA) {
        return (args->file.type == FILE_VSCHEMA_OLD || args->file.type == FILE_VSCHEMA);
    } else if (args->cmdType == GMIMP_CMD_IMP_SCHEMA) {
        return (
            args->file.type == FILE_VSCHEMA_OLD || args->file.type == FILE_VSCHEMA || args->file.type == FILE_CONFIG);
    } else if (args->cmdType == GMIMP_CMD_VDATA) {
        return (args->file.type == FILE_GMDATA || args->file.type == FILE_VDATA);
    } else if (args->cmdType == GMIMP_CMD_ESCHEMA) {
        return args->file.type == FILE_ESCHEMA;
    } else if (args->cmdType == GMIMP_CMD_EDATA) {
        return args->file.type == FILE_EDATA;
    } else if (args->cmdType == GMIMP_CMD_RESPOOL) {
        return args->file.type == FILE_GMRESP;
    } else if (args->cmdType == GMIMP_CMD_KVTABLE) {
        return (args->file.type == FILE_CONFIG || args->file.type == FILE_VSCHEMA_OLD);
    } else if (args->cmdType == GMIMP_CMD_KVDATA) {
        return args->file.type == FILE_KVDATA;
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
    } else if (args->cmdType == GMIMP_CMD_BINDATA) {
        return args->file.type == FILE_BINDATA;
#endif
    } else if (args->cmdType == GMIMP_CMD_DATALOG) {
        return args->file.type == FILE_DATALOG;
    }
    return false;
}

Status ImportPrepareJsonInfoForDir(GmcStmtT *stmt, ImportArgumentT *args, ImpFileTypeE fileType)
{
    Status ret = ImportCheckFileType(args, fileType);
    if (ret != GMERR_OK) {
        return ret;
    }
    return ImportSetFileName(args);
}

Status ImportSetFileName(ImportArgumentT *args)
{
    char tmpBuffer[DB_MAX_PATH] = {0};
    errno_t err = strcpy_s(tmpBuffer, sizeof(tmpBuffer), args->file.path);
    if (err != EOK) {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_FIELD_OVERFLOW, "Copy file path from %s unsucc. ret = %d",
            args->file.path, (int32_t)GMERR_FIELD_OVERFLOW);
        // LCOV_EXCL_STOP
        return GMERR_FIELD_OVERFLOW;
    }
    char *prefix = DbGetFileNameInfo(tmpBuffer, DB_MAX_PATH, DB_FILENAME_PREFIX);
    if (prefix == NULL) {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_FILE_OPERATE_FAILED, "Get file prefix from %s unsucc. ret = %d",
            args->file.path, (int32_t)GMERR_FILE_OPERATE_FAILED);
        // LCOV_EXCL_STOP
        return GMERR_FILE_OPERATE_FAILED;
    }
    err = strcpy_s(args->file.name, sizeof(args->file.name), prefix);
    if (err != EOK) {
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_FIELD_OVERFLOW, "Copy file name \"%s\" unsucc. ret = %d", prefix,
            (int32_t)GMERR_FIELD_OVERFLOW);
        return GMERR_FIELD_OVERFLOW;
    }
    return GMERR_OK;
}

Status ImportResultDisplay(const ImpFileNumStatT *stat, ImpFileTypeE fileType)
{
    if (stat->totalFileNums == 0 && stat->totalDirNums == 0) {
        return GMERR_OK;
    }
    if (stat->totalFileNums != stat->successFileNums || stat->totalDirNums != stat->successDirNums) {
        // LCOV_EXCL_START
        PRINT_ERROR(DbPrintfDefault,
            "Total %" PRIu32 " [%s] files were found, %" PRIu32 " files OK,%" PRIu32 " files skip, %" PRIu32
            " files failed. %" PRIu32 " folder were found,"
            " %" PRIu32 " folder OK, %" PRIu32 " folder failed.\n",
            stat->totalFileNums, g_gmdbImpFileSuffix[fileType], stat->successFileNums, stat->skipFileNums,
            ((stat->totalFileNums) - (stat->successFileNums) - (stat->skipFileNums)), stat->totalDirNums,
            stat->successDirNums, stat->totalDirNums - stat->successDirNums);
        // LCOV_EXCL_STOP
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}

#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
Status ImportBinResultDisplay(
    const ImpFileNumStatT *stat, ImpFileTypeE fileType, uint32_t succeBinFileNums, uint32_t totalBatch)
{
    if (stat->totalFileNums == 0 && stat->totalDirNums == 0) {
        return GMERR_OK;
    }
    if (succeBinFileNums != totalBatch) {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_DATA_EXCEPTION, "Gmimort file unsucc. ret = %d", GMERR_DATA_EXCEPTION);
        // LCOV_EXCL_STOP
        return GMERR_DATA_EXCEPTION;
    }
    return GMERR_OK;
}
#endif

Status ImportFileSizeVerify(uint64_t fileSize)
{
    // if true, it means this file is too large
    if (fileSize > g_importMaxFileSizeLimit) {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)GMERR_INSUFFICIENT_RESOURCES,
            "current file size: %" PRIu64 ", max limit size: %" PRIu64 ", ret = %d", fileSize, g_importMaxFileSizeLimit,
            (int32_t)GMERR_INSUFFICIENT_RESOURCES);
        // LCOV_EXCL_STOP
        return GMERR_INSUFFICIENT_RESOURCES;
    }
    return GMERR_OK;
}

// parse json file type
Status ImportParseFileType(ImportArgumentT *args)
{
    DB_POINTER(args);

    char tmpBuffer[DB_MAX_PATH] = {0};
    errno_t err = strcpy_s(tmpBuffer, sizeof(tmpBuffer), args->file.path);
    if (err != EOK) {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_FIELD_OVERFLOW, "Copy file path from %s unsucc. ret = %d",
            args->file.path, (int32_t)GMERR_FIELD_OVERFLOW);
        // LCOV_EXCL_STOP
        return GMERR_FIELD_OVERFLOW;
    }

    // get file suffix
    char *suffix = DbGetFileNameInfo(tmpBuffer, DB_MAX_PATH, DB_FILENAME_SUFFIX);
    if (suffix == NULL) {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(DbPrintfDefault, GMERR_FILE_OPERATE_FAILED, "Get file suffix from %s unsucc. ret = %d",
            args->file.path, (int32_t)GMERR_FILE_OPERATE_FAILED);
        // LCOV_EXCL_STOP
        return GMERR_FILE_OPERATE_FAILED;
    }
    Status ret = GMERR_FILE_OPERATE_FAILED;
    for (uint16_t index = 0; index < (uint16_t)FILE_FOLDER; ++index) {
        if (DbStrCmp(suffix, g_gmdbImpFileSuffix[index], false) == 0) {
            args->file.type = index;
            ret = GMERR_OK;
            break;
        }
    }
    return ret;
}

char *ImportReadDataFromFile(DbMemCtxT *memCtx, const char *filePath, size_t *fileSize)
{
    DB_POINTER2(memCtx, filePath);
    char *buffer = NULL;
    size_t fileLen = 0;
    int32_t fd = -1;
    Status ret = DbOpenFile(filePath, O_RDONLY, O_RDONLY, &fd);
    if (ret != GMERR_OK) {
        DB_LOG_DBG_ERROR((int32_t)ret, "Open file %s, os ret no: %d", filePath, DbAptGetErrno());
        return NULL;
    }
    do {
        ret = DbFileSize(filePath, &fileLen);
        if (ret != GMERR_OK) {
            DB_LOG_DBG_ERROR((int32_t)ret, "Get file Size of %s.", filePath);
            break;
        }
        size_t bufferSize = sizeof(char) * (fileLen + 1);
        // 在外层函数ImportParseFileContent，使用完该内存后进行释放
        buffer = DbDynMemCtxAlloc(memCtx, (uint32_t)bufferSize);
        ret = (buffer == NULL) ? GMERR_OUT_OF_MEMORY : GMERR_OK;
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "Import alloc file buffer unsucc.");  // LCOV_EXCL_LINE
            break;
        }
        ret = DbReadFileExpSize(fd, buffer, fileLen);
        if (ret != GMERR_OK) {
            DbDynMemCtxFree(memCtx, buffer);
            buffer = NULL;
            DB_LOG_DBG_ERROR(ret, "Read file %s exp size.", filePath);  // LCOV_EXCL_LINE
            break;
        }
        if (buffer[fileLen - 1] == '\n') {
            buffer[fileLen - 1] = '\0';
        } else {
            buffer[fileLen] = '\0';
        }
    } while (0);

    DbCloseFile(fd);
    if (fileSize != NULL) {
        *fileSize = fileLen;
    }
    return buffer;
}

ImpFileTypeE ImportGetFileTypeByCmdType(GmImpCmdTypeE cmdType)
{
    /* 对应GmImpCmdTypeE */
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
    ImpFileTypeE fileTypes[(int32_t)GMIMP_CMD_BOTTOM + 1] = {FILE_NSP, FILE_VSCHEMA, FILE_ESCHEMA, FILE_VSCHEMA_OLD,
        FILE_VSCHEMA, FILE_VDATA, FILE_EDATA, FILE_KVDATA, FILE_BINDATA, FILE_GMRESP, FILE_FOLDER, FILE_DATALOG,
        FILE_BOTTOM, FILE_BOTTOM, FILE_BOTTOM, FILE_BOTTOM, FILE_BOTTOM};
#else
    ImpFileTypeE fileTypes[(int32_t)GMIMP_CMD_BOTTOM + 1] = {FILE_NSP, FILE_VSCHEMA, FILE_ESCHEMA, FILE_VSCHEMA_OLD,
        FILE_VSCHEMA, FILE_VDATA, FILE_EDATA, FILE_KVDATA, FILE_GMRESP, FILE_FOLDER, FILE_DATALOG, FILE_BOTTOM,
        FILE_BOTTOM, FILE_BOTTOM, FILE_BOTTOM, FILE_BOTTOM};
#endif
    return fileTypes[cmdType];
}

Status ImportBatchInit(GmcConnT *conn, GmcBatchT **batch)
{
    GmcBatchOptionT batchOption;
    Status ret = GmcBatchOptionInit(&batchOption);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcBatchOptionSetExecOrder(&batchOption, GMC_BATCH_ORDER_STRICT);
    if (ret != GMERR_OK) {
        return ret;
    }
    const uint32_t importDataBufLimitSize = 2048;
    ret = GmcBatchOptionSetBufLimitSize(&batchOption, importDataBufLimitSize);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmcBatchPrepare(conn, &batchOption, batch);
    return ret;
}

Status ImportReadCfgDataFromFile(GmcStmtT *stmt, ImportArgumentT *args)
{
    DB_POINTER2(stmt, args);
    char cfgFilePath[DB_MAX_PATH] = {0};
    bool isFound = false;
    Status ret = ImportGetCfgFilePath(args->file.path, cfgFilePath, DB_MAX_PATH, &isFound);
    if (ret != GMERR_OK) {
        // LCOV_EXCL_START
        TOOL_RUN_ERROR(DbPrintfDefault, (int32_t)ret, "Get config file path from %s unsucc ret = %d", args->file.path,
            (int32_t)ret);
        // LCOV_EXCL_STOP
        return ret;
    }
    if (isFound) {
        ret = ImportCheckFileSizeWithLimit(cfgFilePath, FILE_CONFIG, args->file.sizeLimit);
        if (ret != GMERR_OK) {
            return ret;
        }
        DbJsonT *configDbJsonObject = NULL;
        args->configJsonStr = ImportAllocJsonStrByFilePath(stmt, cfgFilePath, &configDbJsonObject);
        if (args->configJsonStr == NULL) {
            ret = GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE;
            // LCOV_EXCL_START
            TOOL_RUN_ERROR(DbPrintfDefault, GMERR_OBJECT_NOT_IN_PREREQUISITE_STATE,
                "Read config data unsucc, file: %s. ret = %d", cfgFilePath, (int32_t)ret);
            // LCOV_EXCL_STOP
            return ret;
        }
        args->configDbJsonObject = configDbJsonObject;
    }
    return GMERR_OK;
}

void ImportSetMaxFileSizeLimit(ImpFileTypeE fileType, uint32_t fileSize)
{
    switch (fileType) {
        case FILE_VSCHEMA_OLD:
        case FILE_VSCHEMA:
            g_importMaxFileSizeLimit = MAX_VSCHEMA_FILE_SIZE;
            break;
        case FILE_CONFIG:
            g_importMaxFileSizeLimit = MAX_CONFIG_FILE_SIZE;
            break;
        case FILE_ESCHEMA:
            g_importMaxFileSizeLimit = MAX_ESCHEMA_FILE_SIZE;
            break;
        case FILE_GMRESP:
            g_importMaxFileSizeLimit = MAX_RESPOOL_FILE_SIZE;
            break;
        case FILE_NSP:
            g_importMaxFileSizeLimit = MAX_NSP_FILE_SIZE;
            break;
        case FILE_VDATA:
        case FILE_GMDATA:
        case FILE_KVDATA:
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
        case FILE_BINDATA:
#endif
            if (fileSize > 0) {
                g_importMaxFileSizeLimit = (uint64_t)(fileSize * DB_MEBI);
            } else {
                g_importMaxFileSizeLimit = MAX_VDATA_FILE_SIZE;
            }
            break;
        default:
            g_importMaxFileSizeLimit = MAX_DEFAULT_FILE_SIZE;
    }
}

Status ImportRedirectNewBuffer(DbMemCtxT *memCtx, const char *src, size_t srcLen, char **out)
{
    // 在外层函数ImportParseFileHeader，使用完后释放
    char *new = (char *)DbDynMemCtxAlloc(memCtx, srcLen);
    if (new == NULL) {
        DB_LOG_ERROR(GMERR_OUT_OF_MEMORY, "Realloc mem.");  // LCOV_EXCL_LINE
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t err = memcpy_s(new, srcLen, src, srcLen);
    if (err != EOK) {
        DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Copy buf to reallocated.");  // LCOV_EXCL_LINE
        DbDynMemCtxFree(memCtx, new);  // 失败场景下内存指针没有传递到外面，并且会返回错误码，不用置空
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    *out = new;
    return GMERR_OK;
}

static inline bool ImportCheckDigestCodeValid(TlsDigestAlgorithmE algorithm, const char *algorithmCode)
{
    return (algorithm == DB_DIGEST_ALGORITHM_CRC && *algorithmCode != CRC32_TYPE) ||
           (algorithm == DB_DIGEST_ALGORITHM_HMAC && *algorithmCode != HMAC_TYPE);
}

static Status ImportParseFileHeader(
    DbMemCtxT *memCtx, char **fileContent, size_t *fileContentLen, TlsDigestInfoT *digest)
{
    DB_POINTER4(memCtx, *fileContent, fileContentLen, digest);
    digest->salt = NULL;
    digest->digest = NULL;
    Status ret = GMERR_OK;
    do {
        char *headerDelim = strchr(*fileContent, '\n');
        if (headerDelim == NULL) {
            break;
        }
        // 添加'/0'分割文件校验文本和内容
        *headerDelim = '\0';
        *fileContentLen -= strlen(*fileContent) + 1;
        if (ImportCheckDigestCodeValid(digest->algorithm, *fileContent)) {
            break;
        }
        // 加一移除首位校验类型
        char *digestInfo = *fileContent + 1;
        if (digest->algorithm == DB_DIGEST_ALGORITHM_HMAC) {
            // hmac记录了用户口令派生的盐值，需要解析
            char *digestDelim = strchr(digestInfo, '|');
            if (digestDelim == NULL) {
                break;
            }
            *digestDelim = '\0';
            ret = ImportRedirectNewBuffer(memCtx, digestInfo, strlen(digestInfo) + 1, &digest->salt);
            if (ret != GMERR_OK) {
                break;
            }
            // 偏移指针
            digestInfo = digestDelim + 1;
        }
        ret = ImportRedirectNewBuffer(memCtx, digestInfo, strlen(digestInfo) + 1, &digest->digest);
        if (ret != GMERR_OK) {
            break;
        }
        // 加一移除首位换行符
        *fileContent = headerDelim + 1;
        return GMERR_OK;
    } while (0);
    if (digest->salt != NULL) {
        DbDynMemCtxFree(memCtx, digest->salt);  // 失败场景下内存指针没有传递到外面，并且会返回错误码，不用置空
    }
    if (digest->digest != NULL) {
        DbDynMemCtxFree(memCtx, digest->digest);  // 失败场景下内存指针没有传递到外面，并且会返回错误码，不用置空
    }
    // LCOV_EXCL_START
    TOOL_RUN_ERROR(DbPrintfDefault, GMERR_DATA_EXCEPTION, "unexpected file content when parse digest info.");
    // LCOV_EXCL_STOP
    return GMERR_DATA_EXCEPTION;
}

void ImportFreeJsonInfoForFile(GmcStmtT *stmt, ImportArgumentT *args)
{
    if (args->jsonObject != NULL) {
        DbJsonDelete(args->jsonObject);
        args->jsonObject = NULL;
    }
    if (args->jsonStr != NULL) {
        DbDynMemCtxFree((DbMemCtxT *)DbGetTopDynMemCtx(NULL), args->jsonStr);  // 正常释放内存，后续不会再用到
        args->jsonStr = NULL;
    }
    if (args->configDbJsonObject != NULL) {
        DbJsonDelete(args->configDbJsonObject);
        args->configDbJsonObject = NULL;
    }
    if (args->configJsonStr != NULL) {
        ImportFreeJsonStr(stmt, args->configJsonStr);
        args->configJsonStr = NULL;
    }
}

Status ImportParseFileContent(GmcStmtT *stmt, ImportArgumentT *args)
{
    DB_POINTER2(stmt, args);
    char *fileContent = NULL;
    Status ret = GMERR_DATA_EXCEPTION;
    DbMemCtxT *topMemCtx = (DbMemCtxT *)DbGetTopDynMemCtx(NULL);
    do {
        fileContent = ImportReadDataFromFile(topMemCtx, args->file.path, &args->jsonStrLen);
        if (fileContent == NULL) {
            break;
        }
        if (args->digest.algorithm == DB_DIGEST_ALGORITHM_BUTT) {
            args->jsonStr = fileContent;
            return GMERR_OK;
        }
        char *fileContentPtr = fileContent;
        ret = ImportParseFileHeader(stmt->memCtx, &fileContentPtr, &args->jsonStrLen, &args->digest);
        if (ret != GMERR_OK) {
            break;
        }
        // 加一移除首位换行符
        ret = ImportRedirectNewBuffer(topMemCtx, fileContentPtr, args->jsonStrLen + 1, &args->jsonStr);
        if (ret != GMERR_OK) {
            break;
        }
        // 全部重新申请成功需要释放原先申请的内存
        DbDynMemCtxFree(topMemCtx, fileContent);  // 生命周期在本函数或者上一级函数，这里释放后不会再用到
        return GMERR_OK;
    } while (0);
    ImportFreeJsonInfoForFile(stmt, args);
    if (fileContent != NULL) {
        DbDynMemCtxFree(topMemCtx, fileContent);  // 失败场景下内存指针没有传递到外面，并且会返回错误码，不用置空
    }
    DB_LOG_ERROR(ret, "unexpected file content %s.", args->file.path);  // LCOV_EXCL_LINE
    return ret;
}

static uint32_t GetFileTotalVertexNum(const char *jsonStr, uint32_t fileLen)
{
    uint32_t vertexNum = 0;
    bool isStart = false;
    uint32_t left = 0;
    for (uint32_t i = 0; i < fileLen - 1; i++) {
        if (jsonStr[i] == '[') {
            isStart = true;
            continue;
        }
        if (jsonStr[i] == '{' && isStart) {
            left++;
            continue;
        }
        if (jsonStr[i] == '}' && isStart) {
            left--;
        }
        if (left == 0) {
            vertexNum++;
        }
    }
    // 文件的组成 ['\n'{1xxx},'\n' {2xxx}'\n']'\0'
    return vertexNum;
}

static Status CheckJsonStr(
    const char *jsonStr, ImportArgs *importArgs, uint32_t *indexBook, ImpVertexParasT *paras, uint32_t currVertexIdx)
{
    bool isHead = false;
    for (uint32_t i = 0; i < indexBook[0]; i++) {
        if (jsonStr[i] == '[') {
            isHead = true;
        }
        if (isHead && jsonStr[i] == '{') {
            break;
        }
        if (isHead && (jsonStr[i] != '\n' && jsonStr[i] != ' ' && jsonStr[i] != '\r')) {
            return GMERR_INVALID_JSON_CONTENT;
        }
    }
    paras->allTotalVertexNum = currVertexIdx > 0 ? currVertexIdx - 1 : currVertexIdx;
    for (uint32_t i = indexBook[paras->allTotalVertexNum]; i < importArgs->fileLen; i++) {
        if (jsonStr[i] == ']') {
            break;
        }
        if (jsonStr[i] != '\n' && jsonStr[i] != ' ' && jsonStr[i] != '\r') {
            return GMERR_INVALID_JSON_CONTENT;
        }
    }
    return GMERR_OK;
}

static Status GetJsonStrInfo(const char *jsonStr, ImportArgs *importArgs, uint32_t *indexBook, ImpVertexParasT *paras)
{
    uint32_t currVertexIdx = 0;
    uint32_t bookNums = paras->allTotalVertexNum + 1;
    bool isStart = false;
    uint32_t left = 0;
    for (uint32_t idx = 0; idx < importArgs->fileLen; idx++) {
        if (jsonStr[idx] == '[') {
            isStart = true;
        }
        if (jsonStr[idx] == '{') {
            if (currVertexIdx == 0) {
                indexBook[currVertexIdx++] = idx;
            }
            left++;
            continue;
        }
        if (jsonStr[idx] == '}' && isStart && currVertexIdx < bookNums) {
            left--;
            if (left != 0) {
                continue;
            }
            indexBook[currVertexIdx++] = idx;
        }
    }
    // 检查[aaa{}]和[{}aaa]场景
    return CheckJsonStr(jsonStr, importArgs, indexBook, paras, currVertexIdx);
}

static Status OneTimesImportVertexData(
    GmcStmtT *stmt, ImportArgumentT *args, ImpVertexParasT *paras, ImpVertexBatchParasT *execParams)
{
    // load json obj
    DbJsonT *jsonRoot = NULL;
    jsonRoot = DbLoadJsonClt(args->jsonStr, DB_JSON_REJECT_DUPLICATES);
    if (jsonRoot == NULL) {
        DB_LOG_DBG_ERROR(GMERR_INVALID_JSON_CONTENT, "Read label json from buff.");  // LCOV_EXCL_LINE
        return GMERR_INVALID_JSON_CONTENT;
    }
    paras->json = jsonRoot;
    paras->totalVertexNum = DbJsonGetArraySize(jsonRoot);
    paras->allTotalVertexNum = paras->totalVertexNum;
    paras->ignoreHighVersionData = args->isIgnoreHVersionData;
    // 导入数据
    Status ret = ImportVertexDataInner(stmt, paras, execParams);
    if (ret != GMERR_OK) {
        DbJsonDelete(jsonRoot);
        return ret;
    }
    // 释放json obj
    DbJsonDelete(jsonRoot);
    return GMERR_OK;
}

static Status ConvertJsonAndImport(GmcStmtT *stmt, ImportArgumentT *args, ImpVertexParasT *paras,
    ImpVertexBatchParasT *execParams, ImportArgs *importArgs)
{
    // load json obj
    DbJsonT *jsonRoot = NULL;
    jsonRoot = DbLoadJsonClt(importArgs->buffer, DB_JSON_REJECT_DUPLICATES);
    if (jsonRoot == NULL || paras->allTotalVertexNum == 0) {
        // 不一定是不合法的json，也有可能是做分批时有问题，为了兼容这里尝试使用完整str去转json
        importArgs->isAllImport = true;
        return OneTimesImportVertexData(stmt, args, paras, execParams);
    }
    paras->json = jsonRoot;
    paras->totalVertexNum = DbJsonGetArraySize(jsonRoot);
    // 导入数据
    Status ret = ImportVertexDataInner(stmt, paras, execParams);
    if (ret != GMERR_OK) {
        DbJsonDelete(jsonRoot);
        return ret;
    }
    // 释放json obj
    DbJsonDelete(jsonRoot);
    return GMERR_OK;
}

static Status ProcessAndImport(GmcStmtT *stmt, ImportArgumentT *args, ImpVertexParasT *paras,
    ImpVertexBatchParasT *execParams, ImportArgs *importArgs)
{
    paras->allTotalVertexNum = GetFileTotalVertexNum(args->jsonStr, importArgs->fileLen);
    // 为每行的结束符申请数组存储下标
    uint32_t bookAllocSize = sizeof(uint32_t) * (paras->allTotalVertexNum + 1);
    uint32_t *indexBook = (uint32_t *)DbDynMemCtxAlloc(stmt->memCtx, bookAllocSize);
    if (indexBook == NULL) {
        DB_LOG_DBG_ERROR(
            GMERR_OUT_OF_MEMORY, "Alloc index book, bufferSize:%" PRIu64 ".", (uint64_t)(paras->allTotalVertexNum + 1));
        return GMERR_OUT_OF_MEMORY;
    }
    // 记录每条数据换行符的位置，大括号的起始，结束位置
    Status ret = GetJsonStrInfo(args->jsonStr, importArgs, indexBook, paras);
    if (ret != GMERR_OK) {
        // 不一定是不合法的json，也有可能是做分批时有问题，为了兼容这里尝试使用完整str去转json
        DbDynMemCtxFree(stmt->memCtx, indexBook);
        return OneTimesImportVertexData(stmt, args, paras, execParams);
    }
    char *tmpBuff = importArgs->buffer;
    // 点的个数做循环
    uint32_t importVertexNum = 0;
    while (importVertexNum < paras->allTotalVertexNum) {
        // 转jsonObj的str限制，点的个数少于等于MAX_IMPORT_VERTEX_NUM
        uint32_t startIdx = (paras->allTotalVertexNum - importVertexNum) > MAX_IMPORT_VERTEX_NUM ?
                                MAX_IMPORT_VERTEX_NUM :
                                (paras->allTotalVertexNum - importVertexNum);
        for (uint32_t i = startIdx; i > 0; i--) {
            // str size 小于 1M
            uint32_t vertexStrLen = indexBook[i + importVertexNum] - indexBook[importVertexNum];
            // 1条数据就大过1M
            if (vertexStrLen > SIZE_M(1) && i != 1) {
                continue;
            }
            tmpBuff[0] = '[';
            (void)memcpy_s(tmpBuff + 1, vertexStrLen, args->jsonStr + indexBook[importVertexNum], vertexStrLen);
            if (importVertexNum + i == paras->allTotalVertexNum) {
                tmpBuff[vertexStrLen + 1] = ']';
                uint32_t offset = 2;
                tmpBuff[vertexStrLen + offset] = '\0';
            } else {
                tmpBuff[vertexStrLen] = ']';
                tmpBuff[vertexStrLen + 1] = '\0';
            }
            ret = ConvertJsonAndImport(stmt, args, paras, execParams, importArgs);
            if (ret != GMERR_OK || importArgs->isAllImport) {
                DbDynMemCtxFree(stmt->memCtx, indexBook);
                return ret;
            }
            importVertexNum += i;
            break;
        }
    }
    DbDynMemCtxFree(stmt->memCtx, indexBook);
    return GMERR_OK;
}

Status MultipleTimesImportVertexData(
    GmcStmtT *stmt, ImportArgumentT *args, ImpVertexParasT *paras, ImpVertexBatchParasT *execParams)
{
    // 按文件大小申请buff，内存申请太小预防单条数据过大场景
    size_t fileLen = 0;
    Status ret = DbFileSize(args->file.path, &fileLen);
    if (ret != GMERR_OK) {
        DB_LOG_DBG_ERROR(ret, "Get file Size of %s.", args->file.path);  // LCOV_EXCL_LINE
        return ret;
    }
    if (fileLen < SIZE_M(1)) {
        return OneTimesImportVertexData(stmt, args, paras, execParams);
    }
    int32_t fd = -1;
    ret = DbOpenFile(args->file.path, O_RDONLY, O_RDONLY, &fd);
    if (ret != GMERR_OK) {
        DB_LOG_DBG_ERROR(ret, "Open file %s. os ret no: %d", args->file.path, DbAptGetErrno());
        return ret;
    }

    size_t bufferSize = sizeof(char) * (fileLen + 1);
    // 在外层函数ImportParseFileContent，使用完该内存后进行释放
    char *buffer = DbDynMemCtxAlloc(stmt->memCtx, bufferSize);
    if (buffer == NULL) {
        DB_LOG_DBG_ERROR(GMERR_OUT_OF_MEMORY, "Alloc file buffer, bufferSize:%" PRIu64 ".", (uint64_t)bufferSize);
        DbCloseFile(fd);
        return GMERR_OUT_OF_MEMORY;
    }
    ImportArgs importArgs = {
        .fd = fd,
        .buffer = buffer,
        .bufferSize = bufferSize,
        .fileLen = fileLen,
        .isAllImport = false,
    };
    ret = ProcessAndImport(stmt, args, paras, execParams, &importArgs);
    // 释放buff
    DbDynMemCtxFree(stmt->memCtx, buffer);
    DbCloseFile(fd);
    return ret;
}

#ifdef __cplusplus
}
#endif /* __cplusplus */
