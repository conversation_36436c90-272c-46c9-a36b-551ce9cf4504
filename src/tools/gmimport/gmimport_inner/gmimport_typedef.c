/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2021. All rights reserved.
 * File Name: gmimport_typedef.c
 * Description:
 * Author: <PERSON><PERSON><PERSON><PERSON><PERSON>an
 * Create: 2020-9-16
 */

#include "gmimport_typedef.h"
#include "adpt_locator.h"
#include "db_option_parser.h"
#include "tool_utils.h"
#include "gmimport_file.h"

#ifdef __cplusplus
extern "C" {
#endif

// 用途：存储gmimport工具命令行可用的选项，以及该选项的限制条件
// 是否并发初始化：否
// 是否并发读写：否
// 并发方案：无

static DbRuleItemT g_gmdbImportRuleItems[] = {
    {0, (int32_t)GMIMP_RULE_HELP, {DB_OPTION_ARG_HELP}, {true}},
    {0, (int32_t)GMIMP_RULE_VERSION, {DB_OPTION_ARG_VERSION}, {true}},
    {0, (int32_t)GMIMP_RULE_IMP_FILE,
        {IMP_OPTION_ARG_FILE_PATH, IMP_OPTION_ARG_FILE_SIZE_LIMIT, DB_OPTION_ARG_DOMAIN_NAME, IMP_OPTION_ARG_OPERATION,
            IMP_OPTION_ARG_TABLE_NAME, DB_OPTION_ARG_NAMESPACE, DB_OPTION_ARG_RELIABILITY_CHECK,
            DB_OPTION_ARG_USER_NAME, DB_OPTION_ARG_PRINT_QUICK_LOG, IMP_OPTION_ARG_CMD_INCLUDE,
            IMP_OPTION_ARG_DTL_DISTRIBUTE, IMP_OPTION_ARG_IGNORE_SELECTION},
        {true, false, false, true, false, false, false, false, false, false, false}},
    {0, (int32_t)GMIMP_RULE_BIND_LABEL,
        {DB_OPTION_ARG_DOMAIN_NAME, IMP_OPTION_ARG_OPERATION, IMP_OPTION_ARG_TABLE_NAME, IMP_OPTION_ARG_RES_POOL_NAME,
            DB_OPTION_ARG_NAMESPACE},
        {false, true, true, true, false}},
    {0, (int32_t)GMIMP_RULE_BIND_EXTERNAL_RESPOOL,
        {DB_OPTION_ARG_DOMAIN_NAME, IMP_OPTION_ARG_OPERATION, IMP_OPTION_ARG_RES_POOL_NAME,
            IMP_OPTION_ARG_EXT_RES_POOL_NAME, DB_OPTION_ARG_NAMESPACE},
        {false, true, true, true, false}},
    {0, (int32_t)GMIMP_RULE_RESTORE_CONF,
        {IMP_OPTION_ARG_INPUT_PATH, DB_OPTION_ARG_DOMAIN_NAME, IMP_OPTION_ARG_OPERATION, IMP_OPTION_ARG_OUTPUT_PATH,
            DB_OPTION_ARG_RELIABILITY_CHECK},
        {true, false, true, false, false}},
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
    {0, (int32_t)GMIMP_RULE_IMP_BINDATA,
        {IMP_OPTION_ARG_OPERATION, IMP_OPTION_ARG_FILE_PATH, DB_OPTION_ARG_THREAD_NUM, IMP_OPTION_ARG_DIR_TYPE,
            DB_OPTION_ARG_DOMAIN_NAME},
        {true, true, false, false, false}},
#endif
#ifdef FEATURE_DATALOG
    {0, (int32_t)GMIMP_RULE_UNINSTALL_FILE,
        {IMP_OPTION_ARG_UNINSTALL_SO, IMP_OPTION_ARG_FILE_SIZE_LIMIT, IMP_OPTION_ARG_OPERATION, DB_OPTION_ARG_NAMESPACE,
            DB_OPTION_ARG_DOMAIN_NAME},
        {true, false, true, false, false}},
#endif
    {0, (int32_t)GMIMP_RULE_UPGRADE_FILE,
        {IMP_OPTION_ARG_UPGRADE_SO, IMP_OPTION_ARG_OPERATION, IMP_OPTION_ARG_FILE_SIZE_LIMIT, DB_OPTION_ARG_DOMAIN_NAME,
            DB_OPTION_ARG_NAMESPACE},
        {true, true, false, false, false}},
    {0, (int32_t)GMIMP_RULE_ROLLBACK_UPGRADE_FILE,
        {IMP_OPTION_ARG_ROLLBACK_UPGRADE_SO, IMP_OPTION_ARG_OPERATION, IMP_OPTION_ARG_FILE_SIZE_LIMIT,
            DB_OPTION_ARG_DOMAIN_NAME, DB_OPTION_ARG_NAMESPACE},
        {true, true, false, false, false}},
#ifdef TS_MULTI_INST
    {0, (int32_t)GMIMP_RULE_SQL_FILE, {IMP_OPTION_ARG_OPERATION, IMP_OPTION_ARG_FILE_PATH, DB_OPTION_ARG_DOMAIN_NAME},
        {true, true, false}},
#endif
#ifdef FEATURE_RSMEM
    {0, (int32_t)GMIMP_RULE_IMP_V1_BIN,
        {IMP_OPTION_ARG_OPERATION, IMP_OPTION_ARG_FILE_PATH, DB_OPTION_ARG_DOMAIN_NAME, DB_OPTION_ARG_USER_NAME},
        {true, true, false, false}},
    {0, (int32_t)GMIMP_RULE_IMP_V1_RSM,
        {IMP_OPTION_ARG_OPERATION, IMP_OPTION_ARG_FILE_PATH, DB_OPTION_ARG_DOMAIN_NAME, DB_OPTION_ARG_USER_NAME},
        {true, true, false, false}},
    {0, (int32_t)GMIMP_RULE_IMP_RSM_MIGRATION,
        {IMP_OPTION_ARG_OPERATION, IMP_OPTION_ARG_FILE_PATH, DB_OPTION_ARG_DOMAIN_NAME, DB_OPTION_ARG_USER_NAME},
        {true, true, false, false}},
#endif
};
/* GmImpCmdTypeE 对应 */
// 用途：存储gmimport工具的cmd命令
// 是否并发初始化：否
// 是否并发读写：否
// 并发方案：无

// 此处需要和GmImpCmdTypeE一一对应
const char *g_gmdbImpCmdName[] = {[GMIMP_CMD_NSP] = "import_namespace",
    [GMIMP_CMD_VSCHEMA] = "import_vschema",
    [GMIMP_CMD_ESCHEMA] = "import_eschema",
    [GMIMP_CMD_KVTABLE] = "import_kvtable",
    [GMIMP_CMD_IMP_SCHEMA] = "import_vschema or kvtable",
    [GMIMP_CMD_VDATA] = "import_vdata",
    [GMIMP_CMD_EDATA] = "import_edata",
    [GMIMP_CMD_KVDATA] = "import_kvdata",
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
    [GMIMP_CMD_BINDATA] = "import_bindata",
#endif
    [GMIMP_CMD_RESPOOL] = "import_respool",
    [GMIMP_CMD_CACHE] = "import_cache",
    [GMIMP_CMD_DATALOG] = "import_datalog",
    [GMIMP_CMD_BIND] = "bind",
    [GMIMP_CMD_BIND_LABEL] = "bind_label",
    [GMIMP_CMD_BIND_EXT_RESPOOL] = "bind_external_respool",
    [GMIMP_CMD_RESTORECONF] = "restore_conf_file",
#ifdef TS_MULTI_INST
    [GMIMP_CMD_SQL] = "import_sql_file",
#endif
#ifdef FEATURE_RSMEM
    [GMIMP_CMD_V1_BIN] = "import_v1_bin_file",
    [GMIMP_CMD_V1_RSM] = "import_v1_rsm_file",
    [GMIMP_CMD_RSM_MIGRATION] = "import_rsm_migration",
#endif
    [GMIMP_CMD_BOTTOM] = " "};

Status GmImpAllocOptionRuleItems(DbOptionRuleT *optionRule)
{
    DB_POINTER(optionRule);
    return DbAllocOptionRuleItems(optionRule, (uint32_t)GMIMP_OPTION_NUM, ELEMENT_COUNT(g_gmdbImportRuleItems));
}

// 存储gmImport工具的cmd命令，新增命令要同步新增该处注释以及修改GMIMP_OPTION_NUM确保数组不会越界
// 格式：{ OptionName, Index, Param Type, Min value, Max Value, Min Param Count, Max Param Count,
// Used, Repeatable, Repeat Times, Params }
// {DB_OPTION_ARG_HELP, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}}
// {DB_OPTION_ARG_VERSION, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}}
// {IMP_OPTION_ARG_OPERATION, 0, PARAM_TYPE_STR, 1, MAX_COMMAND_LENGTH, 1, 1, 0, 0, 0, {}}
// {IMP_OPTION_ARG_FILE_PATH, 0, PARAM_TYPE_STR, 1, DB_MAX_PATH, 1, DB_MAX_GMIMPORT_OPTION_REPEAT_NUM, 0, 0, 0, {}}
// {IMP_OPTION_ARG_UPGRADE_SO, 0, PARAM_TYPE_STR, 1, DB_MAX_PATH, 1, 1, 0, 0, 0, {}}
// {IMP_OPTION_ARG_ROLLBACK_UPGRADE_SO, 0, PARAM_TYPE_STR, 1, DB_MAX_PATH, 1, 1, 0, 0, 0, {}}
// {IMP_OPTION_ARG_CMD_INCLUDE, 0, PARAM_TYPE_STR, 1, MAX_COMMAND_LENGTH, 1, GMIMP_CMD_BOTTOM, 0, 0, 0, {}}
// {IMP_OPTION_ARG_FILE_SIZE_LIMIT, 0, PARAM_TYPE_INT32, 1, DB_MAX_FILE_SIZE_LIMIT, 1, 1, 0, 0, 0, {}}
// {IMP_OPTION_ARG_TABLE_NAME, 0, PARAM_TYPE_STR, 1, MAX_TABLE_NAME_LEN, 1, 1, 0, 0, 0, {}}
// {IMP_OPTION_ARG_RES_POOL_NAME, 0, PARAM_TYPE_STR, 1, MAX_RES_POOL_NAME_LEN, 1, 1, 0, 0, 0, {}}
// {IMP_OPTION_ARG_EXT_RES_POOL_NAME, 0, PARAM_TYPE_STR, 1, MAX_RES_POOL_NAME_LEN, 1, 1, 0, 0, 0, {}}
// {IMP_OPTION_ARG_INPUT_PATH, 0, PARAM_TYPE_STR, 1, DB_MAX_PATH, 1, 1, 0, 0, 0, {}}
// {IMP_OPTION_ARG_OUTPUT_PATH, 0, PARAM_TYPE_STR, 1, DB_MAX_PATH, 1, 1, 0, 0, 0, {}}
// DATALOG_UNINSTALL START
// {IMP_OPTION_ARG_UNINSTALL_SO, 0, PARAM_TYPE_STR, 1, MAX_NAMESPACE_LENGTH, 1, 1, 0, 0, 0, {}}
// DATALOG_UNINSTALL END
// {DB_OPTION_ARG_NAMESPACE, 0, PARAM_TYPE_STR, 1, MAX_NAMESPACE_LENGTH, 1, 1, 0, 0, 0, {}}
// {DB_OPTION_ARG_DOMAIN_NAME, 0, PARAM_TYPE_STR, 1, DOMAIN_NAME_MAX_LEN, 1, 1, 0, 0, 0, {}}
// {DB_OPTION_ARG_RELIABILITY_CHECK, 0, PARAM_TYPE_STR, 1, MAX_HMAC_KEY_LENGTH, 1, 1, 0, 0, 0, {}}
// {DB_OPTION_ARG_USER_NAME, 0, PARAM_TYPE_STR, MIN_USER_NAME_LEN, MAX_USER_NAME_LEN, 1, 1, 0, 0, 0, {}}
// {DB_OPTION_ARG_PRINT_QUICK_LOG, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}}
// WARM REBOOT START
// {DB_OPTION_ARG_THREAD_NUM, 0, PARAM_TYPE_UINT32, 1, DB_MAX_PATH, 1, 1, 0, 0, 0, {}}
// {IMP_OPTION_ARG_DIR_TYPE, 0, PARAM_TYPE_STR, 1, DB_MAX_PATH, 1, 1, 0, 0, 0, {}}
// WARM REBOOT END
// {IMP_OPTION_ARG_DTL_DISTRIBUTE, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}}
Status DbInitImportOptionItems(DbOptionRuleT *optionRule, uint32_t num)
{
    DB_POINTER(optionRule);
    if (num > OPTION_ITEM_MAX_NUM) {
        return GMERR_INVALID_PARAMETER_VALUE;
    }
    DbOptionItemT *tmpOptionItems = optionRule->optionItems;
    *tmpOptionItems = (DbOptionItemT){DB_OPTION_ARG_HELP, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}};
    tmpOptionItems += 1;

    *tmpOptionItems = (DbOptionItemT){DB_OPTION_ARG_VERSION, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}};
    tmpOptionItems += 1;

    *tmpOptionItems =
        (DbOptionItemT){IMP_OPTION_ARG_OPERATION, 0, PARAM_TYPE_STR, 1, MAX_COMMAND_LENGTH, 1, 1, 0, 0, 0, {}};
    tmpOptionItems += 1;

    *tmpOptionItems = (DbOptionItemT){
        IMP_OPTION_ARG_FILE_PATH, 0, PARAM_TYPE_STR, 1, DB_MAX_PATH, 1, DB_MAX_GMIMPORT_OPTION_REPEAT_NUM, 0, 0, 0, {}};
    tmpOptionItems += 1;

    *tmpOptionItems = (DbOptionItemT){IMP_OPTION_ARG_UPGRADE_SO, 0, PARAM_TYPE_STR, 1, DB_MAX_PATH, 1, 1, 0, 0, 0, {}};
    tmpOptionItems += 1;

    *tmpOptionItems =
        (DbOptionItemT){IMP_OPTION_ARG_ROLLBACK_UPGRADE_SO, 0, PARAM_TYPE_STR, 1, DB_MAX_PATH, 1, 1, 0, 0, 0, {}};
    tmpOptionItems += 1;

    *tmpOptionItems = (DbOptionItemT){
        IMP_OPTION_ARG_CMD_INCLUDE, 0, PARAM_TYPE_STR, 1, MAX_COMMAND_LENGTH, 1, GMIMP_CMD_BOTTOM, 0, 0, 0, {}};
    tmpOptionItems += 1;

    *tmpOptionItems = (DbOptionItemT){
        IMP_OPTION_ARG_FILE_SIZE_LIMIT, 0, PARAM_TYPE_INT32, 1, DB_MAX_FILE_SIZE_LIMIT, 1, 1, 0, 0, 0, {}};
    tmpOptionItems += 1;

    *tmpOptionItems =
        (DbOptionItemT){IMP_OPTION_ARG_TABLE_NAME, 0, PARAM_TYPE_STR, 1, MAX_TABLE_NAME_LEN, 1, 1, 0, 0, 0, {}};
    tmpOptionItems += 1;

    *tmpOptionItems =
        (DbOptionItemT){IMP_OPTION_ARG_RES_POOL_NAME, 0, PARAM_TYPE_STR, 1, MAX_RES_POOL_NAME_LEN, 1, 1, 0, 0, 0, {}};
    tmpOptionItems += 1;

    *tmpOptionItems = (DbOptionItemT){
        IMP_OPTION_ARG_EXT_RES_POOL_NAME, 0, PARAM_TYPE_STR, 1, MAX_RES_POOL_NAME_LEN, 1, 1, 0, 0, 0, {}};
    tmpOptionItems += 1;

    *tmpOptionItems = (DbOptionItemT){IMP_OPTION_ARG_INPUT_PATH, 0, PARAM_TYPE_STR, 1, DB_MAX_PATH, 1, 1, 0, 0, 0, {}};
    tmpOptionItems += 1;

    *tmpOptionItems = (DbOptionItemT){IMP_OPTION_ARG_OUTPUT_PATH, 0, PARAM_TYPE_STR, 1, DB_MAX_PATH, 1, 1, 0, 0, 0, {}};
    tmpOptionItems += 1;

#ifdef FEATURE_DATALOG
    *tmpOptionItems =
        (DbOptionItemT){IMP_OPTION_ARG_UNINSTALL_SO, 0, PARAM_TYPE_STR, 1, MAX_NAMESPACE_LENGTH, 1, 1, 0, 0, 0, {}};
    tmpOptionItems += 1;
#endif

    *tmpOptionItems =
        (DbOptionItemT){DB_OPTION_ARG_NAMESPACE, 0, PARAM_TYPE_STR, 1, MAX_NAMESPACE_LENGTH, 1, 1, 0, 0, 0, {}};
    tmpOptionItems += 1;

    *tmpOptionItems = (DbOptionItemT){
        IMP_OPTION_ARG_IGNORE_SELECTION, 0, PARAM_TYPE_STR, 1, MAX_IGNORE_OPTION_LENGTH, 1, 1, 0, 0, 0, {}};
    tmpOptionItems += 1;

    *tmpOptionItems =
        (DbOptionItemT){DB_OPTION_ARG_DOMAIN_NAME, 0, PARAM_TYPE_STR, 1, DOMAIN_NAME_MAX_LEN, 1, 1, 0, 0, 0, {}};
    tmpOptionItems += 1;

    *tmpOptionItems =
        (DbOptionItemT){DB_OPTION_ARG_RELIABILITY_CHECK, 0, PARAM_TYPE_STR, 1, MAX_HMAC_KEY_LENGTH, 1, 1, 0, 0, 0, {}};
    tmpOptionItems += 1;

    *tmpOptionItems = (DbOptionItemT){
        DB_OPTION_ARG_USER_NAME, 0, PARAM_TYPE_STR, MIN_USER_NAME_LEN, MAX_USER_NAME_LEN, 1, 1, 0, 0, 0, {}};
    tmpOptionItems += 1;

    *tmpOptionItems = (DbOptionItemT){DB_OPTION_ARG_PRINT_QUICK_LOG, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}};
    tmpOptionItems += 1;

    *tmpOptionItems = (DbOptionItemT){IMP_OPTION_ARG_DTL_DISTRIBUTE, 0, PARAM_TYPE_NULL, 0, 0, 0, 0, 0, 0, 0, {}};

// warm reboot场景
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
    tmpOptionItems += 1;
    *tmpOptionItems =
        (DbOptionItemT){DB_OPTION_ARG_THREAD_NUM, 0, PARAM_TYPE_UINT32, 1, DB_MAX_PATH, 1, 1, 0, 0, 0, {}};
    tmpOptionItems += 1;
    *tmpOptionItems = (DbOptionItemT){IMP_OPTION_ARG_DIR_TYPE, 0, PARAM_TYPE_STR, 1, DB_MAX_PATH, 1, 1, 0, 0, 0, {}};
#endif

    return DbInitOptionItemsEntry(optionRule, num);
}

Status GmImpInit(DbOptionRuleT *optionRule, int32_t argc, char *argv[])
{
    DB_POINTER(optionRule);
    Status ret = DbInitImportOptionItems(optionRule, (uint32_t)GMIMP_OPTION_NUM);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DbInitRuleItems(optionRule, g_gmdbImportRuleItems, (int32_t)ELEMENT_COUNT(g_gmdbImportRuleItems));
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = DbInitOptionParam(optionRule, argc, argv);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

// clang-format off
Status GmImpGetHelp(DbOptionRuleT *optionRule, ImportOptionT *toolOption)
{
    DB_UNUSED(optionRule);
    DB_UNUSED(toolOption);
    const char *importOptionManual[][MANUAL_COL_NUM] = {
        {DB_OPTION_ARG_HELP, "", "print the online help manual"},
        {DB_OPTION_ARG_VERSION, "", "print the gmimport version"},
#ifndef NDEBUG
        {IMP_OPTION_ARG_OPERATION, "<cmd_type>",
            "vschema | imp_schema | vdata | imp_data | eschema | edata |"
            " kvtable | kvdata | imp_kv | respool | cache | bind | datalog | conf | namespace | sql |"
            " v1_bin | v1_rsm | rsm_migration"},
        {IMP_OPTION_ARG_CMD_INCLUDE, "<cmd_type>", "take effect only in the '-c cache' command. support options: "
            "vschema | vdata | imp_data | eschema | edata | kvtable | kvdata | imp_schema | imp_kv | namespace"},
#else
        {IMP_OPTION_ARG_OPERATION, "<cmd_type>",
            "vschema | imp_schema | vdata | imp_data | kvtable | kvdata | imp_kv |"
            " respool | cache | bind | datalog | conf | namespace | v1_bin | v1_rsm | rsm_migration"},
        {IMP_OPTION_ARG_CMD_INCLUDE, "<cmd_type>", "take effect only in the '-c cache' command. support options: "
            "vschema | vdata | imp_data | kvtable | kvdata | imp_schema | imp_kv | namespace"},
#endif
        {IMP_OPTION_ARG_FILE_PATH, "<file_path>", "import json string from a specific file"},
        {IMP_OPTION_ARG_FILE_SIZE_LIMIT, "<file_size_limit>",
            "specific the maximum size(unit: Mb) of the file that can be imported"},
        {IMP_OPTION_ARG_TABLE_NAME, "<label_name>", "inserts data into a specified table"},
        {IMP_OPTION_ARG_RES_POOL_NAME, "<respool_name>", "resource pool name to be bound"},
        {IMP_OPTION_ARG_EXT_RES_POOL_NAME, "<external_respool_name>", "external resource pool name"},
        {IMP_OPTION_ARG_INPUT_PATH, "<restore_input_path>", "restore backup config file input path"},
        {IMP_OPTION_ARG_OUTPUT_PATH, "<restore_output_path>", "restore config file output path"},
        {DB_OPTION_ARG_NAMESPACE, "<namespace_name>", "namespace of the correspond label or resource"},
        {DB_OPTION_ARG_USER_NAME, "<user_name>", "user name for connection"},
        {DB_OPTION_ARG_DOMAIN_NAME, "<server_locator>", "serverLocator for connection"},
#ifdef FEATURE_DATALOG
        {IMP_OPTION_ARG_UNINSTALL_SO, "<so_name>", "uninstall specific datalog so"},
#endif        
        {DB_OPTION_ARG_RELIABILITY_CHECK, "<reliability_check>",
            "the file reliability will be checked when importing file"},
        {IMP_OPTION_ARG_UPGRADE_SO, "<so_name>", "install specific patch datalog so"},
        {IMP_OPTION_ARG_ROLLBACK_UPGRADE_SO, "<so_name>", "install specific rollback patch datalog so"},
        {DB_OPTION_ARG_PRINT_QUICK_LOG, "", "only print unsuccessful log"},
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
        {DB_OPTION_ARG_THREAD_NUM, "<thread_num>", "export bin data with thread num"},
#endif
        {IMP_OPTION_ARG_DTL_DISTRIBUTE, "", "Switch to distribute mode when load or unload datalog so"},
        {IMP_OPTION_ARG_IGNORE_SELECTION, "", "Ignore selection. Only support high_ver_data"},
        };

    DbPrintManual("[OPTION]", importOptionManual, (int32_t)ELEMENT_COUNT(importOptionManual), NULL);
    return GMERR_OK;
}
// clang-format on

Status GmImpGetVersion(DbOptionRuleT *optionRule, ImportOptionT *toolOption)
{
    DB_UNUSED(optionRule);
    DB_UNUSED(toolOption);
    const char *gmimportVersion[][MANUAL_COL_NUM] = {
        {"gmimport GMDBV5", "", ""},
        {"Copyright (c) Huawei Technologies Co.", "", ""},
    };
    DbPrintManual("[VERSION]", gmimportVersion, (int32_t)ELEMENT_COUNT(gmimportVersion), NULL);
    return GMERR_OK;
}

#ifdef __cplusplus
}
#endif
