/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: v1 dbknl fi header file
 * Author:
 * Create: 2025-2-22
 */
#ifndef GMIMPORT_V1_DBKNL_FI_H
#define GMIMPORT_V1_DBKNL_FI_H

#include "v1_def.h"
#include "v1_rsm_init.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

/* Macro to specify the current record is allocated */
#define DBFI_RECNO_ALLOC (T_RECNO)(-2)

/* Number of records that can be stored in a TTree node */
#define DBFI_TTREENODE_SIZE 100

/* Depths of TTree */
#define DBFI_TTREELEVEL_MAX 40

#define DBKNL_THREE_BYTE 24

#define DBMM_PAGE_EXTEND_COUNT 4

#define DBKNL_MASK_BYTE_MSB 0x80
#define DBKNL_MASK_BYTE_7LSB 0x7F

#define DBRK_GET_CURRENT_TASK_ID() ((VOS_UINT32)0)
#define DBTP_INVALID_TRANSBLK (VOS_NULL_WORD)

#define DBFI_GET_RECORD_NO(ucBlkNo, ulBlkIndex, ulRecNo) \
    do {                                                 \
        ulRecNo = ucBlkNo;                               \
        ulRecNo = ((ulRecNo) << DBKNL_THREE_BYTE);       \
        ulRecNo = ((ulRecNo) | (ulBlkIndex));            \
    } while (0)

#define DBFI_HASH_BUCKET_SLOTS_BITS 5

/* 1 << DBFI_HASH_BUCKET_SLOTS_BITS */
#define DBFI_HASH_BUCKET_SLOTS 32

/* TTree node border line */
#define DBFI_TTREENODE_BORDERLINE (DBFI_TTREENODE_SIZE - 2)

#define DBFI_LOGBASE2 2.0
#define DBFI_POWER_TWO 2.0
#define DBFI_PERCENTAGE 100

/* For DML Special Operation handling */
typedef enum TagDbdmlOperationType {
    DBDML_NOSPL_OP = 0,
    DBDML_SPL_UPDATE_OP = 1,
    DBDML_SPL_DELETE_OP = 2,

    DBDML_OP_TYPE_BUTT
} DBDML_OP_TYPE;

/* Status Enumeration for Comm Area */
typedef enum TagDbtpStatusEnum { DBTP_STATUS_USED = 0, DBTP_STATUS_IDLE = 1 } DBTP_STATUS_ENUM;

/* Macro to denote that all records are to be obtained                        */
#define DB_ALL_RECS 0

/* Calculate the number of extend blocks as per the max record */
#define DBFI_CALC_EXTENDBLOCKNUM(pstRelDes, pstExtndInfo, ulNewMaxRec)     \
    do {                                                                   \
        VOS_UINT32 ulExtRecNum;                                            \
        VOS_UINT16 usBlkNum = 1;                                           \
        ulExtRecNum = (ulNewMaxRec) - (pstRelDes)->nCurRecMax;             \
        if (ulExtRecNum > DBFI_DATA_MAX_BLKSIZE) {                         \
            usBlkNum = (VOS_UINT16)(ulExtRecNum / DBFI_DATA_MAX_BLKSIZE);  \
            if (VOS_NULL != ulExtRecNum % DBFI_DATA_MAX_BLKSIZE) {         \
                usBlkNum++;                                                \
            }                                                              \
        }                                                                  \
        (pstExtndInfo)->usNewBlkNum = (pstRelDes)->usNumDIBlks + usBlkNum; \
        (pstExtndInfo)->ulExtRecNum = ulExtRecNum;                         \
    } while (0)

#define DB_ALLOC_CDBBITBLKMGR(pstDBMgr, pstRelDes, usNumBlks)                       \
    (((VOS_NULL_PTR != (pstDBMgr)->pstCDBBlkHook) &&                                \
         (VOS_NULL_PTR != (pstDBMgr)->pstCDBBlkHook->pfnAllocCDBBitBlkMgr)) ?       \
            (pstDBMgr)->pstCDBBlkHook->pfnAllocCDBBitBlkMgr(pstRelDes, usNumBlks) : \
            DB_SUCCESS)

#define DB_FREE_CDBBITBLKMGR(pstDBMgr, pstRelDes, bFlag)                       \
    (((VOS_NULL_PTR != (pstDBMgr)->pstCDBBlkHook) &&                           \
         (VOS_NULL_PTR != (pstDBMgr)->pstCDBBlkHook->pfnFreeCDBBitBlkMgr)) ?   \
            (pstDBMgr)->pstCDBBlkHook->pfnFreeCDBBitBlkMgr(pstRelDes, bFlag) : \
            (VOS_VOID)0)

#pragma pack(4)

/* Data block Address array structure                                         */
typedef struct tagDBFI_DATABLKADDR_STRU {
    DB_MEMBLK_STRU stBlock; /* Info about the Data block                   */
    T_SIZE nRecCnt;         /* Number of records in the block              */
    T_OFFSET offRecBlk;     /* Offset to Record data of current block      */
} DBFI_DATABLKADDR_STRU;

/* Structure to hold the common input parameters of all TTree/Hash operations */
typedef struct tagDBFI_COMMONPARA {
    DBDDL_RELDESTBL_STRU *pstRelDes;
    DBFI_DATABLKADDR_STRU *pstBlkAry;
    DBFI_SEGHEAD_STRU *pstSegHead;
    VOS_UINT8 ucCkpt;
    T_INDEX iRevisitIdxID;
    VOS_UINT8 aucReserve[2]; /* 2 represent 2 reserve bytes for alignment */
} DBFI_COMMONPARA;

/* TTree node structure */
typedef struct tagDBFI_TTREENODE_STRU {
    VOS_UINT8 ucCurIdxNum;
    VOS_INT8 scBalance;
    VOS_UINT8 aucReserve[2]; /* 2 represent 2 reserve bytes for alignment */
    T_NODE nodeParent;
    T_NODE nodeLChild;
    T_NODE nodeRChild;
    T_RECNO arecDataNo[DBFI_TTREENODE_SIZE];
} DBFI_TTREENODE_STRU;

typedef struct tagDBFI_EXTENDPARA_STRU {
    VOS_UINT32 ulHashDirSize;
    VOS_UINT32 ulExtRecNum;
    VOS_UINT32 ulTotHashBuk;
    VOS_UINT32 ulTotTTREENode;
    VOS_UINT32 ulNodePerIndex;
    VOS_UINT32 ulMaxBlkSize;
    T_OFFSET offTTree;
    T_OFFSET offHash;
    VOS_UINT16 usNewBlkNum;
    VOS_UINT8 ucAllocDepth;
    VOS_UINT8 isRDBRelation;
    DBFI_SEGHEAD_STRU stDataSeg;
    DB_MEMBLK_STRU stMemBlk;
    DBRSM_MEMBLK_DIRECTORY_STRU *pMDBArray[DBFI_DATA_MAX_BLKCNT];
} DBFI_EXTENDPARA_STRU;

/* Structure to hold the parameters of RecSearch in TTree operation */
typedef struct tagDBFI_TTREE_RECSEARCHPARA {
    DBDDL_SITECOND_STRU *pstIndexCond;
    VOS_UINT32 *pulDestNode;
    VOS_UINT32 *pulRecNo;
} DBFI_TTREE_RECSEARCHPARA;

/* Query Item Structure for DML                                               */
typedef struct tagDB_QUERYITEM_STRU {
    T_FIELD fFieldNo;
    VOS_UINT8 aucReserve[3]; /* 3 represent 3 reserve bytes for alignment */
    DB_OPTYPE_ENUM enOp;
    VOS_UINT8 aucValue[DB_ELELEN_MAX];
} DB_QUERYITEM_STRU;

/* Query Structure for DML                                                    */
typedef struct tagDB_QUERY_STRU {
    VOS_UINT8 ucCondNum;
    VOS_UINT8 aucReserve[3]; /* 3 represent 3 reserve bytes for alignment */
    DB_QUERYITEM_STRU *pConditions;
} DB_QUERY_STRU;

/* Field Set Structure                                                        */
typedef struct tagDB_FIELDSET_STRU {
    VOS_UINT8 ucFieldNum;
    VOS_UINT8 aucReserve[3]; /* 3 represent 3 reserve bytes for alignment */
    T_FIELD *pFields;
} DB_FIELDSET_STRU;

/* Field Item Structure                                                       */
typedef struct tagDBDDL_FLDITEM_STRU {
    T_FIELD fFldD;
    VOS_UINT8 ucIsVarFld;
    VOS_UINT8 ucPos;
    VOS_UINT8 ucDataType;
    VOS_UINT16 usDefLen;
    VOS_UINT16 usLen;
    T_OFFSET offField;
    DBTC_OPERATION_FUNC *pafnCompareFunc;
} DBDDL_FLDITEM_STRU;

/* Site Field Set Structure                                                   */
typedef struct tagDBDDL_SITEFLDSET_STRU {
    VOS_UINT8 ucFieldNum;
    VOS_UINT8 ucVarFldExist;
    VOS_UINT8 aucReserve[2]; /* 2 represent 2 reserve bytes for alignment */
    DBDDL_FLDITEM_STRU astField[DB_FLDOFREL_MAX];
} DBDDL_SITEFLDSET_STRU;

typedef struct tagDBDDL_SITESORT_STRU {
    VOS_UINT32 enSortType;
    DBDDL_SITEFLDSET_STRU stSiteFldSet;
} DBDDL_SITESORT_STRU;

/* Store details of a perticular result segment result number */
typedef struct tagDBFI_RSLTPOS_STRU {
    VOS_UINT32 ulRsltSegNo; /* Result segment number                         */
    VOS_UINT32 ulRsltNo;    /* Result number in the segment                  */
} DBFI_RSLTPOS_STRU;

/* Result segment structure */
typedef struct tagDBFI_RSLTSEG_STRU {
    VOS_UINT32 ulNext;             /* Next seg ID if any                   */
    VOS_UINT32 ulPre;              /* Previous seg ID if any               */
    T_SIZE nCurRecNum;             /* Current result number                */
    VOS_UINT32 enStatus;           /* Segment allocation status            */
    T_RECNO arecNo[DBFI_RSLT_MAX]; /* Data segment record numbers          */
} DBFI_RSLTSEG_STRU;

/* Result segment manager structure */
typedef struct tagDBFI_RSLTSEGMNG_STRU {
    VOS_UINT32 nRsltSegNum;        /* Now result segments size               */
    VOS_UINT32 nRsltSegStep;       /* Size for each step extends             */
    T_SIZE nIdleNum;               /* Free segment number                    */
    VOS_UINT32 ulFreeList;         /* Free list header pointer               */
    DBFI_RSLTSEG_STRU *pstRsltSeg; /* Pointer to result segments             */
} DBFI_RSLTSEGMNG_STRU;

typedef struct tagDBFI_RSLTSETHEAD_STRU DBFI_RSLTSETHEAD_STRU;

/* The function pointer for variable length record support */
typedef DB_ERR_CODE (*DBFI_RECORD_MODIFIEDLIST)(DBFI_RSLTSETHEAD_STRU *pstHead, T_RECNO recNo);

/* Result set header */
struct tagDBFI_RSLTSETHEAD_STRU {
    DBDDL_RELDESTBL_STRU *pstRelDes;     /* Relation descriptor              */
    VOS_UINT32 ulRsltSegHead;            /* Result set header segment ID     */
    VOS_UINT32 ulRsltSegTail;            /* Result set last segment ID       */
    T_SIZE nCurRsltNum;                  /* Current result number            */
    T_SIZE nInvalidRecNum;               /* Invalid record number            */
    DBDDL_SITESORT_STRU stSiteSort;      /* Result sort detail structure     */
    DBFI_RSLTPOS_STRU stCurRsltPos;      /* Current result position          */
    DBFI_RSLTSEGMNG_STRU *pstRsltSegMgr; /* Result segment manager           */
    VOS_UINT32 ulReqRecNo;               /* Number of records to be selected */
    VOS_UINT32 ulCDBId;                  /* To mark the RDB record as deleted */
    T_OFFSET offRDBValFld;               /* Offset to RDB validity check fld */
    VOS_UINT32 ulFirstValidRec;
    VOS_VOID *pRecordList;               /* Record list, also reused for storing bit map array of CDB.    */
    DBDDL_SITESORT_STRU *pstSiteSort;    /* pointer to Sort set for CDB&RDB record merge    */
    DBFI_RECORD_MODIFIEDLIST pfnModList; /* Function pointer to add record   */
    DBDML_OP_TYPE enSplOp;               /* Used for Update/Delete Operation */
    DB_DBMS_STRU *pstDBMgr;
    VOS_UINT16 usRelId;
};

/* Comm. Area Structure */
typedef struct tagDBTP_COMMAREA_STRU {
    DBFI_RSLTSETHEAD_STRU stRsltSetHead;
    DBDDL_SITECOND_STRU stIndexCond;
    DBDDL_SITECOND_STRU stFilterCond;
    DBDDL_SITEFLDSET_STRU stSiteFldSet;
    DBDDL_RELDESTBL_STRU *pstRelDes;
    T_INDEX iIndexID;
    VOS_UINT8 aucReserve[3]; /* 3 represent 3 reserve bytes for alignment */
    VOS_UINT32 ulDbId;
    DBTP_STATUS_ENUM enStatus;
    VOS_UINT16 usNext;
    VOS_UINT8 ucConflict;
    VOS_UINT8 ucWrite;
    VOS_UINT32 ulTaskId;
} DBTP_COMMAREA_STRU;

/* Comm. Area Manager Structure */
typedef struct tagDBTP_COMMAREAMNG_STRU {
    T_SIZE nIdleCommNum;
    DBTP_COMMAREA_STRU *pstCommArea;
    VOS_UINT16 usFreeList;
    VOS_UINT8 aucReserve[2]; /* 2 represent 2 reserve bytes for alignment */
} DBTP_COMMAREAMNG_STRU;

/* Transaction Block Structure */
typedef struct tagDBTP_TRANSBLK_STRU {
    DBCP_CONTEXT_STRU stContext;
    VOS_UINT16 usCommAreaList;
    VOS_UINT16 usTransID;
    VOS_UINT32 ulRecBufLen;
    DBTP_COMMAREAMNG_STRU stCommAreaMgr;
    DBFI_RSLTSEGMNG_STRU stRsltSegMgr;
    VOS_UINT8 *pucRecBuf1;
    VOS_UINT8 *pucRecBuf2;
    VOS_UINT16 usNext;
    VOS_UINT8 aucReserve[2]; /* 2 represent 2 reserve bytes for alignment */
} DBTP_TRANSBLK_STRU;

/* CP env access type Read/Write or None */
typedef enum tagDBCP_ACCESSTYPE_ENUM {
    DBCP_ACCESSTYPE_NONE = 0,
    DBCP_ACCESSTYPE_READ = 1,
    DBCP_ACCESSTYPE_WRITE = 2
} DBCP_ACCESSTYPE_ENUM;

/* CP Environment Wait Type Enumeration                                       */
typedef enum tagDBCP_WAITTYPE_ENUM {
    DBCP_WAITTYPE_NONE = 0,
    DBCP_WAITTYPE_READ = 1,
    DBCP_WAITTYPE_WRITE = 2,
    DBCP_WAITTYPE_UPGRADE = 3
} DBCP_WAITTYPE_ENUM;

/* Status Enumeration */
typedef enum TagDbfiStatusEnum {
    DBFI_STATUS_USED = 0, /* Result segment is used                      */
    DBFI_STATUS_IDLE = 1, /* Result segment is available                 */
    DBFI_STATUS_BUTT
} DBFI_STATUS_ENUM;

/* Transaction Control Structure */
typedef struct tagDBTP_TRANSCTRL_STRU {
    VOS_UINT32 ulMagicNum;            /* Magic number               */
    VOS_SEMA_T stLock;                /* Transaction manager lock */
    DBTP_TRANSBLK_STRU **ppstTRANScb; /* Array of pointer to Transaction blocks */
    VOS_UINT32 ulMaxBufLen;           /* Max temporary buffer length    */
    VOS_BOOL bTransAutoCompress;      /* Value of suto cpmpress switch  */
    VOS_UINT16 usFreeList;            /* Free list of transaction blocks  */
    VOS_UINT16 usUsedTransCnt;        /* Number of Transactions in use    */
    VOS_UINT16 usAllocTransCnt;       /* Number of Transactions allocated */
    VOS_UINT16 usMaxTransCnt;         /* Size of the Transaction pool     */
} DBTP_TRANSCTRL_STRU;

#pragma pack()

#define DBTP_DBG_INIT_PLAN_LOG_MEM(pstTransBlk)

#define DBFI_GET_BLK_INDEX(ulRecNo, ucBlkNo, ulBlkIndex)      \
    do {                                                      \
        ucBlkNo = (VOS_UINT8)((ulRecNo) >> DBKNL_THREE_BYTE); \
        ulBlkIndex = ((ulRecNo)&DBKNL_MASK_THREE_BYTE);       \
    } while (0)

#define DBFI_GET_REC_BLK_NO(ulRecNo, ucBlkNo)                 \
    do {                                                      \
        ucBlkNo = (VOS_UINT8)((ulRecNo) >> DBKNL_THREE_BYTE); \
    } while (0)

/* Macro to initialize the common input parameters of TTree/Hash operations */
#define DBFI_INIT_COMMON_PARA(pstRelDesc, offSegHead, pstCommPara)                           \
    do {                                                                                     \
        (pstCommPara)->pstRelDes = pstRelDesc;                                               \
        (pstCommPara)->ucCkpt = (pstRelDesc)->ucCkpt;                                        \
        (pstCommPara)->pstBlkAry = DBMM_GET_DATA_BLK_ADDR_ARY(pstRelDesc);                   \
        (pstCommPara)->pstSegHead = DBMM_GET_SEG_HEAD((pstCommPara)->pstBlkAry, offSegHead); \
        (pstCommPara)->iRevisitIdxID = DBKNL_NULL_BYTE;                                      \
    } while (0)

#define DB_REL_CHECKSUM_GET(pstRelDes) \
    (((DB_TBL_FEATURE_MGR *)((pstRelDes)->pTblRegFeatures.pPtr))->apRegFeatures[DB_TBL_CHKSUM_PTR])

#define DB_REL_META_CHECKSUM_GET(pstRelDes) ((DB_RELCHECKSUM_DATA *)DB_REL_CHECKSUM_GET(pstRelDes))->ulRelMetaChecksum

#define DB_REL_DATA_CHECKSUM_GET(pstRelDes) ((DB_RELCHECKSUM_DATA *)DB_REL_CHECKSUM_GET(pstRelDes))->ulRelDataChecksum

#define DBCKSM_ADD(pstChksumCfg, pSeedValue, record, recSize)                                      \
    do {                                                                                           \
        if (VOS_NULL_PTR != (pstChksumCfg)) {                                                      \
            DB_CHECKSUM_T cksmData = {0, 0};                                                       \
            cksmData.ulSize = (VOS_UINT32)sizeof(DB_CKSUM_DATA_T);                                 \
            cksmData.pucCheckSum = (VOS_UINT8 *)(pSeedValue);                                      \
            (VOS_VOID)(pstChksumCfg)->pfCalcChecksum((VOS_UINT8 *)(record), (recSize), &cksmData); \
        }                                                                                          \
    } while (0)

VOS_UINT32 DBDDL_CheckOverflow(VOS_UINT32 ulSize, VOS_UINT32 ulStructNum, VOS_UINT32 ulStructSize);

VOS_VOID DBFI_TTREE_InitBlock(
    DBFI_EXTENDPARA_STRU *pstExtndInfo, DBFI_DATABLKADDR_STRU *pstBlkAry, VOS_UINT16 usNumDIBlks);

VOS_VOID DBFI_InitTableSpace(
    DBDDL_RELDESTBL_STRU *pstRelDes, DBFI_EXTENDPARA_STRU *pstExtndInfo, VOS_UINT8 ucIsRecovery);

VOS_VOID DBMM_PageListAppendPg(DBMM_PAGEMGR_STRU *pstPgMgr, DBMM_VARSEGPAGE_STRU *pstVarPgMap);

DB_ERR_CODE DBFI_GetTTreeNodeAddr(
    const DBFI_COMMONPARA *pstCommPara, T_NODE nodeId, DBFI_TTREENODE_STRU **ppstTTreeNode);

T_SIZE DBFI_DATA_CalcDISize(DBDDL_RELDESTBL_STRU *pstRelDes, DBFI_EXTENDPARA_STRU *pstExtndInfo, VOS_UINT16 usBlk);

VOS_VOID DBTP_SetRstSegForInit(DBFI_RSLTSEG_STRU *pstRstSeg, VOS_UINT32 ulRsltSegNo);

DB_ERR_CODE DBFI_RsltSetInsert(DBFI_RSLTSETHEAD_STRU *pstHead, T_RECNO recNo);

DB_ERR_CODE DML_StartTransactionEx(
    VOS_UINT32 ulDbId, VOS_UINT16 usRelID, VOS_UINT8 ucWrite, DBTP_TRANSBLK_STRU **ppstTransBlk);

VOS_VOID DML_RollbackTransactionEx(DBTP_TRANSBLK_STRU *pstTransBlk);
VOS_VOID DML_CommitTransactionEx(DBTP_TRANSBLK_STRU *pstTransBlk);

VOS_VOID DBMM_UpdateMemBlkSize(
    DBFI_EXTENDPARA_STRU *pstExtndInfo, T_SIZE nMemorySize, VOS_VOID *pPtr, VOS_UINT32 *pulFOffset);

DB_ERR_CODE DBTP_AllocTransaction(VOS_VOID);

DB_ERR_CODE DBFI_ReGenerateIndex(DBDDL_RELDESTBL_STRU *pstRelDes, VOS_UINT8 *pucIdxAffectList);

DB_ERR_CODE DBFI_DATA_UpdateReplayVarRecord(
    DBDDL_RELDESTBL_STRU *pstRelDes, T_RECNO recNo, VOS_VOID *pNewRecord, VOS_BOOL bIsRcvrFlow);

VOS_UINT8 *DBFI_DATA_GetRecord(DBDDL_RELDESTBL_STRU *pstRelDes, T_RECNO recNo);

VOS_VOID DBFI_UpdateRecordRcvr(DBDDL_RELDESTBL_STRU *pstRelDes, T_RECNO recNo, VOS_VOID *pNewRecord, VOS_BOOL bIsTPCDB);

VOS_VOID DBFI_InsertRecordRcvr(
    DB_DBMS_STRU *pstDBMgr, DBDDL_RELDESTBL_STRU *pstRelDes, VOS_VOID *pRecord, T_RECNO *precNo);

VOS_VOID DBFI_DeleteRecordRcvr(DB_DBMS_STRU *pstDBMgr, DBDDL_RELDESTBL_STRU *pstRelDes, T_RECNO recNo);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
