/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: import v1 utils source file
 * Author:
 * Create: 2025-2-22
 */

#include "v1_utils.h"

VOS_UINT16 g_ausDBCrcTbl16[DB_CRC_TBL_SIZE] = {0};

VOS_BOOL g_bIsCRCTblInit = VOS_FALSE;

VOS_VOID DB_InitCrcTable(VOS_VOID)
{
    VOS_UINT32 ulIndex;
    VOS_UINT32 ulTemp;
    VOS_UINT32 ulValue;
    VOS_UINT32 ulTopbit = DB_BITMASK(DB_CRC_16 - 1);

    for (ulIndex = 0; ulIndex < DB_CRC_TBL_SIZE; ulIndex++) {
        ulValue = (ulIndex << (DB_CRC_16 - DBKNL_BYTE));

        for (ulTemp = 0; ulTemp < DBKNL_BYTE; ulTemp++) {
            if ((ulValue & ulTopbit) != 0) {
                ulValue = (ulValue << DBKNL_SHIFT_ONEBITS) ^ DB_CRC_POLY_16;
            } else {
                ulValue <<= DBKNL_SHIFT_ONEBITS;
            }
        }

        /* Mask higher order 16 bit */
        g_ausDBCrcTbl16[ulIndex] = (VOS_UINT16)(ulValue & DB_WIDTHMASK(DB_CRC_16));
    }

    /* Set ths global variable to 1 so that it will not get initialised again */
    g_bIsCRCTblInit = VOS_TRUE;

    return;
}

VOS_UINT32 DB_MakeCrc16bit(VOS_UINT8 *pucBuffer, VOS_UINT32 ulCountPara, VOS_UINT16 *pusCrc)
{
    VOS_UINT16 usReg;
    VOS_UINT32 ulCount = ulCountPara;

    DB_ASSERT(pusCrc != VOS_NULL_PTR);
    DB_ASSERT(pucBuffer != VOS_NULL_PTR);

    /* If the CRC table is not initialized then initialize the CRC table */
    if (g_bIsCRCTblInit == VOS_FALSE) {
        DB_InitCrcTable();
    }

    /* Modified to initialize usReg with initial CRC value instead of zero */
    usReg = *pusCrc;

    while (ulCount-- != 0) {
        usReg = (VOS_UINT16)((usReg << DBKNL_BYTE) ^ g_ausDBCrcTbl16[(usReg >> DBKNL_BYTE) ^ *(pucBuffer++)]);
    }

    *pusCrc = usReg;

    return DB_SUCCESS;
}

DbMemCtxT *g_gmdbImpV1RsmMemCtx = NULL;
void SetImpV1RsmMemCtx(DbMemCtxT *memCtx)
{
    g_gmdbImpV1RsmMemCtx = memCtx;
}

DbMemCtxT *GetImpV1RsmMemCtx(void)
{
    return g_gmdbImpV1RsmMemCtx;
}

uint32_t g_ulDBTPLogBufLen = 1048576;
uint32_t GetImpV1LogBufLen(void)
{
    return g_ulDBTPLogBufLen;
}

void SetImpV1LogBufLen(uint32_t bufLen)
{
    g_ulDBTPLogBufLen = bufLen;
}

VOS_VOID DB_ModuleLock(VOS_VOID)
{
    return;
}
VOS_VOID DB_ModuleUnLock(VOS_VOID)
{
    return;
}

VOS_VOID DBTP_AllocRecBuffForFreelist(VOS_UINT32 ulLen)
{
    return;
}

typedef union {
    uint32_t value;
    uint8_t valueByte;
} EndianDetectT;

#define DB_ENDIAN_DETECT_VALUE 0x1
VOS_UINT8 DB_GetByteOrder(VOS_VOID)
{
    EndianDetectT endianDetect = {.value = DB_ENDIAN_DETECT_VALUE};
    if (endianDetect.valueByte == DB_LITTLE_ENDIAN) {
        return DB_LITTLE_ENDIAN;
    }
    return DB_BIG_ENDIAN;
}
