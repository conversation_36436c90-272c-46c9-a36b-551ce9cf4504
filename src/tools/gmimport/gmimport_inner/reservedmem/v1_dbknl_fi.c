/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: v1 dbknl fi source file
 * Author:
 * Create: 2025-2-27
 */

#include <math.h>
#include "v1_dbknl_fi.h"
#include "v1_dbknl_mem.h"
#include "v1_dbknl.h"
#include "v1_utils.h"
#include "gmimport_file.h"
#include "gmimport_utils.h"
#include "db_v1_file_util.h"
#include "tool_utils.h"
#include "v1_rsm_init.h"

#define DBKNL_HALF_BYTE 4
/* This MACRO finds the total memory required for allocating in terms of 4 bytes alignment */
#define DBMM_ALIGN_4_BYTES(nMemBlkSize)                                                                    \
    (((nMemBlkSize) % DBKNL_HALF_BYTE) != 0 ?                                                              \
            DBDDL_CheckOverflow((nMemBlkSize), 1, (DBKNL_HALF_BYTE - ((nMemBlkSize) % DBKNL_HALF_BYTE))) : \
            (nMemBlkSize))

#define DBCKSM_DEL(pstChksumCfg, pSeedValue, record, recSize)                                        \
    do {                                                                                             \
        if (VOS_NULL_PTR != (pstChksumCfg)) {                                                        \
            DB_CHECKSUM_T cksmData = {0, 0};                                                         \
            cksmData.ulSize = (VOS_UINT32)sizeof(DB_CKSUM_DATA_T);                                   \
            cksmData.pucCheckSum = (VOS_UINT8 *)(pSeedValue);                                        \
            (VOS_VOID)(pstChksumCfg)->pfNegateChecksum((VOS_UINT8 *)(record), (recSize), &cksmData); \
        }                                                                                            \
    } while (0)

#define DB_TRANSNUM_DEFAULT 10
#define DB_COMMAREANUM_DEFAULT 10
#define DBTP_COMM_MAX DB_COMMAREANUM_DEFAULT
#define DB_RSLTSEGNUM_DEFAULT 200
#define DB_RSLTSEGSTEP_DEFAULT 200

#define DB_INVALID_TXNID (VOS_NULL_LONG)

#define DBTP_MAGICNO 0xdeadface
DBTP_TRANSCTRL_STRU g_stTransCtrl = {0};
DBTP_TRANSCTRL_STRU *DB_GetTransCtrlRef(VOS_VOID)
{
    static VOS_BOOL blLogSwitch = VOS_FALSE;

    if (g_stTransCtrl.ulMagicNum != DBTP_MAGICNO) {
        if (blLogSwitch == VOS_FALSE) {
            blLogSwitch = VOS_TRUE;
        }
    }
    return &g_stTransCtrl;
}

VOS_UINT16 g_usDBTransNum = DB_TRANSNUM_DEFAULT;

VOS_UINT32 DBDDL_CheckOverflow(VOS_UINT32 ulSize, VOS_UINT32 ulStructNum, VOS_UINT32 ulStructSize)
{
    /*
     * A71101: Optimized the logic with below method
     * Method (Fast multiplication)
     * 1. Let the numbers be x1, x2
     * 2. take the lowest as the LOOPCOUNT and other as ADDENT
     * 3. if the LOOPCOUNT is odd then add the ADDENT to RESULT
     * 4. calculate ADDENT = ADDENT *2
     * 5. calculate LOOPCOUNT = LOOPCOUNT / 2
     * 6. repeat 3,4,5 until the LOOPCOUNT  becomes zero.
     * 7. Now RESULT contains the product of x1 and x2
     */
    VOS_UINT32 ulSum = ulSize;
    VOS_UINT32 ulLoopVar;
    VOS_UINT32 ulAddent;
    VOS_UINT32 ulNewAddent;

    /* Take the lowest number for loop count */
    if (ulStructSize < ulStructNum) {
        ulLoopVar = ulStructSize;
        ulAddent = ulStructNum;
    } else {
        ulLoopVar = ulStructNum;
        ulAddent = ulStructSize;
    }

    do {
        if ((ulLoopVar & 1) != 0) {
            ulSum += ulAddent;
            if (ulSum < ulAddent) {
                DBDBG_STANDARD_REPT("DBDDL_CheckOverflow", "overflow occurred");
                return VOS_NULL_LONG;
            }
        }

        ulLoopVar = ulLoopVar >> DBKNL_SHIFT_ONEBITS; /* Divide by 2 */
        if (ulLoopVar != 0) {
            ulNewAddent = ulAddent << DBKNL_SHIFT_ONEBITS; /* Multiply by 2 */
            if (ulNewAddent < ulAddent) {
                DBDBG_STANDARD_REPT("DBDDL_CheckOverflow", "overflow occurred");
                return VOS_NULL_LONG;
            }

            ulAddent = ulNewAddent;
        }
    } while (ulLoopVar != 0);

    return ulSum;
}

VOS_VOID DBMM_UpdateMemBlkSize(
    DBFI_EXTENDPARA_STRU *pstExtndInfo, T_SIZE nMemorySize, VOS_VOID *pPtr, VOS_UINT32 *pulFOffset)
{
    DB_ASSERT(pstExtndInfo != VOS_NULL_PTR);
    DB_ASSERT(pulFOffset != VOS_NULL_PTR);

    if (pstExtndInfo->ulMaxBlkSize < nMemorySize) {
        pstExtndInfo->ulMaxBlkSize = nMemorySize;
    }

    pstExtndInfo->stMemBlk.pMemPtr.pPtr = pPtr;
    pstExtndInfo->stMemBlk.ulBlkSize = nMemorySize;
    pstExtndInfo->stMemBlk.ulFOffset = *pulFOffset;
    *pulFOffset += nMemorySize;
}

T_SIZE DBFI_DATA_CalcDISize(DBDDL_RELDESTBL_STRU *pstRelDes, DBFI_EXTENDPARA_STRU *pstExtndInfo, VOS_UINT16 usBlk)
{
    T_SIZE nMemSize;
    DBFI_DATABLKADDR_STRU *pstBlkAry = VOS_NULL_PTR;

    DB_ASSERT(VOS_NULL_PTR != pstExtndInfo);
    DB_ASSERT(VOS_NULL_PTR != pstRelDes);

    pstBlkAry = (DBFI_DATABLKADDR_STRU *)pstExtndInfo->stMemBlk.pMemPtr.pPtr;

    /* Calculate the memory required for alloc array */
    nMemSize = pstBlkAry[usBlk].nRecCnt * (VOS_UINT32)sizeof(VOS_UINT32);
    pstBlkAry[usBlk].offRecBlk = nMemSize;

    /* Calculate memory required to store RECORDS in Data/Index block */
    nMemSize = DBDDL_CheckOverflow(nMemSize, pstBlkAry[usBlk].nRecCnt, pstRelDes->stDataSegHead.ulElemSize);
    if (VOS_NULL_LONG == nMemSize) {
        return VOS_NULL_LONG;
    }

    /* Makiing the size to 4 byte aligned */
    nMemSize = DBMM_ALIGN_4_BYTES(nMemSize);
    if (VOS_NULL_LONG == nMemSize) {
        return VOS_NULL_LONG;
    }

    pstBlkAry[usBlk].stBlock.ulBlkSize = nMemSize;

    return nMemSize;
}

VOS_VOID DBFI_TTREE_InitBlock(
    DBFI_EXTENDPARA_STRU *pstExtndInfo, DBFI_DATABLKADDR_STRU *pstBlkAry, VOS_UINT16 usNumDIBlks)
{
    VOS_UINT32 ulBlkElemCnt;
    VOS_UINT32 ulBlkIdx;
    VOS_UINT32 ulCnt;
    VOS_UINT16 usOldBlkNum = usNumDIBlks;
    DBFI_SEGHEAD_STRU *pstSegHead = VOS_NULL_PTR;
    DBFI_INDEXBLK_STRU *pstTTreeIdxBlk = VOS_NULL_PTR;
    DBFI_TTREENODE_STRU *pstTTreeNode = VOS_NULL_PTR;

    DB_ASSERT(VOS_NULL_PTR != pstExtndInfo);
    DB_ASSERT(VOS_NULL_PTR != pstBlkAry);

    /* Getting the Common header for TTree index block */
    pstSegHead = DBMM_GET_SEG_HEAD(pstBlkAry, pstExtndInfo->offTTree);

    pstTTreeIdxBlk = DBMM_GET_IDX_BLK_ADDR_ARY(pstSegHead);

    ulBlkElemCnt = pstTTreeIdxBlk[usOldBlkNum].nElemCnt;

    if (ulBlkElemCnt != 0) {
        pstTTreeNode = DBMM_GET_IDX_BLK_BY_OFFSET(pstBlkAry, usOldBlkNum, pstTTreeIdxBlk[usOldBlkNum].offIdxBlk);

        /* Get the first element number in the block */
        ulBlkIdx = (VOS_UINT32)(usOldBlkNum << DBKNL_THREE_BYTE);
        ulBlkIdx++;

        /* Initialize the alloc array of the given block */
        for (ulCnt = 0; ulCnt < ulBlkElemCnt; ulCnt++, ulBlkIdx++) {
            /* Link the current element to next element */
            pstTTreeNode[ulCnt].nodeLChild = ulBlkIdx;
            pstTTreeNode[ulCnt].nodeRChild = ulBlkIdx;
        }

        /* Set the last element point to the first element of the next block */
        pstTTreeNode[ulCnt - 1].nodeLChild = pstSegHead->ulFreeList;
        pstTTreeNode[ulCnt - 1].nodeRChild = pstSegHead->ulFreeList;

        pstSegHead->ulFreeList = (VOS_UINT32)(usOldBlkNum << DBKNL_THREE_BYTE);

        /* Set the free element count in the segment header */
        pstSegHead->ulFreeElemCnt += ulBlkElemCnt;
    }

    return;
}

VOS_VOID DBFI_InitTreeIndexSeg(DBDDL_RELDESTBL_STRU *pstRelDes, DBFI_EXTENDPARA_STRU *pstExtndInfo,
    DBDDL_IDXDESTBL_STRU *pstIdxDes, VOS_UINT8 ucIsRecovery)
{
    DB_POINTER3(pstRelDes, pstExtndInfo, pstIdxDes);
    DBFI_TTREESEG_STRU *pstTTreeSeg = &pstIdxDes->unSegHead.stTTreeSeg;
    pstTTreeSeg->offTTreeHead = ((VOS_UINT32)sizeof(DBFI_DATABLKADDR_STRU) * pstExtndInfo->usNewBlkNum);

    /* If is from recover DB flow */
    if (ucIsRecovery != 0) {
        pstTTreeSeg->nodeTTreeRoot = DBKNL_NULL_NODEID;
    }
}

VOS_VOID DBFI_InitIndexSeg(DBDDL_RELDESTBL_STRU *pstRelDes, DBFI_EXTENDPARA_STRU *pstExtndInfo,
    DBDDL_IDXDESTBL_STRU *pstIdxDes, VOS_UINT8 ucIsRecovery, VOS_UINT32 *pulBlkOffset, VOS_UINT32 ulCommonOffset)
{
    DB_ASSERT(VOS_NULL_PTR != pstIdxDes);

    switch (pstIdxDes->ucType) {
        case DBDDL_INDEXTYPE_TTREE: {
            // BIM只有TTREE
            DBFI_InitTreeIndexSeg(pstRelDes, pstExtndInfo, pstIdxDes, ucIsRecovery);
            break;
        }

        default: {
            return;
        }
    }

    return;
}

VOS_VOID DBFI_InitAllIndexSeg(DBDDL_RELDESTBL_STRU *pstRelDes, DBFI_EXTENDPARA_STRU *pstExtndInfo,
    VOS_UINT8 ucIsRecovery, VOS_UINT32 *ulBlkOffset, VOS_UINT32 ulCommonOffset)
{
    DBDDL_IDXDESTBL_STRU *pstIdxDes = VOS_NULL_PTR;
    VOS_UINT8 ucIdxNo;
    DB_ASSERT(VOS_NULL_PTR != pstRelDes);

    /* Get the first index descriptor and update all the index segment headers for hash and TTree indexes */
    pstIdxDes = DBDDL_GET_IDX_DESC(pstRelDes, 0);
    for (ucIdxNo = 0; ucIdxNo < pstRelDes->ucIdxNum; ucIdxNo++, pstIdxDes++) {
        DBFI_InitIndexSeg(pstRelDes, pstExtndInfo, pstIdxDes, ucIsRecovery, ulBlkOffset, ulCommonOffset);
    }
}

VOS_VOID DBFI_InitTableSpace(
    DBDDL_RELDESTBL_STRU *pstRelDes, DBFI_EXTENDPARA_STRU *pstExtndInfo, VOS_UINT8 ucIsRecovery)
{
    VOS_UINT32 ulBlkOffset;
    VOS_UINT32 ulCommonOffset;
    VOS_INT32 lRet;

    DB_ASSERT(VOS_NULL_PTR != pstRelDes);
    DB_ASSERT(VOS_NULL_PTR != pstExtndInfo);

    /* Make the relation descriptor page dirty */
    DBMM_MARK_SEG_PAGE_DIRTY(pstRelDes->ucCkpt, pstRelDes, (VOS_UINT8 *)pstRelDes, pstRelDes->ulSchemaSize);

    /* Update the Number of DI blocks and the number of TTREE nodes per index */
    pstRelDes->usNumDIBlks = pstExtndInfo->usNewBlkNum;
    pstRelDes->ulNodePerIndex = pstExtndInfo->ulNodePerIndex;

    lRet = memcpy_s(
        &pstRelDes->stDataSegHead, sizeof(DBFI_SEGHEAD_STRU), &pstExtndInfo->stDataSeg, sizeof(DBFI_SEGHEAD_STRU));
    if (lRet != EOK) {
        /* Operation cannot continue */
        DBDBG_STANDARD_REPT("DBFI_InitTableSpace", "Init table space copy failed. Failure of memory copy");
        VOS_REBOOT_WITH_FATALERROR();
    }

    /* Update the Data Index manager */
    pstRelDes->stDataIdxMng.pMemPtr.pPtr = pstExtndInfo->stMemBlk.pMemPtr.pPtr;
    pstRelDes->stDataIdxMng.ulBlkSize = pstExtndInfo->stMemBlk.ulBlkSize;
    pstRelDes->stDataIdxMng.ulFOffset = pstExtndInfo->stMemBlk.ulFOffset;

    ulBlkOffset = ((VOS_UINT32)sizeof(DBFI_DATABLKADDR_STRU) * pstExtndInfo->usNewBlkNum);

    ulCommonOffset = (VOS_UINT32)((sizeof(DBFI_INDEXBLK_STRU) * pstExtndInfo->usNewBlkNum) + sizeof(DBFI_SEGHEAD_STRU));

    /*
     * Calculate offset to hash directory. In case of TTREE index is present
     * then need to consider the size of TTREE also
     */
    if (VOS_NULL != pstRelDes->ucTTreeIdxNum) {
        ulBlkOffset += ulCommonOffset;
    }

    DBFI_InitAllIndexSeg(pstRelDes, pstExtndInfo, ucIsRecovery, &ulBlkOffset, ulCommonOffset);

    return;
}

DB_ERR_CODE DML_StartTransactionCheckArgs(DB_DBMS_STRU *pstDBMgr, DBCP_ENV_STRU *pstCpEnv)
{
    DB_ASSERT(VOS_NULL_PTR != pstDBMgr);
    DB_ASSERT(VOS_NULL_PTR != pstCpEnv);

    /* If the DB is not opened, then should not allow any Db operations */
    if (pstCpEnv->ulOpenCount == VOS_NULL) {
        /* If the DB is deleted and the DB was not persistent, then return DB deleted error */
        if ((VOS_TRUE == pstCpEnv->ucDeleted) && (DB_CKP_NONE == pstDBMgr->ucCkpt)) {
            DBDBG_STANDARD_REPT("DML_StartTransactionCheckArgs", "this database is deleted");
            return VOS_ERRNO_DB_DATABASE_IS_DELETED;
        }

        DBDBG_STANDARD_REPT("DML_StartTransactionCheckArgs", "the database is not opened");
        return VOS_ERRNO_DB_DATABASE_NOT_OPENED;
    }

    return DB_SUCCESS;
}

VOS_VOID DBTP_SetBlkStruForGetTransInfo(
    DBTP_TRANSBLK_STRU *pstBlkStru, VOS_UINT32 ulTaskId, VOS_UINT32 ulDbId, T_RELATION rRelID)
{
    DB_ASSERT(VOS_NULL_PTR != pstBlkStru);

    /* Initialize the transaction block for the given details */
    pstBlkStru->stContext.ulTaskId = ulTaskId;
    pstBlkStru->usCommAreaList = VOS_NULL_WORD;
    pstBlkStru->stContext.ulDbId = ulDbId;
    pstBlkStru->stContext.rRelID = rRelID;
    pstBlkStru->stContext.enAccessType = DBCP_ACCESSTYPE_NONE;
    pstBlkStru->stContext.enWaitType = DBCP_WAITTYPE_NONE;
    pstBlkStru->stContext.pstNext = VOS_NULL_PTR;
}

VOS_VOID DBTP_InitTransBlkForAllocTransRes(DBTP_TRANSBLK_STRU *pstTransBlk, VOS_UINT16 usTransID)
{
    DB_ASSERT(VOS_NULL_PTR != pstTransBlk);

    /* Initialize the allocated transaction block */
    pstTransBlk->usNext = (VOS_UINT16)(usTransID + 1);
    pstTransBlk->usTransID = usTransID;
    pstTransBlk->pucRecBuf1 = VOS_NULL_PTR;
    pstTransBlk->pucRecBuf2 = VOS_NULL_PTR;
    pstTransBlk->ulRecBufLen = 0;
    pstTransBlk->usCommAreaList = VOS_NULL_WORD;
}

DB_ERR_CODE DBCP_InitContext(DBCP_CONTEXT_STRU *pstContext)
{
    DB_ASSERT(pstContext != VOS_NULL_PTR);

    /*
     * Every transaction control has a context processing structure
     * Initialize the given CP context structure for all members
     * Set the default access and wait type of context structure
     * Set the task ID, DB ID and Relation ID to the default value
     */
    pstContext->enAccessType = DBCP_ACCESSTYPE_NONE;
    pstContext->enWaitType = DBCP_WAITTYPE_NONE;
    pstContext->ulTaskId = VOS_NULL_LONG;
    pstContext->ulDbId = VOS_NULL_LONG;
    pstContext->rRelID = VOS_NULL_WORD;
    pstContext->pstNext = VOS_NULL_PTR;
    return DB_SUCCESS;
}

VOS_VOID DBTP_InitCommArea(DBTP_COMMAREA_STRU *pstCommArea, VOS_UINT16 usCommAreaNo)
{
    DB_ASSERT(VOS_NULL_PTR != pstCommArea);

    pstCommArea->usNext = (VOS_UINT16)(usCommAreaNo + 1);
    pstCommArea->iIndexID = DBKNL_NULL_BYTE;
    pstCommArea->pstRelDes = VOS_NULL_PTR;
    pstCommArea->enStatus = DBTP_STATUS_IDLE;
}

DB_ERR_CODE DBTP_InitComArea(DBTP_COMMAREAMNG_STRU *pstCommAreaMng)
{
    VOS_UINT16 usCommAreaNo;
    DBTP_COMMAREA_STRU *pstCommArea = VOS_NULL_PTR;

    DB_ASSERT(VOS_NULL_PTR != pstCommAreaMng);

    /* Initialize the comm. area manager */
    pstCommAreaMng->nIdleCommNum = DBTP_COMM_MAX;
    pstCommAreaMng->usFreeList = VOS_NULL;

    /* Allocate sufficient memory for comm. area */
    pstCommAreaMng->pstCommArea = (DBTP_COMMAREA_STRU *)DBRK_MEM_ALLOC(
        VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, (DBTP_COMM_MAX * sizeof(DBTP_COMMAREA_STRU)));
    if (VOS_NULL_PTR == pstCommAreaMng->pstCommArea) {
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }

    pstCommArea = pstCommAreaMng->pstCommArea;

    /* Initilaize the comm. area list nodes */
    for (usCommAreaNo = 0; usCommAreaNo < DBTP_COMM_MAX; usCommAreaNo++) {
        DBTP_InitCommArea(pstCommArea, usCommAreaNo);
        pstCommArea++;
    }

    /* Set the next of last comm araea to invalid value                       */
    pstCommAreaMng->pstCommArea[DBTP_COMM_MAX - 1].usNext = VOS_NULL_WORD;

    return DB_SUCCESS;
}

VOS_VOID DBTP_SetRstSegForInit(DBFI_RSLTSEG_STRU *pstRstSeg, VOS_UINT32 ulRsltSegNo)
{
    DB_ASSERT(pstRstSeg != VOS_NULL_PTR);

    pstRstSeg->ulPre = (VOS_UINT32)(ulRsltSegNo - 1);
    pstRstSeg->ulNext = (VOS_UINT32)(ulRsltSegNo + 1);
    pstRstSeg->enStatus = DBFI_STATUS_IDLE;
    pstRstSeg->nCurRecNum = 0;
}

DB_ERR_CODE DBTP_InitResultSegment(DBFI_RSLTSEGMNG_STRU *pstResSegMgr)
{
    VOS_UINT32 ulRsltSegNo;
    VOS_UINT32 ulRsltSegNum;
    VOS_UINT32 ulRsltSegStep;
    DBFI_RSLTSEG_STRU *pstRstSeg = VOS_NULL_PTR;

    DB_ASSERT(pstResSegMgr != VOS_NULL_PTR);

    /* Get the configured init size of result segment */
    ulRsltSegNum = DB_RSLTSEGNUM_DEFAULT;

    /* Get the configured step size of result segment */
    ulRsltSegStep = DB_RSLTSEGSTEP_DEFAULT;

    /* Allocate sufficient memory for result segment */
    pstResSegMgr->pstRsltSeg = (DBFI_RSLTSEG_STRU *)DBRK_MEM_ALLOC(
        VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, (ulRsltSegNum * sizeof(DBFI_RSLTSEG_STRU)));
    if (pstResSegMgr->pstRsltSeg == VOS_NULL_PTR) {
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }

    /* Set result segment manager with the result segment number */
    pstResSegMgr->nRsltSegNum = ulRsltSegNum;
    pstResSegMgr->nRsltSegStep = ulRsltSegStep;
    pstResSegMgr->nIdleNum = ulRsltSegNum;
    pstResSegMgr->ulFreeList = 0;

    pstRstSeg = pstResSegMgr->pstRsltSeg;

    /* The first result segment is handled outside the for loop because
       previous has be VOS_NULL_LONG for the first segment
       Note: ulRsltSegNum will be a non zero number because configuring it as zero will throw error */
    pstRstSeg->ulPre = VOS_NULL_LONG;
    pstRstSeg->ulNext = 1;
    pstRstSeg->enStatus = DBFI_STATUS_IDLE;
    pstRstSeg->nCurRecNum = 0;
    pstRstSeg++;

    /* Initilaize the result segment list nodes */
    for (ulRsltSegNo = 1; ulRsltSegNo < ulRsltSegNum; ulRsltSegNo++) {
        DBTP_SetRstSegForInit(pstRstSeg, ulRsltSegNo);
        pstRstSeg++;
    }

    /* Set the next of last result segment to invalid value */
    pstResSegMgr->pstRsltSeg[ulRsltSegNum - 1].ulNext = VOS_NULL_LONG;

    return DB_SUCCESS;
}

DB_ERR_CODE DBTP_AllocTransInitResources(VOS_UINT16 usTransID, DBTP_TRANSBLK_STRU *pstTransBlk)
{
    DB_ERR_CODE errCode;
    DBCP_CONTEXT_STRU *pstContext = VOS_NULL_PTR;

    DB_ASSERT(VOS_NULL_PTR != pstTransBlk);

    DBTP_InitTransBlkForAllocTransRes(pstTransBlk, usTransID);

    DBTP_DBG_INIT_PLAN_LOG_MEM(pstTransBlk);

    /* Initialize the CP Context structure */
    pstContext = &pstTransBlk->stContext;

    /* The semaphore is created for the CP context */
    errCode = DBCP_InitContext(pstContext);
    if (DB_SUCCESS != errCode) {
        return errCode;
    }

    /*
     * Initialize the Comm. Area manager structure
     * Memory is allocated for the Comm. Area
     */
    errCode = DBTP_InitComArea(&pstTransBlk->stCommAreaMgr);
    if (DB_SUCCESS != errCode) {
        /* Delete the Semaphore created for the CP Context */
        DBRK_SEMA_DELETE(pstContext->stCtrlSem);
        return errCode;
    }

    /* Initialize the Result Segment manager structure */
    errCode = DBTP_InitResultSegment(&pstTransBlk->stRsltSegMgr);
    if (DB_SUCCESS != errCode) {
        /* Free the memoriy alocated for the Comm. Area & Tx block */
        DBRK_MEM_FREE(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, pstTransBlk->stCommAreaMgr.pstCommArea);

        /* Delete the Semaphore created for the CP Context */
        DBRK_SEMA_DELETE(pstContext->stCtrlSem);
        return errCode;
    }

    return DB_SUCCESS;
}

DB_ERR_CODE DBTP_GetTransAllocBlkNo(
    VOS_UINT32 ulTaskId, VOS_UINT32 ulDbId, T_RELATION rRelID, DBTP_TRANSBLK_STRU **ppstTransBlk, VOS_BOOL bIsSession)
{
    VOS_UINT16 usBlkNo;
    DBTP_TRANSCTRL_STRU *pstTransCtrl = VOS_NULL_PTR;
    DBTP_TRANSBLK_STRU *pstBlkStru = VOS_NULL_PTR;

    DB_ASSERT(VOS_NULL_PTR != ppstTransBlk);

    /* Get the transaction control structure */
    pstTransCtrl = DB_GetTransCtrlRef();

    /* Acquire the lock on the transaction control */
    DBRK_SEMA_P(pstTransCtrl->stLock, 0);

    /* Get a transaction block from the free list of transaction pool */
    usBlkNo = pstTransCtrl->usFreeList;

    // bim rsm恢复场景看护，无并发场景此处一定能找到空闲的transblk
    DB_ASSERT(usBlkNo != DBTP_INVALID_TRANSBLK);

    /* Get a free transaction block */
    pstBlkStru = pstTransCtrl->ppstTRANScb[usBlkNo];
    pstTransCtrl->usFreeList = pstBlkStru->usNext;

    pstTransCtrl->usUsedTransCnt++;
    /* Release the semaphore on the transaction control */
    (VOS_VOID) DBRK_SEMA_V(pstTransCtrl->stLock);

    DBTP_SetBlkStruForGetTransInfo(pstBlkStru, ulTaskId, ulDbId, rRelID);

    *ppstTransBlk = pstBlkStru;

    return DB_SUCCESS;
}

DB_ERR_CODE DBTP_BeginTransaction(DBCP_ENV_STRU *pstCpEnv, VOS_BOOL bModify, DBTP_TRANSBLK_STRU *pstTransBlk)
{
    return DB_SUCCESS;
}

VOS_VOID DBFI_ReleaseRsltSeg(DBFI_RSLTSEGMNG_STRU *pstRsltSegMng, VOS_UINT32 ulRsltSegHeadNo)
{
    DB_ASSERT(VOS_NULL_PTR != pstRsltSegMng);
    DBFI_RSLTSEG_STRU *pstRst = VOS_NULL_PTR;

    if (ulRsltSegHeadNo != VOS_NULL_LONG && pstRsltSegMng->pstRsltSeg[ulRsltSegHeadNo].enStatus == DBFI_STATUS_USED) {
        VOS_UINT32 ulLastRsltSegNo = ulRsltSegHeadNo;
        pstRst = &pstRsltSegMng->pstRsltSeg[ulRsltSegHeadNo];

        while (pstRst->ulNext != VOS_NULL_LONG) {
            /* Set default values to the result segment node that has to be freed */
            pstRst->enStatus = DBFI_STATUS_IDLE;
            pstRsltSegMng->nIdleNum++;

            ulLastRsltSegNo = pstRst->ulNext;
            pstRst = &pstRsltSegMng->pstRsltSeg[ulLastRsltSegNo];
        }
        pstRst->enStatus = DBFI_STATUS_IDLE;
        pstRsltSegMng->nIdleNum++;
        VOS_UINT32 ulFreeListNo = pstRsltSegMng->ulFreeList;
        pstRst->ulNext = ulFreeListNo;

        if (ulFreeListNo != VOS_NULL_LONG) {
            pstRsltSegMng->pstRsltSeg[ulFreeListNo].ulPre = ulLastRsltSegNo;
        }
        pstRsltSegMng->ulFreeList = ulRsltSegHeadNo;
    }

    return;
}

VOS_VOID DBFI_SetDefaultRsltSegNode(DBFI_RSLTSEGMNG_STRU *pstRsltSegMng, VOS_UINT32 ulSegNo)
{
    DB_ASSERT(VOS_NULL_PTR != pstRsltSegMng);

    pstRsltSegMng->pstRsltSeg[ulSegNo].enStatus = DBFI_STATUS_USED;
    pstRsltSegMng->pstRsltSeg[ulSegNo].ulNext = VOS_NULL_LONG;
    pstRsltSegMng->pstRsltSeg[ulSegNo].ulPre = VOS_NULL_LONG;
    pstRsltSegMng->pstRsltSeg[ulSegNo].nCurRecNum = 0;
}

DB_ERR_CODE DBFI_AllocRsltSeg(DBFI_RSLTSEGMNG_STRU *pstRsltSegMng, VOS_UINT32 *pulRsltSegNo)
{
    DB_POINTER2(pstRsltSegMng, pulRsltSegNo);

    /* Allocate the first node of the free List */
    VOS_UINT32 ulSegNo = pstRsltSegMng->ulFreeList;

    /* Make free list Node identifier to point to the next free node */
    pstRsltSegMng->ulFreeList = pstRsltSegMng->pstRsltSeg[ulSegNo].ulNext;
    pstRsltSegMng->nIdleNum--;

    if (pstRsltSegMng->nIdleNum != 0) {
        pstRsltSegMng->pstRsltSeg[pstRsltSegMng->ulFreeList].ulPre = VOS_NULL_LONG;
    }

    /* Set default values to free list Node members */
    DBFI_SetDefaultRsltSegNode(pstRsltSegMng, ulSegNo);

    *pulRsltSegNo = ulSegNo;

    return DB_SUCCESS;
}

DB_ERR_CODE DBFI_AllocFirstRsltSeg(DBFI_RSLTSETHEAD_STRU *pstHead, DBFI_RSLTSEGMNG_STRU *pstRsltSegMng)
{
    DB_ERR_CODE errCode;
    VOS_UINT32 ulRsltSegNo = 0;

    DB_ASSERT(VOS_NULL_PTR != pstHead);

    /* Allocate a free segment from the database result segment manager */
    errCode = DBFI_AllocRsltSeg(pstRsltSegMng, &ulRsltSegNo);
    if (DB_SUCCESS != errCode) {
        DBDBG_STANDARD_REPT("DBFI_AllocFirstRsltSeg", "non availability of free result segment");
        return errCode;
    }

    /* Update result set header with the newly allocated segment number */
    pstHead->ulRsltSegHead = ulRsltSegNo;
    pstHead->ulRsltSegTail = ulRsltSegNo;
    return DB_SUCCESS;
}

DB_ERR_CODE DBFI_AllocMoreRsltSeg(DBFI_RSLTSETHEAD_STRU *pstHead, DBFI_RSLTSEGMNG_STRU *pstRsltSegMng)
{
    DB_ERR_CODE errCode;
    VOS_UINT32 ulRsltSegNo = 0;

    DB_ASSERT(VOS_NULL_PTR != pstHead);
    DB_ASSERT(VOS_NULL_PTR != pstRsltSegMng);

    /* Allocate a free segment from the database result segment manager */
    errCode = DBFI_AllocRsltSeg(pstRsltSegMng, &ulRsltSegNo);
    if (DB_SUCCESS != errCode) {
        DBDBG_STANDARD_REPT("DBFI_AllocMoreRsltSeg", "non availability of free result segment");
        return errCode;
    }

    /* Update the result set header list with the newly allocated segment number */
    pstRsltSegMng->pstRsltSeg[pstHead->ulRsltSegTail].ulNext = ulRsltSegNo;
    pstRsltSegMng->pstRsltSeg[ulRsltSegNo].ulPre = pstHead->ulRsltSegTail;
    pstRsltSegMng->pstRsltSeg[ulRsltSegNo].ulNext = VOS_NULL_LONG;
    pstHead->ulRsltSegTail = ulRsltSegNo;
    return DB_SUCCESS;
}

VOS_VOID DBFI_InsertRecordNumToRsltSeg(
    DBFI_RSLTSETHEAD_STRU *pstHead, T_RECNO recNo, DBFI_RSLTSEGMNG_STRU *pstRsltSegMng)
{
    DBFI_RSLTSEG_STRU *pstRsltSeg = VOS_NULL_PTR;

    DB_ASSERT(VOS_NULL_PTR != pstHead);
    DB_ASSERT(VOS_NULL_PTR != pstRsltSegMng);

    pstRsltSeg = &(pstRsltSegMng->pstRsltSeg[pstHead->ulRsltSegTail]);
    pstRsltSeg->arecNo[pstRsltSeg->nCurRecNum] = recNo;
    pstRsltSeg->nCurRecNum++;
}

DB_ERR_CODE DBFI_RsltSetInsByPosInt(DBFI_RSLTSETHEAD_STRU *pstHead, T_RECNO recNo, DBFI_RSLTSEGMNG_STRU *pstRsltSegMng)
{
    DB_ERR_CODE errCode;

    DB_ASSERT(VOS_NULL_PTR != pstHead);
    DB_ASSERT(VOS_NULL_PTR != pstRsltSegMng);

    /* Result set has no result segments */
    if (VOS_NULL_LONG == pstHead->ulRsltSegHead) {
        errCode = DBFI_AllocFirstRsltSeg(pstHead, pstRsltSegMng);
        if (errCode != DB_SUCCESS) {
            return errCode;
        } /* If all the result segments of the result set are full */
    } else if (pstRsltSegMng->pstRsltSeg[pstHead->ulRsltSegTail].nCurRecNum >= DBFI_RSLT_MAX) {
        errCode = DBFI_AllocMoreRsltSeg(pstHead, pstRsltSegMng);
        if (errCode != DB_SUCCESS) {
            return errCode;
        }
    }
    /* Insert the relation record number in the result segment */
    DBFI_InsertRecordNumToRsltSeg(pstHead, recNo, pstRsltSegMng);

    /* Increment the number of items of the result set head */
    pstHead->nCurRsltNum++;

    return DB_SUCCESS;
}

DB_ERR_CODE DBFI_RsltSetInsTotail(DBFI_RSLTSETHEAD_STRU *pstHead, T_RECNO recNo, DBFI_RSLTSEGMNG_STRU *pstRsltSegMng)
{
    DB_ERR_CODE errCode;

    DB_ASSERT(VOS_NULL_PTR != pstHead);

    /*
     * If the current result number is already reached required count, then
     * no need to insert to the tail
     */
    if (pstHead->nCurRsltNum >= pstHead->ulReqRecNo) {
        return DB_SUCCESS;
    }

    errCode = DBFI_RsltSetInsByPosInt(pstHead, recNo, pstRsltSegMng);
    if (DB_SUCCESS != errCode) {
        return errCode;
    }

    return DB_SUCCESS;
}

DB_ERR_CODE DBFI_RsltSetInsByPos(DBFI_RSLTSETHEAD_STRU *pstHead, T_RECNO recNoPara, DBFI_RSLTPOS_STRU *pstInsertPos)
{
    DB_POINTER2(pstHead, pstInsertPos);
    T_RECNO recNo = recNoPara;
    DBFI_RSLTSEGMNG_STRU *pstRsltSegMng = pstHead->pstRsltSegMgr;
    /* If no position is found, then insert to the tail of the result segment */
    if (pstInsertPos->ulRsltNo >= DBFI_RSLT_MAX) {
        DB_ERR_CODE errCode = DBFI_RsltSetInsTotail(pstHead, recNo, pstRsltSegMng);
        if (errCode != DB_SUCCESS) {
            return errCode;
        }
    }
    return DB_SUCCESS;
}

DB_ERR_CODE DBFI_RsltSetInsertNoOrder(DBFI_RSLTSETHEAD_STRU *pstHead, T_RECNO recNo)
{
    DB_ERR_CODE errCode;
    DBFI_RSLTPOS_STRU stInsertPos;

    stInsertPos.ulRsltNo = DBFI_RSLT_MAX;

    /* Insert the record to the tail of result segment */
    errCode = DBFI_RsltSetInsByPos(pstHead, recNo, &stInsertPos);
    if (errCode != DB_SUCCESS) {
        DBDBG_STANDARD_REPT("DBFI_RsltSetInsertNoOrder", "failure of insert record to tail of result segment");
        return errCode;
    }

    return DB_SUCCESS;
}

DB_ERR_CODE DBFI_RsltSetInsert(DBFI_RSLTSETHEAD_STRU *pstHead, T_RECNO recNo)
{
    DB_ASSERT(VOS_NULL_PTR != pstHead);

    /* If no sort order is defined for the result set */
    if (pstHead->stSiteSort.stSiteFldSet.ucFieldNum == 0) {
        DB_ERR_CODE errCode = DBFI_RsltSetInsertNoOrder(pstHead, recNo);
        if (errCode != DB_SUCCESS) {
            return errCode;
        }
    }
    return DB_SUCCESS;
}

VOS_VOID DBFI_RsltSetHeadReset(DBFI_RSLTSETHEAD_STRU *pstHead, VOS_BOOL IsDestroy)
{
    DBFI_RSLTSEGMNG_STRU *pstRsltSegMng = VOS_NULL_PTR;

    /* Get the Result Segment Management Structure of the database */
    pstRsltSegMng = pstHead->pstRsltSegMgr;

    if (IsDestroy) {
        /* Reset the pointers */
        pstHead->pstRelDes = VOS_NULL_PTR;
        pstHead->pfnModList = VOS_NULL_PTR;
    } else {
        pstHead->pfnModList = DBFI_RsltSetInsert;
    }

    /* Set head and tail segments to VOS_NULL_LONG */
    pstHead->ulRsltSegHead = VOS_NULL_LONG;
    pstHead->ulRsltSegTail = VOS_NULL_LONG;

    /* Set current result number to 0 and set the sort field number to 0 */
    pstHead->nCurRsltNum = 0;
    pstHead->nInvalidRecNum = 0;
    pstHead->stSiteSort.stSiteFldSet.ucFieldNum = 0;
    pstHead->stSiteSort.stSiteFldSet.ucVarFldExist = DBKNL_YES;

    /* Reset result set current position */
    pstHead->stCurRsltPos.ulRsltNo = DBFI_RSLT_MAX;
    pstHead->stCurRsltPos.ulRsltSegNo = pstRsltSegMng->nRsltSegNum;

    pstHead->ulReqRecNo = VOS_NULL_DWORD;
    pstHead->ulCDBId = VOS_NULL_DWORD;
    pstHead->offRDBValFld = DBKNL_NULL_OFFSET;
    pstHead->ulFirstValidRec = DBFI_RECNO_ALLOC;
    pstHead->pRecordList = VOS_NULL_PTR;
}

VOS_VOID DBFI_RsltSetReset(DBFI_RSLTSETHEAD_STRU *pstHead, VOS_BOOL IsDestroy)
{
    DBFI_RSLTSEGMNG_STRU *pstRsltSegMng = VOS_NULL_PTR;

    DB_ASSERT(VOS_NULL_PTR != pstHead);

    /* Get the Result Segment Management Structure of the database */
    pstRsltSegMng = pstHead->pstRsltSegMgr;

    /* Release all the result segments of the result set one by one */
    DBFI_ReleaseRsltSeg(pstRsltSegMng, pstHead->ulRsltSegHead);

    DBFI_RsltSetHeadReset(pstHead, IsDestroy);

    return;
}

VOS_VOID DBTP_ReleaseCommAreaResource(DBTP_TRANSBLK_STRU *pstTransBlk)
{
    DBTP_COMMAREAMNG_STRU *pstCommAreaMgr = VOS_NULL_PTR;
    DBTP_COMMAREA_STRU *pstCommArea = VOS_NULL_PTR;
    VOS_UINT16 usCurCommID;
    VOS_UINT16 usTemp;
    VOS_BOOL bRelease = VOS_TRUE; /* Release one by one or not */

    DB_ASSERT(VOS_NULL_PTR != pstTransBlk);
    usCurCommID = pstTransBlk->usCommAreaList;
    pstCommAreaMgr = &pstTransBlk->stCommAreaMgr;

    /* Release all comm area resources used by this transaction */
    while (VOS_NULL_WORD != usCurCommID) {
        pstCommArea = &pstCommAreaMgr->pstCommArea[usCurCommID];
        if (bRelease) {
            DBFI_RsltSetReset(&pstCommArea->stRsltSetHead, VOS_TRUE);
        } else {
            DBFI_RsltSetHeadReset(&pstCommArea->stRsltSetHead, VOS_TRUE);
        }

        /* Reset the current comm. area structure */
        pstCommArea->enStatus = DBTP_STATUS_IDLE;
        pstCommArea->ulTaskId = VOS_NULL_LONG;

        usTemp = pstCommArea->usNext;
        pstCommArea->usNext = pstCommAreaMgr->usFreeList;
        pstCommAreaMgr->usFreeList = usCurCommID;
        pstCommAreaMgr->nIdleCommNum++;
        usCurCommID = usTemp;
    }

    /* Reset the members of transaction block */
    pstTransBlk->stContext.enAccessType = DBCP_ACCESSTYPE_NONE;
    pstTransBlk->stContext.enWaitType = DBCP_WAITTYPE_NONE;
    pstTransBlk->stContext.ulDbId = VOS_NULL_LONG;
    pstTransBlk->stContext.rRelID = VOS_NULL_WORD;
    pstTransBlk->stContext.ulTaskId = VOS_NULL_LONG;
    pstTransBlk->usCommAreaList = VOS_NULL_WORD;
}

VOS_VOID DBTP_FreeTransBlk(DBTP_TRANSBLK_STRU *pstTransBlk)
{
    VOS_UINT16 usTransBlkNo;
    DBTP_TRANSCTRL_STRU *pstDBTransCtrl = VOS_NULL_PTR;

    /* Get the transaction control structure */
    pstDBTransCtrl = DB_GetTransCtrlRef();
    DB_ASSERT(VOS_NULL_PTR != pstTransBlk);

    usTransBlkNo = pstTransBlk->usTransID;
    DBTP_ReleaseCommAreaResource(pstTransBlk);

    DBRK_SEMA_P(pstDBTransCtrl->stLock, 0);
    pstTransBlk->usNext = pstDBTransCtrl->usFreeList;
    pstDBTransCtrl->usFreeList = usTransBlkNo;

    pstDBTransCtrl->usUsedTransCnt--;

    /* Release the lock on the transaction control */
    (VOS_VOID) DBRK_SEMA_V(pstDBTransCtrl->stLock);
    return;
}

DB_ERR_CODE DML_StartTransactionEx(
    VOS_UINT32 ulDbId, VOS_UINT16 usRelID, VOS_UINT8 ucWrite, DBTP_TRANSBLK_STRU **ppstTransBlk)
{
    DB_ERR_CODE errCode;
    VOS_UINT32 ulTaskId;
    DB_DBMS_STRU *pstDBMgr = VOS_NULL_PTR;
    DBCP_ENV_STRU *pstCpEnv = VOS_NULL_PTR;
    DBTP_TRANSBLK_STRU *pstTransBlk = VOS_NULL_PTR;

    DB_ASSERT(VOS_NULL_PTR != ppstTransBlk);

    pstDBMgr = g_ppstDBArr[ulDbId];

    /* Get the CP Environment */
    pstCpEnv = &(pstDBMgr->stCpEnv);

    errCode = DML_StartTransactionCheckArgs(pstDBMgr, pstCpEnv);
    if (DB_SUCCESS != errCode) {
        return errCode;
    }

    if ((ucWrite & DBKNL_MASK_BYTE_MSB) != 0) {
        if (DBSTATE_INUSE != pstCpEnv->ulDBState) {
            DBDBG_STANDARD_REPT("DML_StartTransactionEx", "database is not recovered");
            return VOS_ERRNO_DB_RCVR_PENDING;
        }

        ucWrite &= DBKNL_MASK_BYTE_7LSB;
    }

    /* Get the current task ID */
    ulTaskId = DBRK_GET_CURRENT_TASK_ID();

    /* Allocate or get the transaction block */
    errCode = DBTP_GetTransAllocBlkNo(ulTaskId, ulDbId, usRelID, &pstTransBlk, VOS_FALSE);
    if (DB_SUCCESS != errCode) {
        DBDBG_STANDARD_REPT("DML_StartTransactionEx", "failure of get/allocate transaction block operation");
        return errCode;
    }
    /* Begin the transaction */
    errCode = DBTP_BeginTransaction(pstCpEnv, (VOS_BOOL)ucWrite, pstTransBlk);
    if (DB_SUCCESS == errCode) {
        *ppstTransBlk = pstTransBlk;
        return DB_SUCCESS;
    }

    /* Free the Transaction ID allocated now */
    DBTP_FreeTransBlk(pstTransBlk);
    DBDBG_STANDARD_REPT("DML_StartTransactionEx", "failure of begin transaction operation");
    return errCode;
}

VOS_VOID DML_RollbackTransactionEx(DBTP_TRANSBLK_STRU *pstTransBlk)
{
    return;
}

VOS_VOID DML_CommitTransactionEx(DBTP_TRANSBLK_STRU *pstTransBlk)
{
    DB_POINTER(pstTransBlk);
    DBTP_FreeTransBlk(pstTransBlk);
    return;
}

DB_ERR_CODE DBTP_AllocTransResources(VOS_UINT16 usTransID, DBTP_TRANSBLK_STRU **ppstTransBlk)
{
    DB_ERR_CODE errCode;
    DBTP_TRANSBLK_STRU *pstTransBlk = VOS_NULL_PTR;

    DB_ASSERT(VOS_NULL_PTR != ppstTransBlk);

    pstTransBlk = (DBTP_TRANSBLK_STRU *)DBRK_MEM_ALLOC(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, sizeof(DBTP_TRANSBLK_STRU));
    if (VOS_NULL_PTR == pstTransBlk) {
        PRINTDB1(DBDEBUG2, VOS_PID_DBKNL, VOS_MODULE_DBKNL, GENERAL, VOS_ERRNO_DB_MEMALLOCFAILURE);
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }
    errCode = DBTP_AllocTransInitResources(usTransID, pstTransBlk);
    if (DB_SUCCESS != errCode) {
        DBRK_MEM_FREE(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, pstTransBlk);
        return errCode;
    }

    *ppstTransBlk = pstTransBlk;
    return DB_SUCCESS;
}

VOS_VOID DBTP_SetTrasnsCtlForAllocTrans(DBTP_TRANSCTRL_STRU *pstTransCtrl, VOS_UINT16 usLoop)
{
    DB_ASSERT(VOS_NULL_PTR != pstTransCtrl);

    /*
     * Initialize the transaction control structure
     * The tail end of the transaction blocks list should point to the invalid transaction block
     */
    pstTransCtrl->ppstTRANScb[usLoop - 1]->usNext = DBTP_INVALID_TRANSBLK;

    pstTransCtrl->ulMagicNum = DBTP_MAGICNO;
    pstTransCtrl->usUsedTransCnt = 0;
    pstTransCtrl->usAllocTransCnt = g_usDBTransNum;
    pstTransCtrl->usMaxTransCnt = g_usDBTransNum;

    /* By default, the auto compress will be OFF */
    pstTransCtrl->bTransAutoCompress = DBKNL_NO;
    pstTransCtrl->ulMaxBufLen = 0;
}

DB_ERR_CODE DBTP_AllocTransaction(VOS_VOID)
{
    DB_ERR_CODE errCode;
    VOS_UINT16 usLoop;
    DBTP_TRANSCTRL_STRU *pstTransCtrl = VOS_NULL_PTR;
    DBTP_TRANSBLK_STRU *pstTransBlk = VOS_NULL_PTR;

    /* Get the transaction block control */
    pstTransCtrl = &g_stTransCtrl;

    /* Allocate memory to store the pointers to transaction blocks */
    pstTransCtrl->ppstTRANScb =
        (DBTP_TRANSBLK_STRU **)DBRK_MEM_ALLOC(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, (sizeof(char *) * g_usDBTransNum));
    if (VOS_NULL_PTR == pstTransCtrl->ppstTRANScb) {
        PRINTDB1(DBDEBUG2, VOS_PID_DBKNL, VOS_MODULE_DBKNL, GENERAL, VOS_ERRNO_DB_MEMALLOCFAILURE);
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }

    /* Initialize the free list of Transaction control */
    pstTransCtrl->usFreeList = 0;

    /* Initialize the each and every transaction blocks */
    for (usLoop = 0; usLoop < g_usDBTransNum; usLoop++) {
        errCode = DBTP_AllocTransResources(usLoop, &pstTransBlk);
        if (DB_SUCCESS != errCode) {
            // 异常分支返回后通过删除memCtx释放
            DBRK_MEM_FREE(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, pstTransCtrl->ppstTRANScb);
            return errCode;
        }

        pstTransCtrl->ppstTRANScb[usLoop] = pstTransBlk;
    }
    DBTP_SetTrasnsCtlForAllocTrans(pstTransCtrl, usLoop);

    return DB_SUCCESS;
}

DB_ERR_CODE DBFI_UpdateIndexSeg(DBDDL_RELDESTBL_STRU *pstRelDes, DBDDL_IDXDESTBL_STRU *pstIdxDes)
{
    return DB_SUCCESS;
}

DB_ERR_CODE DBFI_ReGenerateIndex(DBDDL_RELDESTBL_STRU *pstRelDes, VOS_UINT8 *pucIdxAffectList)
{
    DB_ERR_CODE errCode;
    VOS_UINT8 ucIdx;
    VOS_UINT8 ucIndexNum;
    DBDDL_IDXDESTBL_STRU *pstIdxDes = VOS_NULL_PTR;

    /* If the table is invalid, then return */
    if (VOS_NULL_PTR == pstRelDes) {
        return DB_SUCCESS;
    }

    /* Number of indexes */
    ucIndexNum = pstRelDes->ucIdxNum;

    /* Get the first index descriptor */
    pstIdxDes = DBDDL_GET_IDX_DESC(pstRelDes, 0);

    for (ucIdx = 0; ucIdx < ucIndexNum; ucIdx++, pstIdxDes++) {
        if ((pucIdxAffectList != VOS_NULL_PTR) && (pucIdxAffectList[ucIdx] == VOS_FALSE)) {
            /* If the index is not affected then no need to regenerate the index */
            continue;
        }

        /* As the index is affected, needs to rebuild the index segment */
        errCode = DBFI_UpdateIndexSeg(pstRelDes, pstIdxDes);
        if (DB_SUCCESS != errCode) {
            DBDBG_STANDARD_REPT("DBFI_ReGenerateIndex", "failure of updating index segment");
            return errCode;
        }
    }

    return DB_SUCCESS;
}

VOS_UINT8 *DBFI_DATA_GetRecord(DBDDL_RELDESTBL_STRU *pstRelDes, T_RECNO recNo)
{
    VOS_UINT32 ulBlkIdx;
    VOS_UINT8 ucBlkNo;
    VOS_UINT32 *pulAllocList = VOS_NULL_PTR;
    VOS_UINT8 *pucData = VOS_NULL_PTR;
    DBFI_DATABLKADDR_STRU *pstDataBlk = VOS_NULL_PTR;

    /* Validate the node ID */
    if (DBKNL_NULL_NODEID == recNo) {
        return VOS_NULL_PTR;
    }

    DB_ASSERT(VOS_NULL_PTR != pstRelDes);

    /* Get the element's block number and index within the block */
    DBFI_GET_BLK_INDEX(recNo, ucBlkNo, ulBlkIdx);

    pstDataBlk = DBMM_GET_DATA_BLK_ADDR_ARY(pstRelDes) + ucBlkNo;

    /* Get the starting address of the block */
    pulAllocList = (VOS_UINT32 *)DBMM_GET_DATA_BLK(pstDataBlk);
    pucData = ((VOS_UINT8 *)pulAllocList) + pstDataBlk->offRecBlk;

    /* Check if the given element is allocated */
    if (DBFI_RECNO_ALLOC != pulAllocList[ulBlkIdx]) {
        return VOS_NULL_PTR;
    }

    /*
     * Return the address of the element within the block
     * The offRecBlk specifies the offset to starting address of the elements
     */
    return (pucData + ((VOS_SIZE_T)ulBlkIdx * pstRelDes->stDataSegHead.ulElemSize));
}

DB_ERR_CODE DBFI_DATA_UpdateReplyVarRecordEx(
    DBDDL_RELDESTBL_STRU *pstRelDes, T_RECNO recNo, VOS_VOID *pNewRecord, VOS_UINT8 *pucRecord, VOS_BOOL bIsRcvrFlow)
{
    DBMM_PAGEMGR_STRU *pstPgMgr = VOS_NULL_PTR;
    VOS_UINT8 *pucLogRec = VOS_NULL_PTR;
    VOS_UINT8 *pucVarRec = VOS_NULL_PTR;
    DB_VARIABLE_DATATYPE *pstOrgVarField = VOS_NULL_PTR;
    VOS_UINT32 ulVarRecLen = 0;
    VOS_INT32 lRet;

    DB_ASSERT(VOS_NULL_PTR != pstRelDes);
    DB_ASSERT(VOS_NULL_PTR != pucRecord);

    pstPgMgr = DB_PgMgrGet(pstRelDes);
    DB_ASSERT(VOS_NULL_PTR != pstPgMgr);

    pucLogRec = (VOS_UINT8 *)pNewRecord;

    /* Get the Pointer to Var Field */
    pstOrgVarField = DBFI_VAR_DATA_DESC_GET(pucLogRec);

    DB_ASSERT(VOS_NULL_PTR != pucLogRec);

    pucVarRec = DBMM_GetVarRecPtr(pstPgMgr, pstOrgVarField, &ulVarRecLen);
    lRet = memcpy_s(pucVarRec, ulVarRecLen, pucLogRec + pstRelDes->stDataSegHead.ulElemSize + sizeof(DBMM_ROWDIR_STRU),
        ulVarRecLen);
    if (EOK != lRet) {
        DBDBG_STANDARD_REPT("DBFI_DATA_UpdateReplayVarRecordEx", "Failure of memory copy");
        return VOS_ERRNO_DB_MEMCPY_FAILURE;
    }

    DB_ASSERT(VOS_NULL_PTR != pstOrgVarField);

    /* Mark the Variable Data Record Dirty */
    DBMM_MARK_MEM_BLK_DIRTY(pstRelDes->ucCkpt, pstPgMgr->ppstPgMapArr[pstOrgVarField->ulPageID]->pstPage,
        pstPgMgr->pstPgPersAttr->ulPgSize, pucVarRec, ulVarRecLen);

    /* Copy record from log buffer to data segment. */
    lRet = memcpy_s(pucRecord, pstRelDes->stDataSegHead.ulElemSize, pucLogRec, pstRelDes->stDataSegHead.ulElemSize);
    if (EOK != lRet) {
        DBDBG_STANDARD_REPT("DBFI_DATA_UpdateReplayVarRecordEx", "Failure of memory copy");
        return VOS_ERRNO_DB_MEMCPY_FAILURE;
    }

    /* Mark the current record position page as dirty */
    DBMM_MARK_DATA_PAGE_DIRTY(pstRelDes->ucCkpt, DBMM_GET_DATA_BLK_ADDR_ARY(pstRelDes), recNo, pucRecord,
        pstRelDes->stDataSegHead.ulElemSize);

    return DB_SUCCESS;
}

DB_ERR_CODE DBFI_DATA_UpdateReplayVarRecord(
    DBDDL_RELDESTBL_STRU *pstRelDes, T_RECNO recNo, VOS_VOID *pNewRecord, VOS_BOOL bIsRcvrFlow)
{
    VOS_UINT8 *pucRecord = VOS_NULL_PTR;

    /* Get the record pointer to update with new data */
    pucRecord = (VOS_UINT8 *)DBFI_DATA_GetRecord(pstRelDes, recNo);
    if (VOS_NULL_PTR == pucRecord) {
        DBDBG_STANDARD_REPT("DBFI_DATA_UpdateReplayVarRecord", "invalid record ID");
        return VOS_ERRNO_DB_INVALIDRECNO;
    }

    return DBFI_DATA_UpdateReplyVarRecordEx(pstRelDes, recNo, pNewRecord, pucRecord, bIsRcvrFlow);
}

DB_ERR_CODE DBFI_DATA_UpdateRecordEx(DBDDL_RELDESTBL_STRU *pstRelDes, T_RECNO recNo, const VOS_VOID *pNewRecord,
    VOS_UINT8 *pucRecord, VOS_BOOL bIsRcvrFlow)
{
    VOS_INT32 lRet;

    DB_ASSERT(VOS_NULL_PTR != pstRelDes);
    DB_ASSERT(VOS_NULL_PTR != pucRecord);

    /* Mark the current record position page as dirty */
    DBMM_MARK_DATA_PAGE_DIRTY(pstRelDes->ucCkpt, DBMM_GET_DATA_BLK_ADDR_ARY(pstRelDes), recNo, pucRecord,
        pstRelDes->stDataSegHead.ulElemSize);

    /* Copy the complete record into the record position */
    lRet = memcpy_s(pucRecord, pstRelDes->stDataSegHead.ulElemSize, pNewRecord, pstRelDes->stDataSegHead.ulElemSize);
    if (EOK != lRet) {
        DBDBG_STANDARD_REPT("DBFI_DATA_UpdateRecordEx", "Failure of memory copy");
        return VOS_ERRNO_DB_MEMCPY_FAILURE;
    }

    return DB_SUCCESS;
}

DB_ERR_CODE DBFI_DATA_UpdateRecord(
    DBDDL_RELDESTBL_STRU *pstRelDes, T_RECNO recNo, const VOS_VOID *pNewRecord, VOS_BOOL bIsRcvrFlow)
{
    VOS_UINT8 *pucRecord;

    /* Get the record pointer to update with new data */
    pucRecord = (VOS_UINT8 *)DBFI_DATA_GetRecord(pstRelDes, recNo);
    if (VOS_NULL_PTR == pucRecord) {
        DBDBG_STANDARD_REPT("DBFI_DATA_UpdateRecord", "invalid record ID");
        return VOS_ERRNO_DB_INVALIDRECNO;
    }

    return DBFI_DATA_UpdateRecordEx(pstRelDes, recNo, pNewRecord, pucRecord, bIsRcvrFlow);
}

VOS_VOID DBFI_UpdateRecordRcvr(DBDDL_RELDESTBL_STRU *pstRelDes, T_RECNO recNo, VOS_VOID *pNewRecord, VOS_BOOL bIsTPCDB)
{
    DB_ERR_CODE errCode;

    DB_ASSERT(VOS_NULL_PTR != pstRelDes);

    if (DB_PgMgrGet(pstRelDes) == VOS_NULL_PTR) {
        /* Update the record of the relation data segment */
        /* In case of TPC Db and Recovery Flow, need to reset the last 4 bytes in DI of a record */
        errCode = DBFI_DATA_UpdateRecord(pstRelDes, recNo, pNewRecord, bIsTPCDB);
    } else {
        /* In case of TPC Db and Recovery Flow, need to reset the last 4 bytes in DI of a record */
        errCode = DBFI_DATA_UpdateReplayVarRecord(pstRelDes, recNo, pNewRecord, bIsTPCDB);
    }

    DB_ASSERT(DB_SUCCESS == errCode);
    (VOS_VOID) errCode;
    return;
}

DB_ERR_CODE DBFI_CheckElemCntInSeg(DBFI_SEGHEAD_STRU *pstSegHead, DBDDL_RELDESTBL_STRU *pstRelDes)
{
    DB_ASSERT(VOS_NULL_PTR != pstSegHead);
    DB_ASSERT(VOS_NULL_PTR != pstRelDes);

    /* Check if the data segment has max number of records */
    if (pstSegHead->ulUsedElemCnt == pstRelDes->nCurRecMax) {
        DBDBG_STANDARD_REPT("DBFI_CheckElemCntInSeg", "no free record space in the data segment");
        return VOS_ERRNO_DB_DATASEGFULL;
    }

    DB_ASSERT(DBFI_NODE_ALLOC != pstSegHead->ulFreeList);
    return DB_SUCCESS;
}

DB_ERR_CODE DBFI_DATA_AllocRecNo(
    DB_DBMS_STRU *pstDBMgr, DBDDL_RELDESTBL_STRU *pstRelDes, VOS_UINT8 **ppucRecord, T_RECNO *precNo)
{
    T_RECNO recNo;
    VOS_UINT32 ulBlkIdx;
    VOS_UINT8 ucBlkNo;
    VOS_UINT32 *pulAllocList = VOS_NULL_PTR;
    DBFI_SEGHEAD_STRU *pstSegHead = VOS_NULL_PTR;
    DBFI_DATABLKADDR_STRU *pstDataBlk = VOS_NULL_PTR;
    VOS_UINT32 ulRet;

    DB_ASSERT(VOS_NULL_PTR != pstRelDes);
    DB_ASSERT(VOS_NULL_PTR != pstDBMgr);
    DB_ASSERT(VOS_NULL_PTR != ppucRecord);
    DB_ASSERT(VOS_NULL_PTR != precNo);

    pstSegHead = &pstRelDes->stDataSegHead;

    ulRet = DBFI_CheckElemCntInSeg(pstSegHead, pstRelDes);
    if (DB_SUCCESS != ulRet) {
        return ulRet;
    }

    /* Allocate a record from the data segment */
    recNo = pstSegHead->ulFreeList;

    /* [Protective Code] This is to handle corruption case when ulFreeList is allocated. */
    if (recNo == DBFI_NODE_ALLOC) {
        return VOS_ERRNO_DB_DATASEGFULL;
    }

    /* Get the element's block number and index within the block */
    DBFI_GET_BLK_INDEX(recNo, ucBlkNo, ulBlkIdx);

    pstDataBlk = DBMM_GET_DATA_BLK_ADDR_ARY(pstRelDes);

    /* Get the starting address of the block */
    pulAllocList = (VOS_UINT32 *)DBMM_GET_DATA_BLK(&pstDataBlk[ucBlkNo]);

    /* Update the free list for the segment */
    pstSegHead->ulFreeList = pulAllocList[ulBlkIdx];

    DBCKSM_DEL(
        pstDBMgr->pstChksum, &(DB_REL_DATA_CHECKSUM_GET(pstRelDes)), (pulAllocList + ulBlkIdx), sizeof(VOS_UINT32));

    pstSegHead->ulFreeElemCnt--;
    pstSegHead->ulUsedElemCnt++;

    /* Mark the alloc list element as dirty */
    DBMM_MARK_PAGE_DIRTY(pstRelDes->ucCkpt, &pstDataBlk[ucBlkNo].stBlock, (VOS_UINT8 *)&pulAllocList[ulBlkIdx],
        (VOS_UINT32)sizeof(VOS_UINT32));

    /* Since the segment head has been modified here, this page also has to be marked as dirty */
    DBMM_MARK_SEG_PAGE_DIRTY(
        pstRelDes->ucCkpt, pstRelDes, (VOS_UINT8 *)pstSegHead, (VOS_UINT32)sizeof(DBFI_SEGHEAD_STRU));

    /*
     * Return the address of the element within the block
     * The offRecBlk specifies the offset to starting address of the elements
     */
    *ppucRecord = (VOS_UINT8 *)DBRK_ABS_OFFSET(
        pulAllocList, (pstDataBlk[ucBlkNo].offRecBlk + ((VOS_SIZE_T)ulBlkIdx * pstSegHead->ulElemSize)));
    *precNo = recNo;
    return DB_SUCCESS;
}

VOS_VOID DBFI_DATA_FreeRecNo(DB_DBMS_STRU *pstDBMgr, DBDDL_RELDESTBL_STRU *pstRelDes, T_RECNO recNo)
{
    VOS_UINT32 ulBlkIdx;
    VOS_UINT8 ucBlkNo;
    VOS_UINT32 *pulAllocList = VOS_NULL_PTR;
    DBFI_SEGHEAD_STRU *pstSegHead = VOS_NULL_PTR;
    DBFI_DATABLKADDR_STRU *pstDataBlk = VOS_NULL_PTR;

    DB_ASSERT(VOS_NULL_PTR != pstDBMgr);
    DB_ASSERT(VOS_NULL_PTR != pstRelDes);

    pstSegHead = &pstRelDes->stDataSegHead;

    /* Free the element in the segment */
    /* Get the element's block number and index within the block */
    DBFI_GET_BLK_INDEX(recNo, ucBlkNo, ulBlkIdx);

    pstDataBlk = DBMM_GET_DATA_BLK_ADDR_ARY(pstRelDes);

    /* Get the starting address of the block */
    pulAllocList = (VOS_UINT32 *)DBMM_GET_DATA_BLK(&pstDataBlk[ucBlkNo]);

    /* Update the free list for the segment */
    pulAllocList[ulBlkIdx] = pstSegHead->ulFreeList;

    DBCKSM_ADD(
        pstDBMgr->pstChksum, &(DB_REL_DATA_CHECKSUM_GET(pstRelDes)), (pulAllocList + ulBlkIdx), sizeof(VOS_UINT32));

    pstSegHead->ulFreeList = recNo;
    pstSegHead->ulFreeElemCnt++;
    pstSegHead->ulUsedElemCnt--;

    /* Mark the alloc list element as dirty */
    DBMM_MARK_PAGE_DIRTY(pstRelDes->ucCkpt, &pstDataBlk[ucBlkNo].stBlock, (VOS_UINT8 *)&pulAllocList[ulBlkIdx],
        (VOS_UINT32)sizeof(VOS_UINT32));

    /* Since the segment head has been modified here, this page also has to be marked as dirty */
    DBMM_MARK_SEG_PAGE_DIRTY(
        pstRelDes->ucCkpt, pstRelDes, (VOS_UINT8 *)pstSegHead, (VOS_UINT32)sizeof(DBFI_SEGHEAD_STRU));

    return;
}

DB_ERR_CODE DBFI_DATA_InsertReplayRecord(DB_DBMS_STRU *pstDBMgr, DBDDL_RELDESTBL_STRU *pstRelDes,
    const VOS_VOID *pRecord, T_RECNO *precNo, VOS_BOOL bIsRcvrFlow)
{
    DB_ERR_CODE errCode;
    T_RECNO recNo;
    VOS_UINT32 ulBlkIdx;
    VOS_UINT8 ucBlkNo;
    VOS_UINT32 *pulAllocList = VOS_NULL_PTR;
    VOS_UINT8 *pucRecData = VOS_NULL_PTR;
    DBFI_DATABLKADDR_STRU *pstDataBlk = VOS_NULL_PTR;
    VOS_INT32 lRet;

    DB_ASSERT(VOS_NULL_PTR != pstRelDes);
    DB_ASSERT(VOS_NULL_PTR != precNo);
    DB_ASSERT(VOS_NULL_PTR != pstDBMgr);

    pstDataBlk = DBMM_GET_DATA_BLK_ADDR_ARY(pstRelDes);

    /* Get the element's block number and index within the block */
    DBFI_GET_BLK_INDEX(*precNo, ucBlkNo, ulBlkIdx);

    /* Get the starting address of the block */
    pulAllocList = (VOS_UINT32 *)DBMM_GET_DATA_BLK(&pstDataBlk[ucBlkNo]);
    if (DBFI_NODE_ALLOC != pulAllocList[ulBlkIdx]) {
        errCode = DBFI_DATA_AllocRecNo(pstDBMgr, pstRelDes, &pucRecData, &recNo);
        if (DB_SUCCESS != errCode) {
            DBDBG_STANDARD_REPT("DBFI_DATA_InsertReplayRecord", "failure of allocate record number operation");
            return errCode;
        }

        *precNo = recNo;

        /* Get the element's block number and index within the block */
        DBFI_GET_BLK_INDEX(recNo, ucBlkNo, ulBlkIdx);

        /* Get the starting address of the block */
        pulAllocList = (VOS_UINT32 *)DBMM_GET_DATA_BLK(&pstDataBlk[ucBlkNo]);
    } else {
        recNo = *precNo;
        pucRecData = (VOS_UINT8 *)DBRK_ABS_OFFSET(pulAllocList,
            (pstDataBlk[ucBlkNo].offRecBlk + ((VOS_SIZE_T)ulBlkIdx * pstRelDes->stDataSegHead.ulElemSize)));
    }

    /* Mark the page as dirty */
    DBMM_MARK_DATA_PAGE_DIRTY(pstRelDes->ucCkpt, DBMM_GET_DATA_BLK_ADDR_ARY(pstRelDes), recNo, (VOS_UINT8 *)pucRecData,
        pstRelDes->stDataSegHead.ulElemSize);

    /* Insert the data in the block */
    lRet = memcpy_s(pucRecData, pstRelDes->stDataSegHead.ulElemSize, pRecord, pstRelDes->stDataSegHead.ulElemSize);
    if (EOK != lRet) {
        /* Free the record since record copy in rollback flow failed */
        DBFI_DATA_FreeRecNo(pstDBMgr, pstRelDes, recNo);
        DBDBG_STANDARD_REPT("DBFI_DATA_InsertReplayRecord", "Failure of memory copy");
        return VOS_ERRNO_DB_MEMCPY_FAILURE;
    }

    /* Set the element's status to allocated */
    pulAllocList[ulBlkIdx] = DBFI_NODE_ALLOC;

    return DB_SUCCESS;
}

DB_ERR_CODE DBFI_DATA_InsertReplayVarRecord(
    DB_DBMS_STRU *pstDBMgr, DBDDL_RELDESTBL_STRU *pstRelDes, VOS_VOID *pRecord, T_RECNO *precNo, VOS_BOOL bIsRcvrFlow)
{
    DB_ERR_CODE errCode;
    T_RECNO recNo;
    VOS_UINT32 ulBlkIdx;
    VOS_UINT8 ucBlkNo;
    VOS_UINT32 *pulAllocList = VOS_NULL_PTR;
    VOS_UINT8 *pucRecData = VOS_NULL_PTR;
    DBFI_DATABLKADDR_STRU *pstDataBlk = VOS_NULL_PTR;
    VOS_UINT8 *pucLogRec = VOS_NULL_PTR;
    DBMM_PAGEMGR_STRU *pstPgMgr = VOS_NULL_PTR;
    DB_VARIABLE_DATATYPE *pstOrgVarField = VOS_NULL_PTR;
    VOS_UINT8 *pucVarRec = VOS_NULL_PTR;
    VOS_UINT32 ulVarRecLen = 0;
    VOS_INT32 lRet;

    DB_ASSERT(VOS_NULL_PTR != pstRelDes);
    DB_ASSERT(VOS_NULL_PTR != precNo);

    pstDataBlk = DBMM_GET_DATA_BLK_ADDR_ARY(pstRelDes);

    /* Get the element's block number and index within the block */
    DBFI_GET_BLK_INDEX(*precNo, ucBlkNo, ulBlkIdx);

    /* Get the starting address of the block */
    pulAllocList = (VOS_UINT32 *)DBMM_GET_DATA_BLK(&pstDataBlk[ucBlkNo]);
    if (DBFI_NODE_ALLOC != pulAllocList[ulBlkIdx]) {
        errCode = DBFI_DATA_AllocRecNo(pstDBMgr, pstRelDes, &pucRecData, &recNo);
        if (DB_SUCCESS != errCode) {
            DBDBG_STANDARD_REPT("DBFI_DATA_InsertReplayVarRecord", "failure of allocate record number operation");
            return errCode;
        }

        *precNo = recNo;

        /* Get the element's block number and index within the block */
        DBFI_GET_BLK_INDEX(recNo, ucBlkNo, ulBlkIdx);

        /* Get the starting address of the block */
        pulAllocList = (VOS_UINT32 *)DBMM_GET_DATA_BLK(&pstDataBlk[ucBlkNo]);
    } else {
        recNo = *precNo;
        pucRecData = (VOS_UINT8 *)DBRK_ABS_OFFSET(pulAllocList,
            (pstDataBlk[ucBlkNo].offRecBlk + ((VOS_SIZE_T)ulBlkIdx * pstRelDes->stDataSegHead.ulElemSize)));
    }

    pstPgMgr = DB_PgMgrGet(pstRelDes);
    DB_ASSERT(VOS_NULL_PTR != pstPgMgr);

    pucLogRec = (VOS_UINT8 *)pRecord;

    DB_ASSERT(VOS_NULL_PTR != pucLogRec);

    /* Get the Pointer to Var Field */
    pstOrgVarField = DBFI_VAR_DATA_DESC_GET(pucLogRec);

    pucVarRec = DBMM_GetVarRecPtr(pstPgMgr, pstOrgVarField, &ulVarRecLen);
    lRet = memcpy_s(pucVarRec, ulVarRecLen, pucLogRec + pstRelDes->stDataSegHead.ulElemSize + sizeof(DBMM_ROWDIR_STRU),
        ulVarRecLen);
    if (EOK != lRet) {
        DBFI_DATA_FreeRecNo(pstDBMgr, pstRelDes, recNo);
        DBDBG_STANDARD_REPT("DBFI_DATA_InsertReplayVarRecord", "Failure of memory copy");
        return VOS_ERRNO_DB_MEMCPY_FAILURE;
    }

    /* Update New Record number in varData */
    lRet = memcpy_s(pucVarRec + (ulVarRecLen - sizeof(T_RECNO)), sizeof(T_RECNO), &recNo, sizeof(T_RECNO));
    if (EOK != lRet) {
        DBFI_DATA_FreeRecNo(pstDBMgr, pstRelDes, recNo);
        DBDBG_STANDARD_REPT("DBFI_DATA_InsertReplayVarRecord", "Failure of memory copy");
        return VOS_ERRNO_DB_MEMCPY_FAILURE;
    }

    /* Mark the Variable Data Record Dirty */
    DBMM_MARK_MEM_BLK_DIRTY(pstRelDes->ucCkpt, pstPgMgr->ppstPgMapArr[pstOrgVarField->ulPageID]->pstPage,
        pstPgMgr->pstPgPersAttr->ulPgSize, pucVarRec, ulVarRecLen);

    /* Mark the page as dirty */
    DBMM_MARK_DATA_PAGE_DIRTY(pstRelDes->ucCkpt, DBMM_GET_DATA_BLK_ADDR_ARY(pstRelDes), recNo, (VOS_UINT8 *)pucRecData,
        pstRelDes->stDataSegHead.ulElemSize);

    /* Insert the data in the block */
    lRet = memcpy_s(pucRecData, pstRelDes->stDataSegHead.ulElemSize, pucLogRec, pstRelDes->stDataSegHead.ulElemSize);
    if (EOK != lRet) {
        DBFI_DATA_FreeRecNo(pstDBMgr, pstRelDes, recNo);
        DBDBG_STANDARD_REPT("DBFI_DATA_InsertReplayVarRecord", "Failure of memory copy");
        return VOS_ERRNO_DB_MEMCPY_FAILURE;
    }

    /* Set the element's status to allocated */
    pulAllocList[ulBlkIdx] = DBFI_NODE_ALLOC;

    return DB_SUCCESS;
}

VOS_VOID DBFI_InsertRecordRcvr(
    DB_DBMS_STRU *pstDBMgr, DBDDL_RELDESTBL_STRU *pstRelDes, VOS_VOID *pRecord, T_RECNO *precNo)
{
    DB_ERR_CODE errCode;

    DB_ASSERT(VOS_NULL_PTR != pstRelDes);
    DB_ASSERT(VOS_NULL_PTR != pstDBMgr);

    if (VOS_NULL_PTR == DB_PgMgrGet(pstRelDes)) {
        /* Insert the record into the relation data segment */
        errCode = DBFI_DATA_InsertReplayRecord(pstDBMgr, pstRelDes, pRecord, precNo, (VOS_BOOL)pstDBMgr->ucIsTPC);
    } else {
        /* Insert the var record into the relation data segment and in page */
        errCode = DBFI_DATA_InsertReplayVarRecord(pstDBMgr, pstRelDes, pRecord, precNo, (VOS_BOOL)pstDBMgr->ucIsTPC);
    }

    DB_ASSERT(DB_SUCCESS == errCode);
    (VOS_VOID) errCode;
    return;
}

VOS_VOID DBFI_DeleteRecordRcvr(DB_DBMS_STRU *pstDBMgr, DBDDL_RELDESTBL_STRU *pstRelDes, T_RECNO recNo)
{
    DBFI_DATA_FreeRecNo(pstDBMgr, pstRelDes, recNo);
    return;
}
