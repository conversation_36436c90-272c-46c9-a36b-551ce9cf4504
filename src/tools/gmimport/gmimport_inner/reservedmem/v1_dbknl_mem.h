/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: import v1 mem mgr header file
 * Author:
 * Create: 2025-3-14
 */
#ifndef GMIMPORT_V1_DBKNL_MEM_H
#define GMIMPORT_V1_DBKNL_MEM_H

#include "v1_def.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define DB_LOG_MULTIPLIER 10

typedef enum tagROW_DIR_STATE {
    /* Indicates the row directory is free. Indicate that the Row allocate from free list within a page */
    ROW_DIR_FREE = 0,
    ROW_DIR_USED, /* Indicates the row directory is used. */
    ROW_DIR_NEW,  /* Indicator that a new Row allocate from continuous chunk of memory within a Var Data Page */
    ROW_DIR_BUTT
} ROW_DIR_STATE;

/* Page management lists */
typedef enum TagPageListType {
    PAGE_ACTIVE = 0, /* Indicates the page belongs to active list. */
    PAGE_FULL,       /* Indicates the page belongs to full list. */
    PAGE_BUTT        /* Invalid page type. */
} PageListTypeE;

/* Structure to define Undo Entry Details for Row Alloc from Free List */
typedef struct tagDBTP_ROWALLOCLIST_STRU {
    /* Page Undo Entries */
    VOS_UINT32 ulPageId;
    VOS_UINT32 ulTotalFree;
    VOS_UINT16 usFreeRowHead;
    /* Row Undo Entries */
    VOS_UINT16 usRowId;
    VOS_UINT32 ulRowLen;
    VOS_UINT32 ulRowBlkOffset;
    VOS_UINT16 usNextRowDirIdx;
    VOS_UINT16 usPrevRowDirIdx;
} DBTP_ROWALLOCLIST_STRU;

/* Structure to define Undo Entry Details for Row Alloc from Continuous Memory
 */
typedef struct tagDBTP_ROWALLOCCONT_STRU {
    /* Page Undo Entries */
    VOS_UINT32 ulPageId;
    VOS_UINT32 ulTotalFree;
    VOS_UINT32 ulFreeSpace;
    VOS_UINT32 ulFreeSpaceOffset;
    VOS_UINT16 usNextDirIdx;
    VOS_UINT8 aucRsvd[2]; /* 2 represent 2 reserve bytes for alignment */
} DBTP_ROWALLOCCONT_STRU;

/* Structure to define Undo Entry Details for Row Free to List.
   Structure Size is equal to 20
 */
typedef struct tagDBTP_ROWFREELIST_STRU {
    /* Page Undo Entries */
    VOS_UINT32 ulPageId;
    VOS_UINT32 ulTotalFree;
    VOS_UINT16 usFreeRowHead;
    /* Row Undo Entries */
    VOS_UINT16 usRowId;
    VOS_UINT32 ulRowLen;
    VOS_UINT32 ulRowBlkOffset;
} DBTP_ROWFREELIST_STRU;

#define GET_LOGHEADER(storage, cpEnv) \
    ((DB_DATA_STORAGE_RAM == (storage)) ? ((cpEnv).pstLogBuffer->pstLogHead) : ((cpEnv).pstRsmLogBuffer->pstLogHead))

#define GET_LOGCTRL(storage, cpEnv) \
    ((DB_DATA_STORAGE_RAM == (storage)) ? ((cpEnv)->pstLogBuffer) : ((cpEnv)->pstRsmLogBuffer))

// BIM不使用ckpt，空实现
#define DBMM_MARK_MEM_BLK_DIRTY(ucCkpt, MemBlkPtr, MemBlkSize, DataPtr, DataSize) (void)0
#define DBMM_MARK_SEG_PAGE_DIRTY(ucCkPt, pstRelDes, pucMemPtr, ulSize) (void)0
#define DBMM_MARK_PAGE_DIRTY(ucCkPt, pstMemBlk, pucMemPtr, ulSize) (void)0
#define DBMM_MARK_DATA_PAGE_DIRTY(ucCkPt, pstBlkAry, nodeId, pucMemPtr, ulSize) (void)0

VOS_VOID *DBMM_GetVarRecPtr(const DBMM_PAGEMGR_STRU *pstPgMgr, DB_VARIABLE_DATATYPE *pstVarFld, VOS_UINT32 *pulLen);

VOS_VOID DBMM_UpdatePageMgrList(DBDDL_RELDESTBL_STRU *pstRelDes);

DB_ERR_CODE DBMM_RowFree(DB_DBMS_STRU *pstDBMgr, DBDDL_RELDESTBL_STRU *pstRelDesc, DBMM_PAGEMGR_STRU *pstPgMgr,
    DB_VARIABLE_DATATYPE *pstVarField, DBLOG_FLAG_ENUM enLogUndo);

VOS_VOID DBMM_PageListDeletePg(DBMM_PAGEMGR_STRU *pstPgMgr, DBMM_VARSEGPAGE_STRU *pstVarPgMap);

VOS_VOID DBMM_RecoverPageFromUndo(
    DBMM_PAGEMGR_STRU *pstPgMgr, VOS_VOID *pvUndoEntry, DBTP_LOGTYPE_ENUM enOpType, VOS_BOOL bPageTransFlag);

DB_ERR_CODE DBMM_PageMgrAlloc(DBDDL_RELDESTBL_STRU *pstRelDes, DBMM_PAGEMGR_STRU **ppstPgMgr, VOS_UINT8 ucCkpt);

VOS_VOID *DBMM_AllocMem(T_SIZE nMemSize, VOS_UINT8 ucCkpt);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
