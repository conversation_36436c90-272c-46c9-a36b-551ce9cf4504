/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: import v1 utils header file
 * Author:
 * Create: 2024-12-30
 */
#ifndef GMIMPORT_V1_UTILS_H
#define GMIMPORT_V1_UTILS_H

#include "v1_def.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

/************************* MACRO & TYPE DEFINITION ***************************/
#define DB_BITMASK(X) (1L << (X))
#define DB_CRC_16 16
#define DB_CRC_POLY_16 0x1021

/* filter lower order X bits                                                  */
#define DB_WIDTHMASK(X) (((VOS_UINT32)(((VOS_UINT32)(1L) << ((X)-1)) - 1L) << 1) | 1L)

#define DB_CRC_TBL_SIZE 256

/* This function generates a 16 bit CRC checksum */
VOS_UINT32 DB_MakeCrc16bit(VOS_UINT8 *puc<PERSON>uffer, VOS_UINT32 ulCountPara, VOS_UINT16 *pusCrc);

#define DBRK_STR_N_LEN(buf, maxLen) strnlen(buf, maxLen)
#define DBRK_STR_N_CMP(pDest, pSrc, uvCount) strncmp(pDest, pSrc, uvCount)
#define GET_MAX(a, b) (((a) > (b)) ? (a) : (b))

// cys 下面日志都要替换掉
#define DBDBG_STANDARD_REPT(v1, v2) (void)0
#define PRINTDB1(v1, v2, v3, v4, v5) (void)0  // 当前的调用点需要替换为日志
#define PRINTDB2(DebugFlag, ProcessID, Mid, ulErrorLevel, ErrorNum, Val1) (void)0
#define DBDBG_LOGINFO(usMidNo, usErrorNo, ...)
#define DBDEBUG1 0
#define VOS_PID_DBKNL 0
#define SEVERE 0
#define GENERAL 0
#define DYNAMIC_DOPRA_MEM_PT 0
#define DBDEBUG2 0
#define DBDBG_ADVANCED_LEN_TWO 2

// cys 下面信号量都要替换掉
#define DBRK_SEMA_P(v1, v2) (void)0
#define DBRK_SEMA_V(v1) (void)0

// cys DBRK_MEM_ALLOC, DBRK_MEM_FREE需替换掉
#define DBRK_MEM_ALLOC(v1, v2, size) DbDynMemCtxAlloc(GetImpV1RsmMemCtx(), size);
#define DBRK_MEM_FREE(v1, v2, addr) DbDynMemCtxFree(GetImpV1RsmMemCtx(), addr);
void SetImpV1RsmMemCtx(DbMemCtxT *memCtx);
DbMemCtxT *GetImpV1RsmMemCtx(void);
uint32_t GetImpV1LogBufLen(void);
void SetImpV1LogBufLen(uint32_t bufLen);

// cys 未实现，导入过程中应该不用加锁，先放着，待删除
VOS_VOID DB_ModuleLock(VOS_VOID);
VOS_VOID DB_ModuleUnLock(VOS_VOID);

VOS_VOID DBTP_AllocRecBuffForFreelist(VOS_UINT32 ulLen);

#define DBRK_SM_M_CREATE(pstSem) 0

#define DBRK_SEMA_DELETE(sem) (void)0

// cys v1在遇到严重问题时直接重启，此处改为返回错误码
#define VOS_REBOOT_WITH_FATALERROR() \
    do {                             \
        DB_ASSERT(false);            \
        return;                      \
    } while (0)

VOS_UINT8 DB_GetByteOrder(VOS_VOID);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
