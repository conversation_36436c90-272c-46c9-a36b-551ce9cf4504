/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: v1 dbknl mem mgr source file
 * Author:
 * Create: 2025-3-14
 */

#include "v1_dbknl.h"
#include "v1_dbknl_mem.h"
#include "v1_rsm_init.h"
#include "v1_utils.h"

VOS_VOID *DBMM_GetVarRecPtr(const DBMM_PAGEMGR_STRU *pstPgMgr, DB_VARIABLE_DATATYPE *pstVarFld, VOS_UINT32 *pulLen)
{
    /*
    1. Access the Corresponding Page
    2. Assert it should not be NULL.
    3. Assert Row Dir Id, it should be valid.
    4. Access Row Dir
    5. Assert its State, it should be ROW_DIR_USED
    6. Get the Pointer to datarecord using RowDir's offset
    7. Update pusLen with Record Length from Row Dir
    7. Return the data pointer to record
    */
    VOS_UINT8 *pucVarRec = VOS_NULL_PTR;
    DBMM_PAGE_HEADER *pstPgAddr = VOS_NULL_PTR;
    DBMM_ROWDIR_STRU *pstRowDir = VOS_NULL_PTR;

    DB_ASSERT(pstPgMgr != VOS_NULL_PTR);
    DB_ASSERT(pstVarFld != VOS_NULL_PTR);
    DB_ASSERT(pstVarFld->ulPageID != VOS_NULL_LONG);
    DB_ASSERT(pstVarFld->ulRowDirIdx < VOS_NULL_WORD);

    pstPgAddr = pstPgMgr->ppstPgMapArr[pstVarFld->ulPageID]->pstPage;
    DB_ASSERT(pstPgAddr != VOS_NULL_PTR);

    DB_ASSERT(pstVarFld->ulRowDirIdx < (pstPgAddr)->usNextRowDirIdx);
    pstRowDir = ((DBMM_ROWDIR_STRU *)(pstPgAddr + 1)) + pstVarFld->ulRowDirIdx;

    DB_ASSERT(pstRowDir->ucState == ROW_DIR_USED);
    DB_ASSERT((pstRowDir->ulOffset + pstRowDir->ulLen) <= pstPgMgr->pstPgPersAttr->ulPgSize);
    pucVarRec = ((VOS_UINT8 *)pstPgAddr) + pstRowDir->ulOffset;
    if (pulLen != VOS_NULL_PTR) {
        *pulLen = pstRowDir->ulLen;
    }

    return pucVarRec;
}

VOS_VOID DBMM_PageListAppendPg(DBMM_PAGEMGR_STRU *pstPgMgr, DBMM_VARSEGPAGE_STRU *pstVarPgMap)
{
    DBMM_VARSEGPAGE_STRU **ppstPgList = VOS_NULL_PTR;

    DB_ASSERT(pstVarPgMap != VOS_NULL_PTR);
    DB_ASSERT(pstPgMgr != VOS_NULL_PTR);
    DB_ASSERT(pstVarPgMap->pstPage != VOS_NULL_PTR);

    /* Add page to active list. */
    if (pstVarPgMap->pstPage->ucPageType == PAGE_ACTIVE) {
        ppstPgList = &pstPgMgr->pstActivePgList;
    } else { /* Add page to full list. */
        ppstPgList = &pstPgMgr->pstFullPgList;
    }

    pstVarPgMap->pPrevPage = VOS_NULL_PTR;

    /* Add the page at the beginning of list */
    /* If list is NULL, then automatically first page next point will become NULL. */
    pstVarPgMap->pNextPage = *ppstPgList;

    /* If the list has pages, add new page at the beginning of the list. */
    if (*ppstPgList != VOS_NULL_PTR) {
        (*ppstPgList)->pPrevPage = pstVarPgMap;
    }

    *ppstPgList = pstVarPgMap;
    return;
}

VOS_VOID DBMM_UpdatePageMgrList(DBDDL_RELDESTBL_STRU *pstRelDes)
{
    DBMM_PAGEMGR_STRU *pstPgMgr = VOS_NULL_PTR;
    DBMM_PAGE_HEADER *pstPage = VOS_NULL_PTR;
    VOS_UINT32 ulLoop;

    DB_ASSERT(pstRelDes != VOS_NULL_PTR);

    pstPgMgr = DB_PgMgrGet(pstRelDes);
    if (pstPgMgr == VOS_NULL_PTR) {
        return;
    }

    /* Append the Page to Active/Full List */
    for (ulLoop = 0; ulLoop < pstPgMgr->pstPgPersAttr->ulMaxPgCnt; ulLoop++) {
        if (pstPgMgr->ppstPgMapArr[ulLoop]->pstPage == VOS_NULL_PTR) {
            continue;
        }

        pstPage = pstPgMgr->ppstPgMapArr[ulLoop]->pstPage;
        if (pstPage->ulTotalFree < pstPgMgr->ulThreshold) {
            pstPage->ucPageType = PAGE_FULL;
        } else {
            pstPage->ucPageType = PAGE_ACTIVE;
        }

        DBMM_PageListAppendPg(pstPgMgr, pstPgMgr->ppstPgMapArr[ulLoop]);
    }
}

DBMM_PAGE_HEADER *DBMM_VarSegRcvrRowAllocList(const DBMM_PAGEMGR_STRU *pstPgMgr, VOS_VOID *pvUndoEntry)
{
    DBMM_PAGE_HEADER *pstPage = VOS_NULL_PTR;
    DBMM_ROWDIR_STRU *pstRow = VOS_NULL_PTR;
    DBTP_ROWALLOCLIST_STRU *pstRowAList = VOS_NULL_PTR;

    DB_ASSERT(pstPgMgr != VOS_NULL_PTR);

    pstRowAList = (DBTP_ROWALLOCLIST_STRU *)pvUndoEntry;
    DB_ASSERT(pstRowAList != VOS_NULL_PTR);

    pstPage = pstPgMgr->ppstPgMapArr[pstRowAList->ulPageId]->pstPage;

    DB_ASSERT(pstPage != VOS_NULL_PTR);
    DB_ASSERT(pstRowAList != VOS_NULL_PTR);
    pstPage->ulTotalFree = pstRowAList->ulTotalFree;

    /* Row was obtained from Free Head Node */
    if (pstRowAList->usFreeRowHead != VOS_NULL_WORD) {
        pstPage->usFreeRowHead = pstRowAList->usFreeRowHead;
    } else {
        /* Row obtained within the list */
        pstRow = ((DBMM_ROWDIR_STRU *)((VOS_VOID *)(pstPage + 1))) + pstRowAList->usPrevRowDirIdx;
        pstRow->usNextRowDirIdx = pstRowAList->usRowId;
    }

    pstRow = ((DBMM_ROWDIR_STRU *)((VOS_VOID *)(pstPage + 1))) + pstRowAList->usRowId;
    pstRow->usNextRowDirIdx = pstRowAList->usNextRowDirIdx;
    pstRow->ucState = ROW_DIR_FREE;
    pstRow->ulLen = pstRowAList->ulRowLen;
    pstRow->ulOffset = pstRowAList->ulRowBlkOffset;
    return pstPage;
}

DBMM_PAGE_HEADER *DBMM_VarSegRcvrRowAllocCont(const DBMM_PAGEMGR_STRU *pstPgMgr, VOS_VOID *pvUndoEntry)
{
    DBMM_PAGE_HEADER *pstPage = VOS_NULL_PTR;
    DBTP_ROWALLOCCONT_STRU *pstRowACont = VOS_NULL_PTR;

    DB_ASSERT(pstPgMgr != VOS_NULL_PTR);

    pstRowACont = (DBTP_ROWALLOCCONT_STRU *)pvUndoEntry;
    DB_ASSERT(pstRowACont != VOS_NULL_PTR);

    pstPage = pstPgMgr->ppstPgMapArr[pstRowACont->ulPageId]->pstPage;

    DB_ASSERT(pstPage != VOS_NULL_PTR);

    pstPage->ulTotalFree = pstRowACont->ulTotalFree;
    pstPage->ulFreeSpace = pstRowACont->ulFreeSpace;
    pstPage->ulFreeSpaceOffset = pstRowACont->ulFreeSpaceOffset;
    pstPage->usNextRowDirIdx = pstRowACont->usNextDirIdx;
    return pstPage;
}

DBMM_PAGE_HEADER *DBMM_VarSegRcvrRowFreeList(const DBMM_PAGEMGR_STRU *pstPgMgr, VOS_VOID *pvUndoEntry)
{
    DBMM_PAGE_HEADER *pstPage = VOS_NULL_PTR;
    DBMM_ROWDIR_STRU *pstRow = VOS_NULL_PTR;
    DBTP_ROWFREELIST_STRU *pstRowFList = VOS_NULL_PTR;

    DB_ASSERT(pstPgMgr != VOS_NULL_PTR);

    pstRowFList = (DBTP_ROWFREELIST_STRU *)pvUndoEntry;
    DB_ASSERT(pstRowFList != VOS_NULL_PTR);

    pstPage = pstPgMgr->ppstPgMapArr[pstRowFList->ulPageId]->pstPage;

    DB_ASSERT(pstPage != VOS_NULL_PTR);

    pstPage->ulTotalFree = pstRowFList->ulTotalFree;
    pstPage->usFreeRowHead = pstRowFList->usFreeRowHead;

    pstRow = ((DBMM_ROWDIR_STRU *)((VOS_VOID *)(pstPage + 1))) + pstRowFList->usRowId;
    pstRow->ulOffset = pstRowFList->ulRowBlkOffset;
    pstRow->ulLen = pstRowFList->ulRowLen;
    pstRow->usNextRowDirIdx = VOS_NULL_WORD;
    pstRow->ucState = ROW_DIR_USED;

    return pstPage;
}

DBMM_PAGE_HEADER *DBMM_VarSegRcvrRowFreeCont(const DBMM_PAGEMGR_STRU *pstPgMgr, VOS_VOID *pvUndoEntry)
{
    DBMM_PAGE_HEADER *pstPage = VOS_NULL_PTR;
    DBMM_ROWDIR_STRU *pstRow = VOS_NULL_PTR;
    DBTP_ROWFREECONT_STRU *pstRowFCont = VOS_NULL_PTR;

    DB_ASSERT(pstPgMgr != VOS_NULL_PTR);

    pstRowFCont = (DBTP_ROWFREECONT_STRU *)pvUndoEntry;
    DB_ASSERT(pstRowFCont != VOS_NULL_PTR);

    pstPage = pstPgMgr->ppstPgMapArr[pstRowFCont->ulPageId]->pstPage;

    DB_ASSERT(pstPage != VOS_NULL_PTR);

    pstPage->ulTotalFree = pstRowFCont->ulTotalFree;
    pstPage->ulFreeSpace = pstRowFCont->ulFreeSpace;
    pstPage->usNextRowDirIdx = pstRowFCont->usNextDirIdx;
    pstPage->ulFreeSpaceOffset = pstRowFCont->ulFreeSpaceOffset;

    pstRow = ((DBMM_ROWDIR_STRU *)((VOS_VOID *)(pstPage + 1))) + pstRowFCont->usRowId;
    pstRow->ulLen = pstRowFCont->ulRowLen;
    pstRow->ulOffset = pstRowFCont->ulRowBlkOffset;
    pstRow->usNextRowDirIdx = VOS_NULL_WORD;
    pstRow->ucState = ROW_DIR_USED;
    return pstPage;
}

VOS_VOID DBMM_RecoverPageFromUndo(
    DBMM_PAGEMGR_STRU *pstPgMgr, VOS_VOID *pvUndoEntry, DBTP_LOGTYPE_ENUM enOpType, VOS_BOOL bPageTransFlag)
{
    DBMM_PAGE_HEADER *pstPage = VOS_NULL_PTR;

    DB_ASSERT(pstPgMgr != VOS_NULL_PTR);

    switch (enOpType) {
        /* Recover the Page and Add the Row Entry to Free List */
        case DBTP_LOGTYPE_ROWALLOCLIST:
            pstPage = DBMM_VarSegRcvrRowAllocList(pstPgMgr, pvUndoEntry);
            break;

        /* Recover the Page and Add the Row to Free Block */
        case DBTP_LOGTYPE_ROWALLOCCONT:
            pstPage = DBMM_VarSegRcvrRowAllocCont(pstPgMgr, pvUndoEntry);
            break;

        /* Recover the Page and Recover the Row from Free List */
        case DBTP_LOGTYPE_ROWFREELIST:
            pstPage = DBMM_VarSegRcvrRowFreeList(pstPgMgr, pvUndoEntry);
            break;

        /* Recover the Page and Recover the Row back to Allocation */
        case DBTP_LOGTYPE_ROWFREECONT:
            pstPage = DBMM_VarSegRcvrRowFreeCont(pstPgMgr, pvUndoEntry);
            break;

        default:
            return;
    }
    DB_UNUSED(pstPage);
}

DB_ERR_CODE DBMM_PagePersAttrAlloc(DBMM_PAGEMGR_STRU *pstPgMgr, VOS_UINT8 ucCkpt)
{
    VOS_INT32 lRet;

    DB_ASSERT(pstPgMgr != VOS_NULL_PTR);

    /* Allocate Memory for Persistent Var Segment */
    pstPgMgr->pstPgPersAttr = (DBMM_PAGEMGR_PERS_STRU *)DBMM_AllocMem(
        (sizeof(DBMM_PAGEMGR_PERS_STRU) + DBMM_VARSEGLIST_EXTEND_CNT * sizeof(VOS_UINT32)), ucCkpt);
    if (pstPgMgr->pstPgPersAttr == VOS_NULL_PTR) {
        DBDBG_STANDARD_REPT("DBMM_PagePersAttrAlloc", "memory allocation failed for VarSeg persistent section");
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }

    /* Initialize the Persistent Var Segment */
    lRet = memset_s(pstPgMgr->pstPgPersAttr,
        (sizeof(DBMM_PAGEMGR_PERS_STRU) + DBMM_VARSEGLIST_EXTEND_CNT * sizeof(VOS_UINT32)), 0,
        (sizeof(DBMM_PAGEMGR_PERS_STRU)));
    if (lRet != EOK) {
        DBRK_MEM_FREE(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, pstPgMgr->pstPgPersAttr);
        DBDBG_STANDARD_REPT("DBMM_PagePersAttrAlloc", "Failure of memory set");
        return VOS_ERRNO_DB_MEMSET_FAILURE;
    }

    lRet = memset_s((pstPgMgr->pstPgPersAttr + 1), (DBMM_VARSEGLIST_EXTEND_CNT * sizeof(VOS_UINT32)), VOS_NULL_BYTE,
        (DBMM_VARSEGLIST_EXTEND_CNT * sizeof(VOS_UINT32)));
    if (lRet != EOK) {
        DBRK_MEM_FREE(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, pstPgMgr->pstPgPersAttr);
        DBDBG_STANDARD_REPT("DBMM_PagePersAttrAlloc", "Failure of memory set");
        return VOS_ERRNO_DB_MEMSET_FAILURE;
    }

    pstPgMgr->pstPgPersAttr->ulMaxPgCnt = DBMM_VARSEGLIST_EXTEND_CNT;
    return DB_SUCCESS;
}

DBMM_VARSEGMAPLIST_STRU *DBMM_VarSegAllocMapList(const VOS_UINT32 ulPageCnt)
{
    DBMM_VARSEGMAPLIST_STRU *pstVarMapList = VOS_NULL_PTR;
    DBMM_VARSEGMAPLIST_STRU *pstTmpVarMapList = VOS_NULL_PTR;
    VOS_UINT32 ulCounter;
    VOS_UINT32 ulIdx;
    VOS_INT32 lRet;

    DB_ASSERT(ulPageCnt % DBMM_VARSEGLIST_EXTEND_CNT == 0);

    ulCounter = (ulPageCnt / DBMM_VARSEGLIST_EXTEND_CNT);
    for (ulIdx = 0; ulIdx < ulCounter; ulIdx++) {
        pstTmpVarMapList = DBRK_MEM_ALLOC(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, sizeof(DBMM_VARSEGMAPLIST_STRU));
        if (pstTmpVarMapList == VOS_NULL_PTR) {
            // 异常分支释放流程，统一通过删除memCtx释放
            return VOS_NULL_PTR;
        }

        lRet = memset_s(pstTmpVarMapList, sizeof(DBMM_VARSEGMAPLIST_STRU), VOS_NULL, sizeof(DBMM_VARSEGMAPLIST_STRU));
        if (lRet != EOK) {
            // 异常分支释放流程，统一通过删除memCtx释放
            DBRK_MEM_FREE(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, pstTmpVarMapList);
            DBDBG_STANDARD_REPT("DBMM_VarSegAllocMapList", "Failure of memory set");
            return VOS_NULL_PTR;
        }

        pstTmpVarMapList->pstNext = pstVarMapList;
        pstVarMapList = pstTmpVarMapList;
    }

    return pstVarMapList;
}

DB_ERR_CODE DBMM_PageMapAlloc(DBMM_PAGEMGR_STRU *pstPgMgr)
{
    VOS_INT32 lRet;
    VOS_UINT32 ulItr;
    DBMM_VARSEGMAPLIST_STRU *pstTmpVarMapList = VOS_NULL_PTR;

    DB_ASSERT(pstPgMgr != VOS_NULL_PTR);

    /* Allocate memory for Var Seg - Var Data Pages Map */
    pstPgMgr->ppstPgMapArr =
        DBRK_MEM_ALLOC(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, (DBMM_VARSEGLIST_EXTEND_CNT * sizeof(char *)));
    if (pstPgMgr->ppstPgMapArr == VOS_NULL_PTR) {
        DBDBG_STANDARD_REPT("DBMM_PageMapAlloc", "memory allocation failed for VarSeg Map");
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }

    lRet = memset_s(pstPgMgr->ppstPgMapArr, (DBMM_VARSEGLIST_EXTEND_CNT * sizeof(char *)), 0,
        (DBMM_VARSEGLIST_EXTEND_CNT * sizeof(char *)));
    if (lRet != EOK) {
        DBRK_MEM_FREE(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, pstPgMgr->ppstPgMapArr);
        DBDBG_STANDARD_REPT("DBMM_PageMapAlloc", "Failure of memory set");
        return VOS_ERRNO_DB_MEMSET_FAILURE;
    }

    /* Allocate memory VarSeg Page Map List */
    pstPgMgr->pstVarMapList = DBMM_VarSegAllocMapList(DBMM_VARSEGLIST_EXTEND_CNT);
    if (pstPgMgr->pstVarMapList == VOS_NULL_PTR) {
        DBRK_MEM_FREE(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, pstPgMgr->ppstPgMapArr);
        DBDBG_STANDARD_REPT("DBMM_PageMapAlloc", "memory allocation failed for VarSeg Map List");
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }

    pstTmpVarMapList = pstPgMgr->pstVarMapList;
    for (ulItr = 0; ulItr < DBMM_VARSEGLIST_EXTEND_CNT; ulItr++) {
        /* Assign the Page Map entry in Destination Table local Buffer */
        pstPgMgr->ppstPgMapArr[ulItr] = &pstTmpVarMapList->astVarPage[ulItr];
    }

    return DB_SUCCESS;
}

DB_ERR_CODE DBMM_PageMgrRSMAlloc(DBDDL_RELDESTBL_STRU *pstRelDes, DBMM_PAGEMGR_STRU *pstPgMgr)
{
    VOS_INT32 lRet;

    DB_ASSERT(pstPgMgr != VOS_NULL_PTR);
    DB_ASSERT(pstRelDes != VOS_NULL_PTR);

    if (pstRelDes->ucStorage == DB_DATA_STORAGE_RSM) {
        pstPgMgr->pstPgMgrMBD =
            (DBMM_PAGEMBDMGR_STRU *)DBRK_MEM_ALLOC(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, sizeof(DBMM_PAGEMBDMGR_STRU));
        if (pstPgMgr->pstPgMgrMBD == VOS_NULL_PTR) {
            DBDBG_STANDARD_REPT("DBMM_PageMgrRSMAlloc", "memory allocation failed for VarSeg Reserve Memory");
            return VOS_ERRNO_DB_MEMALLOCFAILURE;
        }

        lRet = memset_s(pstPgMgr->pstPgMgrMBD, sizeof(DBMM_PAGEMBDMGR_STRU), 0, sizeof(DBMM_PAGEMBDMGR_STRU));
        if (lRet != EOK) {
            DBRK_MEM_FREE(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, pstPgMgr->pstPgMgrMBD);
            DBDBG_STANDARD_REPT("DBMM_PageMgrRSMAlloc", "Failure of memory set");
            return VOS_ERRNO_DB_MEMSET_FAILURE;
        }

        pstPgMgr->pstPgMgrMBD->ulPgMgrMBDId = VOS_NULL_LONG;

        /* Allocate memory for MBD List for Var data pages */
        pstPgMgr->pstPgMgrMBD->pulPgMBDList =
            DBRK_MEM_ALLOC(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, (DBMM_VARSEGLIST_EXTEND_CNT * sizeof(VOS_UINT32)));
        if (pstPgMgr->pstPgMgrMBD->pulPgMBDList == VOS_NULL_PTR) {
            DBRK_MEM_FREE(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, pstPgMgr->pstPgMgrMBD);
            DBDBG_STANDARD_REPT(
                "DBMM_PageMgrRSMAlloc", "memory allocation failed for VarSeg Reserve Memory VarData Map");
            return VOS_ERRNO_DB_MEMALLOCFAILURE;
        }

        lRet = memset_s(pstPgMgr->pstPgMgrMBD->pulPgMBDList, (DBMM_VARSEGLIST_EXTEND_CNT * sizeof(VOS_UINT32)),
            VOS_NULL_BYTE, (DBMM_VARSEGLIST_EXTEND_CNT * sizeof(VOS_UINT32)));
        if (lRet != EOK) {
            DBRK_MEM_FREE(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, pstPgMgr->pstPgMgrMBD->pulPgMBDList);
            DBRK_MEM_FREE(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, pstPgMgr->pstPgMgrMBD);
            DBDBG_STANDARD_REPT("DBMM_PageMgrRSMAlloc", "Failure of memory set");
            return VOS_ERRNO_DB_MEMSET_FAILURE;
        }
    }

    return DB_SUCCESS;
}

DB_ERR_CODE DBMM_PageMgrAlloc(DBDDL_RELDESTBL_STRU *pstRelDes, DBMM_PAGEMGR_STRU **ppstPgMgr, VOS_UINT8 ucCkpt)
{
    DBMM_PAGEMGR_STRU *pstPgMgr = VOS_NULL_PTR;
    DB_ERR_CODE errCode;
    VOS_INT32 lRet;

    DB_ASSERT(ppstPgMgr != VOS_NULL_PTR);

    /* Allocate Var Seg Memory */
    pstPgMgr = (DBMM_PAGEMGR_STRU *)DBRK_MEM_ALLOC(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, sizeof(DBMM_PAGEMGR_STRU));
    if (pstPgMgr == VOS_NULL_PTR) {
        DBDBG_STANDARD_REPT("DBMM_PageMgrAlloc", "memory allocation failed for VarSeg Manager");
        return VOS_ERRNO_DB_MEMALLOCFAILURE;
    }

    lRet = memset_s(pstPgMgr, sizeof(DBMM_PAGEMGR_STRU), 0, sizeof(DBMM_PAGEMGR_STRU));
    if (lRet != EOK) {
        DBDBG_STANDARD_REPT("DBMM_PageMgrAlloc", "Failure of memory set");
        DBRK_MEM_FREE(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, pstPgMgr);
        return VOS_ERRNO_DB_MEMSET_FAILURE;
    }

    /* Allocate Memory for Persistent Var Segment */
    errCode = DBMM_PagePersAttrAlloc(pstPgMgr, ucCkpt);
    if (errCode != DB_SUCCESS) {
        DBRK_MEM_FREE(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, pstPgMgr);
        *ppstPgMgr = VOS_NULL_PTR;
        return errCode;
    }

    /* Allocates Page List map memory */
    errCode = DBMM_PageMapAlloc(pstPgMgr);
    if (errCode != DB_SUCCESS) {
        DBRK_MEM_FREE(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, pstPgMgr->pstPgPersAttr);
        DBRK_MEM_FREE(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, pstPgMgr);
        *ppstPgMgr = VOS_NULL_PTR;
        return errCode;
    }

    /* If relation is stored in RSM, then allocate memory for RSM Var Seg Management. */
    errCode = DBMM_PageMgrRSMAlloc(pstRelDes, pstPgMgr);
    if (errCode != DB_SUCCESS) {
        DBDBG_STANDARD_REPT("DBMM_PageMgrAlloc", "memory allocation failed for RSM related item");
        // 异常分支释放流程，统一通过删除memCtx释放
        DBRK_MEM_FREE(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, pstPgMgr->ppstPgMapArr);
        DBRK_MEM_FREE(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, pstPgMgr->pstPgPersAttr);
        DBRK_MEM_FREE(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, pstPgMgr);
        *ppstPgMgr = VOS_NULL_PTR;
        return errCode;
    }

    *ppstPgMgr = pstPgMgr;
    return DB_SUCCESS;
}

VOS_VOID *DBMM_AllocMem(T_SIZE nMemSize, VOS_UINT8 ucCkpt)
{
    DB_UNUSED(ucCkpt);
    T_SIZE nDBActSize = nMemSize;
    VOS_VOID *pPtr = VOS_NULL_PTR;

    /* Allocate the memory from the system */
    pPtr = DBRK_MEM_ALLOC(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, nDBActSize);
    if (pPtr == VOS_NULL_PTR) {
        return VOS_NULL_PTR;
    }

    /* Memset the complete block to avoid file difference in VIST library */
    VOS_INT32 lRet = memset_s(pPtr, nMemSize, VOS_NULL, nMemSize);
    if (lRet != EOK) {
        DBRK_MEM_FREE(VOS_PID_DBKNL, DYNAMIC_DOPRA_MEM_PT, pPtr);
        DBDBG_STANDARD_REPT("DBMM_AllocMem", "Failure of memory set");
        return VOS_NULL_PTR;
    }
    return pPtr;
}
