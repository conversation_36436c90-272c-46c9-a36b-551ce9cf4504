/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 * Description: v1 dbknl header file
 * Author:
 * Create: 2025-2-25
 */
#ifndef GMIMPORT_V1_DBKNL_H
#define GMIMPORT_V1_DBKNL_H

#include "v1_def.h"
#include "v1_rsm_init.h"
#include "db_v1_file_util.h"
#include "gmimport_file.h"
#include "gmimport_utils.h"
#include "tool_utils.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

/* Macros equivalent to VOS_YES and VOS_NO                                    */
#define DBKNL_YES 1
#define DBKNL_NO 0
#define DB_VERSION_NUMBER 0x00100000
/* DB file header size is fixed to 1 KB                                       */
#define DBFILE_HEADER_SIZE 1024
/* Macro to store the last error information */
#define DB_MAX_ERRINFO_LENGTH 1024

/* Iteration: 1 */
#define DBKNL_NULL_BYTE 0xFF
#define DBKNL_NULL_OFFSET 0xFFFFFFFF
#define DBKNL_MASK_THREE_BYTE 0x00FFFFFF
#define DBKNL_NULL_NODEID (T_NODE)(-1)

/* Macro to specify the current element is allocated                          */
#define DBFI_NODE_ALLOC (T_NODE)(-2)

/* Macro to denote NULL index ID                                              */
#define DBKNL_NULL_OBJID (T_OBJID)(-1)

/* Macro to denote a NULL record                                              */
#define DBKNL_NULL_RECNO (T_RECNO)(-1)

#define DBMM_DEF_RECS_PER_PAGE 200

#define DBMM_FIVE_PERCENT 0.05

#define DBDDL_BINARY_DIVIDE 2   // DBT_BCD 获取存储长度使用
#define DB_BIT_FLD_STOREDLEN 4  // DBT_BIT 类型存储长度
#define DB_MAX_BIT_FLD_SIZE 32  // DBT_BIT类型最大长度

#define DB_MAX_FLD_LEN 0xFFFF  // 单字段、表单条记录最大长度
#define DB_BYTEHEAD_LEN 2      // DBT_BYTES DBT_VBYTES类型头部长度
#define DB_NULL_CHAR_LEN 1     // string结束符长度
#define DB_TIME_FLD_STOREDLEN 3
#define DB_MACADDR_FLD_STOREDLEN 6

/* Structure to define Relation Optinal Mgrs Metadata info */
typedef struct tagDB_RELDIR_STRU {
    VOS_UINT32 ulNumOfDirEntry; /* Number of Mgrs              */
    VOS_UINT32 ulDirEntrySize;  /* Size of each Optional Mgr   */
} DB_RELDIR_STRU;

/* Structure defining directory entries (optional) */
typedef struct tagDB_DIR_ENTRY_STRU {
    VOS_UINT8 ucType;     /* Type of the optional data */
    VOS_UINT8 aucRsvd[3]; /* 3 represent 3 reserve bytes for alignment */
    VOS_UINT32 ulOffset;  /* Offset to Optional Mgr in File */
    VOS_UINT32 ulSize;    /* Size of Optional Mgr           */
} DB_DIR_ENTRY_STRU;

/* Row directory structure */
typedef struct tagDBMM_ROWDIR_STRU {
    VOS_UINT32 ulOffset;        /* Offset to Var Data block      */
    VOS_UINT32 ulLen;           /* Length of Var Data block      */
    VOS_UINT16 usNextRowDirIdx; /* Next Free Row in Current Page */
    VOS_UINT8 ucState;          /* State of Row Dir              */
    VOS_UINT8 aucReserved[1];
} DBMM_ROWDIR_STRU;

/* Structure to hold the Relation's checksum value */
typedef struct tagDB_RELCHECKSUM_DATA_STRU {
    DB_CKSUM_DATA_T ulRelMetaChecksum; /* Checksum value for Rel - metadata */
    DB_CKSUM_DATA_T ulRelDataChecksum; /* Checksum value for Rel - records  */
} DB_RELCHECKSUM_DATA;

/* Enum defining optional Managers type */
typedef enum TagDbDirType {
    DB_DIR_VARSEG_REL = 0,   /* Rel Specific Dir Entry */
    DB_DIR_VARSEG_DB = 1,    /* DB Specific Dir Entry */
    DB_DIR_REL_CHECKSUM = 2, /* Opt Manager type: Rel Checksum */
    DB_DIR_TYPE_BUTT = 0xFF
} DbDirTypeE;

/* Enum defining Default indices for Optional Mgrs in Relation Descriptor */
typedef enum tagDB_RELOPTMGRSIDX_ENUM { DB_RELMGR_VARSEG_IDX = 0, DB_RELMGR_BUTT } DB_RELOPTMGRSIDX_ENUM;

/* Macro defining Optional Mgrs Metadata Size in Relation Descriptor */
#define DB_OPT_MGR_SIZE (sizeof(DB_RELDIR_STRU) + sizeof(DB_DIR_ENTRY_STRU))

/* Macro to get the Table Map Manager Array */
#define DBMM_GET_TBL_MAP_ARY(pstDBMgr) ((DB_TABLEMAP_STRU *)(pstDBMgr)->stTblMap.pMemPtr.pPtr)

/* Macro to get the Table Space Manager Address */
#define DBMM_GET_TBL_SPACE_ADDR(pstTblMap) ((DBDDL_RELDESTBL_STRU *)(pstTblMap)->stTblSpace.pMemPtr.pPtr)

/* Macro to get the Data/Index Block Manager Address Array */
#define DBMM_GET_DATA_BLK_ADDR_ARY(pstRelDes) (DBFI_DATABLKADDR_STRU *)(pstRelDes)->stDataIdxMng.pMemPtr.pPtr

/* Macro to get the Index Block Offset Array */
#define DBMM_GET_IDX_BLK_ADDR_ARY(pstSegHead) \
    ((DBFI_INDEXBLK_STRU *)DBRK_ABS_OFFSET(pstSegHead, sizeof(DBFI_SEGHEAD_STRU)))

/* Macro to get the Data Block Start Address */
#define DBMM_GET_DATA_BLK(pstBlkAddr) ((pstBlkAddr)->stBlock.pMemPtr.pPtr)

/* Macro to get the segment header from the Data Index Manager Block */
#define DBMM_GET_SEG_HEAD(pstBlkAry, offSet) (DBRK_ABS_OFFSET(pstBlkAry, offSet))

/* Macro to get the start address of the given Data/Index block */
#define DBMM_GET_IDX_BLK_BY_OFFSET(pstDataBlkAry, ucBlk, offSet) \
    (DBRK_ABS_OFFSET((pstDataBlkAry)[(ucBlk)].stBlock.pMemPtr.pPtr, (offSet)))

/* Macro to copy the table map entries */
#define DBMM_COPY_TBL_MAP_ENTRY(pstDst, pstSrc)                                \
    do {                                                                       \
        (pstDst)->rTableID = (pstSrc)->rTableID;                               \
        (pstDst)->stTblSpace.pMemPtr.pPtr = (pstSrc)->stTblSpace.pMemPtr.pPtr; \
        (pstDst)->stTblSpace.ulBlkSize = (pstSrc)->stTblSpace.ulBlkSize;       \
        (pstDst)->stTblSpace.ulFOffset = (pstSrc)->stTblSpace.ulFOffset;       \
    } while (0)

#define DBDDL_GET_IDX_DESC(pstRelDes, iIdxId) \
    ((DBDDL_IDXDESTBL_STRU *)DBRK_ABS_OFFSET( \
        pstRelDes, (pstRelDes)->usFirstIdx + ((iIdxId) * sizeof(DBDDL_IDXDESTBL_STRU))))

#define DBDDL_GET_FLD_DESC(pstRelDes, fFldId)                                                      \
    ((DBDDL_RFLDDESTBL_STRU *)DBRK_ABS_OFFSET(                                                     \
        ((DB_TBL_FEATURE_MGR *)((pstRelDes)->pTblRegFeatures.pPtr))->apRegFeatures[DB_FLDDES_PTR], \
        ((fFldId) * sizeof(DBDDL_RFLDDESTBL_STRU))))

/* Macro to reset the Memory Block Info */
#define DBMM_RESET_MEM_BLK_PTR(pstMemBlk)           \
    do {                                            \
        (pstMemBlk)->pMemPtr.pPtr = VOS_NULL_PTR;   \
        (pstMemBlk)->ulBlkSize = 0;                 \
        (pstMemBlk)->ulFOffset = DBKNL_NULL_OFFSET; \
    } while (0)

#define DBDDL_GET_FIRST_FLD_DESC(pstRelDes) \
    ((DBDDL_RFLDDESTBL_STRU *)((DB_TBL_FEATURE_MGR *)((pstRelDes)->pTblRegFeatures.pPtr))->apRegFeatures[DB_FLDDES_PTR])

#define GET_ROWIDX(start, row, numCols, type) ((start) + ((VOS_SIZE_T)(row) * (numCols)))

/* Get the result segment current record number                              */
#define DBFI_GETCURRSLTSEGRECORD(pstHead, ulRecNum)                           \
    do {                                                                      \
        VOS_UINT32 ulRsltNo;                                                  \
        VOS_UINT32 ulRsltSegNo;                                               \
        DBFI_RSLTSEGMNG_STRU *pstRsltSegMng;                                  \
        ulRsltSegNo = (pstHead)->stCurRsltPos.ulRsltSegNo;                    \
        pstRsltSegMng = (pstHead)->pstRsltSegMgr;                             \
        ulRsltNo = (pstHead)->stCurRsltPos.ulRsltNo;                          \
        (ulRecNum) = pstRsltSegMng->pstRsltSeg[ulRsltSegNo].arecNo[ulRsltNo]; \
    } while (0)

#define TBLDIM_SIZE 256
#define TBLCNT_SIZE 5
#define TBLCNT_DI 0
#define TBLCNT_REC 1
#define TBLCNT_INVALID 2
#define TBLCNT_PGMGR 3
#define TBLCNT_PGCNT 4

#define GET_TBL(tblList, relID) GET_ROWIDX(tblList, relID, TBLDIM_SIZE, VOS_UINT32)

#define GET_TBLCNT(tblList, relID) GET_ROWIDX(tblList, relID, TBLCNT_SIZE, VOS_UINT32)

/* Gets the Page Manager from Relation Descriptor feature list */
static inline __attribute__((always_inline)) DBMM_PAGEMGR_STRU *DB_PgMgrGet(const DBDDL_RELDESTBL_STRU *pstRelDesc)
{
    return ((DB_TBL_FEATURE_MGR *)(pstRelDesc->pTblRegFeatures.pPtr))->apRegFeatures[DB_PG_MGR_PTR];
}

/* Sets the Page Manager from Relation Descriptor feature list */
static inline __attribute__((always_inline)) VOS_VOID DB_PgMgrSet(
    DBDDL_RELDESTBL_STRU *pstRelDes, DBMM_PAGEMGR_STRU *pstPgMgr)
{
    ((DB_TBL_FEATURE_MGR *)(pstRelDes->pTblRegFeatures.pPtr))->apRegFeatures[DB_PG_MGR_PTR] = pstPgMgr;
}

/* Gets the Optional Manager from Relation Descriptor feature list */
static inline __attribute__((always_inline)) DB_RELDIR_STRU *DB_OptMgrGet(DBDDL_RELDESTBL_STRU *pstRelDesc)
{
    return ((DB_TBL_FEATURE_MGR *)(pstRelDesc->pTblRegFeatures.pPtr))->apRegFeatures[DB_OPT_MGR_PTR];
}

/* Gets the Relation Descriptor */
static inline __attribute__((always_inline)) DB_RELDIR_STRU *DB_RelDirGet(DBDDL_RELDESTBL_STRU *pstRelDes)
{
    return DBRK_ABS_OFFSET(
        (pstRelDes), (sizeof(DBDDL_RELDESTBL_STRU) + (pstRelDes)->ucFldNum * sizeof(DBDDL_RFLDDESTBL_STRU) +
                         (pstRelDes)->ucIdxNum * sizeof(DBDDL_IDXDESTBL_STRU)));
}

/* Gets the Variable Field Data pointer */
#define DBFI_VAR_FLD_DATA(pucVarRec, ucPos) ((pucVarRec) + *(VOS_UINT32 *)((pucVarRec) + ((ucPos) << 2)))

/* Gets the VarField Metadata info from DI Record */
#define DBFI_VAR_DATA_DESC_GET(pucData) (DB_VARIABLE_DATATYPE *)(pucData)

extern DB_DBMS_STRU **g_ppstDBArr;  // cys 重构掉全局变量
extern void *g_RSMSem;              // cys 重构掉全局变量
extern VOS_BOOL g_bRollBackSwitch;  // cys 重构掉全局变量

VOS_VOID *DBMM_AllocMem(T_SIZE nMemSize, VOS_UINT8 ucCkpt);

DBMM_VARSEGMAPLIST_STRU *DBMM_VarSegAllocMapList(const VOS_UINT32 ulPageCnt);

VOS_BOOL DB_IsTblIDExist(DB_DBMS_STRU *pstDBMgr, T_RELATION rRelID, VOS_UINT16 *pusPos);

VOS_UINT32 ImpV1GetDbNum(void);

void ImpV1ResetDb(void);

DB_ERR_CODE DB_CreateDb(
    const VOS_UINT8 *pucDbName, VOS_UINT8 *pucDbDir, const DB_INST_CONFIG_STRU *pstCfg, DB_DATA_STORAGE_ENUM enStorage);

DB_ERR_CODE DB_OpenDB(const VOS_UINT8 *pucDbDir, VOS_UINT8 *pucDbName, VOS_UINT32 *pulDbId);

DB_ERR_CODE DB_CreateTblByIDEx(
    VOS_UINT32 ulDbId, VOS_UINT16 usRelId, DB_REL_DEF_STRU *pstRelDef, DB_REG_FEATURE_STRU *pstFeature);

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
