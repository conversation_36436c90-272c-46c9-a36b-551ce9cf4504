/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2020-2021. All rights reserved.
 * Description: header file for importing schema from  file or directory
 * Author: z<PERSON>youjian
 * Create: 2020-8-24
 */
#ifndef GMIMPORT_FILE_H
#define GMIMPORT_FILE_H

#include <dirent.h>
#include "db_mem_context.h"
#include "db_json_common.h"
#include "adpt_locator.h"
#include "tool_utils.h"

#ifdef __cplusplus
extern "C" {
#endif /* __cplusplus */

#define GMIMP_MAX_FILENAME_LENGTH 128
// 导入点的规律是 1 2 4 ... 1024(最大)
#define MAX_IMPORT_VERTEX_NUM 2047
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
#define CRC_CODE_LEN 8
#define DATA_FILE_LEN_SIZE 10
#endif

/* 修改此处请关注 g_gmdbImpFileSuffix */
typedef enum {
    FILE_VSCHEMA_OLD = 0,  // gmjson 兼容V3版schema文件类型
    FILE_VSCHEMA,          // vertexjson
    FILE_ESCHEMA,          // edgejson
    FILE_CONFIG,           // gmconfig
    FILE_VDATA,            // vertexdata
    FILE_GMDATA,           // gmdata, 兼容V3版data文件类型
    FILE_EDATA,            // edgedata
    FILE_KVDATA,           // gmkv
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
    FILE_BINDATA,  // bin_data
#endif
    FILE_GMRESP,       // gmrespool
    FILE_NSP,          // gmnsp, namespace json
    FILE_GMFILEDPOOL,  // gmresfiledpool
    FILE_DATALOG,      // datalog
    FILE_BACKUP,       // conf backup
#ifdef TS_MULTI_INST
    FILE_GMSQL,  // gmsql
#endif
#ifdef FEATURE_RSMEM
    FILE_V1_BIN,         // v1_bin
    FILE_V1_RSM,         // v1_rsm
    FILE_RSM_MIGRATION,  // rsm_migration
#endif
    // 添加文件类型时需要位于FILE_FOLDER之前，并且在g_gmdbImpFileSuffix补充相应的文件后缀
    FILE_FOLDER,  // directory
    FILE_BOTTOM
} ImpFileTypeE;

/* 修改此处请关注 g_gmdbImpCmdName */
typedef enum GmImpCmdType {
    /* import single file */
    GMIMP_CMD_NSP = 0,
    GMIMP_CMD_VSCHEMA,     // V for vertex
    GMIMP_CMD_ESCHEMA,     // E for edge
    GMIMP_CMD_KVTABLE,     // for kv
    GMIMP_CMD_IMP_SCHEMA,  // for vertex and kvtable
    GMIMP_CMD_VDATA,
    GMIMP_CMD_EDATA,
    GMIMP_CMD_KVDATA,
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
    GMIMP_CMD_BINDATA,  // for warm reboot
#endif
    GMIMP_CMD_RESPOOL,
    /* import cache (folder) */
    GMIMP_CMD_CACHE,
    /* import datalog file */
    GMIMP_CMD_DATALOG,
    /* bind label or respool */
    GMIMP_CMD_BIND,
    GMIMP_CMD_BIND_LABEL,
    GMIMP_CMD_BIND_EXT_RESPOOL,
    GMIMP_CMD_RESTORECONF,
#ifdef TS_MULTI_INST
    GMIMP_CMD_SQL,
#endif
#ifdef FEATURE_RSMEM
    /* import GMDBV1 bin file */
    GMIMP_CMD_V1_BIN,
    GMIMP_CMD_V1_RSM,
    GMIMP_CMD_RSM_MIGRATION,
#endif
    GMIMP_CMD_BOTTOM,
} GmImpCmdTypeE;

typedef struct ImportFileInfo {
    char path[DB_MAX_PATH];
    char name[GMIMP_MAX_FILENAME_LENGTH];
    ImpFileTypeE type;
    int32_t sizeLimit;
    bool isImport;  // 根据max_record_count判断表是否导入
} ImportFileInfoT;

#define IMP_INCLUDE_VSCHEMA 0x01
#define IMP_INCLUDE_KVTABLE 0x02

#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
typedef enum ExportLabelType { GMEXP_NONE, GMEXP_VERTEX_LABEL, GMEXP_KV_LABEL, GMEXP_BOTTOM } ExportLabelTypeE;

// warm reboot场景使用

#define SLEEP_TIME 200
typedef struct {
    char *nameSpace;             // 命名空间名
    char *labelName;             // 表名
    char *oriLabelName;          // 原始表表名
    ExportLabelTypeE labelType;  // 表类型
} FileNameInfoT;
#endif

typedef struct {
    uint32_t successNum;
    uint32_t dupNum;
} ImpVertexBatchParasT;

typedef struct {
    uint32_t successNum;
    uint32_t handleNum;
} ImpVertexLabelParasT;

typedef struct {
    const char *labelName;
    const DbJsonT *json;
    GmcOperationTypeE opType;
    size_t totalVertexNum;       // 一批次导入的 total num
    size_t allTotalVertexNum;    // 整个文件导入的 total num
    bool ignoreHighVersionData;  // 导入过程是否可以忽略高版本数据
} ImpVertexParasT;

typedef struct ImportArgument {
    char labelName[MAX_TABLE_NAME_LEN];
    char resPoolName[MAX_RES_POOL_NAME_LEN];
    char extResPoolName[MAX_RES_POOL_NAME_LEN];
    char outputPath[DB_MAX_PATH];
    char ignoreOption[MAX_IGNORE_OPTION_LENGTH];
    ImportFileInfoT file;
    uint32_t repeatFileNum;
    ImportFileInfoT repeatFileArray[DB_MAX_GMIMPORT_OPTION_REPEAT_NUM - 1];
    GmImpCmdTypeE cmdType;
    DbJsonT *jsonObject;          // 导入文件的json结构体
    char *jsonStr;                // 导入文件的json字符串
    size_t jsonStrLen;            // 导入文件内容长度，kv解析时需要
    char *configJsonStr;          // 配置文件的json字符串
    DbJsonT *configDbJsonObject;  // 配饰文件的json结构体
    bool isUninstall;             // isUninstall==1卸载 | isUninstall==0加载
    bool isUpgrade;               // isUpgrade == 1 升级 | isUpgrade == 0 正常加载流程
    bool isRollbackUpgrade;       // isRollbackUpgrade==1卸载升级so
    bool isIgnoreHVersionData;    // 兼容V3支持导入高版本数据
    bool isDtlDistribute;         // --distribute 用于Datalog分布式场景下加载so
    TlsDigestInfoT digest;
    uint32_t includeCmdFlag;  // 标记--include 包含的cmd类型, 当前仅cache支持--include
    bool enabledFileTypes[(uint32_t)FILE_BOTTOM];
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
    bool isDirPersist;            // isDirPersist == 0 正常导入| == 1 时间戳文件夹的导入
    uint32_t importSize;          // warm reboot场景记录mmap后的内存数据大小
    char *importBuf;              // warm reboot场景记录mmap后的内存数据指针
    FileNameInfoT fileNameInfos;  // warm reboot场景多线程导入用来记录表相关信息
    uint32_t threadNum;           // warm reboot场景多线程导入线程数
    uint16_t propNum;
    uint8_t isUpOrDe;  // warm reboot场景表是否进行过升降级
    char domainName[DOMAIN_NAME_MAX_LEN];
#endif
} ImportArgumentT;

typedef struct {
    int32_t fd;
    char *buffer;
    size_t bufferSize;
    size_t fileLen;
    bool isAllImport;
} ImportArgs;

static inline bool ImportIsCacheCommandIncludeVschema(GmImpCmdTypeE cmdType, uint32_t includeCmdFlag)
{
    return cmdType == GMIMP_CMD_CACHE && ((includeCmdFlag & IMP_INCLUDE_VSCHEMA) != 0);
}

static inline bool ImportIsCacheCommandIncludeKvtable(GmImpCmdTypeE cmdType, uint32_t includeCmdFlag)
{
    return cmdType == GMIMP_CMD_CACHE && ((includeCmdFlag & IMP_INCLUDE_KVTABLE) != 0);
}

typedef struct ImportOption {
    ImportArgumentT args;
#if !defined(WARM_REBOOT) && !defined(FEATURE_RSMEM)
    char domainName[DOMAIN_NAME_MAX_LEN];
#endif
    char namespaceName[MAX_NAMESPACE_LENGTH];
    char userName[MAX_USER_NAME_LEN];
} ImportOptionT;

#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
typedef struct {
    uint32_t totalFileNums;
    uint32_t successFileNums;
    uint32_t skipFileNums;  // 仅当max_record_count=0时，统计到skipNum
    uint32_t totalDirNums;
    uint32_t successDirNums;
} ImpFileNumStatT;

// 用途：存储打印LOG的导入成功/失败文件个数的信息
// 是否并发初始化：否
// 是否并发读写：否
// 并发方案：无
extern ImpFileNumStatT g_gmdbTotalImpNumStat;
#endif

Status ImportSetFileName(ImportArgumentT *args);

Status ImportSingleFile(GmcStmtT *stmt, ImportArgumentT *args);

Status ImportLabel(GmcStmtT *stmt, ImportArgumentT *args);

Status ImportRespool(GmcStmtT *stmt, ImportArgumentT *args);

Status ImportFile(GmcStmtT *stmt, ImportOptionT *toolOption);

ImpFileTypeE ImportGetFileTypeByCmdType(GmImpCmdTypeE cmdType);

Status ImportBindExtResPool(GmcStmtT *stmt, ImportOptionT *toolOption);

Status RestoreConf(GmcStmtT *stmt, ImportOptionT *toolOption);

Status ImportDatalog(GmcStmtT *stmt, ImportOptionT *toolOption);

Status ImportVertexDataInner(GmcStmtT *stmt, const ImpVertexParasT *paras, ImpVertexBatchParasT *execParams);
#if defined(WARM_REBOOT) || defined(FEATURE_RSMEM)
Status ImportParseKvInfo(char *buffer, uint64_t bufferSize, uint64_t *offset, GmcKvTupleT *kvInfo);

Status ImportVerifyFileContent(GmcStmtT *stmt, ImportArgumentT *args);

Status ImportPreparedScanFile(const ImportArgumentT *args, DIR **dir, char *dirPath, uint32_t len);

void ImportScanFolderFromDirInner(
    GmcStmtT *stmt, ImportArgumentT *args, ImpFileNumStatT *impfileStat, uint32_t recurDepth);

Status ImportScanBinFileFromDir(GmcStmtT *stmt, ImportArgumentT *args, ImpFileTypeE fileType, uint32_t recurDepth);

void HandleImportFolderResult(void);
#endif
#ifdef FEATURE_DATALOG
Status UninstallDatalog(GmcStmtT *stmt, ImportOptionT *toolOption);
#endif

#ifdef __cplusplus
}
#endif /* __cplusplus */

#endif
