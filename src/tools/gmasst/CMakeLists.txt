project(gmasst)

if (FUZZ OR COVERAGE)
    ADD_SUBDIRECTORY(gmasst_inner)
endif()

ADD_COMPILE_OPTIONS(-Wl,--copy-dt-needed-entries -fPIE)

include_sub_directories_recursively(${CMAKE_CURRENT_SOURCE_DIR})
include_sub_directories_recursively(${GMDB_ROOT_DIR}/src/common/include)

aux_source_directory(. SRC_MAIN_LIST)
aux_source_directory(${gmasst_SOURCE_DIR}/gmasst_inner SRC_GMASST_LIST)

set(SRC_GMASST_LIST ${SRC_MAIN_LIST} ${SRC_GMASST_LIST})

if (TOOLS_EMBED_SO)
  generate_tools_embed_so(gmasst_embed "${SRC_GMASST_LIST}" "${TOOL_NAME}")
endif()

# 编译patch_obj不编译可执行程序
if(COMPILE_PATCH_OBJ)
  return()
endif()

ADD_EXECUTABLE (gmasst ${SRC_GMASST_LIST})
if(STRIP)
  separate_debug_info(gmasst)
endif()

target_link_libraries(gmasst ${TOOL_NAME})

install(TARGETS gmasst
  RUNTIME DESTINATION bin
)
