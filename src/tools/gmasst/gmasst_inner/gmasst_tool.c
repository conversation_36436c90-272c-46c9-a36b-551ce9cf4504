/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 * Description: source file for commands
 * Create: 2022-10-09
 */

#include "gmasst_tool.h"
#include "ee_stmt.h"
#include "ee_context.h"
#include "ee_cmd.h"
#include "cpl_public_parser.h"
#include "ee_stmt_interface.h"
#include "db_last_error.h"
#include "tool_utils.h"
#include "gmasst_typedef.h"
#include "adpt_define.h"
#include "db_json.h"
#include "tool_main.h"

#ifdef __cplusplus
extern "C" {
#endif

#define MAX_RECORD_COUNT_LIMIT (10 * 1024 * 1024)

void GmAsstHelp(void)
{
    const char *gmAsstOption[][MANUAL_COL_NUM] = {
        {DB_OPTION_ARG_HELP, "", "print online help manual"},
        {GMASST_OPTION_SCHEMA_ALTER, "", "compatibility determination and memory increment"},
        {GMASST_OPTION_V3, "", "compatible with v3 mode"},
        {GMASST_OPTION_OLD_SCHEMA, "<old_schema_file_path>", "schema file path before schema alter"},
        {GMASST_OPTION_NEW_SCHEMA, "<new_schema_file_path> ", "schema file path after schema alter"},
        {GMASST_OPTION_CFG_SCHEMA, "<config_file_path>", "schema config file path"},
    };

    DbPrintManual("[OPTION]", gmAsstOption, ELEMENT_COUNT(gmAsstOption), NULL);
}

static const char *GmAsstGetOption(DbOptionRuleT *optionRule, const char *optionName)
{
    DB_POINTER2(optionRule, optionName);
    DbOptionParamT *param = DbGetParamsByOptionName(optionRule, optionName);
    if (param == NULL) {
        return NULL;
    }

    return param->paramVal[0].strVal;
}

Status GmAsstLoadFile(DbMemCtxT *memCtx, const char *fullPath, char **pbuf)
{
    DB_POINTER3(memCtx, fullPath, pbuf);
    Status ret;
    size_t len = 0;
    ret = DbFileSize(fullPath, &len);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "unable to open file %s.", fullPath);
        return ret;
    }

    if (len > CLT_MAX_LABEL_LENGTH) {
        ret = GMERR_INVALID_PARAMETER_VALUE;
        TOOL_RUN_ERROR(
            DbPrintfDefault, ret, "The size of file %s exceeds %" PRIu32 " bytes.", fullPath, CLT_MAX_LABEL_LENGTH);
        return ret;
    }
    // 此处内存将在GmAsstEntry处通过DbDeleteDynMemCtx(memCtx)统一释放
    char *buf = DbDynMemCtxAlloc(memCtx, len + 1);
    if (buf == NULL) {
        ret = GMERR_OUT_OF_MEMORY;
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "unable to alloc buffer.");
        return ret;
    }

    int32_t fd = 0;
    ret = DbOpenFile(fullPath, READ_ONLY, PERM_USER, &fd);
    if (ret != GMERR_OK) {
        // 失败后内存统一释放，不在这里释放。
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "unable to open file, file path=%s, os ret no: %" PRId32 ".", fullPath,
            DbAptGetErrno());
        return ret;
    }

    ret = DbReadFileExpSize(fd, buf, len);
    if (ret != GMERR_OK) {
        DbCloseFile(fd);
        return ret;
    }

    DbCloseFile(fd);
    buf[len] = '\0';
    *pbuf = buf;
    return GMERR_OK;
}

void GmAsstEvalMemDiff(
    const CmdEstimateMemoryDataT *newData, const CmdEstimateMemoryDataT *oldData, CmdEstimateMemoryDataT *diffData)
{
    DB_POINTER3(newData, oldData, diffData);
    // 这样写以后有新增字段会有alarm
    *diffData = (CmdEstimateMemoryDataT){
        .objectNum = newData->objectNum - oldData->objectNum,
        .objectSize = newData->objectSize - oldData->objectSize,
        .indexSize = newData->indexSize - oldData->indexSize,
        .uniqueHashSize = newData->uniqueHashSize - oldData->uniqueHashSize,
        .nonUniqueHashSize = newData->nonUniqueHashSize - oldData->nonUniqueHashSize,
        .hashclusterSize = newData->hashclusterSize - oldData->hashclusterSize,
        .lpmSize = newData->lpmSize - oldData->lpmSize,
        .localkeySize = newData->localkeySize - oldData->localkeySize,
        .totalSize = newData->totalSize - oldData->totalSize,
    };
}

static void EstimateSizePrint(const CmdEstimateMemoryDataT *diffData)
{
    (void)DbPrintfDefault("       %-36s%-36" PRId64 "\n", "Object num", (int64_t)diffData->objectNum);
    (void)DbPrintfDefault("       %-36s%-36" PRId64 "\n", "Object size(B)", (int64_t)diffData->objectSize);
    (void)DbPrintfDefault("       %-36s%-36" PRId64 "\n", "Unique hash size(B)", (int64_t)diffData->uniqueHashSize);
    (void)DbPrintfDefault(
        "       %-36s%-36" PRId64 "\n", "Non unique hash size(B)", (int64_t)diffData->nonUniqueHashSize);
    (void)DbPrintfDefault("       %-36s%-36" PRId64 "\n", "Hashcluster size(B)", (int64_t)diffData->hashclusterSize);
    (void)DbPrintfDefault("       %-36s%-36" PRId64 "\n", "Lpm size(B)", (int64_t)diffData->lpmSize);
    (void)DbPrintfDefault("       %-36s%-36" PRId64 "\n", "Localkey size(B)", (int64_t)diffData->localkeySize);
    (void)DbPrintfDefault("       %-36s%-36" PRId64 "\n", "Index size(B)", (int64_t)diffData->indexSize);
    (void)DbPrintfDefault("       %-36s%-36" PRId64 "\n", "Total size(B)", (int64_t)diffData->totalSize);
}

bool GmAsstCheckVertexLabelCompatible(DmVertexLabelT *newLabel, DmVertexLabelT *oldLabel)
{
    DB_POINTER2(newLabel, oldLabel);
    Status ret;
    if (oldLabel->metaCommon.version == DM_SCHEMA_INVALID_VERSION) {
        oldLabel->metaCommon.version = 0;
    }
    if (newLabel->metaCommon.version == DM_SCHEMA_INVALID_VERSION) {
        (void)DbPrintfDefault("    Incompatible schema alter!\n");
        (void)DbPrintfDefault("    The update vertexLabel version id has not been set.\n");
        (void)DbPrintfDefault("--------------------------------------------------------------------------------\n");
    }
    if (newLabel->metaCommon.version <= oldLabel->metaCommon.version) {
        (void)DbPrintfDefault("    Incompatible schema alter!\n");
        (void)DbPrintfDefault("    The update vertexLabel version id should be larger.\n");
        (void)DbPrintfDefault("--------------------------------------------------------------------------------\n");
        return false;
    }
    // 校验
    uint32_t vertexLabelLen =
        (newLabel->metaVertexLabel->labelLevel == VERTEX_LEVEL_SIMPLE) ? DmGetFixVertexLabelLen4QE(newLabel) : 0u;
    ret = AlterVertexLabelCheckCompatible(oldLabel, newLabel, vertexLabelLen);
    if (ret != GMERR_OK) {
        TextT *lastError = DbGetLastErrorInfo();
        (void)DbPrintfDefault("    Incompatible schema alter!\n    %s\n", lastError->str);
        (void)DbPrintfDefault("--------------------------------------------------------------------------------\n");
        return false;
    }

    (void)DbPrintfDefault("    Compatible schema alter!\n");
    return true;
}

static Status GetEstimateMemory(const DmVertexLabelT *vertexLabel, CmdEstimateMemoryDataT *data)
{
    DB_POINTER2(vertexLabel, data);
    Status ret = EstimateVertexDataSize(vertexLabel, data);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 索引计算依赖了共享内存，不能调用，目前索引不能修改，增量固定是0，索引先不调用。
    return EstimateTotalSize(data);
}

void GmAsstCheckMemoryIncrement(DmVertexLabelT *newLabel, DmVertexLabelT *oldLabel)
{
    DB_POINTER2(newLabel, oldLabel);
    Status ret;

    (void)DbPrintfDefault("    Memory increment\n");

    DbJsonT *configJson = DbJsonLoads(oldLabel->metaVertexLabel->configJson, DB_JSON_REJECT_DUPLICATES);
    if (configJson != NULL) {
        DbJsonT *maxRecordCount = DbJsonObjectGet(configJson, "max_record_count");
        if (maxRecordCount == NULL) {
            (void)DbPrintfDefault(
                "       [WARN] The config json file is specified but does not contain max_record_count.\n");
        }
        DbJsonDelete(configJson);
    }

    const uint64_t newMaxVertexNum = newLabel->commonInfo->heapInfo.maxVertexNum;
    const uint64_t oldVertexNum = oldLabel->commonInfo->heapInfo.maxVertexNum;
    if (oldVertexNum == UINT64_MAX) {
        (void)DbPrintfDefault("       [ERROR] Unable to obtain max record count.\n");
        return;
    }

    if (oldVertexNum > MAX_RECORD_COUNT_LIMIT) {
        (void)DbPrintfDefault(
            "        [ERROR] Max record count is larger than %" PRIu64 ".\n", (uint64_t)MAX_RECORD_COUNT_LIMIT);
        return;
    }

    if (newMaxVertexNum != oldVertexNum) {
        (void)DbPrintfDefault("       [WARN] MaxVertexNum changed when alter schema, but will not take effect.\n");
        newLabel->commonInfo->heapInfo.maxVertexNum = oldVertexNum;
    }

    CmdEstimateMemoryDataT newData = {0};
    ret = GetEstimateMemory(newLabel, &newData);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "unable to estimate label %s's memory.", newLabel->metaCommon.metaName);
        return;
    }

    CmdEstimateMemoryDataT oldData = {0};
    ret = GetEstimateMemory(oldLabel, &oldData);
    if (ret != GMERR_OK) {
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "unable to estimate label %s's memory.", oldLabel->metaCommon.metaName);
        return;
    }

    CmdEstimateMemoryDataT diffData = {0};
    GmAsstEvalMemDiff(&newData, &oldData, &diffData);
    EstimateSizePrint(&diffData);
}

void GmAsstCheckOneSchema(DmVertexLabelT *newLabel, DmVertexLabelT *oldLabel)
{
    DB_POINTER2(newLabel, oldLabel);
    (void)DbPrintfDefault("--------------------------------------------------------------------------------\n");
    (void)DbPrintfDefault("Schema %s\n", oldLabel->metaCommon.metaName);
    if (!GmAsstCheckVertexLabelCompatible(newLabel, oldLabel)) {
        return;
    }

    GmAsstCheckMemoryIncrement(newLabel, oldLabel);
    (void)DbPrintfDefault("--------------------------------------------------------------------------------\n");
    return;
}

Status GmAsstCheckSchemaDiff(DbListT *newList, DbListT *oldList)
{
    DB_POINTER2(oldList, newList);

    for (uint32_t i = 0; i < DbListGetItemCnt(newList); i++) {
        QryCreateSingleVertexLabelDescT *newLabel = (QryCreateSingleVertexLabelDescT *)DbListItem(newList, i);
        if (SECUREC_UNLIKELY(newLabel == NULL)) {  // 没必要
            DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "value is null when get new schema.");
            return GMERR_NULL_VALUE_NOT_ALLOWED;
        }
        uint32_t j = 0;
        for (j = 0; j < DbListGetItemCnt(oldList); j++) {
            QryCreateSingleVertexLabelDescT *oldLabel = (QryCreateSingleVertexLabelDescT *)DbListItem(oldList, j);
            if (SECUREC_UNLIKELY(oldLabel == NULL)) {
                DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "value is null when get old schema.");
                return GMERR_NULL_VALUE_NOT_ALLOWED;
            }
            if (strcmp(oldLabel->vertexLabel->metaCommon.metaName, newLabel->vertexLabel->metaCommon.metaName) == 0) {
                GmAsstCheckOneSchema(newLabel->vertexLabel, oldLabel->vertexLabel);
                break;
            }
        }

        if (j == DbListGetItemCnt(oldList)) {
            TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_PARAMETER_VALUE,
                "The label %s does not exist in old schema file.", newLabel->vertexLabel->metaCommon.metaName);
            return GMERR_INVALID_PARAMETER_VALUE;
        }
    }

    return GMERR_OK;
}

Status GmAsstParseLabel(QryStmtT *stmt, const char *fullPath, TextT configText, QryCreateVertexLabelDescT **desc)
{
    DB_POINTER3(stmt, fullPath, desc);
    Status ret;

    char *schemaContext = NULL;
    ret = GmAsstLoadFile(stmt->memCtx, fullPath, &schemaContext);
    if (ret != GMERR_OK) {
        return ret;
    }

    TextT text = DbStr2Text(schemaContext);

    // 此处内存将在GmAsstEntry处通过DbDeleteDynMemCtx(memCtx)统一释放
    QryCreateVertexLabelDescT *newDesc = DbDynMemCtxAlloc(stmt->memCtx, sizeof(QryCreateVertexLabelDescT));
    if (newDesc == NULL) {
        ret = GMERR_OUT_OF_MEMORY;
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "unable to alloc QryCreateVertexLabelDescT.");
        return ret;
    }

    *newDesc = (QryCreateVertexLabelDescT){0};
    ret = QryParseVertexLabel(stmt, &text, &configText, newDesc);
    if (ret != GMERR_OK) {
        TextT *lastError = DbGetLastErrorInfo();
        (void)DbPrintfDefault("[ERROR] The schema file %s is invalid.\n", fullPath);
        (void)DbPrintfDefault("%s\n", lastError->str);
        return ret;
    }

    *desc = newDesc;
    return GMERR_OK;
}

Status GmAsstSchemaAlterInner(DbOptionRuleT *optionRule, QryStmtT *stmt)
{
    DB_POINTER2(optionRule, stmt);
    Status ret;

    const char *newPath = GmAsstGetOption(optionRule, GMASST_OPTION_NEW_SCHEMA);
    const char *oldPath = GmAsstGetOption(optionRule, GMASST_OPTION_OLD_SCHEMA);
    const char *configPath = GmAsstGetOption(optionRule, GMASST_OPTION_CFG_SCHEMA);
    if (newPath == NULL || oldPath == NULL) {
        ret = GMERR_INTERNAL_ERROR;
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "The new_schema_file_path or the old_schema_file_path is empty.");
        return ret;
    }

    TextT configText = {0};
    if (configPath != NULL) {
        char *configContext = NULL;
        ret = GmAsstLoadFile(stmt->memCtx, configPath, &configContext);
        if (ret != GMERR_OK) {
            return ret;
        }
        configText = DbStr2Text(configContext);
    }

    QryCreateVertexLabelDescT *newDesc = NULL;
    ret = GmAsstParseLabel(stmt, newPath, configText, &newDesc);
    if (ret != GMERR_OK) {
        return ret;
    }

    QryCreateVertexLabelDescT *oldDesc = NULL;
    ret = GmAsstParseLabel(stmt, oldPath, configText, &oldDesc);
    if (ret != GMERR_OK) {
        return ret;
    }

    return GmAsstCheckSchemaDiff(&newDesc->vertexLabels, &oldDesc->vertexLabels);
}

Status GmAsstSchemaAlter(DbOptionRuleT *optionRule, DbMemCtxT *memCtx)
{
    DB_POINTER2(optionRule, memCtx);
    Status ret;

    // DbCfgMgrT 此处内存将在GmAsstEntry处通过DbDeleteDynMemCtx(memCtx)统一释放
    DbCfgMgrT *cfgMgr = DbDynMemCtxAlloc(memCtx, sizeof(DbCfgMgrT));
    if (cfgMgr == NULL) {
        ret = GMERR_OUT_OF_MEMORY;
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "unable to alloc DbCfgMrgT.");
        return ret;
    }

    *cfgMgr = (DbCfgMgrT){.cfgItemNum = DB_CFG_EM_ITEM_BUTT};
    if (DbCheckIsUsedByOptionName(optionRule, GMASST_OPTION_V3)) {
        cfgMgr->cfgItems[DB_CFG_COMPATIBLE_V3].value.type = DB_DATATYPE_INT32;
        cfgMgr->cfgItems[DB_CFG_COMPATIBLE_V3].value.int32Val = 1;
    }

    const int32_t defaultPageSize = 32;
    cfgMgr->cfgItems[DB_CFG_SE_PAGE_SIZE].value.type = DB_DATATYPE_INT32;
    cfgMgr->cfgItems[DB_CFG_SE_PAGE_SIZE].value.int32Val = defaultPageSize;

    DbSetCfgHandle(cfgMgr);

    // SessionT 此处内存将在GmAsstEntry处通过DbDeleteDynMemCtx(memCtx)统一释放
    SessionT *session = DbDynMemCtxAlloc(memCtx, sizeof(SessionT));
    if (session == NULL) {
        // 内存由memCtx统一释放，这里不释放之前已经申请的内存。
        ret = GMERR_OUT_OF_MEMORY;
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "unable to alloc SessionT.");
        return ret;
    }
    *session = (SessionT){0};

    // QryContextT 此处内存将在GmAsstEntry处通过DbDeleteDynMemCtx(memCtx)统一释放
    QryContextT *qryCtx = DbDynMemCtxAlloc(memCtx, sizeof(QryContextT));
    if (qryCtx == NULL) {
        ret = GMERR_OUT_OF_MEMORY;
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "unable to alloc QryContextT.");
        return ret;
    }
    *qryCtx = (QryContextT){0};
    qryCtx->memCtx = memCtx;

    // QryStmtT 此处内存将在GmAsstEntry处通过DbDeleteDynMemCtx(memCtx)统一释放
    QryStmtT *stmt = DbDynMemCtxAlloc(memCtx, sizeof(QryStmtT));
    if (stmt == NULL) {
        ret = GMERR_OUT_OF_MEMORY;
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "unable to alloc QryStmtT.");
        return ret;
    }
    *stmt = (QryStmtT){0};

    *stmt = (QryStmtT){
        .session = session,
        .context = qryCtx,
        .memCtx = memCtx,
    };

    return GmAsstSchemaAlterInner(optionRule, stmt);
}

Status GmAsstDispatch(DbOptionRuleT *optionRule, DbMemCtxT *memCtx)
{
    DB_POINTER2(optionRule, memCtx);

    // 获取启动规则
    int32_t startupRule = DbGetStartupRule(optionRule);
    switch (startupRule) {
        case GMASST_RULE_HELP:
            GmAsstHelp();
            break;
        case GMASST_RULE_SCHEMA_ALTER:
            return GmAsstSchemaAlter(optionRule, memCtx);
        default:
            TOOL_RUN_ERROR(DbPrintfDefault, GMERR_INVALID_PARAMETER_VALUE, "Unrecognized or incomplete command line.");
            TOOL_RUN_INFO(DbPrintfDefault, "Usage instructions can be viewed through the command './gmasst -h.'");
            return GMERR_INVALID_PARAMETER_VALUE;
    }
    return GMERR_OK;
}

Status GmAsstEntry(int32_t argc, char **argv, DbOptionRuleT *optionRule)
{
    DB_POINTER(optionRule);
    Status ret;

    // 如果没有输入参数，直接打印帮助信息。
    if (argc == 1) {
        GmAsstHelp();
        return GMERR_OK;
    }

    // 注册命令参数
    ret = GmAsstOptionInit(optionRule, argc, argv);
    if (ret != GMERR_OK) {
        return ret;
    }

    // 在初始化Top Memory Context之前，先初始化钩子函数。
    DbMemCtxRegMethods(false);
    ret = DbInitTopDynMemCtx(NULL);
    if (ret != GMERR_OK) {
        return ret;
    }

    DbMemCtxArgsT args = {0};
    DbMemCtxT *memCtx = DbCreateDynMemCtx((void *)DbGetTopDynMemCtx(NULL), false, "GM_ASST", &args);
    if (memCtx == NULL) {
        ret = GMERR_MEMORY_OPERATE_FAILED;
        TOOL_RUN_ERROR(DbPrintfDefault, ret, "unable to create memory context.");
        return ret;
    }

    ret = GmAsstDispatch(optionRule, memCtx);
    DbDeleteDynMemCtx(memCtx);
    DbDestroyTopDynMemCtx(NULL);
    return ret;
}

Status GmAsstMain(int32_t argc, char **argv)
{
    DB_POINTER(argv);
#ifdef FEATURE_TOOLS_SYSLOG
    GmcLogAdptFuncsT tlsLogFuncs = {.userWriteFunc = TlsPrintLog, .handle = NULL};
    DbLogRegAdptFuncs(&tlsLogFuncs);
#endif
    Status ret;

    DbOptionRuleT optionRule = {0};
    ret = GmAsstAllocOptionRuleItems(&optionRule);
    if (ret != GMERR_OK) {
        return ret;
    }

    ret = GmAsstEntry(argc, argv, &optionRule);
    DbReleaseOptionRuleItems(&optionRule);
    return ret;
}

#ifdef FEATURE_CLT_SERVER_SAME_PROCESS
inline int32_t GmsAsstMain(int32_t argc, char *argv[])
{
    if (DbGetServerSameProcessStartFlag() > 0) {
        DB_LOG_ERROR(GMERR_DATA_EXCEPTION, "execute gms-tools in same process as gmserver.");
        return GMERR_DATA_EXCEPTION;
    }

    return GmAsstMain(argc, argv);
}
#endif

#ifdef __cplusplus
}
#endif
