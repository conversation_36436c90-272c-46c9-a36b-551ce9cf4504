/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: nlog importer
 * Create: 2024-05-31
 */

// !!! DO NOT MODIFY - THIS FILE WAS GENERATED AUTOMATICALLY

#include <stdlib.h>
#include <stdio.h>
#include <securec.h>
#include "adpt_log.h"
#include "db_json_common.h"
#include "gmei_alloc.h"
#include "gmei_directory.h"
#include "gmei_schemas.h"

static GmeiTableT *g_tables = NULL;
static uint32_t g_tableNum = 0;

static bool GmeiIsJsonFile(const char *fileName)
{
    const char *ext = strrchr(fileName, '.');
    return ext != NULL && strcmp(ext, ".json") == 0;
}

uint32_t CountJsonFiles(DIR *dirPtr, GmeiStringT pathString)
{
    struct dirent *entry;
    uint32_t count = 0;
    while ((entry = readdir(dirPtr)) != NULL) {
        char pathBuffer[1024] = {0};
        GmeiStringT fullPath =
            GmeiMakePath(pathBuffer, sizeof(pathBuffer), pathString, GmeiStringFromChars(entry->d_name));
        if (GmeiIsDotDotDirectory(fullPath) || !GmeiIsJsonFile(entry->d_name)) {
            continue;
        }
        count++;
    }
    return count;
}

int32_t GmeiCountJsonFiles(const char *path, uint32_t *fileNum, char ***fullPathArr)
{
    int32_t ret = GMERR_OK;
    struct dirent *entry;
    uint32_t count = 0;
    GmeiStringT pathString = {0};
    pathString.data = path;
    pathString.length = (uint32_t)strlen(path);

    DIR *dirPtr = opendir(path);
    if (dirPtr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_NULL_VALUE_NOT_ALLOWED, "Open json path with problems.");
        *fileNum = count;
        return GMERR_NULL_VALUE_NOT_ALLOWED;
    }

    count = CountJsonFiles(dirPtr, pathString);
    *fullPathArr = (char **)GmeiAlloc(sizeof(char *) * count);
    if (*fullPathArr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc space for json path array.");
        ret = GMERR_OUT_OF_MEMORY;
        goto EXIT;
    }
    (void)memset_s(*fullPathArr, sizeof(char *) * count, 0, sizeof(char *) * count);
    rewinddir(dirPtr);
    uint32_t i = 0;
    while ((entry = readdir(dirPtr)) != NULL && i < count) {
        char pathBuffer[1024] = {0};
        GmeiStringT fullPath =
            GmeiMakePath(pathBuffer, sizeof(pathBuffer), pathString, GmeiStringFromChars(entry->d_name));
        if (GmeiIsDotDotDirectory(fullPath) || !GmeiIsJsonFile(entry->d_name)) {
            continue;
        }
        (*fullPathArr)[i] = GmeiAlloc(sizeof(char) * MAX_JSON_PATH_LEN);
        if ((*fullPathArr)[i] == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc space for single json path.");
            ret = GMERR_OUT_OF_MEMORY;
            goto EXIT;
        }
        (void)memset_s((*fullPathArr)[i], sizeof(char) * MAX_JSON_PATH_LEN, 0, sizeof(char) * MAX_JSON_PATH_LEN);
        errno_t err = sprintf_s((*fullPathArr)[i], MAX_JSON_PATH_LEN, "%s/%s", path, entry->d_name);
        if (err < EOK) {
            DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW, "Length of json path is too long.");
            ret = GMERR_FIELD_OVERFLOW;
            goto EXIT;
        }
        i++;
    }
EXIT:
    closedir(dirPtr);
    *fileNum = count;
    return ret;
}

static int32_t GmeiFillSrcFile(DbJsonT *content, GmeiTableT *curTable)
{
    DbJsonT *srcFile = DbJsonObjectGet(content, SRC_FILE);
    if (srcFile == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s's json content.", SRC_FILE);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    const char *srcFileStr = DbJsonStringValue(srcFile);
    if (srcFileStr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s.", SRC_FILE);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint32_t size = (uint32_t)strlen(srcFileStr) + 1;
    curTable->readFile.length = size - 1;
    curTable->readFile.data = GmeiAlloc(size);
    if (curTable->readFile.data == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc space for %s:%s.", SRC_FILE, srcFileStr);
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t err = strcpy_s(curTable->readFile.data, size, srcFileStr);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Unable to copy %s:%s.", SRC_FILE, srcFileStr);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

static int32_t GmeiFillSrcTableAndDstTable(DbJsonT *content, GmeiTableT *curTable)
{
    DbJsonT *srcTable = DbJsonObjectGet(content, SRC_TABLE);
    if (srcTable == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s's json content.", SRC_TABLE);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    const char *srcTableStr = DbJsonStringValue(srcTable);
    if (srcTableStr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s.", SRC_TABLE);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint32_t size = (uint32_t)strlen(srcTableStr) + 1;
    curTable->tableName.length = size - 1;
    curTable->tableName.data = GmeiAlloc(size);
    if (curTable->tableName.data == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc space for %s:%s.", SRC_TABLE, srcTableStr);
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t err = strcpy_s(curTable->tableName.data, size, srcTableStr);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Unable to copy %s:%s.", SRC_TABLE, srcTableStr);
        return GMERR_MEMORY_OPERATE_FAILED;
    }

    DbJsonT *dstTable = DbJsonObjectGet(content, DST_TABLE);
    if (dstTable == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s's json content.", DST_TABLE);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    const char *dstTableStr = DbJsonStringValue(dstTable);
    if (dstTableStr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s.", DST_TABLE);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    size = (uint32_t)strlen(dstTableStr) + 1;
    curTable->tsdbTblName.length = size - 1;
    curTable->tsdbTblName.data = GmeiAlloc(size);
    if (curTable->tsdbTblName.data == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc space for %s:%s.", DST_TABLE, dstTableStr);
        return GMERR_OUT_OF_MEMORY;
    }
    err = strcpy_s(curTable->tsdbTblName.data, size, dstTableStr);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Unable to copy %s:%s.", DST_TABLE, dstTableStr);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

static int32_t GmeiFillTimeCol(DbJsonT *content, GmeiTableT *curTable)
{
    DbJsonT *timeCol = DbJsonObjectGet(content, TIME_COL);
    if (timeCol == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s's json content.", TIME_COL);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    const char *timeColStr = DbJsonStringValue(timeCol);
    if (timeColStr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s.", TIME_COL);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint32_t size = (uint32_t)strlen(timeColStr) + 1;
    curTable->timeColumn.length = size - 1;
    curTable->timeColumn.data = GmeiAlloc(size);
    if (curTable->timeColumn.data == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc space for %s:%s.", TIME_COL, timeColStr);
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t err = strcpy_s(curTable->timeColumn.data, size, timeColStr);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Unable to copy %s:%s.", TIME_COL, timeColStr);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

static int32_t GmeiFillCacheSize(DbJsonT *content, GmeiTableT *curTable)
{
    DbJsonT *cacheSize = DbJsonObjectGet(content, CACHE_SIZE);
    if (cacheSize == NULL) {
        curTable->cacheSize.data = NULL;
        curTable->cacheSize.length = 0;
        return GMERR_OK;
    }
    const char *cacheSizeStr = DbJsonStringValue(cacheSize);
    if (cacheSizeStr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s.", CACHE_SIZE);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint32_t size = (uint32_t)strlen(cacheSizeStr) + 1;
    curTable->cacheSize.length = size - 1;
    curTable->cacheSize.data = GmeiAlloc(size);
    if (curTable->cacheSize.data == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc space for %s:%s.", CACHE_SIZE, cacheSizeStr);
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t err = strcpy_s(curTable->cacheSize.data, size, cacheSizeStr);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Unable to copy %s:%s.", CACHE_SIZE, cacheSizeStr);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

static int32_t GmeiFillCompression(DbJsonT *content, GmeiTableT *curTable)
{
    DbJsonT *compression = DbJsonObjectGet(content, COMPRESSION);
    if (compression == NULL) {
        curTable->compression.data = NULL;
        curTable->compression.length = 0;
        return GMERR_OK;
    }
    const char *compressionStr = DbJsonStringValue(compression);
    if (compressionStr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s.", COMPRESSION);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint32_t size = (uint32_t)strlen(compressionStr) + 1;
    curTable->compression.length = size - 1;
    curTable->compression.data = GmeiAlloc(size);
    if (curTable->compression.data == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc space for %s:%s.", COMPRESSION, compressionStr);
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t err = strcpy_s(curTable->compression.data, size, compressionStr);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Unable to copy %s:%s.", COMPRESSION, compressionStr);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

static int32_t GmeiFillDiskLimit(DbJsonT *content, GmeiTableT *curTable)
{
    DbJsonT *diskLimit = DbJsonObjectGet(content, DISK_LIMIT);
    if (diskLimit == NULL) {
        curTable->diskLimit.data = NULL;
        curTable->diskLimit.length = 0;
        return GMERR_OK;
    }
    const char *diskLimitStr = DbJsonStringValue(diskLimit);
    if (diskLimitStr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s.", DISK_LIMIT);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint32_t size = (uint32_t)strlen(diskLimitStr) + 1;
    curTable->diskLimit.length = size - 1;
    curTable->diskLimit.data = GmeiAlloc(size);
    if (curTable->diskLimit.data == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc space for %s:%s.", DISK_LIMIT, diskLimitStr);
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t err = strcpy_s(curTable->diskLimit.data, size, diskLimitStr);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Unable to copy %s:%s.", DISK_LIMIT, diskLimitStr);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

static int32_t GmeiFillInterval(DbJsonT *content, GmeiTableT *curTable)
{
    DbJsonT *interval = DbJsonObjectGet(content, INTERVAL);
    if (interval == NULL) {
        curTable->interval.data = NULL;
        curTable->interval.length = 0;
        return GMERR_OK;
    }
    const char *intervalStr = DbJsonStringValue(interval);
    if (intervalStr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s.", INTERVAL);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint32_t size = (uint32_t)strlen(intervalStr) + 1;
    curTable->interval.length = size - 1;
    curTable->interval.data = GmeiAlloc(size);
    if (curTable->interval.data == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc space for %s:%s.", INTERVAL, intervalStr);
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t err = strcpy_s(curTable->interval.data, size, intervalStr);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Unable to copy %s:%s.", INTERVAL, intervalStr);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

static int32_t GmeiFillIsVolatileLabel(DbJsonT *content, GmeiTableT *curTable)
{
    DbJsonT *isVolatileLabel = DbJsonObjectGet(content, IS_VOLATILE_LABEL);
    if (isVolatileLabel == NULL) {
        curTable->isVolatileLabel.data = NULL;
        curTable->isVolatileLabel.length = 0;
        return GMERR_OK;
    }
    const char *isVolatileLabelStr = DbJsonStringValue(isVolatileLabel);
    if (isVolatileLabelStr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s.", IS_VOLATILE_LABEL);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint32_t size = (uint32_t)strlen(isVolatileLabelStr) + 1;
    curTable->isVolatileLabel.length = size - 1;
    curTable->isVolatileLabel.data = GmeiAlloc(size);
    if (curTable->isVolatileLabel.data == NULL) {
        DB_LOG_AND_SET_LASERR(
            GMERR_OUT_OF_MEMORY, "Unable to alloc space for %s:%s.", IS_VOLATILE_LABEL, isVolatileLabelStr);
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t err = strcpy_s(curTable->isVolatileLabel.data, size, isVolatileLabelStr);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(
            GMERR_MEMORY_OPERATE_FAILED, "Unable to copy %s:%s.", IS_VOLATILE_LABEL, isVolatileLabelStr);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

static int32_t GmeiFillSensitiveCol(DbJsonT *content, GmeiTableT *curTable)
{
    DbJsonT *sensitiveCol = DbJsonObjectGet(content, SENSITIVE_COL);
    if (sensitiveCol == NULL) {
        curTable->sensitiveCol.data = NULL;
        curTable->sensitiveCol.length = 0;
        return GMERR_OK;
    }
    const char *sensitiveColStr = DbJsonStringValue(sensitiveCol);
    if (sensitiveColStr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s.", SENSITIVE_COL);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint32_t size = (uint32_t)strlen(sensitiveColStr) + 1;
    curTable->sensitiveCol.length = size - 1;
    curTable->sensitiveCol.data = GmeiAlloc(size);
    if (curTable->sensitiveCol.data == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc space for %s:%s.", SENSITIVE_COL, sensitiveColStr);
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t err = strcpy_s(curTable->sensitiveCol.data, size, sensitiveColStr);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Unable to copy %s:%s.", SENSITIVE_COL, sensitiveColStr);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

static int32_t GmeiFillTablePath(DbJsonT *content, GmeiTableT *curTable)
{
    DbJsonT *tablePath = DbJsonObjectGet(content, TABLE_PATH);
    if (tablePath == NULL) {
        curTable->tablePath.data = NULL;
        curTable->tablePath.length = 0;
        return GMERR_OK;
    }
    const char *tablePathStr = DbJsonStringValue(tablePath);
    if (tablePathStr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s.", TABLE_PATH);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint32_t size = (uint32_t)strlen(tablePathStr) + 1;
    curTable->tablePath.length = size - 1;
    curTable->tablePath.data = GmeiAlloc(size);
    if (curTable->tablePath.data == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc space for %s:%s.", TABLE_PATH, tablePathStr);
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t err = strcpy_s(curTable->tablePath.data, size, tablePathStr);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Unable to copy %s:%s.", TABLE_PATH, tablePathStr);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

static int32_t GmeiFillTtl(DbJsonT *content, GmeiTableT *curTable)
{
    DbJsonT *ttl = DbJsonObjectGet(content, TTL);
    if (ttl == NULL) {
        curTable->ttl.data = NULL;
        curTable->ttl.length = 0;
        return GMERR_OK;
    }
    const char *ttlStr = DbJsonStringValue(ttl);
    if (ttlStr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s.", TTL);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint32_t size = (uint32_t)strlen(ttlStr) + 1;
    curTable->ttl.length = size - 1;
    curTable->ttl.data = GmeiAlloc(size);
    if (curTable->ttl.data == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc space for %s:%s.", TTL, ttlStr);
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t err = strcpy_s(curTable->ttl.data, size, ttlStr);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Unable to copy %s:%s.", TTL, ttlStr);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

static int32_t GmeiCheckMemtable(DbJsonT *content, GmeiTableT *curTable)
{
    DbJsonT *memtable = DbJsonObjectGet(content, ENGINE);
    if (memtable != NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_FEATURE_NOT_SUPPORTED, "Memtable is not supported.");
        return GMERR_FEATURE_NOT_SUPPORTED;
    }
    return GMERR_OK;
}

static int32_t GmeiFillOtherWithOption(DbJsonT *content, GmeiTableT *curTable)
{
    int32_t ret = GmeiCheckMemtable(content, curTable);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmeiFillCacheSize(content, curTable);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmeiFillCompression(content, curTable);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmeiFillDiskLimit(content, curTable);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmeiFillInterval(content, curTable);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmeiFillIsVolatileLabel(content, curTable);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmeiFillSensitiveCol(content, curTable);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmeiFillTablePath(content, curTable);
    if (ret != GMERR_OK) {
        return ret;
    }
    ret = GmeiFillTtl(content, curTable);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GMERR_OK;
}

static int32_t GmeiFillChunkCapacity(DbJsonT *content, GmeiTableT *curTable)
{
    DbJsonT *chunkCap = DbJsonObjectGet(content, CHUNK_CAPACITY);
    if (chunkCap == NULL) {
        return GMERR_OK;
    }
    curTable->chunkCapacity = DbJsonIntegerValue(chunkCap);
    return GMERR_OK;
}

static int32_t FillReadType(const char *srcTypeStr, GmeiTableT *curTable, uint32_t i)
{
    if (strcmp(srcTypeStr, GMEI_I64_) == 0) {
        curTable->columns[i].readType = GMEI_I64;
    } else if (strcmp(srcTypeStr, GMEI_STR_) == 0) {
        curTable->columns[i].readType = GMEI_STR;
    } else if (strcmp(srcTypeStr, GMEI_BLOB_) == 0) {
        curTable->columns[i].readType = GMEI_BLOB;
    } else {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_JSON_CONTENT, "Undefined %s, col %" PRIu32 ".", SRC_TYPE, i);
        return GMERR_INVALID_JSON_CONTENT;
    }
    return GMERR_OK;
}

static int32_t FillSrcName(GmeiTableT *curTable, DbJsonT *srcDefCol, uint32_t i)
{
    DbJsonT *srcName = DbJsonObjectGet(srcDefCol, SRC_NAME);
    if (srcName == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s's json content.", SRC_NAME);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    const char *srcNameStr = DbJsonStringValue(srcName);
    if (srcNameStr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s, col %" PRIu32 ".", SRC_NAME, i);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint32_t size = (uint32_t)strlen(srcNameStr) + 1;
    curTable->columns[i].srcName.length = size - 1;
    curTable->columns[i].srcName.data = GmeiAlloc(size);
    if (curTable->columns[i].srcName.data == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc space for %s:%s.", SRC_NAME, srcNameStr);
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t err = strcpy_s(curTable->columns[i].srcName.data, size, srcNameStr);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Unable to copy %s:%s.", SRC_NAME, srcNameStr);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

static int32_t GmeiFillSrcDef(DbJsonT *content, GmeiTableT *curTable)
{
    DbJsonT *srcDef = DbJsonObjectGet(content, SRC_DEF);
    if (srcDef == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s's json content.", SRC_DEF);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    curTable->columnCount = DbJsonGetArraySize(srcDef);
    if (curTable->columnCount == 0 || curTable->columnCount > DB_MAX_INT32) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_JSON_CONTENT,
            "Column count should GT 0 and LE max int32_t, cur idx: %" PRIu64 ".", (uint64_t)curTable->columnCount);
        return GMERR_INVALID_JSON_CONTENT;
    }
    size_t colArrSize = sizeof(GmeiColumnT) * curTable->columnCount;
    curTable->columns = GmeiAlloc(colArrSize);
    if (curTable->columns == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc space for %s.", SRC_DEF);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(curTable->columns, colArrSize, 0, colArrSize);
    for (uint32_t i = 0; i < curTable->columnCount; i++) {
        DbJsonT *srcDefCol = DbJsonArrayGet(srcDef, i);
        int32_t ret = FillSrcName(curTable, srcDefCol, i);
        if (ret != GMERR_OK) {
            return ret;
        }

        DbJsonT *srcType = DbJsonObjectGet(srcDefCol, SRC_TYPE);
        if (srcType == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s's json content.", SRC_TYPE);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        const char *srcTypeStr = DbJsonStringValue(srcType);
        if (srcTypeStr == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s, col %" PRIu32 ".", SRC_TYPE, i);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        ret = FillReadType(srcTypeStr, curTable, i);
        if (ret != GMERR_OK) {
            return ret;
        }
        curTable->columns[i].targetColIdxNum = 0;
    }

    return GMERR_OK;
}

static int32_t FillDstName(DbJsonT *dstDefCol, GmeiTableT *curTable, uint32_t i)
{
    DbJsonT *dstName = DbJsonObjectGet(dstDefCol, DST_NAME);
    if (dstName == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s's json content.", DST_NAME);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    const char *dstNameStr = DbJsonStringValue(dstName);
    if (dstNameStr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s, col %" PRIu32 ".", DST_NAME, i);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    uint32_t size = (uint32_t)strlen(dstNameStr) + 1;
    curTable->tsdbCols[i].dstName.length = size - 1;
    curTable->tsdbCols[i].dstName.data = GmeiAlloc(size);
    if (curTable->tsdbCols[i].dstName.data == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc space for %s:%s.", DST_NAME, dstNameStr);
        return GMERR_OUT_OF_MEMORY;
    }
    errno_t err = strcpy_s(curTable->tsdbCols[i].dstName.data, size, dstNameStr);
    if (err != EOK) {
        DB_LOG_AND_SET_LASERR(GMERR_MEMORY_OPERATE_FAILED, "Unable to copy %s:%s.", DST_NAME, dstNameStr);
        return GMERR_MEMORY_OPERATE_FAILED;
    }
    return GMERR_OK;
}

static int32_t FillDstType(DbJsonT *dstDefCol, GmeiTableT *curTable, uint32_t i)
{
    DbJsonT *dstType = DbJsonObjectGet(dstDefCol, DST_TYPE);
    if (dstType == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s's json content.", DST_TYPE);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    const char *dstTypeStr = DbJsonStringValue(dstType);
    if (dstTypeStr == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s, col %" PRIu32 ".", DST_TYPE, i);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }

    if (strcmp(dstTypeStr, GMEI_I64_) == 0) {
        curTable->tsdbCols[i].writeType = GMEI_I64;
    } else if (strcmp(dstTypeStr, GMEI_STR_) == 0) {
        curTable->tsdbCols[i].writeType = GMEI_STR;
    } else if (strcmp(dstTypeStr, GMEI_BLOB_) == 0) {
        curTable->tsdbCols[i].writeType = GMEI_BLOB;
    } else if (strcmp(dstTypeStr, GMEI_TEXT_) == 0) {
        curTable->tsdbCols[i].writeType = GMEI_TEXT;
    } else if (strcmp(dstTypeStr, GMEI_INET_) == 0) {
        curTable->tsdbCols[i].writeType = GMEI_INET;
    } else {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_JSON_CONTENT, "Undefined %s, col %" PRIu32 ".", DST_TYPE, i);
        return GMERR_INVALID_JSON_CONTENT;
    }
    return GMERR_OK;
}

static int32_t FillMaxSize(DbJsonT *dstDefCol, GmeiTableT *curTable, uint32_t i)
{
    DbJsonT *maxSize = DbJsonObjectGet(dstDefCol, MAX_SIZE);
    if (maxSize == NULL) {
        curTable->tsdbCols[i].maxSize = 0;
        return GMERR_OK;
    }
    curTable->tsdbCols[i].maxSize = DbJsonIntegerValue(maxSize);
    if (curTable->tsdbCols[i].maxSize > DB_MAX_UINT32) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_JSON_CONTENT, "%s is over max uint32, col %" PRIu32 ".", MAX_SIZE, i);
        return GMERR_INVALID_JSON_CONTENT;
    }
    return GMERR_OK;
}

static int32_t FillCvtType(GmeiTableT *curTable, uint32_t i, uint32_t j)
{
    if (curTable->columns[j].readType == GMEI_I64 && curTable->tsdbCols[i].writeType == GMEI_I64) {
        curTable->columns[j].cvtType = CVT_I64_TO_I64;
    } else if (curTable->columns[j].readType == GMEI_STR && curTable->tsdbCols[i].writeType == GMEI_I64) {
        curTable->columns[j].cvtType = CVT_STRNUM_TO_I64;
    } else if (curTable->columns[j].readType == GMEI_STR && curTable->tsdbCols[i].writeType == GMEI_STR) {
        curTable->columns[j].cvtType = CVT_STR_TO_STR;
    } else if (curTable->columns[j].readType == GMEI_STR && curTable->tsdbCols[i].writeType == GMEI_TEXT) {
        curTable->columns[j].cvtType = CVT_STR_TO_TEXT;
    } else if (curTable->columns[j].readType == GMEI_BLOB && curTable->tsdbCols[i].writeType == GMEI_BLOB) {
        curTable->columns[j].cvtType = CVT_BLOB_TO_BLOB;
    } else if (curTable->columns[j].readType == GMEI_I64 && curTable->tsdbCols[i].writeType == GMEI_INET) {
        curTable->columns[j].cvtType = CVT_I64_TO_INET;
    } else if (curTable->columns[j].readType == GMEI_STR && curTable->tsdbCols[i].writeType == GMEI_INET) {
        curTable->columns[j].cvtType = CVT_STR_TO_INET;
    } else {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_JSON_CONTENT, "Undefined conversion type, col %" PRIu32 ".", i);
        return GMERR_INVALID_JSON_CONTENT;
    }
    return GMERR_OK;
}

static int32_t FillTargetColIdx(GmeiTableT *curTable, uint32_t i, uint32_t j)
{
    if (curTable->columns[j].targetColIdxNum == 0) {
        curTable->columns[j].targetColIdx = i;
        curTable->columns[j].targetColIdxNum++;
    } else if (curTable->columns[j].targetColIdxNum == 1) {  // 如果对应多个dst直接分配长度为100的数组
        uint32_t firstIdx = (uint32_t)curTable->columns[j].targetColIdx;
        size_t arrSize = sizeof(uint32_t) * ONE_TO_MULTI;
        curTable->columns[j].targetColIdx = (uintptr_t)GmeiAlloc(arrSize);
        if ((void *)curTable->columns[j].targetColIdx == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc space for targetColIdx arr.");
            return GMERR_OUT_OF_MEMORY;
        }
        (void)memset_s((void *)curTable->columns[j].targetColIdx, arrSize, 0, arrSize);
        ((uint32_t *)curTable->columns[j].targetColIdx)[0] = firstIdx;
        ((uint32_t *)curTable->columns[j].targetColIdx)[curTable->columns[j].targetColIdxNum] = i;
        curTable->columns[j].targetColIdxNum++;
    } else if (curTable->columns[j].targetColIdxNum < ONE_TO_MULTI) {
        ((uint32_t *)curTable->columns[j].targetColIdx)[curTable->columns[j].targetColIdxNum] = i;
        curTable->columns[j].targetColIdxNum++;
    } else {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_VALUE, "Too many dst col, limit is %" PRIu32 ".", ONE_TO_MULTI);
        return GMERR_INVALID_VALUE;
    }
    return GMERR_OK;
}

static int32_t FillSrcData(DbJsonT *dstDefCol, GmeiTableT *curTable, uint32_t i)
{
    DbJsonT *srcData = DbJsonObjectGet(dstDefCol, SRC_DATA);
    if (srcData == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s's json content.", SRC_DATA);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    DbJsonTypeE type = DbJsonGetType(srcData);
    uint32_t srcColNum = 1;  // 通常情况下数据来源是一列，两列或者多列的情况较少
    if (type == DB_JSON_ARRAY) {
        srcColNum = (uint32_t)DbJsonGetArraySize(srcData);
        if (srcColNum > ONE_TO_MULTI) {
            DB_LOG_AND_SET_LASERR(GMERR_INVALID_VALUE, "Too many src col, limit is %" PRIu32 ".", ONE_TO_MULTI);
            return GMERR_INVALID_VALUE;
        }
    }
    const char *srcDataStr = NULL;
    for (uint32_t srcDataIdx = 0; srcDataIdx < srcColNum; srcDataIdx++) {
        bool isMultiSrcData = false;
        if (type == DB_JSON_ARRAY) {
            srcDataStr = DbJsonStringValue(DbJsonArrayGet(srcData, srcDataIdx));
            isMultiSrcData = true;
        } else {
            srcDataStr = DbJsonStringValue(srcData);
        }
        if (srcDataStr == NULL) {
            DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s, col %" PRIu32 ".", SRC_DATA, i);
            return GMERR_UNEXPECTED_NULL_VALUE;
        }
        for (uint32_t j = 0; j < curTable->columnCount; j++) {
            if (strcmp(curTable->columns[j].srcName.data, srcDataStr) != 0) {
                continue;
            }
            int32_t ret = FillTargetColIdx(curTable, i, j);
            if (ret != GMERR_OK) {
                return ret;
            }
            curTable->tsdbCols[i].isMultiSrcData = isMultiSrcData;
            ret = FillCvtType(curTable, i, j);
            if (ret != GMERR_OK) {
                return ret;
            }
            break;
        }
    }
    return GMERR_OK;
}

static int32_t GmeiFillDstDef(DbJsonT *content, GmeiTableT *curTable)
{
    int32_t ret = GMERR_OK;
    DbJsonT *dstDef = DbJsonObjectGet(content, DST_DEF);
    if (dstDef == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get %s's json content.", DST_DEF);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    curTable->tsdbColCount = DbJsonGetArraySize(dstDef);
    if (curTable->tsdbColCount == 0) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_JSON_CONTENT, "Tsdb column count should be positive.");
        return GMERR_INVALID_JSON_CONTENT;
    }

    size_t colArrSize = sizeof(GmeiColumnT) * curTable->tsdbColCount;
    curTable->tsdbCols = GmeiAlloc(colArrSize);
    if (curTable->tsdbCols == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc space for %s.", DST_DEF);
        return GMERR_OUT_OF_MEMORY;
    }
    (void)memset_s(curTable->tsdbCols, colArrSize, 0, colArrSize);
    for (uint32_t i = 0; i < curTable->tsdbColCount; i++) {
        DbJsonT *dstDefCol = DbJsonArrayGet(dstDef, i);
        ret = FillDstName(dstDefCol, curTable, i);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "FillDstName with problem, col %" PRIu32 ".", i);
            return ret;
        }

        ret = FillDstType(dstDefCol, curTable, i);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "FillDstType with problem, col %" PRIu32 ".", i);
            return ret;
        }

        ret = FillMaxSize(dstDefCol, curTable, i);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "FillMaxSize with problem, col %" PRIu32 ".", i);
            return ret;
        }

        ret = FillSrcData(dstDefCol, curTable, i);
        if (ret != GMERR_OK) {
            DB_LOG_ERROR(ret, "FillSrcData with problem, col %" PRIu32 ".", i);
            return ret;
        }
    }
    return GMERR_OK;
}

void FreeAllocatedSpace4WithOption(GmeiTableT *curTable)
{
    if (curTable->cacheSize.data != NULL) {
        GmeiFree((void *)curTable->cacheSize.data);
        curTable->cacheSize.data = NULL;
        curTable->cacheSize.length = 0;
    }
    if (curTable->compression.data != NULL) {
        GmeiFree((void *)curTable->compression.data);
        curTable->compression.data = NULL;
        curTable->compression.length = 0;
    }
    if (curTable->diskLimit.data != NULL) {
        GmeiFree((void *)curTable->diskLimit.data);
        curTable->diskLimit.data = NULL;
        curTable->diskLimit.length = 0;
    }
    if (curTable->interval.data != NULL) {
        GmeiFree((void *)curTable->interval.data);
        curTable->interval.data = NULL;
        curTable->interval.length = 0;
    }
    if (curTable->isVolatileLabel.data != NULL) {
        GmeiFree((void *)curTable->isVolatileLabel.data);
        curTable->isVolatileLabel.data = NULL;
        curTable->isVolatileLabel.length = 0;
    }
    if (curTable->sensitiveCol.data != NULL) {
        GmeiFree((void *)curTable->sensitiveCol.data);
        curTable->sensitiveCol.data = NULL;
        curTable->sensitiveCol.length = 0;
    }
    if (curTable->tablePath.data != NULL) {
        GmeiFree((void *)curTable->tablePath.data);
        curTable->tablePath.data = NULL;
        curTable->tablePath.length = 0;
    }
    if (curTable->ttl.data != NULL) {
        GmeiFree((void *)curTable->ttl.data);
        curTable->ttl.data = NULL;
        curTable->ttl.length = 0;
    }
    if (curTable->timeColumn.data != NULL) {
        GmeiFree((void *)curTable->timeColumn.data);
        curTable->timeColumn.data = NULL;
        curTable->timeColumn.length = 0;
    }
}

void FreeAllocatedSpace4Table(GmeiTableT *curTable)
{
    if (curTable == NULL) {
        return;
    }
    if (curTable->tsdbColCount != 0 && curTable->tsdbCols != NULL) {
        for (uint32_t i = 0; i < curTable->tsdbColCount; i++) {
            if (curTable->tsdbCols[i].dstName.data != NULL) {
                GmeiFree((void *)curTable->tsdbCols[i].dstName.data);
                curTable->tsdbCols[i].dstName.data = NULL;
            }
        }
        GmeiFree((void *)curTable->tsdbCols);
        curTable->tsdbCols = NULL;
        curTable->tsdbColCount = 0;
    }
    if (curTable->columnCount != 0 && curTable->columns != NULL) {
        for (uint32_t i = 0; i < curTable->columnCount; i++) {
            if (curTable->columns[i].srcName.data != NULL) {
                GmeiFree((void *)curTable->columns[i].srcName.data);
                curTable->columns[i].srcName.data = NULL;
            }
        }
        GmeiFree((void *)curTable->columns);
        curTable->columns = NULL;
        curTable->columnCount = 0;
    }
    FreeAllocatedSpace4WithOption(curTable);
    if (curTable->tsdbTblName.data != NULL) {
        GmeiFree((void *)curTable->tsdbTblName.data);
        curTable->tsdbTblName.data = NULL;
        curTable->tsdbTblName.length = 0;
    }
    if (curTable->tableName.data != NULL) {
        GmeiFree((void *)curTable->tableName.data);
        curTable->tableName.data = NULL;
        curTable->tableName.length = 0;
    }
    if (curTable->readFile.data != NULL) {
        GmeiFree((void *)curTable->readFile.data);
        curTable->readFile.data = NULL;
        curTable->readFile.length = 0;
    }
}

int32_t GmeiParseJsonContentAndFillMetaData(char *fullJsonPath, GmeiTableT *curTable)
{
    DbJsonT *content = DbJsonLoadsFile(fullJsonPath, 0);
    if (content == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_UNEXPECTED_NULL_VALUE, "Unable to get json file from %s.", fullJsonPath);
        return GMERR_UNEXPECTED_NULL_VALUE;
    }
    int32_t ret = GmeiFillSrcFile(content, curTable);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    ret = GmeiFillSrcTableAndDstTable(content, curTable);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    ret = GmeiFillTimeCol(content, curTable);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    ret = GmeiFillOtherWithOption(content, curTable);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    ret = GmeiFillChunkCapacity(content, curTable);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    ret = GmeiFillSrcDef(content, curTable);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
    ret = GmeiFillDstDef(content, curTable);
    if (ret != GMERR_OK) {
        goto EXIT;
    }
EXIT:
    if (ret != GMERR_OK) {
        FreeAllocatedSpace4Table(curTable);
    }
    DbJsonDelete(content);
    GmeiFree((void *)fullJsonPath);
    return ret;
}

int32_t GmeiInitMetaData(char **fullJsonPathArr)
{
    GmeiTableT *curTable = NULL;
    int32_t ret = GMERR_OK;
    for (uint32_t i = 0; i < g_tableNum; i++) {
        curTable = &g_tables[i];
        ret = GmeiParseJsonContentAndFillMetaData(fullJsonPathArr[i], curTable);
        if (ret != GMERR_OK) {  // 初始化一张表不成功不退出，只记录下错误日志随后继续初始化剩下的表
            DB_LOG_AND_SET_LASERR(ret, "Init table %s with problem.", curTable->tableName.data);
            continue;
        }
        (void)DbPrintfDefault("Table %s has been inited.\n", curTable->tableName.data);
    }
    return GMERR_OK;
}

int32_t GmeiInitSchemas(GmeiOptionsT *opts)
{
    const char *jsonDir = GmeiOptionsGet(opts, GmeiStringFromChars("table-schema-dir")).value.stringVal.data;
    // no need to verify folder dir again
    g_tableNum = 0;
    char **fullJsonPathArr = NULL;
    // one kind of table has corresponding one json file
    int32_t ret = GmeiCountJsonFiles(jsonDir, &g_tableNum, &fullJsonPathArr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Get json file count with problem.");
        goto EXIT;
    }
    if (g_tables != NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_INTERNAL_ERROR, "Table info array should be NULL before allocating space.");
        return GMERR_INTERNAL_ERROR;
    }
    g_tables = GmeiAlloc(sizeof(GmeiTableT) * g_tableNum);
    if (g_tables == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to alloc space for GmeiTableT array.");
        ret = GMERR_OUT_OF_MEMORY;
        goto EXIT;
    }
    (void)memset_s(g_tables, sizeof(GmeiTableT) * g_tableNum, 0, sizeof(GmeiTableT) * g_tableNum);
    ret = GmeiInitMetaData(fullJsonPathArr);
    if (ret != GMERR_OK) {
        DB_LOG_ERROR(ret, "Init meta data with problem.");
        goto EXIT;
    }
EXIT:
    if (fullJsonPathArr != NULL) {
        GmeiFree(fullJsonPathArr);
    }
    return ret;
}

void GmeiUnInitSchemas(void)
{
    GmeiTableT *curTable = NULL;
    for (uint32_t i = 0; i < g_tableNum; i++) {
        curTable = &g_tables[i];
        FreeAllocatedSpace4Table(curTable);
    }
    GmeiFree((void *)g_tables);
    g_tables = NULL;
    g_tableNum = 0;
}

const GmeiTableT *GmeiMatchFilenameToTable(GmeiStringT filename)
{
    size_t idx = 0u;
    while (idx < g_tableNum) {
        GmeiOwnedStringT readFile = g_tables[idx].readFile;
        if (readFile.data == NULL || readFile.length == 0) {
            ++idx;
            continue;
        }
        if (readFile.length < filename.length && strncmp(readFile.data, filename.data, readFile.length) == 0) {
            break;
        }
        ++idx;
    }
    return idx == g_tableNum ? NULL : &g_tables[idx];
}

static int32_t SprintfTypeName(GmeiColumnT col, char *allocated, uint32_t maxSize, uint32_t offset)
{
    switch (col.writeType) {
        case GMEI_I64:
            return snprintf_s(allocated + offset, maxSize - offset, maxSize - offset, "INTEGER");
        case GMEI_STR:
            return snprintf_s(allocated + offset, maxSize - offset, maxSize - offset, "CHAR(%" PRIi64 ")", col.maxSize);
        case GMEI_BLOB:
            return snprintf_s(allocated + offset, maxSize - offset, maxSize - offset, "BLOB(%" PRIi64 ")", col.maxSize);
        case GMEI_TEXT:
            return snprintf_s(allocated + offset, maxSize - offset, maxSize - offset, "TEXT");
        case GMEI_INET:
            return snprintf_s(allocated + offset, maxSize - offset, maxSize - offset, "INET");
        case GMEI_KV:
            break;
    }
    return snprintf_s(allocated + offset, maxSize - offset, maxSize - offset, "(unknown)");
}

static int32_t FillOtherOptions(const GmeiTableT *table, char *allocated, int32_t maxSize, int32_t originalOffset)
{
    int32_t offset = originalOffset;
    int32_t retValue = 0;
    if (table->cacheSize.data != NULL) {
        uint32_t sizeLimit = (uint32_t)(maxSize - offset);
        retValue = snprintf_s(allocated + offset, sizeLimit, sizeLimit, ", cache_size = '%s'", table->cacheSize.data);
        if (retValue < 0) {
            return retValue;
        }
        offset += retValue;
    }
    if (table->diskLimit.data != NULL) {
        uint32_t sizeLimit = (uint32_t)(maxSize - offset);
        retValue = snprintf_s(allocated + offset, sizeLimit, sizeLimit, ", disk_limit = '%s'", table->diskLimit.data);
        if (retValue < 0) {
            return retValue;
        }
        offset += retValue;
    }
    if (table->isVolatileLabel.data != NULL) {
        uint32_t sizeLimit = (uint32_t)(maxSize - offset);
        retValue = snprintf_s(
            allocated + offset, sizeLimit, sizeLimit, ", is_volatile_label = '%s'", table->isVolatileLabel.data);
        if (retValue < 0) {
            return retValue;
        }
        offset += retValue;
    }
    if (table->sensitiveCol.data != NULL) {
        uint32_t sizeLimit = (uint32_t)(maxSize - offset);
        retValue =
            snprintf_s(allocated + offset, sizeLimit, sizeLimit, ", sensitive_col = '%s'", table->sensitiveCol.data);
        if (retValue < 0) {
            return retValue;
        }
        offset += retValue;
    }
    if (table->tablePath.data != NULL) {
        uint32_t sizeLimit = (uint32_t)(maxSize - offset);
        retValue = snprintf_s(allocated + offset, sizeLimit, sizeLimit, ", table_path = '%s'", table->tablePath.data);
        if (retValue < 0) {
            return retValue;
        }
        offset += retValue;
    }
    return offset;
}

static int32_t FillWithOptions(const GmeiTableT *table, char *allocated, uint32_t maxSize, uint32_t originalOffset)
{
    int32_t offset = (int32_t)originalOffset;
    int32_t retValue = 0;
    // fill time col
    retValue = snprintf_s(
        allocated + offset, maxSize - offset, maxSize - offset, ") WITH (time_col = '%s'", table->timeColumn.data);
    if (retValue < 0) {
        return retValue;
    }
    offset += retValue;
    if (table->interval.data != NULL) {
        retValue = snprintf_s(
            allocated + offset, maxSize - offset, maxSize - offset, ", interval = '%s'", table->interval.data);
        if (retValue < 0) {
            return retValue;
        }
        offset += retValue;
    }
    if (table->compression.data != NULL) {
        retValue = snprintf_s(
            allocated + offset, maxSize - offset, maxSize - offset, ", compression = '%s'", table->compression.data);
        if (retValue < 0) {
            return retValue;
        }
        offset += retValue;
    }
    if (table->ttl.data != NULL) {
        retValue = snprintf_s(allocated + offset, maxSize - offset, maxSize - offset, ", ttl = '%s'", table->ttl.data);
        if (retValue < 0) {
            return retValue;
        }
        offset += retValue;
    }
    retValue = FillOtherOptions(table, allocated, (int32_t)maxSize, offset);
    if (retValue < 0) {
        return retValue;
    }
    offset = retValue;  // 此处为赋值！不是累加！累加在FillOtherOptions中做过了
    return offset;
}

int32_t FillSingleCol(GmeiColumnT col, char *allocated, uint32_t maxSize, uint32_t originalOffset)
{
    int32_t offset = (int32_t)originalOffset;
    int32_t retValue = snprintf_s(allocated + offset, maxSize - offset, maxSize - offset, "%s ", col.dstName.data);
    if (retValue < 0) {
        DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW, "Unable to init create stmt with col name %s.", col.dstName.data);
        return retValue;
    }
    offset += retValue;
    retValue = SprintfTypeName(col, allocated, maxSize, (uint32_t)offset);
    if (retValue < 0) {
        DB_LOG_AND_SET_LASERR(
            GMERR_FIELD_OVERFLOW, "Unable to init create stmt with type %" PRIu32 ".", (uint32_t)col.writeType);
        return retValue;
    }
    offset += retValue;
    return offset;
}

GmeiStringT GmeiStmtInit(const GmeiTableT *table, GmeiStringT tableName, char *allocated, uint32_t maxSize)
{
    uint32_t offset = 0;
    GmeiStringT out = {0};
    int32_t retValue = snprintf_s(allocated, maxSize, maxSize, "CREATE TABLE IF NOT EXISTS %s(", tableName.data);
    if (retValue < 0) {
        DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW, "Unable to init create stmt with table name %s.", tableName.data);
        goto EXIT;
    }
    offset += (uint32_t)retValue;
    for (size_t idx = 0; idx < table->tsdbColCount; ++idx) {
        GmeiColumnT col = table->tsdbCols[idx];
        if (idx != 0) {
            retValue = snprintf_s(allocated + offset, maxSize - offset, maxSize - offset, ", ");
            if (retValue < 0) {
                DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW, "Unable to init create stmt while adding coma.");
                goto EXIT;
            }
            offset += (uint32_t)retValue;
        }
        retValue = FillSingleCol(col, allocated, maxSize, offset);
        if (retValue < 0) {
            DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW, "Unable to init create stmt while filling single col.");
            goto EXIT;
        }
        offset = (uint32_t)retValue;
    }
    retValue = FillWithOptions(table, allocated, maxSize, offset);
    if (retValue < 0) {
        DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW, "Unable to init create stmt with options.");
        goto EXIT;
    }
    offset = (uint32_t)retValue;  // 此处为赋值！不是累加！累加在FillWithOptions中做过了
    retValue = snprintf_s(allocated + offset, maxSize - offset, maxSize - offset, ");");
    if (retValue < 0) {
        DB_LOG_AND_SET_LASERR(GMERR_FIELD_OVERFLOW, "Unable to init create stmt while adding last coma and semicolon.");
        goto EXIT;
    }
    offset += (uint32_t)retValue;
EXIT:
    out.data = allocated;
    out.length = offset;
    return out;
}

// Returns a match if it finds a perfect match
// Or return the default value from the table schema
GmeiStringT GmeiMatchTableName(GmeiTableT table, GmeiKvArrayT arr, GmeiStringT filename)
{
    // Remove extension
    GmeiStringT name = {filename.data, filename.length - 3};
    for (size_t kvIdx = 0u; kvIdx < arr.size; ++kvIdx) {
        GmeiKvT kv = arr.array[kvIdx];
        if (kv.key.length == name.length && strncmp(kv.key.data, name.data, kv.key.length) != 0) {
            return (GmeiStringT){kv.val.data, kv.val.length};
        }
    }
    return (GmeiStringT){table.tableName.data, table.tableName.length};
}
