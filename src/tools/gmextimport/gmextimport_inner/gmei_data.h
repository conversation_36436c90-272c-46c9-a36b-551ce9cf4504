/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: nlog importer
 * Create: 2024-05-31
 */
#ifndef GMEI_DATA_H
#define GMEI_DATA_H

#include "adpt_printf.h"
#include "db_log.h"
#include "db_last_error.h"
#include "gmei_schemas.h"

#define XXHASH_SEED 10u

typedef struct {
    size_t capacity;
    size_t size;
    size_t maxElementSize;
    GmeiTypeE type;
    union {
        int64_t *int64s;
        // Single `char*` pointer but it's in fact a buffer containing all the strings
        //  -> number of rows * max_size(col)
        char *strings;
        // Single `uint8_t*` pointer but it's in fact a buffer containing all the blobs
        //  -> number of rows * max_size(col)
        uint8_t *blobs;
    } array;
    uintptr_t *ptrs;
    bool isSizeUpdate;
} GmeiDataColumnT;

typedef struct {
    bool tableCreated;
    GmeiStringT name;
    const GmeiTableT *info;
    size_t columnCount;
    GmeiDataColumnT *columns;
    size_t sqliteColumnCount;
    bool isLastBatch;
    uint32_t offset;
    GmeiStringT dataFullFilePath;
    GmeiOwnedStringT offsetFullFilePath;
} GmeiDataTableT;

GmeiDataTableT *GmeiDataTableInit(const GmeiTableT *table, GmeiStringT tableName, size_t *rowCount);
void GmeiDataTableReset(GmeiDataTableT *data);
void GmeiDataTableDestroy(GmeiDataTableT *data);

#endif
