/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: nlog importer
 * Create: 2024-05-31
 */
#ifndef GMEI_BASIC_TYPES_H
#define GMEI_BASIC_TYPES_H

#include <string.h>
#include "gmei_string.h"

typedef struct {
    GmeiOwnedStringT key;
    GmeiOwnedStringT val;
} GmeiKvT;

typedef struct {
    size_t size;
    GmeiKvT *array;
} GmeiKvArrayT;

typedef enum { GMEI_I64, GMEI_STR, GMEI_BLOB, GMEI_TEXT, GMEI_INET, GMEI_KV } GmeiTypeE;

#endif
