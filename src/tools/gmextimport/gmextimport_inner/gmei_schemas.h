/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: nlog importer
 * Create: 2024-05-31
 */
#ifndef GMEI_SCHEMAS_H
#define GMEI_SCHEMAS_H

#include <dirent.h>
#include "gmc_errno.h"
#include "gmei_basic_types.h"
#include "gmei_convert.h"
#include "gmei_options.h"

#define MAX_JSON_PATH_LEN 1024
#define SRC_FILE "src_file"
#define SRC_TABLE "src_table"
#define DST_TABLE "dst_table"

#define TIME_COL "time_col"
#define INTERVAL "interval"
#define IS_VOLATILE_LABEL "is_volatile_label"
#define COMPRESSION "compression"
#define CACHE_SIZE "cache_size"
#define TTL "ttl"
#define DISK_LIMIT "disk_limit"
#define TABLE_PATH "table_path"
#define SENSITIVE_COL "sensitive_col"

#define ENGINE "engine"

#define CHUNK_CAPACITY "chunk_capacity"
#define SRC_DEF "src_def"
#define DST_DEF "dst_def"
#define SRC_NAME "src_name"
#define SRC_TYPE "src_type"
#define MAX_SIZE "max_size"
#define DST_NAME "dst_name"
#define DST_TYPE "dst_type"
#define SRC_DATA "src_data"

#define GMEI_STR_ "GMEI_STR_"
#define GMEI_I64_ "GMEI_I64_"
#define GMEI_BLOB_ "GMEI_BLOB_"
#define GMEI_TEXT_ "GMEI_TEXT_"
#define GMEI_INET_ "GMEI_INET_"

#define ONE_TO_MULTI 100

typedef struct {
    union {
        GmeiTypeE readType;
        GmeiTypeE writeType;
    };
    union {
        int64_t maxSize;
        int64_t targetColIdxNum;  // 考虑单个src_data列可能对应多个dst列的场景
    };
    union {
        GmeiOwnedStringT srcName;
        GmeiOwnedStringT dstName;
    };
    GmeiConvertT cvtType;
    bool isMultiSrcData;
    uintptr_t targetColIdx;  // 一对一时为下标，一对多时为数组
} GmeiColumnT;

typedef struct {
    GmeiOwnedStringT readFile;
    GmeiOwnedStringT tableName;
    GmeiOwnedStringT tsdbTblName;
    GmeiOwnedStringT timeColumn;
    GmeiOwnedStringT cacheSize;
    GmeiOwnedStringT compression;
    GmeiOwnedStringT diskLimit;
    GmeiOwnedStringT interval;
    GmeiOwnedStringT isVolatileLabel;
    GmeiOwnedStringT sensitiveCol;
    GmeiOwnedStringT tablePath;
    GmeiOwnedStringT ttl;
    int64_t chunkCapacity;
    size_t columnCount;
    size_t tsdbColCount;
    GmeiColumnT *columns;
    GmeiColumnT *tsdbCols;
} GmeiTableT;

GmeiStringT GmeiStmtInit(const GmeiTableT *table, GmeiStringT tableName, char *allocated, uint32_t maxSize);
const GmeiTableT *GmeiMatchFilenameToTable(GmeiStringT filename);
GmeiStringT GmeiMatchTableName(GmeiTableT table, GmeiKvArrayT arr, GmeiStringT filename);
void FreeAllocatedSpace4Table(GmeiTableT *curTable);
int32_t GmeiInitSchemas(GmeiOptionsT *opts);
void GmeiUnInitSchemas(void);

#endif
