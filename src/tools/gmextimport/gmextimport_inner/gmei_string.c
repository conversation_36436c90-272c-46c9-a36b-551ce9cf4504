/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: nlog importer
 * Create: 2024-05-31
 */
#include <stdlib.h>
#include <securec.h>
#include "gmei_alloc.h"
#include "gmei_string.h"

GmeiStringT GmeiStringFromChars(const char *cs)
{
    GmeiStringT str = {.data = cs, .length = strlen(cs)};
    return str;
}

GmeiOwnedStringT GmeiStringCopy(const char *cs, size_t size)
{
    GmeiOwnedStringT str = {0};
    char *allocated = (char *)GmeiAlloc(size + 1);
    if (allocated == NULL) {
        return str;
    }
    memcpy_s(allocated, size, cs, size);
    allocated[size] = '\0';
    str.data = allocated;
    str.length = size;
    return str;
}

bool GmeiStringEqual(GmeiStringT lhs, GmeiStringT rhs)
{
    return (lhs.length == rhs.length) && (strcmp(lhs.data, rhs.data) == 0);
}
