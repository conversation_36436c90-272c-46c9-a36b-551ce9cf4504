/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: nlog importer
 * Create: 2024-05-31
 */
#ifndef GMEI_WRITER_GMC_H
#define GMEI_WRITER_GMC_H

#include "gmei_data.h"
#include "gmc.h"

typedef struct {
    GmcConnT *conn;
    GmcConnOptionsT *opts;
    GmcStmtT *stmt;
    uint64_t timeLogger;
    Status ret;
} GmeiWriterT;

GmeiWriterT GmeiWriterInit(GmeiStringT uri);
bool GmeiWriterValidate(GmeiWriterT writer);
bool GmeiWriterCreateAndWrite(GmeiWriterT writer, GmeiDataTableT *data);
bool GmeiWriterWrite(GmeiWriterT *writer, const GmeiDataTableT *data);
bool GmeiWriterCreateTable(GmeiWriterT *writer, const GmeiTableT *table, GmeiStringT tableName);
bool GmeiWriterPrepareTable(GmeiWriterT *writer, GmeiStringT tableName, const GmeiDataTableT *data);
void GmeiWriterDestroy(GmeiWriterT *writer);

int32_t TryReConnect(GmeiWriterT *writer);

static inline bool NotConnected(int32_t ret)
{
    return ret == GMERR_CONNECTION_RESET_BY_PEER || ret == GMERR_CONNECTION_FAILURE ||
           ret == GMERR_CONNECTION_EXCEPTION;
}

#endif
