/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: nlog importer
 * Create: 2024-05-31
 */
#include "gmc_sql.h"
#include "gmc_tablespace.h"
#include "gmc.h"
#include "adpt_string.h"
#include "gmei_log.h"
#include "gmei_utils.h"
#include "gmei_writer.h"

#define CREATE_STATEMENT_BUFFER_SIZE 65535

Status WriterConnect(GmeiWriterT *writer)
{
    Status ret = GmcConnect(GMC_CONN_TYPE_SYNC, writer->opts, &writer->conn);
    if (!GmeiHandleGmdbError(GmeiStringFromChars("GmcConnect"), ret)) {
        return ret;
    }
    ret = GmcAllocStmt(writer->conn, &writer->stmt);
    if (!GmeiHandleGmdbError(GmeiStringFromChars("GmcAllocStmt"), ret)) {
        return ret;
    }
    return GMERR_OK;
}

int32_t TryReConnect(GmeiWriterT *writer)
{
    GmcFreeStmt(writer->stmt);
    writer->stmt = NULL;
    GmcDisconnect(writer->conn);
    writer->conn = NULL;
    usleep(RETRY_WAIT_TIME_US);
    return WriterConnect(writer);
}

static GmcDataTypeE GmeiGetDbType(GmeiTypeE t)
{
    switch (t) {
        case GMEI_I64:
            return GMC_DATATYPE_INT64;
        case GMEI_INET:
        case GMEI_STR:
            return GMC_DATATYPE_FIXED;
        case GMEI_BLOB:
            return GMC_DATATYPE_BYTES;
        case GMEI_TEXT:
            return GMC_DATATYPE_STRING;
        case GMEI_KV:
            break;
    }
    (void)DbPrintfDefault("Unsupported type.\n");
    return GMC_DATATYPE_BUTT;
}

static uint32_t GmeiGetDbLength(GmeiDataColumnT col)
{
    switch (col.type) {
        case GMEI_I64:
            return sizeof(int64_t);
        case GMEI_INET:
        case GMEI_STR:
            return (uint32_t)col.maxElementSize;
        case GMEI_TEXT:
        case GMEI_BLOB:
            return sizeof(uintptr_t);
        case GMEI_KV:
            break;
    }
    (void)DbPrintfDefault("Unsupported type.\n");
    return DB_INVALID_UINT32;
}

static bool GmeiBindGmdbColumns(GmeiWriterT *writer, const GmeiDataTableT *data)
{
    for (uint32_t idx = 0; idx < data->columnCount; ++idx) {
        GmeiDataColumnT col = data->columns[idx];
        void *value = (col.type == GMEI_BLOB || col.type == GMEI_TEXT) ? (void *)col.ptrs : (void *)col.array.int64s;
        // we can take any pointer in `col.array`
        int32_t ret = GmcBindCol(writer->stmt, idx, GmeiGetDbType(col.type), value, GmeiGetDbLength(col), 0);
        if (!GmeiHandleGmdbError(GmeiStringFromChars("GmcBindCol"), ret)) {
            return false;
        }
    }
    return true;
}

GmeiWriterT GmeiWriterInit(GmeiStringT uri)
{
    GmeiWriterT writer;
    writer.conn = NULL;
    writer.opts = NULL;
    writer.stmt = NULL;
    writer.timeLogger = 0u;

    int32_t ret = GmcConnOptionsCreate(&writer.opts);
    if (!GmeiHandleGmdbError(GmeiStringFromChars("GmcConnOptionsCreate"), ret)) {
        return writer;
    }
    ret = GmcConnOptionsSetServerLocator(writer.opts, uri.data);
    if (!GmeiHandleGmdbError(GmeiStringFromChars("GmcConnOptionsSetServerLocator"), ret)) {
        return writer;
    }
    ret = GmcConnOptionsSetCSRead(writer.opts);
    if (!GmeiHandleGmdbError(GmeiStringFromChars("GmcConnOptionsSetCSRead"), ret)) {
        return writer;
    }
    ret = GmcConnOptionsSetCSMode(writer.opts);
    if (!GmeiHandleGmdbError(GmeiStringFromChars("GmcConnOptionsSetCSMode"), ret)) {
        return writer;
    }
    ret = WriterConnect(&writer);
    if (NotConnected(ret)) {
        uint32_t retryCount = 0;
        while (retryCount < RETRY_LIMIT) {
            retryCount++;
            ret = TryReConnect(&writer);
            if (NotConnected(ret)) {
                continue;
            }
            break;
        }
    }
    if (!GmeiHandleGmdbError(GmeiStringFromChars("TryReConnect"), ret)) {
        return writer;
    }
    return writer;
}

bool GmeiWriterValidate(GmeiWriterT writer)
{
    return writer.conn && writer.opts && writer.stmt;
}

static inline int32_t RePrepareAndExecute(GmeiWriterT *writer, const GmeiDataTableT *data)
{
    int32_t ret = GmcPrepareStmtByLabelName(writer->stmt, data->name.data, GMC_OPERATION_SQL_INSERT);
    if (ret != GMERR_OK) {
        return ret;
    }
    return GmcExecute(writer->stmt);
}

bool GmeiWriterWrite(GmeiWriterT *writer, const GmeiDataTableT *data)
{
    uint32_t arraySize = (uint32_t)data->columns[0].size;
    int32_t ret = GmcSetStmtAttr(writer->stmt, GMC_STMT_ATTR_TS_ROW_ARRAY_SIZE, &arraySize, sizeof(uint32_t));
    if (!GmeiHandleGmdbError(GmeiStringFromChars("GmcSetStmtAttr:ArraySize"), ret)) {
        return false;
    }

    GmeiTimeLoggerT measure = GmeiTimeLogInit(&writer->timeLogger, __func__, __LINE__);
    ret = GmcExecute(writer->stmt);
    if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
        GmcStmtViewT *stmtView = NULL;
        ret = GmcGetStmtView(writer->stmt, &stmtView);
        if (ret != GMERR_OK) {
            goto FINISH;
        }
        uint32_t retryCount = 0;
        while (retryCount < RETRY_LIMIT) {
            retryCount++;
            ret = TryReConnect(writer);
            if (NotConnected(ret)) {
                continue;
            } else if (ret != GMERR_OK) {
                goto FINISH;
            }
            ret = GmcSetStmtView(writer->stmt, stmtView);
            if (ret != GMERR_OK) {
                goto FINISH;
            }
            ret = GmcExecute(writer->stmt);
            if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
                continue;
            } else if (ret == GMERR_UNDEFINED_TABLE) {
                ret = RePrepareAndExecute(writer, data);
                if (ret != GMERR_OK) {
                    goto FINISH;
                }
            }
            break;
        }
        GmcFreeStmtView(stmtView);
    }
FINISH:
    writer->ret = ret;
    GmeiTimeLogDestroy(&measure);
    if (!GmeiHandleGmdbError(GmeiStringFromChars("GmcExecute"), ret)) {
        return false;
    }
    return true;
}

bool GmeiWriterCreateTable(GmeiWriterT *writer, const GmeiTableT *table, GmeiStringT tableName)
{
    int32_t ret;
    char *createStatementBuffer = (char *)GmeiAlloc(sizeof(char) * CREATE_STATEMENT_BUFFER_SIZE);
    if (createStatementBuffer == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to allocate create statement buffer.");
        return false;
    }
    (void)memset_s(createStatementBuffer, CREATE_STATEMENT_BUFFER_SIZE, 0, CREATE_STATEMENT_BUFFER_SIZE);
    GmeiStringT createStmt = GmeiStmtInit(table, tableName, createStatementBuffer, CREATE_STATEMENT_BUFFER_SIZE);
    ret = GmcExecDirect(writer->stmt, createStatementBuffer, createStmt.length);
    if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
        uint32_t retryCount = 0;
        while (retryCount < RETRY_LIMIT) {
            retryCount++;
            ret = TryReConnect(writer);
            if (NotConnected(ret)) {
                continue;
            }
            ret = GmcExecDirect(writer->stmt, createStatementBuffer, createStmt.length);
            if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
                continue;
            }
            break;
        }
    }
    GmeiFree(createStatementBuffer);
    if (ret != GMERR_OK) {
        char buf[4096] = {};
        int offset = snprintf_s(buf, sizeof(buf), sizeof(buf),
            "Could not create table '%s' with statement:\n```\n%s\n```\n", tableName.data, createStmt.data);
        if (offset < 0) {
            DB_LOG_ERROR(GMERR_MEMORY_OPERATE_FAILED, "Unable to write create table message.");
        }
        GmeiHandleGmdbError((GmeiStringT){buf, offset}, ret);
        return false;
    }
    return true;
}

bool GmeiWriterPrepareTable(GmeiWriterT *writer, GmeiStringT tableName, const GmeiDataTableT *data)
{
    int32_t ret = 0;
    // This value is not used but apparently necessary
    uint32_t value = 0;
    ret = GmcSetStmtAttr(writer->stmt, GMC_STMT_ATTR_TS_BIND_BY_COL, &value, sizeof(uint32_t));
    if (!GmeiHandleGmdbError(GmeiStringFromChars("GmcSetStmtAttr BindByColumn"), ret)) {
        return false;
    }
    if (!GmeiBindGmdbColumns(writer, data)) {
        return false;
    }

    ret = GmcPrepareStmtByLabelName(writer->stmt, tableName.data, GMC_OPERATION_SQL_INSERT);
    if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
        uint32_t retryCount = 0;
        while (retryCount < RETRY_LIMIT) {
            retryCount++;
            ret = TryReConnect(writer);
            if (NotConnected(ret)) {
                continue;
            }
            ret = GmcPrepareStmtByLabelName(writer->stmt, tableName.data, GMC_OPERATION_SQL_INSERT);
            if (ret == GMERR_CONNECTION_RESET_BY_PEER) {
                continue;
            }
            break;
        }
    }
    if (!GmeiHandleGmdbError(GmeiStringFromChars("GmcPrepare"), ret)) {
        return false;
    }
    return true;
}

void GmeiWriterDestroy(GmeiWriterT *writer)
{
    if (!writer) {
        return;
    }

    if (writer->stmt) {
        GmcFreeStmt(writer->stmt);
    }
    if (writer->conn) {
        GmcDisconnect(writer->conn);
    }
    if (writer->opts) {
        GmcConnOptionsDestroy(writer->opts);
    }
    if (writer->timeLogger > 0u) {
        (void)DbPrintfDefault("writer duration: %" PRIu64 " us.\n", writer->timeLogger);
    }
}
