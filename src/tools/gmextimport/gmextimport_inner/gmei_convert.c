/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: nlog importer
 * Create: 2024-05-31
 */

#include <arpa/inet.h>
#include <stdlib.h>
#include "db_log.h"
#include "db_last_error.h"
#include "gmc_errno.h"
#include "gmei_convert.h"

#define DECIMAL 10

int64_t GmeiConvertStrnumToI64(GmeiStringT val)
{
    if (val.data == NULL || val.length == 0) {
        return 0;
    }
    char *endPtr = NULL;
    int64_t result = strtoll(val.data, &endPtr, DECIMAL);
    if (strcmp(endPtr, "\0") != 0) {
        DB_LOG_AND_SET_LASERR(GMERR_INVALID_VALUE, "Convert strnum with problem, strnum:%s", val.data);
    }
    return result;
}
