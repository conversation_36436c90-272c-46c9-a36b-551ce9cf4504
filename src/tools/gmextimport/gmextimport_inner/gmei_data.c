/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: nlog importer
 * Create: 2024-05-31
 */

#include <stdlib.h>
#include <stdio.h>
#include <unistd.h>
#include "adpt_string.h"
#include "gmei_alloc.h"
#include "gmei_data.h"

static int32_t GmeiDataColumnInit(GmeiDataColumnT *col, size_t capacity, GmeiTypeE t, size_t maxElementSize)
{
    int32_t ret = GMERR_OK;
    col->capacity = capacity;
    col->size = 0u;
    col->maxElementSize = maxElementSize;
    col->type = t;
    col->isSizeUpdate = false;
    switch (col->type) {
        case GMEI_I64:
            col->array.int64s = (int64_t *)GmeiAlloc(capacity * sizeof(int64_t));
            if (col->array.int64s == NULL) {
                ret = GMERR_OUT_OF_MEMORY;
                break;
            }
            (void)memset_s(col->array.int64s, capacity * sizeof(int64_t), 0, capacity * sizeof(int64_t));
            break;
        case GMEI_INET:
        case GMEI_STR:
            col->array.strings = (char *)GmeiAlloc(capacity * maxElementSize);
            if (col->array.strings == NULL) {
                ret = GMERR_OUT_OF_MEMORY;
                break;
            }
            (void)memset_s(col->array.strings, capacity * maxElementSize, 0, capacity * maxElementSize);
            break;
        case GMEI_TEXT:
        case GMEI_BLOB:
            col->array.blobs = (uint8_t *)GmeiAlloc(capacity * maxElementSize);
            if (col->array.blobs == NULL) {
                ret = GMERR_OUT_OF_MEMORY;
                break;
            }
            (void)memset_s(col->array.blobs, capacity * maxElementSize, 0, capacity * maxElementSize);
            col->ptrs = (uintptr_t *)GmeiAlloc(capacity * sizeof(uintptr_t));
            if (col->ptrs == NULL) {
                ret = GMERR_OUT_OF_MEMORY;
                break;
            }
            (void)memset_s(col->ptrs, capacity * sizeof(uintptr_t), 0, capacity * sizeof(uintptr_t));
        case GMEI_KV:
            break;
    }
    return ret;
}

GmeiDataTableT *GmeiDataTableInit(const GmeiTableT *table, GmeiStringT tableName, size_t *rowCount)
{
    GmeiDataTableT *data = (GmeiDataTableT *)GmeiAlloc(sizeof(GmeiDataTableT));
    if (!data) {
        return NULL;
    }
    (void)memset_s(data, sizeof(GmeiDataTableT), 0, sizeof(GmeiDataTableT));
    data->tableCreated = false;
    data->name = tableName;
    data->info = table;
    data->sqliteColumnCount = table->columnCount;
    data->columnCount = table->tsdbColCount;
    size_t colSize = data->columnCount * sizeof(GmeiDataColumnT);
    data->columns = GmeiAlloc(colSize);
    if (data->columns == NULL) {
        DB_LOG_AND_SET_LASERR(GMERR_OUT_OF_MEMORY, "Unable to allocate memory for data columns.");
        return NULL;
    }
    (void)memset_s(data->columns, colSize, 0, colSize);
    if (table->chunkCapacity > 0) {
        *rowCount = (size_t)table->chunkCapacity;
        (void)DbPrintfDefault("ChunkCapacity for %s is %" PRIi64 ".\n", tableName.data, table->chunkCapacity);
    } else {
        DB_LOG_AND_SET_LASERR(
            GMERR_INVALID_VALUE, "Chunk capacity should be positive, but it is %" PRIi64 ".", table->chunkCapacity);
    }
    for (size_t idx = 0u; idx < data->columnCount; ++idx) {
        const GmeiColumnT col = table->tsdbCols[idx];
        int32_t ret = GmeiDataColumnInit(&data->columns[idx], *rowCount, col.writeType, (size_t)col.maxSize);
        if (ret != GMERR_OK) {
            DB_LOG_AND_SET_LASERR(ret, "Init data column with problem.");
            return NULL;
        }
    }
    return data;
}

void GmeiDataTableReset(GmeiDataTableT *data)
{
    for (size_t idx = 0u; idx < data->columnCount; ++idx) {
        data->columns[idx].size = 0u;
        data->columns[idx].isSizeUpdate = false;
    }
}

void GmeiDataTableDestroy(GmeiDataTableT *data)
{
    if (data == NULL) {
        return;
    }
    for (size_t idx = 0u; idx < data->columnCount; ++idx) {
        // Any pointer will do
        GmeiFree((void *)data->columns[idx].array.int64s);
        if (data->columns[idx].ptrs != NULL) {
            GmeiFree((void *)data->columns[idx].ptrs);
        }
    }
    if (data->columnCount > 0) {
        GmeiFree((void *)data->columns);
    }
    if (data->offsetFullFilePath.data != NULL) {
        GmeiFree((void *)data->offsetFullFilePath.data);
        data->offsetFullFilePath.data = NULL;
        data->offsetFullFilePath.length = 0;
    }
    if (data->dataFullFilePath.data != NULL) {
        // 不用释放data->dataFullFilePath.data，该指针为栈变量指针
        data->dataFullFilePath.data = NULL;
        data->dataFullFilePath.length = 0;
    }
    data->columnCount = 0u;
    GmeiFree((void *)data);
}
