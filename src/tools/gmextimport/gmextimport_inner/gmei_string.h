/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 * Description: nlog importer
 * Create: 2024-05-31
 */
#ifndef GMEI_STRING_H
#define GMEI_STRING_H

#include <stdbool.h>
#include <stdint.h>
#include <string.h>

// Just a simple struct to avoid computing the length of the string when possible
typedef struct {
    const char *data;
    uint32_t length;
} GmeiStringT;

typedef struct {
    char *data;
    uint32_t length;
} GmeiOwnedStringT;

GmeiStringT GmeiStringFromChars(const char *cs);
GmeiOwnedStringT GmeiStringCopy(const char *cs, size_t size);
bool GmeiStringEqual(GmeiStringT lhs, GmeiStringT rhs);

// make a string with compile time size
#define /* encanto */    \
    GMEI_MAKE_STRING(s)  \
    {                    \
        s, sizeof(s) - 1 \
    }

#endif
