set(CMAKE_SYSTEM_NAME Linux)

set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAME NEVER)
set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)

set(CMAKE_C_COMPILER /usr/bin/gcc)
set(CMAKE_CXX_COMPILER /usr/bin/g++)
set(CMAKE_LIBRARY_ARCHITECTURE x86_64-pc-linux-gnu)
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -m32")

set(CC_CXX_OPTIONS "-std=gnu++17")
set(CMAKE_CXX_FLAGS "${CC_ALL_OPTIONS} ${CC_CXX_OPTIONS}")
set(CMAKE_CXX_FLAGS -m32)

add_compile_definitions(X86)

set(GMDB_ARCH "x86")
set(<PERSON>_NAM<PERSON> "suse")

add_compile_options(-Wall -MMD -Wl,--build-id=none)
if (NOT COMPILE_PATCH AND NOT COMPILE_PATCH_OBJ)
    add_compile_options(-ffunction-sections -fdata-sections)
endif()
add_link_options(-Wl,--gc-sections)
#add_compile_options(-march=corei7)
add_compile_options(-Wall -Wno-unused-function -MMD  -mtune=nocona  -fno-strict-aliasing -D_GNU_SOURCE -D_LARGEFILE64_SOURCE -rdynamic -rdynamic)
add_compile_options(-ffloat-store) # x86上的FPU是x87, x87有80bit的浮点数, 计算的时候中间结果会存到80bit浮点数, 因为精度不匹配可能导致非预期的结果
add_compile_options(-mcrc32)
add_definitions(-DSYS32)
